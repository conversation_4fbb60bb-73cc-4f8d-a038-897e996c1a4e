package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 通过PNR编码查询所有旅客响应VO
 *
 * <AUTHOR>
 * @date 2025/1/14 10:30
 */
@Data
@ApiModel(value = "QueryAllPassengersByPnrNoVo", description = "通过PNR编码查询所有旅客响应VO")
public class QueryAllPassengersByPnrNoVo {

    @ApiModelProperty(value = "基本信息区域")
    private BasicArea basicArea;

    @ApiModelProperty(value = "旅客信息")
    private List<Passenger> passengers;

    @ApiModelProperty(value = "PNR是否已取消")
    private Boolean canceled;

    @ApiModelProperty(value = "PNR是否不存在")
    private Boolean nonExistent;

    /**
     * 基本信息区域
     */
    @Data
    public static class BasicArea {
        @ApiModelProperty(value = "CT列表")
        private List<String> cts;

        @ApiModelProperty(value = "联系人邮箱")
        private String contactorEmail;

        @ApiModelProperty(value = "联系信息列表")
        private List<Contact> contact;

        @ApiModelProperty(value = "联系电话列表")
        private List<String> contactPhones;
    }

    /**
     * 联系信息
     */
    @Data
    public static class Contact {
        @ApiModelProperty(value = "航空公司")
        private String airline;

        @ApiModelProperty(value = "电话号码")
        private String phoneNumber;
    }

    /**
     * 旅客信息
     */
    @Data
    public static class Passenger {
        @ApiModelProperty(value = "生日")
        private String birthday;

        @ApiModelProperty(value = "中文名")
        private String chineseName;

        @ApiModelProperty(value = "名")
        private String firstName;

        @ApiModelProperty(value = "姓")
        private String lastName;

        @ApiModelProperty(value = "全名")
        private String fullName;

        @ApiModelProperty(value = "名字后缀")
        private String nameSuffix;

        @ApiModelProperty(value = "是否无陪儿童")
        private Boolean unMinor;

        @ApiModelProperty(value = "无陪儿童年龄")
        private Integer unMinorAge;

        @ApiModelProperty(value = "旅客ID")
        private Integer passengerId;

        @ApiModelProperty(value = "旅客类型")
        private String passengerType;

        @ApiModelProperty(value = "特殊旅客类型")
        private String specialPassengerType;

        @ApiModelProperty(value = "OSI CTCM")
        private String osiCtcm;

        @ApiModelProperty(value = "SSR CTCM")
        private String ssrCtcm;

        @ApiModelProperty(value = "婴儿详情")
        private InfantDetail infantDetail;

        @ApiModelProperty(value = "航段信息")
        private List<PassengerSegment> segments;

        @ApiModelProperty(value = "证件信息")
        private Document document;

        @ApiModelProperty(value = "护照信息")
        private Document documentPP;

        @ApiModelProperty(value = "票号连接")
        private String ticketNumberJoin;

        @ApiModelProperty(value = "TN票号列表")
        private List<String> ticketNumbersForTN;

        @ApiModelProperty(value = "PNR中的旅客姓名")
        private String passengerNameInPnr;

        @ApiModelProperty(value = "DOCA信息R")
        private DocaInfo docaInfoR;

        @ApiModelProperty(value = "DOCA信息D")
        private DocaInfo docaInfoD;

        @ApiModelProperty(value = "护照用于DOCS")
        private String ppForDocs;

        @ApiModelProperty(value = "身份证用于DOCS")
        private String niForDocs;

        @ApiModelProperty(value = "二次验证信息")
        private SecondFactor secondFactor;
    }

    /**
     * 旅客航段信息
     */
    @Data
    public static class PassengerSegment {
        @ApiModelProperty(value = "操作代码")
        private String actionCode;

        @ApiModelProperty(value = "票号")
        private String ticketNumber;

        @ApiModelProperty(value = "票状态")
        private String ticketStatus;

        @ApiModelProperty(value = "始发地")
        private String origin;

        @ApiModelProperty(value = "目的地")
        private String destination;

        @ApiModelProperty(value = "服务列表")
        private List<Object> services;

        @ApiModelProperty(value = "是否ARNK标识")
        private Boolean arnkInd;

        @ApiModelProperty(value = "是否开放标识")
        private Boolean openInd;

        @ApiModelProperty(value = "航空公司")
        private String airline;

        @ApiModelProperty(value = "航班号")
        private String flightNumber;

        @ApiModelProperty(value = "舱位ID")
        private String classId;

        @ApiModelProperty(value = "航班日期")
        private String flightDate;

        @ApiModelProperty(value = "出发时间")
        private String departureTime;

        @ApiModelProperty(value = "到达时间")
        private String arrivalTime;

        @ApiModelProperty(value = "OC航空公司")
        private String ocAirline;

        @ApiModelProperty(value = "OC航班号")
        private String ocFlightNumber;

        @ApiModelProperty(value = "行索引")
        private Integer lineIndex;

        @ApiModelProperty(value = "是否TCHB")
        private Boolean tchb;
    }

    /**
     * 证件信息
     */
    @Data
    public static class Document {
        @ApiModelProperty(value = "性别")
        private String gender;

        @ApiModelProperty(value = "证件号码")
        private String idCardNumber;

        @ApiModelProperty(value = "旅客国籍")
        private String passengerNationality;

        @ApiModelProperty(value = "持有人")
        private String holder;

        @ApiModelProperty(value = "签证签发国")
        private String visaIssueCountry;

        @ApiModelProperty(value = "生日")
        private String birthday;

        @ApiModelProperty(value = "签证到期日")
        private String visaExpiryDate;

        @ApiModelProperty(value = "姓")
        private String lastName;

        @ApiModelProperty(value = "名")
        private String firstName;

        @ApiModelProperty(value = "证件类型")
        private String documentType;

        @ApiModelProperty(value = "SSR类型")
        private String ssrType;

        @ApiModelProperty(value = "docsName")
        private String docsName;

        @ApiModelProperty(value = "NI用于DOCS")
        private String niForDocs;
    }

    /**
     * DOCA信息
     */
    @Data
    public static class DocaInfo {
        @ApiModelProperty(value = "国家")
        private String country;

        @ApiModelProperty(value = "州/省")
        private String state;

        @ApiModelProperty(value = "城市")
        private String city;

        @ApiModelProperty(value = "地址")
        private String address;

        @ApiModelProperty(value = "邮编")
        private String zip;
    }

    /**
     * 婴儿详情
     */
    @Data
    public static class InfantDetail {
        @ApiModelProperty(value = "生日")
        private String birthday;

        @ApiModelProperty(value = "全名")
        private String fullName;

        @ApiModelProperty(value = "姓")
        private String lastName;

        @ApiModelProperty(value = "名")
        private String firstName;

        @ApiModelProperty(value = "中文名")
        private String chineseName;

        @ApiModelProperty(value = "PNR中的婴儿姓名")
        private String passengerNameInInft;

        @ApiModelProperty(value = "航段信息")
        private List<PassengerSegment> segments;

        @ApiModelProperty(value = "旅客ID")
        private Integer passengerId;

        @ApiModelProperty(value = "行索引")
        private Integer lineIndex;

        @ApiModelProperty(value = "证件信息")
        private Document document;

        @ApiModelProperty(value = "护照信息")
        private Document documentPP;

        @ApiModelProperty(value = "DOCA信息R")
        private DocaInfo docaInfoR;

        @ApiModelProperty(value = "DOCA信息D")
        private DocaInfo docaInfoD;

        @ApiModelProperty(value = "TN票号列表")
        private List<String> ticketNumbersForTN;

        @ApiModelProperty(value = "TN行索引列表")
        private List<String> tnLineIndex;

        @ApiModelProperty(value = "TKNE行索引列表")
        private List<String> tkneLineIndex;

        @ApiModelProperty(value = "票号连接")
        private String ticketNumberJoin;

        @ApiModelProperty(value = "出票类型")
        private String issueType;

        @ApiModelProperty(value = "PNR中的旅客姓名")
        private String passengerNameInPnr;

        @ApiModelProperty(value = "旅客类型")
        private String passengerType;

        @ApiModelProperty(value = "名字后缀")
        private String nameSuffix;

        @ApiModelProperty(value = "特殊旅客类型")
        private String specialPassengerType;

        @ApiModelProperty(value = "未识别的TKNE列表")
        private List<String> unidentifiedTknes;
    }

    /**
     * 二次验证信息
     */
    @Data
    public static class SecondFactor {
        @ApiModelProperty(value = "二次验证代码")
        private String secondFactorCode;

        @ApiModelProperty(value = "二次验证值")
        private String secondFactorValue;
    }
}
