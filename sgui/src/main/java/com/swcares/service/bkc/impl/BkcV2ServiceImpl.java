package com.swcares.service.bkc.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.security.custom.CustomUserDetails;
import com.swcares.core.security.custom.UserInfo;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.*;
import com.swcares.obj.dto.*;
import com.swcares.obj.vo.*;
import com.swcares.service.*;
import com.swcares.service.bkc.*;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/23 14:34
 */
@Service
public class BkcV2ServiceImpl implements IBkcV2Service {

    @Resource
    private IMnjxAgentService iMnjxAgentService;

    @Resource
    private IMnjxAgentAirlineService iMnjxAgentAirlineService;

    @Resource
    private IMnjxAirlineService iMnjxAirlineService;

    @Resource
    private IPnrDetailService iPnrDetailService;

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxPnrRecordService iMnjxPnrRecordService;

    @Resource
    private IMnjxPnrAtService iMnjxPnrAtService;

    @Resource
    private IMnjxSiService iMnjxSiService;

    @Resource
    private IMnjxOfficeService iMnjxOfficeService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;

    @Resource
    private ISguiCommonService iSguiCommonService;

    @Resource
    private IUpdatePnrService iUpdatePnrService;

    @Resource
    private IIssueService iIssueService;

    @Resource
    private IRemoveNameService iRemoveNameService;

    @Resource
    private ISplitPnrService iSplitPnrService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Override
    public CheckVersionVo checkVersion(CheckVersionDto dto) {
        CheckVersionVo vo = new CheckVersionVo();
        vo.setApp(dto.getApp());
        vo.setModule(dto.getModule());
        vo.setVersion(dto.getVersion());
        vo.setVersionAttr(dto.getVersionAttr());
        vo.setUserInfo(BeanUtil.copyProperties(dto.getUserInfo(), CheckVersionVo.UserInfo.class));
        vo.setUpgradeStrategy(1);
        vo.setOnlineUpgradeFlag(false);
        vo.setDaysRemaining(0);
        return vo;
    }

    @Override
    public List<String> domesticairline() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof CustomUserDetails) {
            CustomUserDetails customUserDetails = (CustomUserDetails) authentication.getPrincipal();
            UserInfo userInfo = customUserDetails.getUserInfo();
            MnjxOffice office = userInfo.getMnjxOffice();
            if ("0".equals(office.getOfficeType())) {
                MnjxAgent agent = iMnjxAgentService.getById(office.getOrgId());
                List<MnjxAgentAirline> agentAirlineList = iMnjxAgentAirlineService.lambdaQuery()
                        .eq(MnjxAgentAirline::getAgentId, agent.getAgentId())
                        .list();
                List<String> airlineIdList = agentAirlineList.stream()
                        .map(MnjxAgentAirline::getAirlineId)
                        .collect(Collectors.toList());
                List<MnjxAirline> airlineList = iMnjxAirlineService.listByIds(airlineIdList);
                return airlineList.stream()
                        .map(MnjxAirline::getAirlineCode)
                        .collect(Collectors.toList());
            }
        }
        return Collections.emptyList();
    }

    @Override
    public QueryPnrDetailVo queryPnrDetail(QueryPnrDetailDto dto) throws SguiResultException {
        // 调用PnrCommonService的queryPnrDetail方法
        return iPnrDetailService.queryPnrDetail(dto);
    }

    @Override
    public PnrHistoryVo queryPnrHistory(String pnrNo) throws SguiResultException {
        if (StrUtil.isEmpty(pnrNo)) {
            throw new SguiResultException("PNR编号不能为空");
        }

        // 查询PNR信息
        MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, pnrNo)
                .one();
        if (pnr == null) {
            throw new SguiResultException("未找到PNR信息");
        }

        // 查询PNR历史记录
        List<MnjxPnrRecord> pnrRecords = iMnjxPnrRecordService.lambdaQuery()
                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrRecord::getPnrIndex)
                .list();

        if (CollUtil.isEmpty(pnrRecords)) {
            return null;
        }

        // 筛选pnrRecords中changeAtNo不为空的数据
        List<MnjxPnrRecord> changeRecordList = pnrRecords.stream()
                .filter(r -> StrUtil.isNotEmpty(r.getChangeAtNo()))
                .collect(Collectors.toList());

        // 查询PNR封口记录
        List<MnjxPnrAt> pnrAts = iMnjxPnrAtService.lambdaQuery()
                .eq(MnjxPnrAt::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrAt::getAtNo)
                .orderByAsc(MnjxPnrAt::getAtDateTime)
                .list();

        // 构建返回对象
        PnrHistoryVo vo = new PnrHistoryVo();
        vo.setPnrNo(pnrNo);

        // 构建历史行内容
        List<PnrHistoryVo.LineContent> historyLineContents = new ArrayList<>();

        // 处理历史记录
        for (MnjxPnrRecord record : pnrRecords) {
            if (StrUtil.isNotEmpty(record.getChangeMark())) {
                continue;
            }
            PnrHistoryVo.LineContent lineContent = new PnrHistoryVo.LineContent();
            lineContent.setIndex(record.getAtNo());
            lineContent.setContent(StrUtil.format("    {}.{}", record.getPnrIndex(), record.getInputValue()));
            if (StrUtil.equalsAny(record.getPnrType(), "EI", "NM EI")) {
                lineContent.setContent(StrUtil.format("    {}.EI/{}", record.getPnrIndex(), record.getInputValue()));
            }
            lineContent.setCanDelete(false);
            lineContent.setCode(null);

            lineContent.setInvalidSeg(null);

            historyLineContents.add(lineContent);
        }

        // 处理封口记录
        MnjxSi mnjxSi = iMnjxSiService.getById(pnrAts.get(0).getAtSiId());
        MnjxOffice mnjxOffice = iMnjxOfficeService.getById(mnjxSi.getOfficeId());
        String siNo = mnjxSi.getSiNo();
        String officeNo = mnjxOffice.getOfficeNo();
        for (MnjxPnrAt at : pnrAts) {
            PnrHistoryVo.LineContent lineContent = new PnrHistoryVo.LineContent();
            lineContent.setIndex(at.getAtNo());

            // 格式化封口时间
            String atDate = DateUtils.ymd2Com(DateUtil.format(at.getAtDateTime(), "yyyy-MM-dd")).substring(0, 5);
            String atTime = DateUtil.format(at.getAtDateTime(), "HHmm");

            // 构建封口行内容
            String content;
            if (StrUtil.isEmpty(at.getAtType())) {
                content = StrUtil.format("    {} {} {} {}", pnr.getPnrIcs(), officeNo, atTime, atDate);
            } else {
                if (StrUtil.equalsAny(at.getAtType(), "I", "KI")) {
                    content = StrUtil.format("    {} {} {} {} {}", pnr.getPnrIcs(), officeNo, atTime, atDate, at.getAtType());
                } else {
                    if (Integer.parseInt(at.getAtNo()) > 1) {
                        content = StrUtil.format("    HDQ{} {} {} {} /RLC{}", at.getAtType(), siNo, atTime, atDate, Integer.parseInt(at.getAtNo()) - 1);
                    } else {
                        content = StrUtil.format("    HDQ{} {} {} {}", at.getAtType(), siNo, atTime, atDate);
                    }
                }
            }

            lineContent.setContent(content);
            lineContent.setCanDelete(false);
            lineContent.setCode(null);
            lineContent.setInvalidSeg(false);

            historyLineContents.add(lineContent);

            for (MnjxPnrRecord record : changeRecordList) {
                if ((StrUtil.isNotEmpty(at.getAtType()) && !StrUtil.equalsAny(at.getAtType(), "K", "I", "KI")) || !at.getAtNo().equals(record.getAtNo())) {
                    continue;
                }
                PnrHistoryVo.LineContent changelineContent = new PnrHistoryVo.LineContent();
                changelineContent.setIndex(record.getAtNo() + "/" + record.getChangeAtNo());
                changelineContent.setContent(record.getInputValue());
                if (StrUtil.equalsAny(record.getPnrType(), "EI", "NM EI")) {
                    changelineContent.setContent("EI/" + record.getInputValue());
                }
                changelineContent.setCanDelete(false);
                changelineContent.setCode(null);

                changelineContent.setInvalidSeg(null);

                historyLineContents.add(changelineContent);
            }
        }

        vo.setHistoryLineContents(historyLineContents);
        return vo;
    }

    @Override
    public QueryPnrRtlVo queryPnrRtl(QueryPnrRtlDto dto) throws SguiResultException {
        if (StrUtil.isEmpty(dto.getPnrNo())) {
            throw new SguiResultException("PNR编号不能为空");
        }

        // 查询PNR信息
        MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, dto.getPnrNo())
                .one();
        if (pnr == null) {
            throw new SguiResultException("未找到PNR信息");
        }

        // 查询PNR记录
        List<MnjxPnrRecord> pnrRecords = iMnjxPnrRecordService.lambdaQuery()
                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrRecord::getPnrIndex)
                .list();

        if (CollUtil.isEmpty(pnrRecords)) {
            return null;
        }

        pnrRecords = pnrRecords.stream()
                .filter(p -> StrUtil.isEmpty(p.getChangeAtNo()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(pnrRecords)) {
            return null;
        }

        // 构建返回对象
        QueryPnrRtlVo vo = new QueryPnrRtlVo();
        List<QueryPnrRtlVo.LineContent> rtlLineContents = new ArrayList<>();

        // 处理PNR记录
        for (int i = 0; i < pnrRecords.size(); i++) {
            MnjxPnrRecord record = pnrRecords.get(i);
            QueryPnrRtlVo.LineContent lineContent = new QueryPnrRtlVo.LineContent();
            lineContent.setIndex(record.getPnrIndex() + ".");
            lineContent.setContent(record.getInputValue());
            if (i + 1 < pnrRecords.size() && "NM".equals(record.getPnrType()) && !"NM".equals(pnrRecords.get(i + 1).getPnrType())) {
                lineContent.setContent(record.getInputValue() + " " + dto.getPnrNo());
            }
            lineContent.setCanDelete(false);
            lineContent.setCode(null);
            lineContent.setInvalidSeg(false);

            rtlLineContents.add(lineContent);
        }

        vo.setRtlLineContent(rtlLineContents);
        return vo;
    }

    @Override
    public void strongRefresh(StrongRefreshDto dto) throws SguiResultException {
        if (StrUtil.isEmpty(dto.getPnrNo())) {
            throw new SguiResultException("PNR编号不能为空");
        }

        // 查询PNR信息
        MnjxPnr pnr = this.iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, dto.getPnrNo())
                .one();
        if (pnr == null) {
            throw new SguiResultException("未找到PNR信息");
        }

        // 查询PNR中的旅客信息
        List<MnjxPnrNm> pnrNmList = this.iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .list();
        if (CollUtil.isEmpty(pnrNmList)) {
            return; // 没有旅客信息，直接返回
        }

        // 获取当前登录用户的工作号信息
        UserInfo userInfo = iSguiCommonService.getCurrentUserInfo();
        if (userInfo == null) {
            throw new SguiResultException("获取当前用户信息失败");
        }

        MnjxSi currentSi = iMnjxSiService.getById(userInfo.getSiId());
        if (currentSi == null) {
            throw new SguiResultException("获取当前用户工作号信息失败");
        }

        // 查询PNR记录
        List<MnjxPnrRecord> pnrRecords = iMnjxPnrRecordService.lambdaQuery()
                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                .list();

        // 生成新的封口编号
        String newAtNo = iSguiCommonService.generateNewAtNo(pnr.getPnrId());

        // 遍历旅客，查找婴儿信息
        boolean hasUpdates = false;
        for (MnjxPnrNm pnrNm : pnrNmList) {
            // 查询旅客是否有婴儿
            MnjxNmXn nmXn = this.iMnjxNmXnService.lambdaQuery()
                    .eq(MnjxNmXn::getPnrNmId, pnrNm.getPnrNmId())
                    .one();

            if (nmXn != null) {
                // 查询婴儿的SSR INFT信息
                List<MnjxNmSsr> infantSsrList = this.iMnjxNmSsrService.lambdaQuery()
                        .eq(MnjxNmSsr::getPnrNmId, pnrNm.getPnrNmId())
                        .eq(MnjxNmSsr::getSsrType, "INFT")
                        .list();

                if (CollUtil.isNotEmpty(infantSsrList)) {
                    // 遍历婴儿的SSR INFT信息，检查行动代码
                    List<MnjxNmSsr> updateSsrList = new ArrayList<>();
                    List<MnjxPnrRecord> updateRecordList = new ArrayList<>();

                    for (MnjxNmSsr infantSsr : infantSsrList) {
                        if (!"HK".equals(infantSsr.getActionCode())) {
                            hasUpdates = true;
                            // 修改行动代码为HK
                            String oldActionCode = infantSsr.getActionCode();
                            infantSsr.setActionCode("HK");

                            // 修改ssr_info和input_value中的行动代码
                            if (StrUtil.isNotEmpty(infantSsr.getSsrInfo())) {
                                infantSsr.setSsrInfo(infantSsr.getSsrInfo().replace(oldActionCode, "HK"));
                            }

                            if (StrUtil.isNotEmpty(infantSsr.getInputValue())) {
                                infantSsr.setInputValue(infantSsr.getInputValue().replace(oldActionCode, "HK"));
                            }

                            updateSsrList.add(infantSsr);

                            // 查找对应的PNR记录
                            List<MnjxPnrRecord> ssrRecords = pnrRecords.stream()
                                    .filter(record -> "SSR".equals(record.getPnrType()) &&
                                            record.getPnrNmId() != null &&
                                            record.getPnrNmId().equals(pnrNm.getPnrNmId()) &&
//                                            StrUtil.isEmpty(record.getChangeAtNo()) &&
                                            record.getInputValue() != null &&
                                            record.getInputValue().contains("INFT") &&
                                            record.getInputValue().contains(oldActionCode))
                                    .collect(Collectors.toList());

                            for (MnjxPnrRecord ssrRecord : ssrRecords) {
                                // 更新现有记录
                                ssrRecord.setInputValue(ssrRecord.getInputValue().replace(oldActionCode, "HK"));
                                ssrRecord.setChangeMark("U"); // 设置为修改
                                ssrRecord.setChangeAtNo(newAtNo);

                                updateRecordList.add(ssrRecord);
                            }
                        }
                    }

                    // 批量更新SSR信息
                    if (CollUtil.isNotEmpty(updateSsrList)) {
                        this.iMnjxNmSsrService.updateBatchById(updateSsrList);
                    }

                    // 批量更新PNR记录
                    if (CollUtil.isNotEmpty(updateRecordList)) {
                        this.iMnjxPnrRecordService.updateBatchById(updateRecordList);
                    }
                }
            }
        }

        // 如果有更新，创建新的封口记录
        if (hasUpdates) {

            // 创建新的封口记录
            MnjxPnrAt pnrAt = new MnjxPnrAt();
            pnrAt.setPnrAtId(IdUtil.getSnowflake(1, 1).nextIdStr());
            pnrAt.setPnrId(pnr.getPnrId());
            pnrAt.setAtNo(newAtNo);
            pnrAt.setAtSiId(currentSi.getSiId());
            pnrAt.setAtDateTime(new Date());
            pnrAt.setAtType("KI"); // 设置封口类型为KI

            iMnjxPnrAtService.save(pnrAt);
        }
    }

    @Override
    public UpdatePnrVo updatePnr(UpdatePnrReqDto reqDto) throws SguiResultException {
        // 参数校验
        if (reqDto == null || StrUtil.isEmpty(reqDto.getRequest())) {
            throw new SguiResultException("请求参数不能为空");
        }

        // Base64解码
        String decodedJson = new String(java.util.Base64.getDecoder().decode(reqDto.getRequest()),
                java.nio.charset.StandardCharsets.UTF_8);

        // JSON转换为DTO
        UpdatePnrDto dto = JSONUtil.toBean(decodedJson, UpdatePnrDto.class);

        // 调用业务服务
        return iUpdatePnrService.updatePnr(dto);
    }

    @Override
    public Object querySeatMap(QuerySeatMapDto dto) throws SguiResultException {
        // 暂时不处理业务逻辑，返回null
        return null;
    }

    @Override
    public IssueTicketVo issueTicket(IssueTicketDto dto) throws SguiResultException {
        // 调用IIssueService的issueTicket方法
        return iIssueService.issueTicket(dto);
    }

    @Override
    public void removeName(RemoveNameDto dto) throws SguiResultException {
        // 调用IRemoveNameService的removeName方法
        iRemoveNameService.removeName(dto);
    }

    @Override
    public SplitPnrByPassengerVo splitPnrByPassenger(SplitPnrByPassengerDto dto) throws SguiResultException {
        // 调用ISplitPnrService的splitPnrByPassenger方法
        return iSplitPnrService.splitPnrByPassenger(dto);
    }

    @Override
    public String xePnr(XePnrDto dto) throws SguiResultException {
        // 参数校验
        if (StrUtil.isEmpty(dto.getPnrNo())) {
            throw new SguiResultException("PNR编号不能为空");
        }

        // 通过pnrNo查询PNR
        MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, dto.getPnrNo())
                .one();

        if (pnr == null) {
            throw new SguiResultException("PNR不存在");
        }

        // 检查PNR状态
        if ("DEL".equals(pnr.getPnrStatus())) {
            throw new SguiResultException("该PNR已取消");
        }

        // 将PNR状态修改为DEL
        pnr.setPnrStatus("DEL");
        iMnjxPnrService.updateById(pnr);

        // 更新行动代码为XX
        List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .isNotNull(MnjxPnrSeg::getActionCode)
                .list();
        pnrSegList.forEach(p -> {
            p.setActionCode("XX");
            String inputValue = p.getInputValue();
            inputValue = inputValue.replace(" RR", " XX");
            inputValue = inputValue.replace(" HK", " XX");
            inputValue = inputValue.replace(" DK", " XX");
            p.setInputValue(inputValue);
        });
        List<MnjxPnrNm> pnrNmList = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .list();
        List<MnjxNmSsr> ssrList = iMnjxNmSsrService.lambdaQuery()
                .in(MnjxNmSsr::getPnrNmId, pnrNmList.stream().map(MnjxPnrNm::getPnrNmId).collect(Collectors.toList()))
                .isNotNull(MnjxNmSsr::getActionCode)
                .list();
        ssrList.forEach(p -> {
            p.setActionCode("XX");
            String inputValue = p.getInputValue();
            inputValue = inputValue.replace(" RR", " XX");
            inputValue = inputValue.replace(" HK", " XX");
            inputValue = inputValue.replace(" DK", " XX");
            p.setInputValue(inputValue);
            p.setSsrInfo(inputValue);
        });
        iMnjxPnrSegService.updateBatchById(pnrSegList);
        iMnjxNmSsrService.updateBatchById(ssrList);

        // 生成新的封口记录
        String newAtNo = iSguiCommonService.generateNewAtNo(pnr.getPnrId());
        MnjxPnrAt pnrAt = new MnjxPnrAt();
        pnrAt.setPnrAtId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrAt.setPnrId(pnr.getPnrId());
        pnrAt.setAtNo(newAtNo);
        MnjxPnrSeg seg = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .orderByDesc(MnjxPnrSeg::getPnrSegNo)
                .list()
                .get(0);
        pnrAt.setAtType(seg.getFlightNo().substring(0, 2));

        // 获取当前用户信息
        UserInfo userInfo = iSguiCommonService.getCurrentUserInfo();
        if (userInfo != null) {
            pnrAt.setAtSiId(userInfo.getSiId());
        }

        pnrAt.setAtDateTime(new Date());

        // 保存封口记录
        iMnjxPnrAtService.save(pnrAt);

        // 更新所有changeMark为空的pnrRecord记录
        List<MnjxPnrRecord> recordsToUpdate = iMnjxPnrRecordService.lambdaQuery()
                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                .and(wrapper -> wrapper.isNull(MnjxPnrRecord::getChangeMark)
                        .or()
                        .eq(MnjxPnrRecord::getChangeMark, ""))
                .list();

        if (CollUtil.isNotEmpty(recordsToUpdate)) {
            for (MnjxPnrRecord record : recordsToUpdate) {
                record.setChangeMark("D");
                record.setChangeAtNo(newAtNo);
                if (StrUtil.equalsAny(record.getPnrType(), "SEG", "SSR")) {
                    String inputValue = record.getInputValue();
                    inputValue = inputValue.replace(" RR", " XX");
                    inputValue = inputValue.replace(" HK", " XX");
                    inputValue = inputValue.replace(" KK", " XX");
                    record.setInputValue(inputValue);
                }
            }
            iMnjxPnrRecordService.updateBatchById(recordsToUpdate);
        }

        return dto.getPnrNo();
    }
}
