package com.swcares.service.tc.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.swcares.common.result.SguiResultException;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.*;
import com.swcares.obj.dto.QueryAllPassengersByPnrNoDto;
import com.swcares.obj.vo.QueryAllPassengersByPnrNoVo;
import com.swcares.service.tc.ITcV3Service;
import com.swcares.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * TC V3服务实现类
 *
 * <AUTHOR>
 * @date 2025/1/14 10:30
 */
@Slf4j
@Service
public class TcV3ServiceImpl implements ITcV3Service {

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;

    @Resource
    private IMnjxPnrCtService iMnjxPnrCtService;

    @Resource
    private IMnjxNmCtService iMnjxNmCtService;

    @Resource
    private IMnjxPnrNmTicketService iMnjxPnrNmTicketService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;

    @Resource
    private IMnjxNmOsiService iMnjxNmOsiService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    /**
     * 证件信息正则表达式
     */
    private static final Pattern REG_FOID = Pattern.compile("SSR\\sFOID\\s[A-Z0-9]{2}\\s\\w{3}\\s([\\w]+)/\\w{2,}");

    /**
     * DOCS信息正则表达式
     */
    private static final Pattern REG_DOCS = Pattern.compile("SSR\\sDOCS\\s[A-Z0-9]{2}\\sHK1\\s([A-Z]{2})/([A-Z]{2})/([A-Z0-9]+)/([A-Z]{2})/([0-9A-Z]+)/([MF])/([0-9A-Z]+)/([A-Z]+)/([A-Z]+)");

    @Override
    public QueryAllPassengersByPnrNoVo queryAllPassengersByPnrNo(QueryAllPassengersByPnrNoDto dto) throws SguiResultException {
        // 参数验证
        if (StrUtil.isEmpty(dto.getPnrNo())) {
            throw new SguiResultException("PNR编号不能为空");
        }

        // 查询PNR信息
        MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, dto.getPnrNo())
                .one();

        QueryAllPassengersByPnrNoVo result = new QueryAllPassengersByPnrNoVo();

        if (pnr == null) {
            result.setNonExistent(true);
            result.setCanceled(false);
            result.setBasicArea(new QueryAllPassengersByPnrNoVo.BasicArea());
            result.setPassengers(Collections.emptyList());
            return result;
        }

        // 检查PNR状态
        if ("DEL".equals(pnr.getPnrStatus())) {
            result.setCanceled(true);
            result.setNonExistent(false);
            result.setBasicArea(new QueryAllPassengersByPnrNoVo.BasicArea());
            result.setPassengers(Collections.emptyList());
            return result;
        }

        result.setCanceled(false);
        result.setNonExistent(false);

        // 构建基本信息区域
        QueryAllPassengersByPnrNoVo.BasicArea basicArea = this.buildBasicArea(pnr.getPnrId());
        result.setBasicArea(basicArea);

        // 查询并构建旅客信息
        List<QueryAllPassengersByPnrNoVo.Passenger> passengers = this.buildPassengers(pnr.getPnrId());
        result.setPassengers(passengers);

        return result;
    }

    /**
     * 构建基本信息区域
     */
    private QueryAllPassengersByPnrNoVo.BasicArea buildBasicArea(String pnrId) {
        QueryAllPassengersByPnrNoVo.BasicArea basicArea = new QueryAllPassengersByPnrNoVo.BasicArea();

        // 查询PNR联系人信息
        List<MnjxPnrCt> pnrCtList = iMnjxPnrCtService.lambdaQuery()
                .eq(MnjxPnrCt::getPnrId, pnrId)
                .list();

        List<String> cts = new ArrayList<>();
        List<String> contactPhones = new ArrayList<>();
        List<QueryAllPassengersByPnrNoVo.Contact> contacts = new ArrayList<>();

        if (CollUtil.isNotEmpty(pnrCtList)) {
            for (MnjxPnrCt pnrCt : pnrCtList) {
                String ctText = pnrCt.getCtText();
                if (StrUtil.isNotEmpty(ctText)) {
                    cts.add(ctText);
                    contactPhones.add(ctText);

                    // 解析联系信息，假设格式为 "航空公司代码-电话号码"
                    QueryAllPassengersByPnrNoVo.Contact contact = new QueryAllPassengersByPnrNoVo.Contact();
                    if (ctText.contains("-")) {
                        String[] parts = ctText.split("-", 2);
                        contact.setAirline(parts[0]);
                        contact.setPhoneNumber(parts[1]);
                    } else {
                        contact.setAirline("CA"); // 默认航空公司
                        contact.setPhoneNumber(ctText);
                    }
                    contacts.add(contact);
                }
            }
        }

        basicArea.setCts(cts);
        basicArea.setContactPhones(contactPhones);
        basicArea.setContact(contacts);
        basicArea.setContactorEmail(""); // 暂时设为空

        return basicArea;
    }

    /**
     * 构建旅客信息列表
     */
    private List<QueryAllPassengersByPnrNoVo.Passenger> buildPassengers(String pnrId) {
        // 查询PNR下的所有旅客
        List<MnjxPnrNm> pnrNmList = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnrId)
                .orderBy(true, true, MnjxPnrNm::getPsgIndex)
                .list();

        if (CollUtil.isEmpty(pnrNmList)) {
            return Collections.emptyList();
        }

        List<QueryAllPassengersByPnrNoVo.Passenger> passengers = new ArrayList<>();

        for (MnjxPnrNm pnrNm : pnrNmList) {
            QueryAllPassengersByPnrNoVo.Passenger passenger = this.buildPassenger(pnrNm, pnrId);
            passengers.add(passenger);
        }

        return passengers;
    }

    /**
     * 构建单个旅客信息
     */
    private QueryAllPassengersByPnrNoVo.Passenger buildPassenger(MnjxPnrNm pnrNm, String pnrId) {
        QueryAllPassengersByPnrNoVo.Passenger passenger = new QueryAllPassengersByPnrNoVo.Passenger();

        // 基本信息
        passenger.setPassengerId(pnrNm.getPsgIndex());
        passenger.setFullName(pnrNm.getName());
        passenger.setPassengerNameInPnr(pnrNm.getName());

        // 转换旅客类型
        String passengerType = this.convertPassengerType(pnrNm.getPsgType());
        passenger.setPassengerType(passengerType);
        passenger.setSpecialPassengerType(passengerType);

        // 设置默认值
        passenger.setChineseName(null);
        passenger.setFirstName("");
        passenger.setLastName("");
        passenger.setNameSuffix("");
        passenger.setUnMinor(false);
        passenger.setUnMinorAge(0);
        passenger.setOsiCtcm(null);
        passenger.setSsrCtcm(null);
        passenger.setInfantDetail(null);

        // 构建证件信息
        this.buildDocumentInfo(passenger, pnrNm.getPnrNmId());

        // 构建航段信息
        List<QueryAllPassengersByPnrNoVo.PassengerSegment> segments = this.buildPassengerSegments(pnrId, pnrNm.getPnrNmId());
        passenger.setSegments(segments);

        // 构建票号信息
        this.buildTicketInfo(passenger, pnrNm.getPnrNmId());

        // 构建DOCA信息
        passenger.setDocaInfoR(new QueryAllPassengersByPnrNoVo.DocaInfo());
        passenger.setDocaInfoD(new QueryAllPassengersByPnrNoVo.DocaInfo());

        // 设置其他默认值
        passenger.setPpForDocs(null);
        passenger.setNiForDocs(null);

        // 构建二次验证信息
        QueryAllPassengersByPnrNoVo.SecondFactor secondFactor = new QueryAllPassengersByPnrNoVo.SecondFactor();
        secondFactor.setSecondFactorCode("CN");
        secondFactor.setSecondFactorValue(pnrNm.getName()); // 使用旅客姓名作为二次验证值
        passenger.setSecondFactor(secondFactor);

        return passenger;
    }

    /**
     * 转换旅客类型
     */
    private String convertPassengerType(String psgType) {
        if (StrUtil.isEmpty(psgType)) {
            return "ADT";
        }

        switch (psgType) {
            case "0":
                return "ADT"; // 成人
            case "1":
                return "CHD"; // 儿童
            case "2":
                return "UM";  // 无人陪伴儿童
            case "3":
                return "INF"; // 婴儿
            default:
                return "ADT";
        }
    }

    /**
     * 构建证件信息
     */
    private void buildDocumentInfo(QueryAllPassengersByPnrNoVo.Passenger passenger, String pnrNmId) {
        // 查询SSR信息
        List<MnjxNmSsr> ssrList = iMnjxNmSsrService.lambdaQuery()
                .eq(MnjxNmSsr::getPnrNmId, pnrNmId)
                .list();

        QueryAllPassengersByPnrNoVo.Document document = new QueryAllPassengersByPnrNoVo.Document();
        QueryAllPassengersByPnrNoVo.Document documentPP = new QueryAllPassengersByPnrNoVo.Document();

        // 设置默认值
        this.setDefaultDocumentValues(document);
        this.setDefaultDocumentValues(documentPP);

        if (CollUtil.isNotEmpty(ssrList)) {
            for (MnjxNmSsr ssr : ssrList) {
                if ("FOID".equals(ssr.getSsrType())) {
                    this.processFoidSsr(passenger, document, ssr);
                } else if ("DOCS".equals(ssr.getSsrType())) {
                    this.processDocsSsr(passenger, document, ssr);
                } else if ("CHLD".equals(ssr.getSsrType())) {
                    this.processChldSsr(passenger, document, ssr);
                }
            }
        }

        passenger.setDocument(document);
        passenger.setDocumentPP(documentPP);
    }

    /**
     * 设置证件信息默认值
     */
    private void setDefaultDocumentValues(QueryAllPassengersByPnrNoVo.Document document) {
        document.setGender(null);
        document.setIdCardNumber(null);
        document.setPassengerNationality(null);
        document.setHolder(null);
        document.setVisaIssueCountry(null);
        document.setBirthday(null);
        document.setVisaExpiryDate(null);
        document.setLastName(null);
        document.setFirstName(null);
        document.setDocumentType(null);
        document.setSsrType(null);
        document.setDocsName(null);
        document.setNiForDocs(null);
    }

    /**
     * 处理FOID SSR信息
     */
    private void processFoidSsr(QueryAllPassengersByPnrNoVo.Passenger passenger, QueryAllPassengersByPnrNoVo.Document document, MnjxNmSsr ssr) {
        String ssrInfo = ssr.getSsrInfo();
        if (StrUtil.isNotEmpty(ssrInfo) && ReUtil.isMatch(REG_FOID, ssrInfo)) {
            String idCardNumber = ReUtil.get(REG_FOID, ssrInfo, 1);

            if (StrUtil.isNotEmpty(idCardNumber)) {
                if (idCardNumber.startsWith("NI")) {
                    document.setDocumentType("NI");
                    document.setIdCardNumber(idCardNumber.substring(2));
                    document.setSsrType("FOID");

                    // 从身份证号提取生日
                    if (idCardNumber.length() >= 17) {
                        String birthStr = idCardNumber.substring(8, 14); // YYMMDD
                        try {
                            String birthday = DateUtils.birthDayCom2ymd(birthStr);
                            document.setBirthday(birthday);
                            passenger.setBirthday(birthday);
                        } catch (Exception e) {
                            log.warn("解析生日失败: {}", birthStr, e);
                        }
                    }
                } else if (idCardNumber.startsWith("PP")) {
                    document.setDocumentType("PP");
                    document.setIdCardNumber(idCardNumber.substring(2));
                    document.setSsrType("FOID");
                }
            }
        }
    }

    /**
     * 处理DOCS SSR信息
     */
    private void processDocsSsr(QueryAllPassengersByPnrNoVo.Passenger passenger, QueryAllPassengersByPnrNoVo.Document document, MnjxNmSsr ssr) {
        String ssrInfo = ssr.getSsrInfo();
        if (StrUtil.isNotEmpty(ssrInfo) && ReUtil.isMatch(REG_DOCS, ssrInfo)) {
            List<String> groups = ReUtil.getAllGroups(REG_DOCS, ssrInfo);
            if (groups.size() >= 10) {
                document.setDocumentType(groups.get(1));
                document.setPassengerNationality(groups.get(2));
                document.setIdCardNumber(groups.get(3));

                try {
                    String birthday = DateUtils.birthDayCom2ymd(groups.get(5));
                    document.setBirthday(birthday);
                    passenger.setBirthday(birthday);
                } catch (Exception e) {
                    log.warn("解析DOCS生日失败: {}", groups.get(5), e);
                }

                document.setGender(groups.get(6));

                try {
                    document.setVisaExpiryDate(DateUtils.com2ymd(groups.get(7)));
                } catch (Exception e) {
                    log.warn("解析签证到期日失败: {}", groups.get(7), e);
                }

                document.setFirstName(groups.get(8));
                document.setLastName(groups.get(9));
                document.setDocsName(document.getFirstName() + "/" + document.getLastName());
            }
        }
    }

    /**
     * 处理CHLD SSR信息
     */
    private void processChldSsr(QueryAllPassengersByPnrNoVo.Passenger passenger, QueryAllPassengersByPnrNoVo.Document document, MnjxNmSsr ssr) {
        String inputValue = ssr.getInputValue();
        if (StrUtil.isNotEmpty(inputValue)) {
            String[] parts = inputValue.split("/");
            if (parts.length > 0) {
                String[] ssrParts = parts[0].split(" ");
                if (ssrParts.length >= 5) {
                    try {
                        String birthday = DateUtils.birthDayCom2ymd(ssrParts[4]);
                        passenger.setBirthday(birthday);
                        document.setBirthday(birthday);
                    } catch (Exception e) {
                        log.warn("解析CHLD生日失败: {}", ssrParts[4], e);
                    }
                }
            }
        }
    }

    /**
     * 构建旅客航段信息
     */
    private List<QueryAllPassengersByPnrNoVo.PassengerSegment> buildPassengerSegments(String pnrId, String pnrNmId) {
        // 查询PNR的所有航段
        List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnrId)
                .orderBy(true, true, MnjxPnrSeg::getPnrSegNo)
                .list();

        if (CollUtil.isEmpty(pnrSegList)) {
            return Collections.emptyList();
        }

        List<QueryAllPassengersByPnrNoVo.PassengerSegment> segments = new ArrayList<>();

        for (MnjxPnrSeg pnrSeg : pnrSegList) {
            QueryAllPassengersByPnrNoVo.PassengerSegment segment = new QueryAllPassengersByPnrNoVo.PassengerSegment();

            // 基本航段信息
            segment.setActionCode(pnrSeg.getActionCode());
            segment.setOrigin(pnrSeg.getOrg());
            segment.setDestination(pnrSeg.getDst());
            segment.setAirline(pnrSeg.getFlightNo().substring(0, 2));
            segment.setFlightNumber(pnrSeg.getFlightNo().substring(2));
            segment.setClassId(pnrSeg.getSellCabin());
            segment.setFlightDate(pnrSeg.getFlightDate());
            segment.setDepartureTime(pnrSeg.getEstimateOff());
            segment.setArrivalTime(pnrSeg.getEstimateArr());
            segment.setLineIndex(pnrSeg.getPnrSegNo());

            // 设置默认值
            segment.setTicketNumber(null);
            segment.setTicketStatus(null);
            segment.setServices(Collections.emptyList());
            segment.setArnkInd("SA".equals(pnrSeg.getPnrSegType()));
            segment.setOpenInd(false);
            segment.setOcAirline(null);
            segment.setOcFlightNumber(null);
            segment.setTchb(false);

            // 查询该旅客在该航段的票号信息
            this.setSegmentTicketInfo(segment, pnrNmId, pnrSeg.getPnrSegId());

            segments.add(segment);
        }

        return segments;
    }

    /**
     * 设置航段票号信息
     */
    private void setSegmentTicketInfo(QueryAllPassengersByPnrNoVo.PassengerSegment segment, String pnrNmId, String pnrSegId) {
        // 查询旅客票号信息
        List<MnjxPnrNmTn> pnrNmTnList = iMnjxPnrNmTnService.lambdaQuery()
                .eq(MnjxPnrNmTn::getPnrNmId, pnrNmId)
                .list();

        if (CollUtil.isNotEmpty(pnrNmTnList)) {
            for (MnjxPnrNmTn pnrNmTn : pnrNmTnList) {
                List<MnjxPnrNmTicket> tickets = iMnjxPnrNmTicketService.lambdaQuery()
                        .eq(MnjxPnrNmTicket::getPnrNmTnId, pnrNmTn.getTnId())
                        .list();

                for (MnjxPnrNmTicket ticket : tickets) {
                    if (pnrSegId.equals(ticket.getS1Id()) || pnrSegId.equals(ticket.getS2Id())) {
                        segment.setTicketNumber(ticket.getTicketNo());

                        // 设置票状态
                        if (pnrSegId.equals(ticket.getS1Id())) {
                            segment.setTicketStatus(ticket.getTicketStatus1());
                        } else if (pnrSegId.equals(ticket.getS2Id())) {
                            segment.setTicketStatus(ticket.getTicketStatus2());
                        }
                        break;
                    }
                }
            }
        }
    }

    /**
     * 构建票号信息
     */
    private void buildTicketInfo(QueryAllPassengersByPnrNoVo.Passenger passenger, String pnrNmId) {
        // 查询旅客票号信息
        List<MnjxPnrNmTn> pnrNmTnList = iMnjxPnrNmTnService.lambdaQuery()
                .eq(MnjxPnrNmTn::getPnrNmId, pnrNmId)
                .list();

        List<String> ticketNumbers = new ArrayList<>();
        StringBuilder ticketJoin = new StringBuilder();

        if (CollUtil.isNotEmpty(pnrNmTnList)) {
            for (MnjxPnrNmTn pnrNmTn : pnrNmTnList) {
                List<MnjxPnrNmTicket> tickets = iMnjxPnrNmTicketService.lambdaQuery()
                        .eq(MnjxPnrNmTicket::getPnrNmTnId, pnrNmTn.getTnId())
                        .list();

                for (MnjxPnrNmTicket ticket : tickets) {
                    if (StrUtil.isNotEmpty(ticket.getTicketNo())) {
                        ticketNumbers.add(ticket.getTicketNo());
                        if (ticketJoin.length() > 0) {
                            ticketJoin.append(",");
                        }
                        ticketJoin.append(ticket.getTicketNo());
                    }
                }
            }
        }

        passenger.setTicketNumbersForTN(ticketNumbers);
        passenger.setTicketNumberJoin(ticketJoin.toString());
    }
}
