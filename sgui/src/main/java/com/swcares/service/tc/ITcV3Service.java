package com.swcares.service.tc;

import com.swcares.common.result.SguiResultException;
import com.swcares.obj.dto.QueryAllPassengersByPnrNoDto;
import com.swcares.obj.vo.QueryAllPassengersByPnrNoVo;

/**
 * TC V3服务接口
 *
 * <AUTHOR>
 * @date 2025/1/14 10:30
 */
public interface ITcV3Service {

    /**
     * 通过PNR编码查询所有旅客
     *
     * @param dto 查询参数
     * @return 旅客信息
     * @throws SguiResultException 异常
     */
    QueryAllPassengersByPnrNoVo queryAllPassengersByPnrNo(QueryAllPassengersByPnrNoDto dto) throws SguiResultException;
}
