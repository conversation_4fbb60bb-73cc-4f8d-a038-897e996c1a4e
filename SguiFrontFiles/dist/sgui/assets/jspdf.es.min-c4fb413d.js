var Bc=Object.defineProperty;var Rc=(r,e,n)=>e in r?Bc(r,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):r[e]=n;var Ai=(r,e,n)=>(Rc(r,typeof e!="symbol"?e+"":e,n),n);import{L as qc,h7 as hl,dO as Uc,h8 as zc,q as Ds,N as Hc,v as Vc,r as po,w as Ii,a3 as Wc,x as Qn,y as ma,z as Pr,P as ki,D as pa,A as ye,H as va,C as Gc,b9 as Yc,J as wo,ak as ds,Q as ps,G as Pi,al as fl,F as $c,X as Jc,_ as Kc,K as Xc,ab as Qc,aE as Zc,aS as xs,B as fa,ai as dl,aj as pl,ah as tu,c3 as It,at as ba,aG as xe,ey as jl,ex as El,ez as ji,eM as Ol,b0 as Bl,aB as eu,aC as nu,R as qe,fg as ru,aR as Rl,fI as iu,g0 as au,fU as ou,d0 as su,h2 as Ss}from"./index-a2fbd71b.js";import{u as ml,E as lu}from"./index-fe3402a7.js";import{E as cu,a as uu}from"./index-68054860.js";import{E as hu}from"./index-96be5bee.js";import{_ as fu}from"./_plugin-vue_export-helper-c27b6911.js";import{a as Ms}from"./TicketInfoPopover-9ac1ad15.js";import{z as _s,u as du}from"./browser-6cfa1fde.js";const pu=qc({title:String,confirmButtonText:String,cancelButtonText:String,confirmButtonType:{type:String,values:hl,default:"primary"},cancelButtonType:{type:String,values:hl,default:"text"},icon:{type:Uc,default:()=>zc},iconColor:{type:String,default:"#f90"},hideIcon:{type:Boolean,default:!1},hideAfter:{type:Number,default:200},teleported:ml.teleported,persistent:ml.persistent,width:{type:[String,Number],default:150}}),mu={confirm:r=>r instanceof MouseEvent,cancel:r=>r instanceof MouseEvent},gu=Ds({name:"ElPopconfirm"}),vu=Ds({...gu,props:pu,emits:mu,setup(r,{emit:e}){const n=r,{t:a}=Hc(),o=Vc("popconfirm"),s=po(),c=()=>{var x,m;(m=(x=s.value)==null?void 0:x.onClose)==null||m.call(x)},h=Ii(()=>({width:Wc(n.width)})),f=x=>{e("confirm",x),c()},p=x=>{e("cancel",x),c()},w=Ii(()=>n.confirmButtonText||a("el.popconfirm.confirmButtonText")),L=Ii(()=>n.cancelButtonText||a("el.popconfirm.cancelButtonText"));return(x,m)=>(Qn(),ma(ye(lu),Jc({ref_key:"tooltipRef",ref:s,trigger:"click",effect:"light"},x.$attrs,{"popper-class":`${ye(o).namespace.value}-popover`,"popper-style":ye(h),teleported:x.teleported,"fallback-placements":["bottom","top","right","left"],"hide-after":x.hideAfter,persistent:x.persistent}),{content:Pr(()=>[ki("div",{class:pa(ye(o).b())},[ki("div",{class:pa(ye(o).e("main"))},[!x.hideIcon&&x.icon?(Qn(),ma(ye(va),{key:0,class:pa(ye(o).e("icon")),style:Gc({color:x.iconColor})},{default:Pr(()=>[(Qn(),ma(Yc(x.icon)))]),_:1},8,["class","style"])):wo("v-if",!0),ds(" "+ps(x.title),1)],2),ki("div",{class:pa(ye(o).e("action"))},[Pi(ye(fl),{size:"small",type:x.cancelButtonType==="text"?"":x.cancelButtonType,text:x.cancelButtonType==="text",onClick:p},{default:Pr(()=>[ds(ps(ye(L)),1)]),_:1},8,["type","text"]),Pi(ye(fl),{size:"small",type:x.confirmButtonType==="text"?"":x.confirmButtonType,text:x.confirmButtonType==="text",onClick:f},{default:Pr(()=>[ds(ps(ye(w)),1)]),_:1},8,["type","text"])],2)],2)]),default:Pr(()=>[x.$slots.reference?$c(x.$slots,"reference",{key:0}):wo("v-if",!0)]),_:3},16,["popper-class","popper-style","teleported","hide-after","persistent"]))}});var bu=Kc(vu,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/popconfirm/src/popconfirm.vue"]]);const qh=Xc(bu),yu=(r,e)=>{const{t:n}=Qc(),a=po({secondFactorType:r.secondFactorType||"certificate",secondFactorCode:r.secondFactorCode||"NI",secondFactorValue:""}),o=po([{label:n("app.agentTicketQuery.certificateNo"),value:"certificate"},{label:n("app.pnrManagement.queryArea.queryLabel.pnrNo"),value:"PNR"},{label:n("app.agentTicketQuery.name"),value:"name"}]),s=po([{label:`NI ${n("app.agentTicketQuery.certs.by_IDAndResidence")}`,value:"NI"},{label:`PP ${n("app.agentTicketQuery.certs.by_PassportAndOther")}`,value:"PP"},{label:`UU ${n("app.agentTicketQuery.certs.by_Unable")}`,value:"UU"}]),c=()=>{a.value.secondFactorValue="",e("updateFormData",xs(a.value)),e("clearPropValidate")},h=()=>{a.value.secondFactorType==="PNR"?a.value.secondFactorCode="CN":a.value.secondFactorType==="name"?a.value.secondFactorCode="NM":a.value.secondFactorCode="NI",c()},f=()=>{var w;a.value.secondFactorValue=((w=a.value.secondFactorValue)==null?void 0:w.toUpperCase())??"",e("updateFormData",xs(a.value)),e("validateProp")},p=w=>{switch(w){case"certificate":return n("app.agentTicketQuery.inputCertificateNo");case"PNR":return n("app.agentTicketQuery.inputPNRNo");case"name":return n("app.agentTicketQuery.validate.inputPassengerName")}};return Zc(()=>{a.value.secondFactorType=r.secondFactorType,a.value.secondFactorCode=r.secondFactorCode,a.value.secondFactorValue=r.secondFactorValue}),{formData:a,secondFactorTypeList:o,secondFactorCodeList:s,changeSecondFactorType:h,changeSecondFactorCode:c,changeSecondFactorValue:f,getInputPlaceHolder:p}},wu=yu,Nu={class:"certificate-number inline-block"},Au={class:"h-8 justify-center items-center gap-1 inline-flex"},Lu={class:"justify-start items-center flex"},xu={key:0,class:"h-8 bg-white justify-between items-center flex"},Su={key:1,class:"h-8 bg-white justify-between items-center flex"},_u={class:"h-8 bg-white rounded-tr-sm rounded-br-sm justify-start items-center flex relative"},Pu=Ds({__name:"CertificateNumber",props:{itemProps:{},secondFactorType:{},secondFactorCode:{},secondFactorValue:{},hideSecondFactorType:{type:Boolean}},emits:["updateFormData","validateProp","clearPropValidate"],setup(r,{emit:e}){const n=e,a=r,{formData:o,secondFactorTypeList:s,secondFactorCodeList:c,changeSecondFactorType:h,changeSecondFactorCode:f,changeSecondFactorValue:p,getInputPlaceHolder:w}=wu(a,n);return(L,x)=>{const m=cu,D=uu,k=tu,O=hu;return Qn(),fa("div",Nu,[ki("div",Au,[ki("div",Lu,[L.hideSecondFactorType?wo("",!0):(Qn(),fa("div",xu,[Pi(D,{modelValue:ye(o).secondFactorType,"onUpdate:modelValue":x[0]||(x[0]=_=>ye(o).secondFactorType=_),placeholder:L.$t("app.ticketQuery.choose"),class:"second-factor-type",onChange:ye(h)},{default:Pr(()=>[(Qn(!0),fa(dl,null,pl(ye(s),_=>(Qn(),ma(m,{key:_.value,label:_.label,value:_.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder","onChange"])])),ye(o).secondFactorType==="certificate"?(Qn(),fa("div",Su,[Pi(D,{modelValue:ye(o).secondFactorCode,"onUpdate:modelValue":x[1]||(x[1]=_=>ye(o).secondFactorCode=_),placeholder:L.$t("app.ticketQuery.choose"),class:"second-factor-code",onChange:ye(f)},{default:Pr(()=>[(Qn(!0),fa(dl,null,pl(ye(c),_=>(Qn(),ma(m,{key:_.value,label:_.label,value:_.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder","onChange"])])):wo("",!0),Pi(O,{prop:L.itemProps},{default:Pr(()=>[ki("div",_u,[Pi(k,{modelValue:ye(o).secondFactorValue,"onUpdate:modelValue":x[2]||(x[2]=_=>ye(o).secondFactorValue=_),modelModifiers:{trim:!0},placeholder:ye(w)(ye(o).secondFactorType),class:pa([ye(o).secondFactorType==="certificate"?"certificate-no":"number-no"]),clearable:"",onBlur:ye(p)},null,8,["modelValue","placeholder","class","onBlur"])])]),_:1},8,["prop"])])])])}}});const Uh=fu(Pu,[["__scopeId","data-v-22262218"]]);const ql=It.global.t,zh=async(r,e,n=!1)=>ba.alert(xe("div",{class:"text-lg text-gray-1"},r),{icon:xe("em",{class:"iconfont icon-info-circle-line text-brand-2 text-[32px]"}),customClass:"alert-message-common crs-btn-ui",confirmButtonText:e,showCancelButton:n,showClose:!1}),Hh=async r=>ba.confirm(r,{icon:xe(va,{color:ji("--bkc-tw-green-2",null).value,size:32},()=>xe(Ol)),customClass:"success-message-common crs-btn-ui",dangerouslyUseHTMLString:!0,closeOnClickModal:!1,showClose:!1,confirmButtonText:ql("app.avSearch.confirm"),showCancelButton:!1}),Vh=async(r,e,n="warn")=>{const a=r.map(o=>xe("p",{className:"flex",style:{"margin-top":"8px"}},[xe("i",{className:`iconfont mr-2.5 ${o.success?"icon-ticket text-green-2":"icon-close text-red-400"}`}),xe("span",{className:`font-bold text-[14px] mr-2.5 ${o.ticketNo?"":"hidden"}`},o.ticketNo),xe("span",{className:"text-[14px] w-[240px] whitespace-nowrap overflow-hidden text-ellipsis"},o.passengerName)]));return ba.confirm(xe("div",{},[xe("p",{className:"text-[18px]",style:{"margin-bottom":"16px"}},e??""),a]),{icon:xe(va,{color:n==="warn"?ji("--bkc-tw-brand-2",null).value:ji("--bkc-tw-red-1",null).value,size:32},()=>xe(n==="warn"?jl:El)),customClass:"invalidated-warning-message crs-btn-ui",closeOnClickModal:!1,showClose:!1,showCancelButton:!1,dangerouslyUseHTMLString:!0,draggable:!0})},Wh=async(r,e,n="warn")=>{const a=r.map(o=>xe("p",{className:"flex",style:{"margin-top":"8px"}},[xe("i",{className:"iconfont mr-2.5 icon-ticket text-green-2"}),xe("span",{className:"text-[14px] w-[240px] whitespace-nowrap overflow-hidden text-ellipsis"},o)]));return ba.confirm(xe("div",{},[xe("p",{className:"text-[18px]",style:{"margin-bottom":"16px"}},e??""),a]),{icon:xe(va,{color:n==="warn"?ji("--bkc-tw-brand-2",null).value:ji("--bkc-tw-red-1",null).value,size:32},()=>xe(n==="warn"?jl:El)),customClass:"invalidated-warning-message crs-btn-ui",closeOnClickModal:!1,showClose:!1,showCancelButton:!1,dangerouslyUseHTMLString:!0})},Gh=async(r,e)=>{const n={icon:xe(va,{color:ji("--bkc-tw-green-2",null).value,size:32},()=>xe(Ol)),customClass:"crs-btn-ui crs-btn-message-ui manual-refund-icon",dangerouslyUseHTMLString:!0,confirmButtonText:ql("app.agentTicketRefund.sure"),closeOnClickModal:!1,showConfirmButton:!0,showCancelButton:!1,showClose:!1};return ba.confirm(xe("div",{className:"whitespace-pre"},[xe("p",{className:"text-[18px]",style:{"margin-bottom":"16px"}},r??""),e]),n)};class Cu{constructor(){Ai(this,"mealApply",Bl({vegetarianRadioList:[{lable:"AVML",value:"亚洲素食(AVML)"},{lable:"VOML",value:"东方素食(VOML)"},{lable:"VLML",value:"素食(含糖、鸡蛋)(VLML)"},{lable:"VGML",value:"素食(无糖)(VGML)"},{lable:"VJML",value:"耆那教徒素食(VJML)"}],specialRadioList:[{lable:"BBML",value:"婴儿餐(BBML)"},{lable:"CHML",value:"儿童餐(CHML)"},{lable:"HNML",value:"印度餐(HNML)"},{lable:"KSML",value:"犹太教餐(KSML)"},{lable:"MOML",value:"穆斯林餐(MOML)"},{lable:"DBML",value:"糖尿病患者餐(DBML)"}],preferenceRadioList:[{lable:"FPML",value:"鲜水果餐(FPML)"},{lable:"LCML",value:"低卡路里餐(LCML)"},{lable:"SFML",value:"海鲜餐(SFML)"},{lable:"LSML",value:"无盐餐(LSML)"},{lable:"BLML",value:"流食(BLML)"},{lable:"NLML",value:"不含乳糖食品(NLML)"},{lable:"RVML",value:"生菜蔬食品(RVML)"},{lable:"LFML",value:"低胆固醇、低脂肪餐(LFML)"},{lable:"GFML",value:"过重自由食物(GFML)"},{lable:"SPML",value:"禁忌餐食(SPML)"}],mealValue:""}))}}const ms=new Cu,gl={normalType:[{lable:"UMNR",value:"无陪儿童(UMNR)"},{lable:"CHLD",value:"儿童出生日期(CHLD)"},{lable:"CTCE",value:"邮箱(CTCE)"},{lable:"FQTV",value:"常旅客(FQTV)"},{lable:"CTCM",value:"电话(CTCM)"}],specialType:[{lable:"DEPA",value:"被驱逐出境(有人陪伴)(DEPA)"},{lable:"DEPU",value:"被驱逐出境(无人陪伴)(DEPU)"},{lable:"HNML",value:"印度餐(HNML)"},{lable:"STCR",value:"担架旅客(STCR)"},{lable:"GMJC",value:"革命伤残军人(GMJC)"},{lable:"SEMN",value:"船员水手(SEMN)"},{lable:"MEDA",value:"严重疾病旅客(MEDA)"},{lable:"DEAF",value:"聋哑旅客(DEAF)"},{lable:"BLND",value:"盲人旅客(BLND)"},{lable:"WCHR",value:"有自理能力轮椅(WCHR)"},{lable:"WCHS",value:"半自理能力轮椅(WCHS)"},{lable:"WCHC",value:"无自理能力轮椅(WCHC)"},{lable:"STPC",value:"中转休息室(STPC)"},{lable:"OTHER",value:"其他类型"}]};class Iu{constructor(){Ai(this,"baggageFormData",Bl({radio1:"",quantityInput:"",weightInput:"",sizeInput:""}));Ai(this,"specialRadioList1",new Map([["宠物(PETC)","PETC"],["被约束的动物(AVIH)","AVIH"],["占座行李(CBBG)","CBBG"],["超大行李(BULK)","BULK"],["自行车(BIKE)","BIKE"],["体育设施(SPEQ)","SPEQ"],["易碎行李(FRAG)","FRAG"]]));Ai(this,"specialRadioList2",new Map([["婴儿摇篮(BSCT)","BSCT"],["被约束的动物(AVIH)","AVIH"]]));Ai(this,"specialRadioList3",new Map([["无烟靠走廊(NSSA)","NSSA"],["无烟靠窗(NSSW)","NSSW"],["吸烟靠走廊(SMSA)","SMSA"],["吸烟靠窗(SMSW)","SMSW"],["额外占座(EXST)","EXST"],["无烟座位(NSST)","NSST"],["吸烟座位(SMST)","SMST"],["座位(SEAT)","SEAT"]]))}GET_CODE(e){return this.specialRadioList1.get(e)||this.specialRadioList2.get(e)}}const gs=new Iu,ku=eu(),Fu=/[\u4e00-\u9fa5]+/,{activeTag:js,orderInfo:Ul,originPnrData:Tu}=nu(ku),Lo=Ii(()=>Ul.value.get(js.value).flight??[]),vl=Ii(()=>{var r;return xs(((r=Ul.value.get(js.value))==null?void 0:r.passengers)??[])}),Du=Ii(()=>{var r,e;return((e=(r=Tu.value)==null?void 0:r.get(js.value))==null?void 0:e.passengers)??[]}),Yh=[{label:It.global.t("app.intlPassengerForm.Instructions_IDCarDAndResidence"),value:"NI_I",certificateType:"FOID"},{label:It.global.t("app.intlPassengerForm.Instructions_UnableAndSpecialID"),value:"UU",certificateType:"FOID"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_P"),value:"PP_P",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_IP"),value:"PP_IP",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_M"),value:"PP_M",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_G"),value:"PP_G",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_A"),value:"PP_A",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_C"),value:"PP_C",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_I"),value:"PP_I",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_F"),value:"PP_F",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_T"),value:"PP_T",certificateType:"DOCS"}],$h=[{label:It.global.t("app.intlPassengerForm.Instructions_IDCarDAndResidence"),value:"NI_I",certificateType:"FOID"},{label:It.global.t("app.intlPassengerForm.Instructions_UnableAndSpecialID"),value:"UU",certificateType:"FOID"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_P"),value:"PP_P",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_IP"),value:"PP_IP",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_A"),value:"PP_A",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_I"),value:"PP_I",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_T"),value:"PP_T",certificateType:"DOCS"}],Jh=[{label:It.global.t("app.intlPassengerForm.Instructions_PP_P"),value:"PP_P",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_IP"),value:"PP_IP",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_I"),value:"PP_I",certificateType:"DOCS"}],Mu=[{label:It.global.t("app.intlPassengerForm.Instructions_PP_P"),value:"PP_P",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_IP"),value:"PP_IP",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_AIN"),value:"PP_A",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_M"),value:"PP_M",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_G"),value:"PP_G",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_C"),value:"PP_C",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_I"),value:"PP_I",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_F"),value:"PP_F",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_IN"),value:"PP_IN",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_T"),value:"PP_T",certificateType:"DOCS"}],Kh=[{label:It.global.t("app.intlPassengerForm.Instructions_PP_P"),value:"PP_P",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_IP"),value:"PP_IP",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_AIN"),value:"PP_A",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_I"),value:"PP_I",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_F"),value:"PP_F",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_IN"),value:"PP_IN",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_T"),value:"PP_T",certificateType:"DOCS"}],Xh=[{label:It.global.t("app.intlPassengerForm.Instructions_PP_P"),value:"PP_P",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_IP"),value:"PP_IP",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_A"),value:"PP_A",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_I"),value:"PP_I",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_F"),value:"PP_F",certificateType:"DOCS"},{label:It.global.t("app.intlPassengerForm.Instructions_PP_IN"),value:"PP_IN",certificateType:"DOCS"}],Qh=[{label:It.global.t("app.intlPassengerForm.CBBG"),value:"CBBG"},{label:It.global.t("app.intlPassengerForm.STCR"),value:"STCR"},{label:It.global.t("app.intlPassengerForm.EXST"),value:"EXST"}],vs=(r,e)=>{for(const[n,a]of r.entries())if(a===e)return n;return""},Zh=r=>{const e=[...ms.mealApply.preferenceRadioList,...ms.mealApply.specialRadioList,...ms.mealApply.vegetarianRadioList],n=[...gl.normalType,...gl.specialType];let a="";return n.find(o=>o.lable===r)&&(a="iconfont icon-specialpassenger"),e.find(o=>o.lable===r)&&(a="iconfont icon-meal"),(vs(gs.specialRadioList1,r)||vs(gs.specialRadioList2,r))&&(a="iconfont icon-suitcase-fill"),vs(gs.specialRadioList3,r)&&(a="iconfont icon-seat-fill"),a},tf=r=>{const e=/[\s/\n]/g;return(r==null?void 0:r.replace(e,""))||""},ef=(r,e,n)=>{const a=[],o=Lo.value.filter(f=>!f.disabled),s=Du.value.find(f=>f.fullName===e);o.forEach(f=>{f.segments.forEach(p=>{var x;const w=r.find(m=>{var D;return`${p.departureAirportCode}-${p.arrivalAirportCode}${qe(p.departureDate).format("YYYY-MM-DD")}${p.airlines.airCode}${p.airlines.flightNo} ${(D=p.cabins)==null?void 0:D[0].cabinName}`==`${m.origin}-${m.destination}${m.flightDate}${m.airline}${m.flightNumber} ${m.classId}`}),L=((s==null?void 0:s.segments)??[]).find(m=>{var D;return`${p.departureAirportCode}-${p.arrivalAirportCode}${qe(p.departureDate).format("YYYY-MM-DD")}${p.airlines.airCode}${p.airlines.flightNo} ${(D=p.cabins)==null?void 0:D[0].cabinName}`==`${m.origin}-${m.destination}${m.flightDate}${m.airline}${m.flightNumber} ${m.classId}`});if(w)a.push(w);else if(s&&L)a.push(L);else{const m={actionCode:p.actionCode,origin:p.departureAirportCode,originCH:p.departureAirportCN,destination:p.arrivalAirportCode,destinationCH:p.arrivalAirportCN,services:[],arnkInd:p.airlines.flightNo==="ARNK",openInd:f.openFlag,airline:p.airlines.airCode,flightNumber:p.airlines.flightNo,classId:(x=p.cabins)!=null&&x.length?p.cabins[0].cabinName:"",flightDate:qe(p.departureDate).format("YYYY-MM-DD"),departureDate:p.departureDate,departureTime:p.departureTime,arrivalTime:p.arrivalTime,ocAirline:p.airlines.ocAirline??"",ocFlightNumber:p.airlines.ocFlightNumber??""};n&&(m.hasInft=!0,m.inftActionCode="NN"),a.push(m)}})});const c=[];return(ju(!0)??[]).forEach(f=>{const p=(a??[]).find(w=>`${f.origin}-${f.destination}${qe(f.flightDate).format("YYYY-MM-DD")}${f.airline}${f.flightNumber} ${f.classId}`==`${w.origin}-${w.destination}${w.flightDate}${w.airline}${w.flightNumber} ${w.classId}`);p&&c.push(p)}),c},nf=r=>{const e=[];return Lo.value.forEach(n=>{n.segments.forEach(a=>{var s;const o=r.find(c=>{var h;return`${a.departureAirportCode}-${a.arrivalAirportCode}${qe(a.departureDate).format("YYYY-MM-DD")}${a.airlines.airCode}${a.airlines.flightNo} ${(h=a.cabins)==null?void 0:h[0].cabinName}`==`${c.origin}-${c.destination}${qe(c.flightDate).format("YYYY-MM-DD")}${c.airline}${c.flightNumber} ${c.classId}`});e.push({actionCode:a.actionCode,origin:a.departureAirportCode,originCH:a.departureAirportCN,destination:a.arrivalAirportCode,destinationCH:a.arrivalAirportCN,services:(o==null?void 0:o.services)??[],arnkInd:a.airlines.flightNo==="ARNK",openInd:n.openFlag,airline:a.airlines.airCode,flightNumber:a.airlines.flightNo,classId:(s=a.cabins)!=null&&s.length?a.cabins[0].cabinName:"",flightDate:a.departureDate,departureDate:a.departureDate,departureTime:a.departureTime,arrivalTime:a.arrivalTime,ocAirline:a.airlines.ocAirline??"",ocFlightNumber:a.airlines.ocFlightNumber??""})})}),e},ju=r=>{const e=[];return Lo.value.forEach(n=>{r&&n.disabled||n.segments.forEach(a=>{var o;e.push({actionCode:a.actionCode,origin:a.departureAirportCode,originCH:a.departureAirportCN,destination:a.arrivalAirportCode,destinationCH:a.arrivalAirportCN,services:[],arnkInd:a.airlines.flightNo==="ARNK",openInd:n.openFlag,airline:a.airlines.airCode,flightNumber:a.airlines.flightNo,classId:(o=a.cabins)!=null&&o.length?a.cabins[0].cabinName:"",flightDate:a.departureDate,departureDate:a.departureDate,departureTime:a.departureTime,arrivalTime:a.arrivalTime,ocAirline:a.airlines.ocAirline??"",ocFlightNumber:a.airlines.ocFlightNumber??""})})}),e},rf=()=>{const r=[];return Lo.value.forEach(e=>{e.segments.forEach(n=>{r.push({departureAirport:n.departureAirportCode,arrivalAirport:n.arrivalAirportCode})})}),r},af=r=>{const e=[];return r?vl.value.forEach(n=>{var a;e.push({name:((a=n.infantDetail)==null?void 0:a.fullName)??"",type:"INF"})}):vl.value.forEach(n=>{e.push({name:n.fullName,type:n.passengerType})}),e},of=r=>{if(r&&!r.includes("_"))switch(r){case"NI":return"NI_I";case"UU":return"UU";default:return`PP_${r}`}return r??""},sf=r=>{let e="";switch(r){case"ADT":e=It.global.t("app.passengerType.ADT");break;case"CHD":e=It.global.t("app.passengerType.CHD");break;case"INF":e=It.global.t("app.passengerType.INF");break;case"UM":e=It.global.t("app.passengerType.UMCHD");break;case"STU":e=It.global.t("app.passengerType.STU");break;case"EMI":e=It.global.t("app.passengerType.EMI");break;case"SEA":e=It.global.t("app.passengerType.SEA");break;case"LBR":e=It.global.t("app.passengerType.LBR");break;case"GM":e=It.global.t("app.passengerType.GM");break;case"JC":e=It.global.t("app.passengerType.JC");break;default:e=It.global.t("app.passengerType.ADT");break}return e},lf=async()=>{const e=(await Ms()).filter(n=>n.field1==="CHD"&&n.field2!=="UM").map(n=>({value:n.field2,label:n.field2}));return e.map((n,a)=>{(n.value==="MISS"||n.value==="MSTR")&&e.unshift(e.splice(a,1)[0])}),e},cf=async()=>{const e=(await Ms()).filter(n=>n.field1==="ADT"&&n.field2!=="JC"&&n.field2!=="GM").map(n=>({checked:!1,value:n.field2,label:n.field2}));return e.map((n,a)=>{(n.value==="MR"||n.value==="MS")&&e.unshift(e.splice(a,1)[0])}),e},uf=async()=>{const e=(await Ms()).filter(n=>n.field1==="INF").map(n=>({checked:!1,value:n.field2,label:n.field2}));return e.map((n,a)=>{(n.value==="MISS"||n.value==="MSTR")&&e.unshift(e.splice(a,1)[0])}),e},hf=(r,e)=>{const n=Fu.test(r);return e?n?"":" ":""};var Eu=(r=>(r.CONTACT_PERSON="contactPerson",r.ISSUE_LIMIT="issueLimit",r.PASSENGER="passenger",r.FLIGHT="flight",r.SPECIAL_SERVICES="specialServices",r.SALE_FARE="saleFare",r.CHANGE="changeTicket",r.FP="fp",r.CC="cc",r.EI="ei",r.TC="tc",r.OI="oi",r.SVC="svc",r.REMARK="remark",r.TICKET_NUMBERS="ticketNumbers",r.UN_TICKET_NUMBERS="unTicketNumbers",r.SSR_INFT="ssrInft",r.TN_NUMBERS="tn",r.XN_NUMBERS="xn",r.GROUP="group",r.SSR_CHD="ssrChd",r.SSR_ADTK="ssrAdtk",r))(Eu||{}),Ou=(r=>(r.UPDATE="UPDATE",r.DELETE="DELETE",r.ADD="ADD",r))(Ou||{}),mo=(r=>(r.TKT="TKT",r.QTE="QTE",r.TQ="T&Q",r))(mo||{}),zl=(r=>(r.ADT="ADT",r.CHD="CHD",r.INF="INF",r.JC="JC",r.GM="GM",r.SEA="SEA",r.LBR="LBR",r.ADT_EMI="ADT_EMI",r.ADT_STU="ADT_STU",r.CHD_STU="CHD_STU",r.CHD_EMI="CHD_EMI",r))(zl||{}),Bu=(r=>(r.M="M",r.F="F",r))(Bu||{});const da=(r,e)=>(r??[]).map(n=>({type:e,text:n.text.trim(),airlineCode:n.airline})),Ru=r=>(r??[]).map(e=>{var n,a,o,s,c,h,f;return{type:`SSR ${(a=e==null?void 0:e.text)==null?void 0:a.substring(0,(n=e==null?void 0:e.text)==null?void 0:n.indexOf(" "))}`,text:(c=(s=(o=e==null?void 0:e.text)==null?void 0:o.split(" "))==null?void 0:s.slice(2).join(" "))==null?void 0:c.trim(),airlineCode:((f=(h=e==null?void 0:e.text)==null?void 0:h.split(" "))==null?void 0:f[1])??""}}),ff=r=>r.some(e=>(e.segments??[]).some(n=>n.segmentType==="2")),df=r=>r.every(e=>(e.segments??[]).every(n=>{var a;return n.segmentType==="2"||(((a=n==null?void 0:n.airlines)==null?void 0:a.flightNo)??"")==="ARNK"})),pf=r=>r.every(e=>(e.segments??[]).every(n=>{var a;return n.segmentType!=="2"||(((a=n==null?void 0:n.airlines)==null?void 0:a.flightNo)??"")==="ARNK"})),mf=async r=>{const e=r.flatMap(n=>n.segments.flatMap(a=>[a.departureAirportCode,a.arrivalAirportCode]));return await ru(e)},gf=async r=>{const e=await Rl("DOMESTIC_AIRLINE_LOCAL_DATA"),n=(e==null?void 0:e.localData)??[];if(n.length===0)return!1;const a=new Set;return r.forEach(o=>o.segments.forEach(s=>{s.airlines&&(s.airlines.airCode&&a.add(s.airlines.airCode),s.airlines.ocAirline&&a.add(s.airlines.ocAirline))})),[...a].some(o=>!n.includes(o))},qu=(r,e)=>{const n=[];return r.forEach(a=>{e.forEach(o=>{o===a.code&&a.hostgds==="1E"&&n.push(a)})}),n},vf=async r=>{const e=await Rl("AIRLINE_LOCAL_DATA");if(e!=null&&e.localData){const n=JSON.parse((e==null?void 0:e.localData)??"")??[],a=new Set;r.forEach(s=>s.segments.forEach(c=>{var h,f;s.disabled||((h=c.airlines)!=null&&h.airCode&&a.add(c.airlines.airCode),(f=c.airlines)!=null&&f.ocAirline&&a.add(c.airlines.ocAirline))}));const o=qu(n,[...a]);return a.size===0?!0:(o==null?void 0:o.length)===a.size}return!0},bf=(r,e)=>{let n=!1;return{flights:r.map(o=>{var c;if(!o.openFlag)return{segments:o.segments.map(h=>{var w,L,x,m;let f={};const p=qe(h.departureDate).isValid()?qe(h.departureDate).format("YYYY-MM-DD"):"";return h.airlines.flightNo==="ARNK"?(f={segmentType:"ARNK",departureAirport:h.departureAirportCode,arrivalAirport:h.arrivalAirportCode,marketingAirline:"",cabinCode:"",departureDate:p},e&&(f.selectedFlag=(w=h.selectedFlag)!=null&&w.includes(mo.TQ)?"T_Q":h.selectedFlag),f):(f={actionCode:iu(((L=h.cabins[0])==null?void 0:L.state)??""),departureDate:p,departureTime:h.departureTime,arrivalDate:qe(h.arrivalDate).format("YYYY-MM-DD"),arrivalTime:h.arrivalTime,departureTerminal:h.departureTerminal,arrivalTerminal:h.arrivalTerminal,departureAirport:h.departureAirportCode,arrivalAirport:h.arrivalAirportCode,marketingFlightNumber:h.airlines.flightNo,sharedInfo:h.airlines.isShared,marketingPlaneType:h.airlines.planeType,marketingAirline:h.airlines.airCode,cabinCode:(x=h.cabins)!=null&&x.length?h.cabins[0].cabinName:"",segmentType:h.airlines.flightNo==="ARNK"?"ARNK":"NORMAL"},e&&(f.selectedFlag=(m=h.selectedFlag)!=null&&m.includes(mo.TQ)?"T_Q":h.selectedFlag),f)})};n=!0;let s={};return s={segments:[{segmentType:"OPEN",departureAirport:o.segments[0].departureAirportCode,arrivalAirport:o.segments[0].arrivalAirportCode,marketingAirline:o.segments[0].airlines.airCode,cabinCode:o.segments[0].cabins[0].cabinName,departureDate:o.segments[0].departureDate?qe(o.segments[0].departureDate).format("YYYY-MM-DD"):""}]},e&&(s.segments[0].selectedFlag=(c=o.segments[0].selectedFlag)!=null&&c.includes(mo.TQ)?"T_Q":o.segments[0].selectedFlag),s}),openFlag:n}},Uu=(r,e)=>{const n=[];return e.forEach(a=>a.segments.forEach(o=>{var c;if((r??[]).find(h=>h.hasInft&&`${h.origin}-${h.destination}-${h.departureDate}`==`${o.departureAirportCode}-${o.arrivalAirportCode}-${o.departureDate}`)){const h={departureDate:qe(o.departureDate).format("YYYY-MM-DD"),departureTime:o.departureTime,arrivalDate:qe(o.arrivalDate).format("YYYY-MM-DD"),arrivalTime:o.arrivalTime,departureTerminal:o.departureTerminal,arrivalTerminal:o.arrivalTerminal,departureAirport:o.departureAirportCode,arrivalAirport:o.arrivalAirportCode,marketingFlightNumber:o.airlines.flightNo,sharedInfo:o.airlines.isShared,marketingPlaneType:o.airlines.planeType,marketingAirline:o.airlines.airCode,cabinCode:(c=o.cabins)!=null&&c.length?o.cabins[0].cabinName:"",segmentType:o.airlines.flightNo==="ARNK"?"ARNK":"NORMAL"};n.push(h)}})),n},yf=(r,e)=>{if(!r)return!0;const n=Mu.map(a=>a.value);return e.every(a=>{var s;const o=((s=a==null?void 0:a.document)==null?void 0:s.documentType)??"";return o===""?!0:n.includes(o)})},zu=(r,e)=>{var n;(n=r==null?void 0:r.document)!=null&&n.idCardNumber&&!e&&ou.test(r.document.idCardNumber)&&(r.document.ssrType="FOID",r.document.documentType="NI_I")},Hu=(r,e)=>{const n=[];return e.forEach(a=>a.segments.forEach(o=>{var c;const s=(r??[]).find(h=>{const f=qe(o.departureDate).format("YYYY-MM-DD"),p=qe(h.flightDate).format("YYYY-MM-DD");return h.frequentNumber&&`${h.origin}-${h.destination}-${p}`==`${o.departureAirportCode}-${o.arrivalAirportCode}-${f}`});if(s){const h={departureDate:qe(o.departureDate).format("YYYY-MM-DD"),departureTime:o.departureTime,arrivalDate:qe(o.arrivalDate).format("YYYY-MM-DD"),arrivalTime:o.arrivalTime,departureTerminal:o.departureTerminal,arrivalTerminal:o.arrivalTerminal,departureAirport:o.departureAirportCode,arrivalAirport:o.arrivalAirportCode,marketingFlightNumber:o.airlines.flightNo,sharedInfo:o.airlines.isShared,marketingPlaneType:o.airlines.planeType,marketingAirline:o.airlines.airCode,cabinCode:(c=o.cabins)!=null&&c.length?o.cabins[0].cabinName:"",segmentType:o.airlines.flightNo==="ARNK"?"ARNK":"NORMAL",frequentNumber:s.frequentNumber};n.push(h)}})),n},Vu=r=>{const e=[];return r.osiCtcm&&e.push({type:"OSI CTCM",text:r.osiCtcm??""}),r.ssrCtcm&&e.push({type:"SSR CTCM",text:r.ssrCtcm??""}),e},wf=r=>r?(r??"").split("、").map(n=>`${n}`)??[]:[],Wu=r=>r.map(e=>/\/[pP]\d+$/i.test(e)?e.replace(/\/[pP]\d+$/i,""):e),Nf=(r,e,n)=>{const a=[];return r.forEach(o=>{var p,w,L,x,m,D,k,O,_,j,J,st,ft,At,rt,G,vt,bt,C,I,z,R,lt,ot,mt,tt,pt,ut,Ot,N,T,E,V,Y,Q,et,Z,Lt,Nt,Tt,kt,Ht,it,M,Yt,Et,wt,xt,Ct,_t,Bt,Wt,Jt;zu(o,n);const s=["JC","GM","UM"];!n&&(o.passengerType.includes("CHD")||s.includes((o==null?void 0:o.specialPassengerType)??""));const c=(p=o==null?void 0:o.document)==null?void 0:p.idCardNumber,h=/\(\s*UM\d+\)/g.test(o.fullName)&&o.passengerType.includes("CHD"),f={infantRef:o.infantDetail?o.infantDetail.id:"",id:o.id,sex:(o==null?void 0:o.document.gender)??"",passengerType:o.passengerType,specialPassengerType:o.specialPassengerType??"",certificateType:c?o==null?void 0:o.document.documentType:"",certificateNo:o==null?void 0:o.document.idCardNumber,certificateIssueCountry:c?((w=o==null?void 0:o.document)==null?void 0:w.visaIssueCountry)??"":"",certificateHolderInternational:c?((L=o==null?void 0:o.document)==null?void 0:L.passengerNationality)??"":"",certificateExpiryDate:c?((x=o==null?void 0:o.document)==null?void 0:x.visaExpiryDate)??"":"",birthday:(o==null?void 0:o.birthday)??"",fullName:h?o.fullName.replace(/\(\s*UM\d+\)/g,"").replace(/\s{2,}/g," ").trim():o.fullName,umnrInd:h,infantChineseName:((m=o==null?void 0:o.infantDetail)==null?void 0:m.chineseName)??"",nameType:o.chineseName?"CN":"EN",contactAndCommunicationList:Vu(o),email:(o==null?void 0:o.passengerEmail)??"",vipType:o.vipType,vipTextList:Wu(o.vipTexts??[]),identityText:o.identityText??"",supplementaryIdentityInfoList:o.supplementaryIdentityInfoList??[],ppForDocs:o.ppForDocs??"",docsName:c?(D=o==null?void 0:o.document)==null?void 0:D.docsName:"",addressInfo:{live:{countryCode:((k=o==null?void 0:o.docaInfoR)==null?void 0:k.country)??"",provinceCode:((O=o==null?void 0:o.docaInfoR)==null?void 0:O.state)??"",cityNameEn:((_=o==null?void 0:o.docaInfoR)==null?void 0:_.city)??"",postcode:((j=o==null?void 0:o.docaInfoR)==null?void 0:j.zip)??"",detailAddress:((J=o==null?void 0:o.docaInfoR)==null?void 0:J.address)??""},arrival:{countryCode:((st=o==null?void 0:o.docaInfoD)==null?void 0:st.country)??"",provinceCode:((ft=o==null?void 0:o.docaInfoD)==null?void 0:ft.state)??"",cityNameEn:((At=o==null?void 0:o.docaInfoD)==null?void 0:At.city)??"",postcode:((rt=o==null?void 0:o.docaInfoD)==null?void 0:rt.zip)??"",detailAddress:((G=o==null?void 0:o.docaInfoD)==null?void 0:G.address)??""}},niForDocs:(o==null?void 0:o.niForDocs)??"",frequenters:Hu(o.segments??[],e??[]),holder:((vt=o.document)==null?void 0:vt.holder)??"",familyId:o.familyId??0};if(o.infantDetail){const te=(C=(bt=o.infantDetail)==null?void 0:bt.document)==null?void 0:C.idCardNumber,ie={infantRef:"",id:o.infantDetail.id,sex:((I=o.infantDetail)==null?void 0:I.document.gender)??"",passengerType:zl.INF,certificateType:te?(z=o.infantDetail)==null?void 0:z.document.documentType:"",certificateNo:(R=o.infantDetail)==null?void 0:R.document.idCardNumber,certificateIssueCountry:te?((ot=(lt=o.infantDetail)==null?void 0:lt.document)==null?void 0:ot.visaIssueCountry)??"":"",certificateHolderInternational:te?((tt=(mt=o.infantDetail)==null?void 0:mt.document)==null?void 0:tt.passengerNationality)??"":"",certificateExpiryDate:((pt=o.infantDetail)==null?void 0:pt.document.visaExpiryDate)??"",birthday:(o.infantDetail.birthday||((ut=o.infantDetail)==null?void 0:ut.document.birthday))??"",fullName:o.infantDetail.fullName,infantChineseName:((Ot=o==null?void 0:o.infantDetail)==null?void 0:Ot.chineseName)??"",nameType:o.infantDetail.chineseName?"CN":"EN",phoneNumber:"",identityText:"",nameSuffix:n?((N=o.infantDetail)==null?void 0:N.nameSuffix)??"":"",ppForDocs:"",addressInfo:{live:{countryCode:((E=(T=o.infantDetail)==null?void 0:T.docaInfoR)==null?void 0:E.country)??"",provinceCode:((Y=(V=o.infantDetail)==null?void 0:V.docaInfoR)==null?void 0:Y.state)??"",cityNameEn:((et=(Q=o.infantDetail)==null?void 0:Q.docaInfoR)==null?void 0:et.city)??"",postcode:((Lt=(Z=o.infantDetail)==null?void 0:Z.docaInfoR)==null?void 0:Lt.zip)??"",detailAddress:((Tt=(Nt=o.infantDetail)==null?void 0:Nt.docaInfoR)==null?void 0:Tt.address)??""},arrival:{countryCode:((Ht=(kt=o.infantDetail)==null?void 0:kt.docaInfoD)==null?void 0:Ht.country)??"",provinceCode:((M=(it=o.infantDetail)==null?void 0:it.docaInfoD)==null?void 0:M.state)??"",cityNameEn:((Et=(Yt=o.infantDetail)==null?void 0:Yt.docaInfoD)==null?void 0:Et.city)??"",postcode:((xt=(wt=o.infantDetail)==null?void 0:wt.docaInfoD)==null?void 0:xt.zip)??"",detailAddress:((_t=(Ct=o.infantDetail)==null?void 0:Ct.docaInfoD)==null?void 0:_t.address)??""}},holder:((Wt=(Bt=o.infantDetail)==null?void 0:Bt.document)==null?void 0:Wt.holder)??"",inftSelectedSegments:Uu(((Jt=o.infantDetail)==null?void 0:Jt.segments)??[],e??[])};a.push(ie)}a.push(f)}),a},Af=r=>{const e=[];return e.push(...da(r.remarks??[],"RMK")),e.push(...da(r.remarkOsis??[],"OSI")),e.push(...da(r.others??[],"SSR OTHS")),e.push(...da(r.ckins??[],"SSR CKIN")),e.push(...da(r.clids??[],"SSR CLID")),e.push(...Ru(r.ssrContents??[])),e},Gu=r=>{if(r&&r.length){const e=r.map(n=>au(n)?n.ct:n);return e==null?void 0:e.filter(n=>n)}return[]},Yu=r=>(r??[]).filter(e=>e.airline&&e.phoneNumber),Lf=r=>({ctList:Gu(r.cts),email:r.contactorEmail,phoneInfoList:Yu(r.contact)}),xf=(r,e)=>({timeLimit:r.issueLimitType==="TL"&&r.issueLimitCrs?qe(r.issueLimitCrs).format("YYYY-MM-DD HH:mm"):"",office:r.issueLimitType==="TL"&&r.issueLimitOffice?r.issueLimitOffice:e??"",ticketNumber:r.issueLimitType==="T"?(r.issueLimitCrs??"").substring(2):""}),$u=(r,e)=>({actionCode:e??"NN",marketingAirline:r.airline,departureAirport:r.origin,arrivalAirport:r.destination,marketingFlightNumber:r.flightNumber,cabinCode:r.classId,departureDate:qe(r.flightDate).format("YYYY-MM-DD")}),Sf=r=>{const e=[];return r.forEach(n=>{n.segments.forEach(a=>{a.services.forEach(o=>{const s={type:o.ssrCode,text:o.text??"",passengerIds:[n.id],specialServiceSegments:[$u(a,o.actionCode)],specialServiceNumber:1};e.push(s)})})}),e},Ju=(r,e,n,a)=>(r??[]).map(s=>{var f,p;const c=(s.fareInfo.segInfos??[]).map(w=>({fareBasisCodes:w.fareBasisCodes,airline:w.companyCode})),h=[];return(s.baggageAllowance??[]).forEach((w,L)=>{h.push({baggage:w,segIndex:(L+1).toString()})}),{priceItemId:su(),specialPassengerType:s.code,sequenceNmbr:s.fareInfo.sequenceNmbr,ticketAmount:s.fareInfo.tktAmount,totalAmount:s.fareInfo.totalAmount,currency:s.fareInfo.currency,fuel:s.fareInfo.fuel,fund:s.fareInfo.fund,fn:s.fareInfo.fn,fc:s.fareInfo.fc,commissionRate:Number((s==null?void 0:s.agentRate)??0),segmentInfos:c,baggageWithSegIndex:h,passengerNum:((f=e==null?void 0:e[s==null?void 0:s.code])==null?void 0:f.passengerNum)??1,passengerTypeUpdate:s.childUseAdultFare?"ADT":a.passengerTypeUpdate?a.passengerTypeUpdate:s.code,passengerAge:((p=e==null?void 0:e[s==null?void 0:s.code])==null?void 0:p.passengerAge)??0,passengerIds:s.pasgId??[],chdUsingAdtPrice:(s==null?void 0:s.childUseAdultFare)??!1}}),_f=(r,e,n,a)=>{if(!(r!=null&&r.passengerFares))return[];const o=new Map;(r.passengerFares??[]).forEach(h=>{const f=h.advanced,p=JSON.stringify(f);o.has(p)||o.set(p,{...f,priceItem:[]}),o.get(p).priceItem.push(h)});const s=[...o.values()],c=[];return s.forEach((h,f)=>{const p=h.priceItem[0].advanced;c.push({priceId:f+1+"",ticketType:h.ticketType,negotiatedFareCode:p!=null&&p.isGovernment?"GP":(p==null?void 0:p.keyCustomerCode)??"",queryExclusiveNegotiatedFare:(p==null?void 0:p.exclusiveNegotiated)??!1,fareBasic:(p==null?void 0:p.farebasic)??"",fareType:(p==null?void 0:p.fareType)??"",pricingSource:(p==null?void 0:p.classType)??"Both",currencyCode:(p==null?void 0:p.currencyCode)??"CNY",issuingAirline:(p==null?void 0:p.airlines)??"",airportCode:(p==null?void 0:p.placeOfSale)??"",calculateLowestPrice:(p==null?void 0:p.minimumPrice)??"",calculateAllBrand:(p==null?void 0:p.allBrands)??"",payMethod:p!=null&&p.governmentCC?"CC":p!=null&&p.paymentMethod?p==null?void 0:p.paymentMethod:"CASH",priceItems:Ju(h.priceItem,n,f,p),groupPassengerType:(p==null?void 0:p.passengerType)??"",discountCode:a})}),c},Pf=r=>Object.entries(r).filter(n=>n[0]!=="CC").reduce((n,[a,o])=>{const s=o.map(c=>({passengerIds:c.passengerIds,text:c.text,type:a}));return n.concat(s)},[]),Cf=(r,e)=>{var c,h,f,p;const n=((p=(f=(h=(c=r??[])==null?void 0:c[0])==null?void 0:h.segments)==null?void 0:f[0])==null?void 0:p.departureDate)??"",a=(e??[]).filter(w=>w.passengerType.includes("ADT"));if(a.length===0)return!1;const o=(a??[]).every(w=>qe(n).subtract(18,"year").isBefore(qe((w==null?void 0:w.birthday)??""))),s=(e??[]).some(w=>w.passengerType.includes("CHD")||w.infantDetail);return o&&s},If=(r,e)=>{if(!(r??[]).length)return[];const n=[{label:e==="issue"?It.global.t("app.pnrManagement.paymentMethod.bspTicket"):It.global.t("app.pnrManagement.paymentMethod.bspTicketTry"),value:"BSP"},{label:e==="issue"?It.global.t("app.pnrManagement.paymentMethod.bopTicket"):It.global.t("app.pnrManagement.paymentMethod.bopTicketTry"),value:"BOP"},{label:e==="issue"?It.global.t("app.pnrManagement.paymentMethod.cdsTicket"):It.global.t("app.pnrManagement.paymentMethod.cdsTicketTry"),value:"CDS"},{label:e==="issue"?It.global.t("app.pnrManagement.paymentMethod.currentTicket"):It.global.t("app.pnrManagement.paymentMethod.currentTicketTry"),value:"ARL"}];if((r??[]).find(s=>s==="$$$"))return n;const o=[];return(r??[]).forEach(s=>{switch(s){case"BSP":o.push({label:e==="issue"?It.global.t("app.pnrManagement.paymentMethod.bspTicket"):It.global.t("app.pnrManagement.paymentMethod.bspTicketTry"),value:"BSP"});break;case"BOP":o.push({label:e==="issue"?It.global.t("app.pnrManagement.paymentMethod.bopTicket"):It.global.t("app.pnrManagement.paymentMethod.bopTicketTry"),value:"BOP"});break;case"CDS":o.push({label:e==="issue"?It.global.t("app.pnrManagement.paymentMethod.cdsTicket"):It.global.t("app.pnrManagement.paymentMethod.cdsTicketTry"),value:"CDS"});break;case"本票":o.push({label:e==="issue"?It.global.t("app.pnrManagement.paymentMethod.currentTicket"):It.global.t("app.pnrManagement.paymentMethod.currentTicketTry"),value:"ARL"});break}}),o};function de(r){"@babel/helpers - typeof";return de=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},de(r)}var zt=function(){return typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:this}();function bs(){zt.console&&typeof zt.console.log=="function"&&zt.console.log.apply(zt.console,arguments)}var be={log:bs,warn:function(r){zt.console&&(typeof zt.console.warn=="function"?zt.console.warn.apply(zt.console,arguments):bs.call(null,arguments))},error:function(r){zt.console&&(typeof zt.console.error=="function"?zt.console.error.apply(zt.console,arguments):bs(r))}};function ys(r,e,n){var a=new XMLHttpRequest;a.open("GET",r),a.responseType="blob",a.onload=function(){Hr(a.response,e,n)},a.onerror=function(){be.error("could not download file")},a.send()}function bl(r){var e=new XMLHttpRequest;e.open("HEAD",r,!1);try{e.send()}catch{}return e.status>=200&&e.status<=299}function co(r){try{r.dispatchEvent(new MouseEvent("click"))}catch{var e=document.createEvent("MouseEvents");e.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),r.dispatchEvent(e)}}var ga,Ps,Hr=zt.saveAs||((typeof window>"u"?"undefined":de(window))!=="object"||window!==zt?function(){}:typeof HTMLAnchorElement<"u"&&"download"in HTMLAnchorElement.prototype?function(r,e,n){var a=zt.URL||zt.webkitURL,o=document.createElement("a");e=e||r.name||"download",o.download=e,o.rel="noopener",typeof r=="string"?(o.href=r,o.origin!==location.origin?bl(o.href)?ys(r,e,n):co(o,o.target="_blank"):co(o)):(o.href=a.createObjectURL(r),setTimeout(function(){a.revokeObjectURL(o.href)},4e4),setTimeout(function(){co(o)},0))}:"msSaveOrOpenBlob"in navigator?function(r,e,n){if(e=e||r.name||"download",typeof r=="string")if(bl(r))ys(r,e,n);else{var a=document.createElement("a");a.href=r,a.target="_blank",setTimeout(function(){co(a)})}else navigator.msSaveOrOpenBlob(function(o,s){return s===void 0?s={autoBom:!1}:de(s)!=="object"&&(be.warn("Deprecated: Expected third argument to be a object"),s={autoBom:!s}),s.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(o.type)?new Blob([String.fromCharCode(65279),o],{type:o.type}):o}(r,n),e)}:function(r,e,n,a){if((a=a||open("","_blank"))&&(a.document.title=a.document.body.innerText="downloading..."),typeof r=="string")return ys(r,e,n);var o=r.type==="application/octet-stream",s=/constructor/i.test(zt.HTMLElement)||zt.safari,c=/CriOS\/[\d]+/.test(navigator.userAgent);if((c||o&&s)&&(typeof FileReader>"u"?"undefined":de(FileReader))==="object"){var h=new FileReader;h.onloadend=function(){var w=h.result;w=c?w:w.replace(/^data:[^;]*;/,"data:attachment/file;"),a?a.location.href=w:location=w,a=null},h.readAsDataURL(r)}else{var f=zt.URL||zt.webkitURL,p=f.createObjectURL(r);a?a.location=p:location.href=p,a=null,setTimeout(function(){f.revokeObjectURL(p)},4e4)}});/**
 * A class to parse color values
 * <AUTHOR> Stefanov <<EMAIL>>
 * {@link   http://www.phpied.com/rgb-color-parser-in-javascript/}
 * @license Use it if you like it
 */function Hl(r){var e;r=r||"",this.ok=!1,r.charAt(0)=="#"&&(r=r.substr(1,6)),r={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"}[r=(r=r.replace(/ /g,"")).toLowerCase()]||r;for(var n=[{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(h){return[parseInt(h[1]),parseInt(h[2]),parseInt(h[3])]}},{re:/^(\w{2})(\w{2})(\w{2})$/,example:["#00ff00","336699"],process:function(h){return[parseInt(h[1],16),parseInt(h[2],16),parseInt(h[3],16)]}},{re:/^(\w{1})(\w{1})(\w{1})$/,example:["#fb0","f0f"],process:function(h){return[parseInt(h[1]+h[1],16),parseInt(h[2]+h[2],16),parseInt(h[3]+h[3],16)]}}],a=0;a<n.length;a++){var o=n[a].re,s=n[a].process,c=o.exec(r);c&&(e=s(c),this.r=e[0],this.g=e[1],this.b=e[2],this.ok=!0)}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toHex=function(){var h=this.r.toString(16),f=this.g.toString(16),p=this.b.toString(16);return h.length==1&&(h="0"+h),f.length==1&&(f="0"+f),p.length==1&&(p="0"+p),"#"+h+f+p}}/**
 * @license
 * Joseph Myers does not specify a particular license for his work.
 *
 * Author: Joseph Myers
 * Accessed from: http://www.myersdaily.org/joseph/javascript/md5.js
 *
 * Modified by: Owen Leong
 */function ws(r,e){var n=r[0],a=r[1],o=r[2],s=r[3];n=Ze(n,a,o,s,e[0],7,-680876936),s=Ze(s,n,a,o,e[1],12,-389564586),o=Ze(o,s,n,a,e[2],17,606105819),a=Ze(a,o,s,n,e[3],22,-**********),n=Ze(n,a,o,s,e[4],7,-176418897),s=Ze(s,n,a,o,e[5],12,**********),o=Ze(o,s,n,a,e[6],17,-**********),a=Ze(a,o,s,n,e[7],22,-45705983),n=Ze(n,a,o,s,e[8],7,**********),s=Ze(s,n,a,o,e[9],12,-**********),o=Ze(o,s,n,a,e[10],17,-42063),a=Ze(a,o,s,n,e[11],22,-**********),n=Ze(n,a,o,s,e[12],7,**********),s=Ze(s,n,a,o,e[13],12,-40341101),o=Ze(o,s,n,a,e[14],17,-**********),n=tn(n,a=Ze(a,o,s,n,e[15],22,**********),o,s,e[1],5,-165796510),s=tn(s,n,a,o,e[6],9,-**********),o=tn(o,s,n,a,e[11],14,643717713),a=tn(a,o,s,n,e[0],20,-373897302),n=tn(n,a,o,s,e[5],5,-701558691),s=tn(s,n,a,o,e[10],9,38016083),o=tn(o,s,n,a,e[15],14,-660478335),a=tn(a,o,s,n,e[4],20,-405537848),n=tn(n,a,o,s,e[9],5,568446438),s=tn(s,n,a,o,e[14],9,-1019803690),o=tn(o,s,n,a,e[3],14,-187363961),a=tn(a,o,s,n,e[8],20,1163531501),n=tn(n,a,o,s,e[13],5,-1444681467),s=tn(s,n,a,o,e[2],9,-51403784),o=tn(o,s,n,a,e[7],14,1735328473),n=en(n,a=tn(a,o,s,n,e[12],20,-1926607734),o,s,e[5],4,-378558),s=en(s,n,a,o,e[8],11,-2022574463),o=en(o,s,n,a,e[11],16,1839030562),a=en(a,o,s,n,e[14],23,-35309556),n=en(n,a,o,s,e[1],4,-1530992060),s=en(s,n,a,o,e[4],11,1272893353),o=en(o,s,n,a,e[7],16,-155497632),a=en(a,o,s,n,e[10],23,-1094730640),n=en(n,a,o,s,e[13],4,681279174),s=en(s,n,a,o,e[0],11,-358537222),o=en(o,s,n,a,e[3],16,-722521979),a=en(a,o,s,n,e[6],23,76029189),n=en(n,a,o,s,e[9],4,-640364487),s=en(s,n,a,o,e[12],11,-421815835),o=en(o,s,n,a,e[15],16,530742520),n=nn(n,a=en(a,o,s,n,e[2],23,-995338651),o,s,e[0],6,-198630844),s=nn(s,n,a,o,e[7],10,1126891415),o=nn(o,s,n,a,e[14],15,-1416354905),a=nn(a,o,s,n,e[5],21,-57434055),n=nn(n,a,o,s,e[12],6,1700485571),s=nn(s,n,a,o,e[3],10,-1894986606),o=nn(o,s,n,a,e[10],15,-1051523),a=nn(a,o,s,n,e[1],21,-2054922799),n=nn(n,a,o,s,e[8],6,1873313359),s=nn(s,n,a,o,e[15],10,-30611744),o=nn(o,s,n,a,e[6],15,-1560198380),a=nn(a,o,s,n,e[13],21,1309151649),n=nn(n,a,o,s,e[4],6,-145523070),s=nn(s,n,a,o,e[11],10,-1120210379),o=nn(o,s,n,a,e[2],15,718787259),a=nn(a,o,s,n,e[9],21,-343485551),r[0]=Cr(n,r[0]),r[1]=Cr(a,r[1]),r[2]=Cr(o,r[2]),r[3]=Cr(s,r[3])}function xo(r,e,n,a,o,s){return e=Cr(Cr(e,r),Cr(a,s)),Cr(e<<o|e>>>32-o,n)}function Ze(r,e,n,a,o,s,c){return xo(e&n|~e&a,r,e,o,s,c)}function tn(r,e,n,a,o,s,c){return xo(e&a|n&~a,r,e,o,s,c)}function en(r,e,n,a,o,s,c){return xo(e^n^a,r,e,o,s,c)}function nn(r,e,n,a,o,s,c){return xo(n^(e|~a),r,e,o,s,c)}function Vl(r){var e,n=r.length,a=[1732584193,-271733879,-1732584194,271733878];for(e=64;e<=r.length;e+=64)ws(a,Ku(r.substring(e-64,e)));r=r.substring(e-64);var o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(e=0;e<r.length;e++)o[e>>2]|=r.charCodeAt(e)<<(e%4<<3);if(o[e>>2]|=128<<(e%4<<3),e>55)for(ws(a,o),e=0;e<16;e++)o[e]=0;return o[14]=8*n,ws(a,o),a}function Ku(r){var e,n=[];for(e=0;e<64;e+=4)n[e>>2]=r.charCodeAt(e)+(r.charCodeAt(e+1)<<8)+(r.charCodeAt(e+2)<<16)+(r.charCodeAt(e+3)<<24);return n}ga=zt.atob.bind(zt),Ps=zt.btoa.bind(zt);var yl="0123456789abcdef".split("");function Xu(r){for(var e="",n=0;n<4;n++)e+=yl[r>>8*n+4&15]+yl[r>>8*n&15];return e}function Qu(r){return String.fromCharCode((255&r)>>0,(65280&r)>>8,(16711680&r)>>16,(**********&r)>>24)}function Cs(r){return Vl(r).map(Qu).join("")}var Zu=function(r){for(var e=0;e<r.length;e++)r[e]=Xu(r[e]);return r.join("")}(Vl("hello"))!="5d41402abc4b2a76b9719d911017c592";function Cr(r,e){if(Zu){var n=(65535&r)+(65535&e);return(r>>16)+(e>>16)+(n>>16)<<16|65535&n}return r+e&**********}/**
 * @license
 * FPDF is released under a permissive license: there is no usage restriction.
 * You may embed it freely in your application (commercial or not), with or
 * without modifications.
 *
 * Reference: http://www.fpdf.org/en/script/script37.php
 */function Is(r,e){var n,a,o,s;if(r!==n){for(var c=(o=r,s=1+(256/r.length>>0),new Array(s+1).join(o)),h=[],f=0;f<256;f++)h[f]=f;var p=0;for(f=0;f<256;f++){var w=h[f];p=(p+w+c.charCodeAt(f))%256,h[f]=h[p],h[p]=w}n=r,a=h}else h=a;var L=e.length,x=0,m=0,D="";for(f=0;f<L;f++)m=(m+(w=h[x=(x+1)%256]))%256,h[x]=h[m],h[m]=w,c=h[(h[x]+h[m])%256],D+=String.fromCharCode(e.charCodeAt(f)^c);return D}/**
 * @license
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 * Author: Owen Leong (@owenl131)
 * Date: 15 Oct 2020
 * References:
 * https://www.cs.cmu.edu/~dst/Adobe/Gallery/anon21jul01-pdf-encryption.txt
 * https://github.com/foliojs/pdfkit/blob/master/lib/security.js
 * http://www.fpdf.org/en/script/script37.php
 */var wl={print:4,modify:8,copy:16,"annot-forms":32};function Si(r,e,n,a){this.v=1,this.r=2;var o=192;r.forEach(function(h){if(wl.perm!==void 0)throw new Error("Invalid permission: "+h);o+=wl[h]}),this.padding="(¿N^NuAd\0NVÿú\b..\0¶Ðh>/\f©þdSiz";var s=(e+this.padding).substr(0,32),c=(n+this.padding).substr(0,32);this.O=this.processOwnerPassword(s,c),this.P=-(1+(255^o)),this.encryptionKey=Cs(s+this.O+this.lsbFirstWord(this.P)+this.hexToBytes(a)).substr(0,5),this.U=Is(this.encryptionKey,this.padding)}function _i(r){if(/[^\u0000-\u00ff]/.test(r))throw new Error("Invalid PDF Name Object: "+r+", Only accept ASCII characters.");for(var e="",n=r.length,a=0;a<n;a++){var o=r.charCodeAt(a);o<33||o===35||o===37||o===40||o===41||o===47||o===60||o===62||o===91||o===93||o===123||o===125||o>126?e+="#"+("0"+o.toString(16)).slice(-2):e+=r[a]}return e}function Nl(r){if(de(r)!=="object")throw new Error("Invalid Context passed to initialize PubSub (jsPDF-module)");var e={};this.subscribe=function(n,a,o){if(o=o||!1,typeof n!="string"||typeof a!="function"||typeof o!="boolean")throw new Error("Invalid arguments passed to PubSub.subscribe (jsPDF-module)");e.hasOwnProperty(n)||(e[n]={});var s=Math.random().toString(35);return e[n][s]=[a,!!o],s},this.unsubscribe=function(n){for(var a in e)if(e[a][n])return delete e[a][n],Object.keys(e[a]).length===0&&delete e[a],!0;return!1},this.publish=function(n){if(e.hasOwnProperty(n)){var a=Array.prototype.slice.call(arguments,1),o=[];for(var s in e[n]){var c=e[n][s];try{c[0].apply(r,a)}catch(h){zt.console&&be.error("jsPDF PubSub Error",h.message,h)}c[1]&&o.push(s)}o.length&&o.forEach(this.unsubscribe)}},this.getTopics=function(){return e}}function No(r){if(!(this instanceof No))return new No(r);var e="opacity,stroke-opacity".split(",");for(var n in r)r.hasOwnProperty(n)&&e.indexOf(n)>=0&&(this[n]=r[n]);this.id="",this.objectNumber=-1}function Wl(r,e){this.gState=r,this.matrix=e,this.id="",this.objectNumber=-1}function Vr(r,e,n,a,o){if(!(this instanceof Vr))return new Vr(r,e,n,a,o);this.type=r==="axial"?2:3,this.coords=e,this.colors=n,Wl.call(this,a,o)}function Ci(r,e,n,a,o){if(!(this instanceof Ci))return new Ci(r,e,n,a,o);this.boundingBox=r,this.xStep=e,this.yStep=n,this.stream="",this.cloneIndex=0,Wl.call(this,a,o)}function Vt(r){var e,n=typeof arguments[0]=="string"?arguments[0]:"p",a=arguments[1],o=arguments[2],s=arguments[3],c=[],h=1,f=16,p="S",w=null;de(r=r||{})==="object"&&(n=r.orientation,a=r.unit||a,o=r.format||o,s=r.compress||r.compressPdf||s,(w=r.encryption||null)!==null&&(w.userPassword=w.userPassword||"",w.ownerPassword=w.ownerPassword||"",w.userPermissions=w.userPermissions||[]),h=typeof r.userUnit=="number"?Math.abs(r.userUnit):1,r.precision!==void 0&&(e=r.precision),r.floatPrecision!==void 0&&(f=r.floatPrecision),p=r.defaultPathOperation||"S"),c=r.filters||(s===!0?["FlateEncode"]:c),a=a||"mm",n=(""+(n||"P")).toLowerCase();var L=r.putOnlyUsedFonts||!1,x={},m={internal:{},__private__:{}};m.__private__.PubSub=Nl;var D="1.3",k=m.__private__.getPdfVersion=function(){return D};m.__private__.setPdfVersion=function(l){D=l};var O={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};m.__private__.getPageFormats=function(){return O};var _=m.__private__.getPageFormat=function(l){return O[l]};o=o||"a4";var j={COMPAT:"compat",ADVANCED:"advanced"},J=j.COMPAT;function st(){this.saveGraphicsState(),M(new Ut(Ft,0,0,-Ft,0,pr()*Ft).toString()+" cm"),this.setFontSize(this.getFontSize()/Ft),p="n",J=j.ADVANCED}function ft(){this.restoreGraphicsState(),p="S",J=j.COMPAT}var At=m.__private__.combineFontStyleAndFontWeight=function(l,v){if(l=="bold"&&v=="normal"||l=="bold"&&v==400||l=="normal"&&v=="italic"||l=="bold"&&v=="italic")throw new Error("Invalid Combination of fontweight and fontstyle");return v&&(l=v==400||v==="normal"?l==="italic"?"italic":"normal":v!=700&&v!=="bold"||l!=="normal"?(v==700?"bold":v)+""+l:"bold"),l};m.advancedAPI=function(l){var v=J===j.COMPAT;return v&&st.call(this),typeof l!="function"||(l(this),v&&ft.call(this)),this},m.compatAPI=function(l){var v=J===j.ADVANCED;return v&&ft.call(this),typeof l!="function"||(l(this),v&&st.call(this)),this},m.isAdvancedAPI=function(){return J===j.ADVANCED};var rt,G=function(l){if(J!==j.ADVANCED)throw new Error(l+" is only available in 'advanced' API mode. You need to call advancedAPI() first.")},vt=m.roundToPrecision=m.__private__.roundToPrecision=function(l,v){var F=e||v;if(isNaN(l)||isNaN(F))throw new Error("Invalid argument passed to jsPDF.roundToPrecision");return l.toFixed(F).replace(/0+$/,"")};rt=m.hpf=m.__private__.hpf=typeof f=="number"?function(l){if(isNaN(l))throw new Error("Invalid argument passed to jsPDF.hpf");return vt(l,f)}:f==="smart"?function(l){if(isNaN(l))throw new Error("Invalid argument passed to jsPDF.hpf");return vt(l,l>-1&&l<1?16:5)}:function(l){if(isNaN(l))throw new Error("Invalid argument passed to jsPDF.hpf");return vt(l,16)};var bt=m.f2=m.__private__.f2=function(l){if(isNaN(l))throw new Error("Invalid argument passed to jsPDF.f2");return vt(l,2)},C=m.__private__.f3=function(l){if(isNaN(l))throw new Error("Invalid argument passed to jsPDF.f3");return vt(l,3)},I=m.scale=m.__private__.scale=function(l){if(isNaN(l))throw new Error("Invalid argument passed to jsPDF.scale");return J===j.COMPAT?l*Ft:J===j.ADVANCED?l:void 0},z=function(l){return J===j.COMPAT?pr()-l:J===j.ADVANCED?l:void 0},R=function(l){return I(z(l))};m.__private__.setPrecision=m.setPrecision=function(l){typeof parseInt(l,10)=="number"&&(e=parseInt(l,10))};var lt,ot="00000000000000000000000000000000",mt=m.__private__.getFileId=function(){return ot},tt=m.__private__.setFileId=function(l){return ot=l!==void 0&&/^[a-fA-F0-9]{32}$/.test(l)?l.toUpperCase():ot.split("").map(function(){return"ABCDEF0123456789".charAt(Math.floor(16*Math.random()))}).join(""),w!==null&&(Xe=new Si(w.userPermissions,w.userPassword,w.ownerPassword,ot)),ot};m.setFileId=function(l){return tt(l),this},m.getFileId=function(){return mt()};var pt=m.__private__.convertDateToPDFDate=function(l){var v=l.getTimezoneOffset(),F=v<0?"+":"-",B=Math.floor(Math.abs(v/60)),$=Math.abs(v%60),at=[F,E(B),"'",E($),"'"].join("");return["D:",l.getFullYear(),E(l.getMonth()+1),E(l.getDate()),E(l.getHours()),E(l.getMinutes()),E(l.getSeconds()),at].join("")},ut=m.__private__.convertPDFDateToDate=function(l){var v=parseInt(l.substr(2,4),10),F=parseInt(l.substr(6,2),10)-1,B=parseInt(l.substr(8,2),10),$=parseInt(l.substr(10,2),10),at=parseInt(l.substr(12,2),10),yt=parseInt(l.substr(14,2),10);return new Date(v,F,B,$,at,yt,0)},Ot=m.__private__.setCreationDate=function(l){var v;if(l===void 0&&(l=new Date),l instanceof Date)v=pt(l);else{if(!/^D:(20[0-2][0-9]|203[0-7]|19[7-9][0-9])(0[0-9]|1[0-2])([0-2][0-9]|3[0-1])(0[0-9]|1[0-9]|2[0-3])(0[0-9]|[1-5][0-9])(0[0-9]|[1-5][0-9])(\+0[0-9]|\+1[0-4]|-0[0-9]|-1[0-1])'(0[0-9]|[1-5][0-9])'?$/.test(l))throw new Error("Invalid argument passed to jsPDF.setCreationDate");v=l}return lt=v},N=m.__private__.getCreationDate=function(l){var v=lt;return l==="jsDate"&&(v=ut(lt)),v};m.setCreationDate=function(l){return Ot(l),this},m.getCreationDate=function(l){return N(l)};var T,E=m.__private__.padd2=function(l){return("0"+parseInt(l)).slice(-2)},V=m.__private__.padd2Hex=function(l){return("00"+(l=l.toString())).substr(l.length)},Y=0,Q=[],et=[],Z=0,Lt=[],Nt=[],Tt=!1,kt=et,Ht=function(){Y=0,Z=0,et=[],Q=[],Lt=[],nr=Ee(),kn=Ee()};m.__private__.setCustomOutputDestination=function(l){Tt=!0,kt=l};var it=function(l){Tt||(kt=l)};m.__private__.resetCustomOutputDestination=function(){Tt=!1,kt=et};var M=m.__private__.out=function(l){return l=l.toString(),Z+=l.length+1,kt.push(l),kt},Yt=m.__private__.write=function(l){return M(arguments.length===1?l.toString():Array.prototype.join.call(arguments," "))},Et=m.__private__.getArrayBuffer=function(l){for(var v=l.length,F=new ArrayBuffer(v),B=new Uint8Array(F);v--;)B[v]=l.charCodeAt(v);return F},wt=[["Helvetica","helvetica","normal","WinAnsiEncoding"],["Helvetica-Bold","helvetica","bold","WinAnsiEncoding"],["Helvetica-Oblique","helvetica","italic","WinAnsiEncoding"],["Helvetica-BoldOblique","helvetica","bolditalic","WinAnsiEncoding"],["Courier","courier","normal","WinAnsiEncoding"],["Courier-Bold","courier","bold","WinAnsiEncoding"],["Courier-Oblique","courier","italic","WinAnsiEncoding"],["Courier-BoldOblique","courier","bolditalic","WinAnsiEncoding"],["Times-Roman","times","normal","WinAnsiEncoding"],["Times-Bold","times","bold","WinAnsiEncoding"],["Times-Italic","times","italic","WinAnsiEncoding"],["Times-BoldItalic","times","bolditalic","WinAnsiEncoding"],["ZapfDingbats","zapfdingbats","normal",null],["Symbol","symbol","normal",null]];m.__private__.getStandardFonts=function(){return wt};var xt=r.fontSize||16;m.__private__.setFontSize=m.setFontSize=function(l){return xt=J===j.ADVANCED?l/Ft:l,this};var Ct,_t=m.__private__.getFontSize=m.getFontSize=function(){return J===j.COMPAT?xt:xt*Ft},Bt=r.R2L||!1;m.__private__.setR2L=m.setR2L=function(l){return Bt=l,this},m.__private__.getR2L=m.getR2L=function(){return Bt};var Wt,Jt=m.__private__.setZoomMode=function(l){var v=[void 0,null,"fullwidth","fullheight","fullpage","original"];if(/^(?:\d+\.\d*|\d*\.\d+|\d+)%$/.test(l))Ct=l;else if(isNaN(l)){if(v.indexOf(l)===-1)throw new Error('zoom must be Integer (e.g. 2), a percentage Value (e.g. 300%) or fullwidth, fullheight, fullpage, original. "'+l+'" is not recognized.');Ct=l}else Ct=parseInt(l,10)};m.__private__.getZoomMode=function(){return Ct};var te,ie=m.__private__.setPageMode=function(l){if([void 0,null,"UseNone","UseOutlines","UseThumbs","FullScreen"].indexOf(l)==-1)throw new Error('Page mode must be one of UseNone, UseOutlines, UseThumbs, or FullScreen. "'+l+'" is not recognized.');Wt=l};m.__private__.getPageMode=function(){return Wt};var pe=m.__private__.setLayoutMode=function(l){if([void 0,null,"continuous","single","twoleft","tworight","two"].indexOf(l)==-1)throw new Error('Layout mode must be one of continuous, single, twoleft, tworight. "'+l+'" is not recognized.');te=l};m.__private__.getLayoutMode=function(){return te},m.__private__.setDisplayMode=m.setDisplayMode=function(l,v,F){return Jt(l),pe(v),ie(F),this};var Gt={title:"",subject:"",author:"",keywords:"",creator:""};m.__private__.getDocumentProperty=function(l){if(Object.keys(Gt).indexOf(l)===-1)throw new Error("Invalid argument passed to jsPDF.getDocumentProperty");return Gt[l]},m.__private__.getDocumentProperties=function(){return Gt},m.__private__.setDocumentProperties=m.setProperties=m.setDocumentProperties=function(l){for(var v in Gt)Gt.hasOwnProperty(v)&&l[v]&&(Gt[v]=l[v]);return this},m.__private__.setDocumentProperty=function(l,v){if(Object.keys(Gt).indexOf(l)===-1)throw new Error("Invalid arguments passed to jsPDF.setDocumentProperty");return Gt[l]=v};var ne,Ft,Ke,se,Pn,ge={},Ae={},Un=[],ce={},kr={},Se={},Cn={},er=null,_e=0,Kt=[],ue=new Nl(m),Fr=r.hotfixes||[],Ye={},zn={},Hn=[],Ut=function l(v,F,B,$,at,yt){if(!(this instanceof l))return new l(v,F,B,$,at,yt);isNaN(v)&&(v=1),isNaN(F)&&(F=0),isNaN(B)&&(B=0),isNaN($)&&($=1),isNaN(at)&&(at=0),isNaN(yt)&&(yt=0),this._matrix=[v,F,B,$,at,yt]};Object.defineProperty(Ut.prototype,"sx",{get:function(){return this._matrix[0]},set:function(l){this._matrix[0]=l}}),Object.defineProperty(Ut.prototype,"shy",{get:function(){return this._matrix[1]},set:function(l){this._matrix[1]=l}}),Object.defineProperty(Ut.prototype,"shx",{get:function(){return this._matrix[2]},set:function(l){this._matrix[2]=l}}),Object.defineProperty(Ut.prototype,"sy",{get:function(){return this._matrix[3]},set:function(l){this._matrix[3]=l}}),Object.defineProperty(Ut.prototype,"tx",{get:function(){return this._matrix[4]},set:function(l){this._matrix[4]=l}}),Object.defineProperty(Ut.prototype,"ty",{get:function(){return this._matrix[5]},set:function(l){this._matrix[5]=l}}),Object.defineProperty(Ut.prototype,"a",{get:function(){return this._matrix[0]},set:function(l){this._matrix[0]=l}}),Object.defineProperty(Ut.prototype,"b",{get:function(){return this._matrix[1]},set:function(l){this._matrix[1]=l}}),Object.defineProperty(Ut.prototype,"c",{get:function(){return this._matrix[2]},set:function(l){this._matrix[2]=l}}),Object.defineProperty(Ut.prototype,"d",{get:function(){return this._matrix[3]},set:function(l){this._matrix[3]=l}}),Object.defineProperty(Ut.prototype,"e",{get:function(){return this._matrix[4]},set:function(l){this._matrix[4]=l}}),Object.defineProperty(Ut.prototype,"f",{get:function(){return this._matrix[5]},set:function(l){this._matrix[5]=l}}),Object.defineProperty(Ut.prototype,"rotation",{get:function(){return Math.atan2(this.shx,this.sx)}}),Object.defineProperty(Ut.prototype,"scaleX",{get:function(){return this.decompose().scale.sx}}),Object.defineProperty(Ut.prototype,"scaleY",{get:function(){return this.decompose().scale.sy}}),Object.defineProperty(Ut.prototype,"isIdentity",{get:function(){return this.sx===1&&this.shy===0&&this.shx===0&&this.sy===1&&this.tx===0&&this.ty===0}}),Ut.prototype.join=function(l){return[this.sx,this.shy,this.shx,this.sy,this.tx,this.ty].map(rt).join(l)},Ut.prototype.multiply=function(l){var v=l.sx*this.sx+l.shy*this.shx,F=l.sx*this.shy+l.shy*this.sy,B=l.shx*this.sx+l.sy*this.shx,$=l.shx*this.shy+l.sy*this.sy,at=l.tx*this.sx+l.ty*this.shx+this.tx,yt=l.tx*this.shy+l.ty*this.sy+this.ty;return new Ut(v,F,B,$,at,yt)},Ut.prototype.decompose=function(){var l=this.sx,v=this.shy,F=this.shx,B=this.sy,$=this.tx,at=this.ty,yt=Math.sqrt(l*l+v*v),Dt=(l/=yt)*F+(v/=yt)*B;F-=l*Dt,B-=v*Dt;var Rt=Math.sqrt(F*F+B*B);return Dt/=Rt,l*(B/=Rt)<v*(F/=Rt)&&(l=-l,v=-v,Dt=-Dt,yt=-yt),{scale:new Ut(yt,0,0,Rt,0,0),translate:new Ut(1,0,0,1,$,at),rotate:new Ut(l,v,-v,l,0,0),skew:new Ut(1,0,Dt,1,0,0)}},Ut.prototype.toString=function(l){return this.join(" ")},Ut.prototype.inversed=function(){var l=this.sx,v=this.shy,F=this.shx,B=this.sy,$=this.tx,at=this.ty,yt=1/(l*B-v*F),Dt=B*yt,Rt=-v*yt,Zt=-F*yt,Xt=l*yt;return new Ut(Dt,Rt,Zt,Xt,-Dt*$-Zt*at,-Rt*$-Xt*at)},Ut.prototype.applyToPoint=function(l){var v=l.x*this.sx+l.y*this.shx+this.tx,F=l.x*this.shy+l.y*this.sy+this.ty;return new oi(v,F)},Ut.prototype.applyToRectangle=function(l){var v=this.applyToPoint(l),F=this.applyToPoint(new oi(l.x+l.w,l.y+l.h));return new Gi(v.x,v.y,F.x-v.x,F.y-v.y)},Ut.prototype.clone=function(){var l=this.sx,v=this.shy,F=this.shx,B=this.sy,$=this.tx,at=this.ty;return new Ut(l,v,F,B,$,at)},m.Matrix=Ut;var In=m.matrixMult=function(l,v){return v.multiply(l)},Vn=new Ut(1,0,0,1,0,0);m.unitMatrix=m.identityMatrix=Vn;var sn=function(l,v){if(!kr[l]){var F=(v instanceof Vr?"Sh":"P")+(Object.keys(ce).length+1).toString(10);v.id=F,kr[l]=F,ce[F]=v,ue.publish("addPattern",v)}};m.ShadingPattern=Vr,m.TilingPattern=Ci,m.addShadingPattern=function(l,v){return G("addShadingPattern()"),sn(l,v),this},m.beginTilingPattern=function(l){G("beginTilingPattern()"),Oa(l.boundingBox[0],l.boundingBox[1],l.boundingBox[2]-l.boundingBox[0],l.boundingBox[3]-l.boundingBox[1],l.matrix)},m.endTilingPattern=function(l,v){G("endTilingPattern()"),v.stream=Nt[T].join(`
`),sn(l,v),ue.publish("endTilingPattern",v),Hn.pop().restore()};var Ue=m.__private__.newObject=function(){var l=Ee();return pn(l,!0),l},Ee=m.__private__.newObjectDeferred=function(){return Y++,Q[Y]=function(){return Z},Y},pn=function(l,v){return v=typeof v=="boolean"&&v,Q[l]=Z,v&&M(l+" 0 obj"),l},Yr=m.__private__.newAdditionalObject=function(){var l={objId:Ee(),content:""};return Lt.push(l),l},nr=Ee(),kn=Ee(),Fn=m.__private__.decodeColorString=function(l){var v=l.split(" ");if(v.length!==2||v[1]!=="g"&&v[1]!=="G")v.length===5&&(v[4]==="k"||v[4]==="K")&&(v=[(1-v[0])*(1-v[3]),(1-v[1])*(1-v[3]),(1-v[2])*(1-v[3]),"r"]);else{var F=parseFloat(v[0]);v=[F,F,F,"r"]}for(var B="#",$=0;$<3;$++)B+=("0"+Math.floor(255*parseFloat(v[$])).toString(16)).slice(-2);return B},Tn=m.__private__.encodeColorString=function(l){var v;typeof l=="string"&&(l={ch1:l});var F=l.ch1,B=l.ch2,$=l.ch3,at=l.ch4,yt=l.pdfColorType==="draw"?["G","RG","K"]:["g","rg","k"];if(typeof F=="string"&&F.charAt(0)!=="#"){var Dt=new Hl(F);if(Dt.ok)F=Dt.toHex();else if(!/^\d*\.?\d*$/.test(F))throw new Error('Invalid color "'+F+'" passed to jsPDF.encodeColorString.')}if(typeof F=="string"&&/^#[0-9A-Fa-f]{3}$/.test(F)&&(F="#"+F[1]+F[1]+F[2]+F[2]+F[3]+F[3]),typeof F=="string"&&/^#[0-9A-Fa-f]{6}$/.test(F)){var Rt=parseInt(F.substr(1),16);F=Rt>>16&255,B=Rt>>8&255,$=255&Rt}if(B===void 0||at===void 0&&F===B&&B===$)if(typeof F=="string")v=F+" "+yt[0];else switch(l.precision){case 2:v=bt(F/255)+" "+yt[0];break;case 3:default:v=C(F/255)+" "+yt[0]}else if(at===void 0||de(at)==="object"){if(at&&!isNaN(at.a)&&at.a===0)return v=["1.","1.","1.",yt[1]].join(" ");if(typeof F=="string")v=[F,B,$,yt[1]].join(" ");else switch(l.precision){case 2:v=[bt(F/255),bt(B/255),bt($/255),yt[1]].join(" ");break;default:case 3:v=[C(F/255),C(B/255),C($/255),yt[1]].join(" ")}}else if(typeof F=="string")v=[F,B,$,at,yt[2]].join(" ");else switch(l.precision){case 2:v=[bt(F),bt(B),bt($),bt(at),yt[2]].join(" ");break;case 3:default:v=[C(F),C(B),C($),C(at),yt[2]].join(" ")}return v},Wn=m.__private__.getFilters=function(){return c},yn=m.__private__.putStream=function(l){var v=(l=l||{}).data||"",F=l.filters||Wn(),B=l.alreadyAppliedFilters||[],$=l.addLength1||!1,at=v.length,yt=l.objectId,Dt=function(Qe){return Qe};if(w!==null&&yt===void 0)throw new Error("ObjectId must be passed to putStream for file encryption");w!==null&&(Dt=Xe.encryptor(yt,0));var Rt={};F===!0&&(F=["FlateEncode"]);var Zt=l.additionalKeyValues||[],Xt=(Rt=Vt.API.processDataByFilters!==void 0?Vt.API.processDataByFilters(v,F):{data:v,reverseChain:[]}).reverseChain+(Array.isArray(B)?B.join(" "):B.toString());if(Rt.data.length!==0&&(Zt.push({key:"Length",value:Rt.data.length}),$===!0&&Zt.push({key:"Length1",value:at})),Xt.length!=0)if(Xt.split("/").length-1==1)Zt.push({key:"Filter",value:Xt});else{Zt.push({key:"Filter",value:"["+Xt+"]"});for(var ae=0;ae<Zt.length;ae+=1)if(Zt[ae].key==="DecodeParms"){for(var Le=[],Pe=0;Pe<Rt.reverseChain.split("/").length-1;Pe+=1)Le.push("null");Le.push(Zt[ae].value),Zt[ae].value="["+Le.join(" ")+"]"}}M("<<");for(var Oe=0;Oe<Zt.length;Oe++)M("/"+Zt[Oe].key+" "+Zt[Oe].value);M(">>"),Rt.data.length!==0&&(M("stream"),M(Dt(Rt.data)),M("endstream"))},Gn=m.__private__.putPage=function(l){var v=l.number,F=l.data,B=l.objId,$=l.contentsObjId;pn(B,!0),M("<</Type /Page"),M("/Parent "+l.rootDictionaryObjId+" 0 R"),M("/Resources "+l.resourceDictionaryObjId+" 0 R"),M("/MediaBox ["+parseFloat(rt(l.mediaBox.bottomLeftX))+" "+parseFloat(rt(l.mediaBox.bottomLeftY))+" "+rt(l.mediaBox.topRightX)+" "+rt(l.mediaBox.topRightY)+"]"),l.cropBox!==null&&M("/CropBox ["+rt(l.cropBox.bottomLeftX)+" "+rt(l.cropBox.bottomLeftY)+" "+rt(l.cropBox.topRightX)+" "+rt(l.cropBox.topRightY)+"]"),l.bleedBox!==null&&M("/BleedBox ["+rt(l.bleedBox.bottomLeftX)+" "+rt(l.bleedBox.bottomLeftY)+" "+rt(l.bleedBox.topRightX)+" "+rt(l.bleedBox.topRightY)+"]"),l.trimBox!==null&&M("/TrimBox ["+rt(l.trimBox.bottomLeftX)+" "+rt(l.trimBox.bottomLeftY)+" "+rt(l.trimBox.topRightX)+" "+rt(l.trimBox.topRightY)+"]"),l.artBox!==null&&M("/ArtBox ["+rt(l.artBox.bottomLeftX)+" "+rt(l.artBox.bottomLeftY)+" "+rt(l.artBox.topRightX)+" "+rt(l.artBox.topRightY)+"]"),typeof l.userUnit=="number"&&l.userUnit!==1&&M("/UserUnit "+l.userUnit),ue.publish("putPage",{objId:B,pageContext:Kt[v],pageNumber:v,page:F}),M("/Contents "+$+" 0 R"),M(">>"),M("endobj");var at=F.join(`
`);return J===j.ADVANCED&&(at+=`
Q`),pn($,!0),yn({data:at,filters:Wn(),objectId:$}),M("endobj"),B},Tr=m.__private__.putPages=function(){var l,v,F=[];for(l=1;l<=_e;l++)Kt[l].objId=Ee(),Kt[l].contentsObjId=Ee();for(l=1;l<=_e;l++)F.push(Gn({number:l,data:Nt[l],objId:Kt[l].objId,contentsObjId:Kt[l].contentsObjId,mediaBox:Kt[l].mediaBox,cropBox:Kt[l].cropBox,bleedBox:Kt[l].bleedBox,trimBox:Kt[l].trimBox,artBox:Kt[l].artBox,userUnit:Kt[l].userUnit,rootDictionaryObjId:nr,resourceDictionaryObjId:kn}));pn(nr,!0),M("<</Type /Pages");var B="/Kids [";for(v=0;v<_e;v++)B+=F[v]+" 0 R ";M(B+"]"),M("/Count "+_e),M(">>"),M("endobj"),ue.publish("postPutPages")},$r=function(l){ue.publish("putFont",{font:l,out:M,newObject:Ue,putStream:yn}),l.isAlreadyPutted!==!0&&(l.objectNumber=Ue(),M("<<"),M("/Type /Font"),M("/BaseFont /"+_i(l.postScriptName)),M("/Subtype /Type1"),typeof l.encoding=="string"&&M("/Encoding /"+l.encoding),M("/FirstChar 32"),M("/LastChar 255"),M(">>"),M("endobj"))},Jr=function(){for(var l in ge)ge.hasOwnProperty(l)&&(L===!1||L===!0&&x.hasOwnProperty(l))&&$r(ge[l])},Kr=function(l){l.objectNumber=Ue();var v=[];v.push({key:"Type",value:"/XObject"}),v.push({key:"Subtype",value:"/Form"}),v.push({key:"BBox",value:"["+[rt(l.x),rt(l.y),rt(l.x+l.width),rt(l.y+l.height)].join(" ")+"]"}),v.push({key:"Matrix",value:"["+l.matrix.toString()+"]"});var F=l.pages[1].join(`
`);yn({data:F,additionalKeyValues:v,objectId:l.objectNumber}),M("endobj")},Xr=function(){for(var l in Ye)Ye.hasOwnProperty(l)&&Kr(Ye[l])},ya=function(l,v){var F,B=[],$=1/(v-1);for(F=0;F<1;F+=$)B.push(F);if(B.push(1),l[0].offset!=0){var at={offset:0,color:l[0].color};l.unshift(at)}if(l[l.length-1].offset!=1){var yt={offset:1,color:l[l.length-1].color};l.push(yt)}for(var Dt="",Rt=0,Zt=0;Zt<B.length;Zt++){for(F=B[Zt];F>l[Rt+1].offset;)Rt++;var Xt=l[Rt].offset,ae=(F-Xt)/(l[Rt+1].offset-Xt),Le=l[Rt].color,Pe=l[Rt+1].color;Dt+=V(Math.round((1-ae)*Le[0]+ae*Pe[0]).toString(16))+V(Math.round((1-ae)*Le[1]+ae*Pe[1]).toString(16))+V(Math.round((1-ae)*Le[2]+ae*Pe[2]).toString(16))}return Dt.trim()},So=function(l,v){v||(v=21);var F=Ue(),B=ya(l.colors,v),$=[];$.push({key:"FunctionType",value:"0"}),$.push({key:"Domain",value:"[0.0 1.0]"}),$.push({key:"Size",value:"["+v+"]"}),$.push({key:"BitsPerSample",value:"8"}),$.push({key:"Range",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),$.push({key:"Decode",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),yn({data:B,additionalKeyValues:$,alreadyAppliedFilters:["/ASCIIHexDecode"],objectId:F}),M("endobj"),l.objectNumber=Ue(),M("<< /ShadingType "+l.type),M("/ColorSpace /DeviceRGB");var at="/Coords ["+rt(parseFloat(l.coords[0]))+" "+rt(parseFloat(l.coords[1]))+" ";l.type===2?at+=rt(parseFloat(l.coords[2]))+" "+rt(parseFloat(l.coords[3])):at+=rt(parseFloat(l.coords[2]))+" "+rt(parseFloat(l.coords[3]))+" "+rt(parseFloat(l.coords[4]))+" "+rt(parseFloat(l.coords[5])),M(at+="]"),l.matrix&&M("/Matrix ["+l.matrix.toString()+"]"),M("/Function "+F+" 0 R"),M("/Extend [true true]"),M(">>"),M("endobj")},_o=function(l,v){var F=Ee(),B=Ue();v.push({resourcesOid:F,objectOid:B}),l.objectNumber=B;var $=[];$.push({key:"Type",value:"/Pattern"}),$.push({key:"PatternType",value:"1"}),$.push({key:"PaintType",value:"1"}),$.push({key:"TilingType",value:"1"}),$.push({key:"BBox",value:"["+l.boundingBox.map(rt).join(" ")+"]"}),$.push({key:"XStep",value:rt(l.xStep)}),$.push({key:"YStep",value:rt(l.yStep)}),$.push({key:"Resources",value:F+" 0 R"}),l.matrix&&$.push({key:"Matrix",value:"["+l.matrix.toString()+"]"}),yn({data:l.stream,additionalKeyValues:$,objectId:l.objectNumber}),M("endobj")},Qr=function(l){var v;for(v in ce)ce.hasOwnProperty(v)&&(ce[v]instanceof Vr?So(ce[v]):ce[v]instanceof Ci&&_o(ce[v],l))},wa=function(l){for(var v in l.objectNumber=Ue(),M("<<"),l)switch(v){case"opacity":M("/ca "+bt(l[v]));break;case"stroke-opacity":M("/CA "+bt(l[v]))}M(">>"),M("endobj")},Po=function(){var l;for(l in Se)Se.hasOwnProperty(l)&&wa(Se[l])},Ei=function(){for(var l in M("/XObject <<"),Ye)Ye.hasOwnProperty(l)&&Ye[l].objectNumber>=0&&M("/"+l+" "+Ye[l].objectNumber+" 0 R");ue.publish("putXobjectDict"),M(">>")},Co=function(){Xe.oid=Ue(),M("<<"),M("/Filter /Standard"),M("/V "+Xe.v),M("/R "+Xe.r),M("/U <"+Xe.toHexString(Xe.U)+">"),M("/O <"+Xe.toHexString(Xe.O)+">"),M("/P "+Xe.P),M(">>"),M("endobj")},Na=function(){for(var l in M("/Font <<"),ge)ge.hasOwnProperty(l)&&(L===!1||L===!0&&x.hasOwnProperty(l))&&M("/"+l+" "+ge[l].objectNumber+" 0 R");M(">>")},Io=function(){if(Object.keys(ce).length>0){for(var l in M("/Shading <<"),ce)ce.hasOwnProperty(l)&&ce[l]instanceof Vr&&ce[l].objectNumber>=0&&M("/"+l+" "+ce[l].objectNumber+" 0 R");ue.publish("putShadingPatternDict"),M(">>")}},Zr=function(l){if(Object.keys(ce).length>0){for(var v in M("/Pattern <<"),ce)ce.hasOwnProperty(v)&&ce[v]instanceof m.TilingPattern&&ce[v].objectNumber>=0&&ce[v].objectNumber<l&&M("/"+v+" "+ce[v].objectNumber+" 0 R");ue.publish("putTilingPatternDict"),M(">>")}},ko=function(){if(Object.keys(Se).length>0){var l;for(l in M("/ExtGState <<"),Se)Se.hasOwnProperty(l)&&Se[l].objectNumber>=0&&M("/"+l+" "+Se[l].objectNumber+" 0 R");ue.publish("putGStateDict"),M(">>")}},ke=function(l){pn(l.resourcesOid,!0),M("<<"),M("/ProcSet [/PDF /Text /ImageB /ImageC /ImageI]"),Na(),Io(),Zr(l.objectOid),ko(),Ei(),M(">>"),M("endobj")},Aa=function(){var l=[];Jr(),Po(),Xr(),Qr(l),ue.publish("putResources"),l.forEach(ke),ke({resourcesOid:kn,objectOid:Number.MAX_SAFE_INTEGER}),ue.publish("postPutResources")},La=function(){ue.publish("putAdditionalObjects");for(var l=0;l<Lt.length;l++){var v=Lt[l];pn(v.objId,!0),M(v.content),M("endobj")}ue.publish("postPutAdditionalObjects")},xa=function(l){Ae[l.fontName]=Ae[l.fontName]||{},Ae[l.fontName][l.fontStyle]=l.id},Oi=function(l,v,F,B,$){var at={id:"F"+(Object.keys(ge).length+1).toString(10),postScriptName:l,fontName:v,fontStyle:F,encoding:B,isStandardFont:$||!1,metadata:{}};return ue.publish("addFont",{font:at,instance:this}),ge[at.id]=at,xa(at),at.id},Fo=function(l){for(var v=0,F=wt.length;v<F;v++){var B=Oi.call(this,l[v][0],l[v][1],l[v][2],wt[v][3],!0);L===!1&&(x[B]=!0);var $=l[v][0].split("-");xa({id:B,fontName:$[0],fontStyle:$[1]||""})}ue.publish("addFonts",{fonts:ge,dictionary:Ae})},Dn=function(l){return l.foo=function(){try{return l.apply(this,arguments)}catch(B){var v=B.stack||"";~v.indexOf(" at ")&&(v=v.split(" at ")[1]);var F="Error in function "+v.split(`
`)[0].split("<")[0]+": "+B.message;if(!zt.console)throw new Error(F);zt.console.error(F,B),zt.alert&&alert(F)}},l.foo.bar=l,l.foo},ti=function(l,v){var F,B,$,at,yt,Dt,Rt,Zt,Xt;if($=(v=v||{}).sourceEncoding||"Unicode",yt=v.outputEncoding,(v.autoencode||yt)&&ge[ne].metadata&&ge[ne].metadata[$]&&ge[ne].metadata[$].encoding&&(at=ge[ne].metadata[$].encoding,!yt&&ge[ne].encoding&&(yt=ge[ne].encoding),!yt&&at.codePages&&(yt=at.codePages[0]),typeof yt=="string"&&(yt=at[yt]),yt)){for(Rt=!1,Dt=[],F=0,B=l.length;F<B;F++)(Zt=yt[l.charCodeAt(F)])?Dt.push(String.fromCharCode(Zt)):Dt.push(l[F]),Dt[F].charCodeAt(0)>>8&&(Rt=!0);l=Dt.join("")}for(F=l.length;Rt===void 0&&F!==0;)l.charCodeAt(F-1)>>8&&(Rt=!0),F--;if(!Rt)return l;for(Dt=v.noBOM?[]:[254,255],F=0,B=l.length;F<B;F++){if((Xt=(Zt=l.charCodeAt(F))>>8)>>8)throw new Error("Character at position "+F+" of string '"+l+"' exceeds 16bits. Cannot be encoded into UCS-2 BE");Dt.push(Xt),Dt.push(Zt-(Xt<<8))}return String.fromCharCode.apply(void 0,Dt)},ln=m.__private__.pdfEscape=m.pdfEscape=function(l,v){return ti(l,v).replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},Bi=m.__private__.beginPage=function(l){Nt[++_e]=[],Kt[_e]={objId:0,contentsObjId:0,userUnit:Number(h),artBox:null,bleedBox:null,cropBox:null,trimBox:null,mediaBox:{bottomLeftX:0,bottomLeftY:0,topRightX:Number(l[0]),topRightY:Number(l[1])}},_a(_e),it(Nt[T])},Sa=function(l,v){var F,B,$;switch(n=v||n,typeof l=="string"&&(F=_(l.toLowerCase()),Array.isArray(F)&&(B=F[0],$=F[1])),Array.isArray(l)&&(B=l[0]*Ft,$=l[1]*Ft),isNaN(B)&&(B=o[0],$=o[1]),(B>14400||$>14400)&&(be.warn("A page in a PDF can not be wider or taller than 14400 userUnit. jsPDF limits the width/height to 14400"),B=Math.min(14400,B),$=Math.min(14400,$)),o=[B,$],n.substr(0,1)){case"l":$>B&&(o=[$,B]);break;case"p":B>$&&(o=[$,B])}Bi(o),Ta(zi),M(Mn),Vi!==0&&M(Vi+" J"),Wi!==0&&M(Wi+" j"),ue.publish("addPage",{pageNumber:_e})},To=function(l){l>0&&l<=_e&&(Nt.splice(l,1),Kt.splice(l,1),_e--,T>_e&&(T=_e),this.setPage(T))},_a=function(l){l>0&&l<=_e&&(T=l)},Do=m.__private__.getNumberOfPages=m.getNumberOfPages=function(){return Nt.length-1},Pa=function(l,v,F){var B,$=void 0;return F=F||{},l=l!==void 0?l:ge[ne].fontName,v=v!==void 0?v:ge[ne].fontStyle,B=l.toLowerCase(),Ae[B]!==void 0&&Ae[B][v]!==void 0?$=Ae[B][v]:Ae[l]!==void 0&&Ae[l][v]!==void 0?$=Ae[l][v]:F.disableWarning===!1&&be.warn("Unable to look up font label for font '"+l+"', '"+v+"'. Refer to getFontList() for available fonts."),$||F.noFallback||($=Ae.times[v])==null&&($=Ae.times.normal),$},Mo=m.__private__.putInfo=function(){var l=Ue(),v=function(B){return B};for(var F in w!==null&&(v=Xe.encryptor(l,0)),M("<<"),M("/Producer ("+ln(v("jsPDF "+Vt.version))+")"),Gt)Gt.hasOwnProperty(F)&&Gt[F]&&M("/"+F.substr(0,1).toUpperCase()+F.substr(1)+" ("+ln(v(Gt[F]))+")");M("/CreationDate ("+ln(v(lt))+")"),M(">>"),M("endobj")},Ri=m.__private__.putCatalog=function(l){var v=(l=l||{}).rootDictionaryObjId||nr;switch(Ue(),M("<<"),M("/Type /Catalog"),M("/Pages "+v+" 0 R"),Ct||(Ct="fullwidth"),Ct){case"fullwidth":M("/OpenAction [3 0 R /FitH null]");break;case"fullheight":M("/OpenAction [3 0 R /FitV null]");break;case"fullpage":M("/OpenAction [3 0 R /Fit]");break;case"original":M("/OpenAction [3 0 R /XYZ null null 1]");break;default:var F=""+Ct;F.substr(F.length-1)==="%"&&(Ct=parseInt(Ct)/100),typeof Ct=="number"&&M("/OpenAction [3 0 R /XYZ null null "+bt(Ct)+"]")}switch(te||(te="continuous"),te){case"continuous":M("/PageLayout /OneColumn");break;case"single":M("/PageLayout /SinglePage");break;case"two":case"twoleft":M("/PageLayout /TwoColumnLeft");break;case"tworight":M("/PageLayout /TwoColumnRight")}Wt&&M("/PageMode /"+Wt),ue.publish("putCatalog"),M(">>"),M("endobj")},jo=m.__private__.putTrailer=function(){M("trailer"),M("<<"),M("/Size "+(Y+1)),M("/Root "+Y+" 0 R"),M("/Info "+(Y-1)+" 0 R"),w!==null&&M("/Encrypt "+Xe.oid+" 0 R"),M("/ID [ <"+ot+"> <"+ot+"> ]"),M(">>")},Eo=m.__private__.putHeader=function(){M("%PDF-"+D),M("%ºß¬à")},Oo=m.__private__.putXRef=function(){var l="0000000000";M("xref"),M("0 "+(Y+1)),M("0000000000 65535 f ");for(var v=1;v<=Y;v++)typeof Q[v]=="function"?M((l+Q[v]()).slice(-10)+" 00000 n "):Q[v]!==void 0?M((l+Q[v]).slice(-10)+" 00000 n "):M("0000000000 00000 n ")},rr=m.__private__.buildDocument=function(){Ht(),it(et),ue.publish("buildDocument"),Eo(),Tr(),La(),Aa(),w!==null&&Co(),Mo(),Ri();var l=Z;return Oo(),jo(),M("startxref"),M(""+l),M("%%EOF"),it(Nt[T]),et.join(`
`)},ei=m.__private__.getBlob=function(l){return new Blob([Et(l)],{type:"application/pdf"})},ni=m.output=m.__private__.output=Dn(function(l,v){switch(typeof(v=v||{})=="string"?v={filename:v}:v.filename=v.filename||"generated.pdf",l){case void 0:return rr();case"save":m.save(v.filename);break;case"arraybuffer":return Et(rr());case"blob":return ei(rr());case"bloburi":case"bloburl":if(zt.URL!==void 0&&typeof zt.URL.createObjectURL=="function")return zt.URL&&zt.URL.createObjectURL(ei(rr()))||void 0;be.warn("bloburl is not supported by your system, because URL.createObjectURL is not supported by your browser.");break;case"datauristring":case"dataurlstring":var F="",B=rr();try{F=Ps(B)}catch{F=Ps(unescape(encodeURIComponent(B)))}return"data:application/pdf;filename="+v.filename+";base64,"+F;case"pdfobjectnewwindow":if(Object.prototype.toString.call(zt)==="[object Window]"){var $="https://cdnjs.cloudflare.com/ajax/libs/pdfobject/2.1.1/pdfobject.min.js",at=' integrity="sha512-4ze/a9/4jqu+tX9dfOqJYSvyYd5M6qum/3HpCLr+/Jqf0whc37VUbkpNGHR7/8pSnCFw47T1fmIpwBV7UySh3g==" crossorigin="anonymous"';v.pdfObjectUrl&&($=v.pdfObjectUrl,at="");var yt='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><script src="'+$+'"'+at+'><\/script><script >PDFObject.embed("'+this.output("dataurlstring")+'", '+JSON.stringify(v)+");<\/script></body></html>",Dt=zt.open();return Dt!==null&&Dt.document.write(yt),Dt}throw new Error("The option pdfobjectnewwindow just works in a browser-environment.");case"pdfjsnewwindow":if(Object.prototype.toString.call(zt)==="[object Window]"){var Rt='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe id="pdfViewer" src="'+(v.pdfJsUrl||"examples/PDF.js/web/viewer.html")+"?file=&downloadName="+v.filename+'" width="500px" height="400px" /></body></html>',Zt=zt.open();if(Zt!==null){Zt.document.write(Rt);var Xt=this;Zt.document.documentElement.querySelector("#pdfViewer").onload=function(){Zt.document.title=v.filename,Zt.document.documentElement.querySelector("#pdfViewer").contentWindow.PDFViewerApplication.open(Xt.output("bloburl"))}}return Zt}throw new Error("The option pdfjsnewwindow just works in a browser-environment.");case"dataurlnewwindow":if(Object.prototype.toString.call(zt)!=="[object Window]")throw new Error("The option dataurlnewwindow just works in a browser-environment.");var ae='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe src="'+this.output("datauristring",v)+'"></iframe></body></html>',Le=zt.open();if(Le!==null&&(Le.document.write(ae),Le.document.title=v.filename),Le||typeof safari>"u")return Le;break;case"datauri":case"dataurl":return zt.document.location.href=this.output("datauristring",v);default:return null}}),Ca=function(l){return Array.isArray(Fr)===!0&&Fr.indexOf(l)>-1};switch(a){case"pt":Ft=1;break;case"mm":Ft=72/25.4;break;case"cm":Ft=72/2.54;break;case"in":Ft=72;break;case"px":Ft=Ca("px_scaling")==1?.75:96/72;break;case"pc":case"em":Ft=12;break;case"ex":Ft=6;break;default:if(typeof a!="number")throw new Error("Invalid unit: "+a);Ft=a}var Xe=null;Ot(),tt();var Bo=function(l){return w!==null?Xe.encryptor(l,0):function(v){return v}},Ia=m.__private__.getPageInfo=m.getPageInfo=function(l){if(isNaN(l)||l%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfo");return{objId:Kt[l].objId,pageNumber:l,pageContext:Kt[l]}},$t=m.__private__.getPageInfoByObjId=function(l){if(isNaN(l)||l%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfoByObjId");for(var v in Kt)if(Kt[v].objId===l)break;return Ia(v)},Ro=m.__private__.getCurrentPageInfo=m.getCurrentPageInfo=function(){return{objId:Kt[T].objId,pageNumber:T,pageContext:Kt[T]}};m.addPage=function(){return Sa.apply(this,arguments),this},m.setPage=function(){return _a.apply(this,arguments),it.call(this,Nt[T]),this},m.insertPage=function(l){return this.addPage(),this.movePage(T,l),this},m.movePage=function(l,v){var F,B;if(l>v){F=Nt[l],B=Kt[l];for(var $=l;$>v;$--)Nt[$]=Nt[$-1],Kt[$]=Kt[$-1];Nt[v]=F,Kt[v]=B,this.setPage(v)}else if(l<v){F=Nt[l],B=Kt[l];for(var at=l;at<v;at++)Nt[at]=Nt[at+1],Kt[at]=Kt[at+1];Nt[v]=F,Kt[v]=B,this.setPage(v)}return this},m.deletePage=function(){return To.apply(this,arguments),this},m.__private__.text=m.text=function(l,v,F,B,$){var at,yt,Dt,Rt,Zt,Xt,ae,Le,Pe,Oe=(B=B||{}).scope||this;if(typeof l=="number"&&typeof v=="number"&&(typeof F=="string"||Array.isArray(F))){var Qe=F;F=v,v=l,l=Qe}if(arguments[3]instanceof Ut?(G("The transform parameter of text() with a Matrix value"),Pe=$):(Dt=arguments[4],Rt=arguments[5],de(ae=arguments[3])==="object"&&ae!==null||(typeof Dt=="string"&&(Rt=Dt,Dt=null),typeof ae=="string"&&(Rt=ae,ae=null),typeof ae=="number"&&(Dt=ae,ae=null),B={flags:ae,angle:Dt,align:Rt})),isNaN(v)||isNaN(F)||l==null)throw new Error("Invalid arguments passed to jsPDF.text");if(l.length===0)return Oe;var He="",jn=!1,mn=typeof B.lineHeightFactor=="number"?B.lineHeightFactor:Mr,Jn=Oe.internal.scaleFactor;function Ba(we){return we=we.split("	").join(Array(B.TabLen||9).join(" ")),ln(we,ae)}function Ki(we){for(var Ne,Te=we.concat(),ze=[],lr=Te.length;lr--;)typeof(Ne=Te.shift())=="string"?ze.push(Ne):Array.isArray(we)&&(Ne.length===1||Ne[1]===void 0&&Ne[2]===void 0)?ze.push(Ne[0]):ze.push([Ne[0],Ne[1],Ne[2]]);return ze}function Xi(we,Ne){var Te;if(typeof we=="string")Te=Ne(we)[0];else if(Array.isArray(we)){for(var ze,lr,aa=we.concat(),vi=[],Ha=aa.length;Ha--;)typeof(ze=aa.shift())=="string"?vi.push(Ne(ze)[0]):Array.isArray(ze)&&typeof ze[0]=="string"&&(lr=Ne(ze[0],ze[1],ze[2]),vi.push([lr[0],lr[1],lr[2]]));Te=vi}return Te}var li=!1,Qi=!0;if(typeof l=="string")li=!0;else if(Array.isArray(l)){var Zi=l.concat();yt=[];for(var ci,$e=Zi.length;$e--;)(typeof(ci=Zi.shift())!="string"||Array.isArray(ci)&&typeof ci[0]!="string")&&(Qi=!1);li=Qi}if(li===!1)throw new Error('Type of text must be string or Array. "'+l+'" is not recognized.');typeof l=="string"&&(l=l.match(/[\r?\n]/)?l.split(/\r\n|\r|\n/g):[l]);var ui=xt/Oe.internal.scaleFactor,hi=ui*(mn-1);switch(B.baseline){case"bottom":F-=hi;break;case"top":F+=ui-hi;break;case"hanging":F+=ui-2*hi;break;case"middle":F+=ui/2-hi}if((Xt=B.maxWidth||0)>0&&(typeof l=="string"?l=Oe.splitTextToSize(l,Xt):Object.prototype.toString.call(l)==="[object Array]"&&(l=l.reduce(function(we,Ne){return we.concat(Oe.splitTextToSize(Ne,Xt))},[]))),at={text:l,x:v,y:F,options:B,mutex:{pdfEscape:ln,activeFontKey:ne,fonts:ge,activeFontSize:xt}},ue.publish("preProcessText",at),l=at.text,Dt=(B=at.options).angle,!(Pe instanceof Ut)&&Dt&&typeof Dt=="number"){Dt*=Math.PI/180,B.rotationDirection===0&&(Dt=-Dt),J===j.ADVANCED&&(Dt=-Dt);var fi=Math.cos(Dt),ta=Math.sin(Dt);Pe=new Ut(fi,ta,-ta,fi,0,0)}else Dt&&Dt instanceof Ut&&(Pe=Dt);J!==j.ADVANCED||Pe||(Pe=Vn),(Zt=B.charSpace||ai)!==void 0&&(He+=rt(I(Zt))+` Tc
`,this.setCharSpace(this.getCharSpace()||0)),(Le=B.horizontalScale)!==void 0&&(He+=rt(100*Le)+` Tz
`),B.lang;var cn=-1,Jo=B.renderingMode!==void 0?B.renderingMode:B.stroke,ea=Oe.internal.getCurrentPageInfo().pageContext;switch(Jo){case 0:case!1:case"fill":cn=0;break;case 1:case!0:case"stroke":cn=1;break;case 2:case"fillThenStroke":cn=2;break;case 3:case"invisible":cn=3;break;case 4:case"fillAndAddForClipping":cn=4;break;case 5:case"strokeAndAddPathForClipping":cn=5;break;case 6:case"fillThenStrokeAndAddToPathForClipping":cn=6;break;case 7:case"addToPathForClipping":cn=7}var Ra=ea.usedRenderingMode!==void 0?ea.usedRenderingMode:-1;cn!==-1?He+=cn+` Tr
`:Ra!==-1&&(He+=`0 Tr
`),cn!==-1&&(ea.usedRenderingMode=cn),Rt=B.align||"left";var wn,di=xt*mn,qa=Oe.internal.pageSize.getWidth(),Ua=ge[ne];Zt=B.charSpace||ai,Xt=B.maxWidth||0,ae=Object.assign({autoencode:!0,noBOM:!0},B.flags);var mr=[],Or=function(we){return Oe.getStringUnitWidth(we,{font:Ua,charSpace:Zt,fontSize:xt,doKerning:!1})*xt/Jn};if(Object.prototype.toString.call(l)==="[object Array]"){var un;yt=Ki(l),Rt!=="left"&&(wn=yt.map(Or));var rn,gr=0;if(Rt==="right"){v-=wn[0],l=[],$e=yt.length;for(var ar=0;ar<$e;ar++)ar===0?(rn=$n(v),un=ir(F)):(rn=I(gr-wn[ar]),un=-di),l.push([yt[ar],rn,un]),gr=wn[ar]}else if(Rt==="center"){v-=wn[0]/2,l=[],$e=yt.length;for(var or=0;or<$e;or++)or===0?(rn=$n(v),un=ir(F)):(rn=I((gr-wn[or])/2),un=-di),l.push([yt[or],rn,un]),gr=wn[or]}else if(Rt==="left"){l=[],$e=yt.length;for(var pi=0;pi<$e;pi++)l.push(yt[pi])}else if(Rt==="justify"&&Ua.encoding==="Identity-H"){l=[],$e=yt.length,Xt=Xt!==0?Xt:qa;for(var sr=0,Fe=0;Fe<$e;Fe++)if(un=Fe===0?ir(F):-di,rn=Fe===0?$n(v):sr,Fe<$e-1){var na=I((Xt-wn[Fe])/(yt[Fe].split(" ").length-1)),an=yt[Fe].split(" ");l.push([an[0]+" ",rn,un]),sr=0;for(var Nn=1;Nn<an.length;Nn++){var mi=(Or(an[Nn-1]+" "+an[Nn])-Or(an[Nn]))*Jn+na;Nn==an.length-1?l.push([an[Nn],mi,0]):l.push([an[Nn]+" ",mi,0]),sr-=mi}}else l.push([yt[Fe],rn,un]);l.push(["",sr,0])}else{if(Rt!=="justify")throw new Error('Unrecognized alignment option, use "left", "center", "right" or "justify".');for(l=[],$e=yt.length,Xt=Xt!==0?Xt:qa,Fe=0;Fe<$e;Fe++)un=Fe===0?ir(F):-di,rn=Fe===0?$n(v):0,Fe<$e-1?mr.push(rt(I((Xt-wn[Fe])/(yt[Fe].split(" ").length-1)))):mr.push(0),l.push([yt[Fe],rn,un])}}var za=typeof B.R2L=="boolean"?B.R2L:Bt;za===!0&&(l=Xi(l,function(we,Ne,Te){return[we.split("").reverse().join(""),Ne,Te]})),at={text:l,x:v,y:F,options:B,mutex:{pdfEscape:ln,activeFontKey:ne,fonts:ge,activeFontSize:xt}},ue.publish("postProcessText",at),l=at.text,jn=at.mutex.isHex||!1;var ra=ge[ne].encoding;ra!=="WinAnsiEncoding"&&ra!=="StandardEncoding"||(l=Xi(l,function(we,Ne,Te){return[Ba(we),Ne,Te]})),yt=Ki(l),l=[];for(var Br,Rr,vr,qr=0,gi=1,Ur=Array.isArray(yt[0])?gi:qr,br="",ia=function(we,Ne,Te){var ze="";return Te instanceof Ut?(Te=typeof B.angle=="number"?In(Te,new Ut(1,0,0,1,we,Ne)):In(new Ut(1,0,0,1,we,Ne),Te),J===j.ADVANCED&&(Te=In(new Ut(1,0,0,-1,0,0),Te)),ze=Te.join(" ")+` Tm
`):ze=rt(we)+" "+rt(Ne)+` Td
`,ze},An=0;An<yt.length;An++){switch(br="",Ur){case gi:vr=(jn?"<":"(")+yt[An][0]+(jn?">":")"),Br=parseFloat(yt[An][1]),Rr=parseFloat(yt[An][2]);break;case qr:vr=(jn?"<":"(")+yt[An]+(jn?">":")"),Br=$n(v),Rr=ir(F)}mr!==void 0&&mr[An]!==void 0&&(br=mr[An]+` Tw
`),An===0?l.push(br+ia(Br,Rr,Pe)+vr):Ur===qr?l.push(br+vr):Ur===gi&&l.push(br+ia(Br,Rr,Pe)+vr)}l=Ur===qr?l.join(` Tj
T* `):l.join(` Tj
`),l+=` Tj
`;var Ln=`BT
/`;return Ln+=ne+" "+xt+` Tf
`,Ln+=rt(xt*mn)+` TL
`,Ln+=jr+`
`,Ln+=He,Ln+=l,M(Ln+="ET"),x[ne]=!0,Oe};var qo=m.__private__.clip=m.clip=function(l){return M(l==="evenodd"?"W*":"W"),this};m.clipEvenOdd=function(){return qo("evenodd")},m.__private__.discardPath=m.discardPath=function(){return M("n"),this};var Yn=m.__private__.isValidStyle=function(l){var v=!1;return[void 0,null,"S","D","F","DF","FD","f","f*","B","B*","n"].indexOf(l)!==-1&&(v=!0),v};m.__private__.setDefaultPathOperation=m.setDefaultPathOperation=function(l){return Yn(l)&&(p=l),this};var ka=m.__private__.getStyle=m.getStyle=function(l){var v=p;switch(l){case"D":case"S":v="S";break;case"F":v="f";break;case"FD":case"DF":v="B";break;case"f":case"f*":case"B":case"B*":v=l}return v},Fa=m.close=function(){return M("h"),this};m.stroke=function(){return M("S"),this},m.fill=function(l){return ri("f",l),this},m.fillEvenOdd=function(l){return ri("f*",l),this},m.fillStroke=function(l){return ri("B",l),this},m.fillStrokeEvenOdd=function(l){return ri("B*",l),this};var ri=function(l,v){de(v)==="object"?zo(v,l):M(l)},qi=function(l){l===null||J===j.ADVANCED&&l===void 0||(l=ka(l),M(l))};function Uo(l,v,F,B,$){var at=new Ci(v||this.boundingBox,F||this.xStep,B||this.yStep,this.gState,$||this.matrix);at.stream=this.stream;var yt=l+"$$"+this.cloneIndex+++"$$";return sn(yt,at),at}var zo=function(l,v){var F=kr[l.key],B=ce[F];if(B instanceof Vr)M("q"),M(Ho(v)),B.gState&&m.setGState(B.gState),M(l.matrix.toString()+" cm"),M("/"+F+" sh"),M("Q");else if(B instanceof Ci){var $=new Ut(1,0,0,-1,0,pr());l.matrix&&($=$.multiply(l.matrix||Vn),F=Uo.call(B,l.key,l.boundingBox,l.xStep,l.yStep,$).id),M("q"),M("/Pattern cs"),M("/"+F+" scn"),B.gState&&m.setGState(B.gState),M(v),M("Q")}},Ho=function(l){switch(l){case"f":case"F":return"W n";case"f*":return"W* n";case"B":return"W S";case"B*":return"W* S";case"S":return"W S";case"n":return"W n"}},Ui=m.moveTo=function(l,v){return M(rt(I(l))+" "+rt(R(v))+" m"),this},Dr=m.lineTo=function(l,v){return M(rt(I(l))+" "+rt(R(v))+" l"),this},fr=m.curveTo=function(l,v,F,B,$,at){return M([rt(I(l)),rt(R(v)),rt(I(F)),rt(R(B)),rt(I($)),rt(R(at)),"c"].join(" ")),this};m.__private__.line=m.line=function(l,v,F,B,$){if(isNaN(l)||isNaN(v)||isNaN(F)||isNaN(B)||!Yn($))throw new Error("Invalid arguments passed to jsPDF.line");return J===j.COMPAT?this.lines([[F-l,B-v]],l,v,[1,1],$||"S"):this.lines([[F-l,B-v]],l,v,[1,1]).stroke()},m.__private__.lines=m.lines=function(l,v,F,B,$,at){var yt,Dt,Rt,Zt,Xt,ae,Le,Pe,Oe,Qe,He,jn;if(typeof l=="number"&&(jn=F,F=v,v=l,l=jn),B=B||[1,1],at=at||!1,isNaN(v)||isNaN(F)||!Array.isArray(l)||!Array.isArray(B)||!Yn($)||typeof at!="boolean")throw new Error("Invalid arguments passed to jsPDF.lines");for(Ui(v,F),yt=B[0],Dt=B[1],Zt=l.length,Qe=v,He=F,Rt=0;Rt<Zt;Rt++)(Xt=l[Rt]).length===2?(Qe=Xt[0]*yt+Qe,He=Xt[1]*Dt+He,Dr(Qe,He)):(ae=Xt[0]*yt+Qe,Le=Xt[1]*Dt+He,Pe=Xt[2]*yt+Qe,Oe=Xt[3]*Dt+He,Qe=Xt[4]*yt+Qe,He=Xt[5]*Dt+He,fr(ae,Le,Pe,Oe,Qe,He));return at&&Fa(),qi($),this},m.path=function(l){for(var v=0;v<l.length;v++){var F=l[v],B=F.c;switch(F.op){case"m":Ui(B[0],B[1]);break;case"l":Dr(B[0],B[1]);break;case"c":fr.apply(this,B);break;case"h":Fa()}}return this},m.__private__.rect=m.rect=function(l,v,F,B,$){if(isNaN(l)||isNaN(v)||isNaN(F)||isNaN(B)||!Yn($))throw new Error("Invalid arguments passed to jsPDF.rect");return J===j.COMPAT&&(B=-B),M([rt(I(l)),rt(R(v)),rt(I(F)),rt(I(B)),"re"].join(" ")),qi($),this},m.__private__.triangle=m.triangle=function(l,v,F,B,$,at,yt){if(isNaN(l)||isNaN(v)||isNaN(F)||isNaN(B)||isNaN($)||isNaN(at)||!Yn(yt))throw new Error("Invalid arguments passed to jsPDF.triangle");return this.lines([[F-l,B-v],[$-F,at-B],[l-$,v-at]],l,v,[1,1],yt,!0),this},m.__private__.roundedRect=m.roundedRect=function(l,v,F,B,$,at,yt){if(isNaN(l)||isNaN(v)||isNaN(F)||isNaN(B)||isNaN($)||isNaN(at)||!Yn(yt))throw new Error("Invalid arguments passed to jsPDF.roundedRect");var Dt=4/3*(Math.SQRT2-1);return $=Math.min($,.5*F),at=Math.min(at,.5*B),this.lines([[F-2*$,0],[$*Dt,0,$,at-at*Dt,$,at],[0,B-2*at],[0,at*Dt,-$*Dt,at,-$,at],[2*$-F,0],[-$*Dt,0,-$,-at*Dt,-$,-at],[0,2*at-B],[0,-at*Dt,$*Dt,-at,$,-at]],l+$,v,[1,1],yt,!0),this},m.__private__.ellipse=m.ellipse=function(l,v,F,B,$){if(isNaN(l)||isNaN(v)||isNaN(F)||isNaN(B)||!Yn($))throw new Error("Invalid arguments passed to jsPDF.ellipse");var at=4/3*(Math.SQRT2-1)*F,yt=4/3*(Math.SQRT2-1)*B;return Ui(l+F,v),fr(l+F,v-yt,l+at,v-B,l,v-B),fr(l-at,v-B,l-F,v-yt,l-F,v),fr(l-F,v+yt,l-at,v+B,l,v+B),fr(l+at,v+B,l+F,v+yt,l+F,v),qi($),this},m.__private__.circle=m.circle=function(l,v,F,B){if(isNaN(l)||isNaN(v)||isNaN(F)||!Yn(B))throw new Error("Invalid arguments passed to jsPDF.circle");return this.ellipse(l,v,F,F,B)},m.setFont=function(l,v,F){return F&&(v=At(v,F)),ne=Pa(l,v,{disableWarning:!1}),this};var Vo=m.__private__.getFont=m.getFont=function(){return ge[Pa.apply(m,arguments)]};m.__private__.getFontList=m.getFontList=function(){var l,v,F={};for(l in Ae)if(Ae.hasOwnProperty(l))for(v in F[l]=[],Ae[l])Ae[l].hasOwnProperty(v)&&F[l].push(v);return F},m.addFont=function(l,v,F,B,$){var at=["StandardEncoding","MacRomanEncoding","Identity-H","WinAnsiEncoding"];return arguments[3]&&at.indexOf(arguments[3])!==-1?$=arguments[3]:arguments[3]&&at.indexOf(arguments[3])==-1&&(F=At(F,B)),$=$||"Identity-H",Oi.call(this,l,v,F,$)};var Mr,zi=r.lineWidth||.200025,ii=m.__private__.getLineWidth=m.getLineWidth=function(){return zi},Ta=m.__private__.setLineWidth=m.setLineWidth=function(l){return zi=l,M(rt(I(l))+" w"),this};m.__private__.setLineDash=Vt.API.setLineDash=Vt.API.setLineDashPattern=function(l,v){if(l=l||[],v=v||0,isNaN(v)||!Array.isArray(l))throw new Error("Invalid arguments passed to jsPDF.setLineDash");return l=l.map(function(F){return rt(I(F))}).join(" "),v=rt(I(v)),M("["+l+"] "+v+" d"),this};var Da=m.__private__.getLineHeight=m.getLineHeight=function(){return xt*Mr};m.__private__.getLineHeight=m.getLineHeight=function(){return xt*Mr};var Ma=m.__private__.setLineHeightFactor=m.setLineHeightFactor=function(l){return typeof(l=l||1.15)=="number"&&(Mr=l),this},ja=m.__private__.getLineHeightFactor=m.getLineHeightFactor=function(){return Mr};Ma(r.lineHeight);var $n=m.__private__.getHorizontalCoordinate=function(l){return I(l)},ir=m.__private__.getVerticalCoordinate=function(l){return J===j.ADVANCED?l:Kt[T].mediaBox.topRightY-Kt[T].mediaBox.bottomLeftY-I(l)},Wo=m.__private__.getHorizontalCoordinateString=m.getHorizontalCoordinateString=function(l){return rt($n(l))},dr=m.__private__.getVerticalCoordinateString=m.getVerticalCoordinateString=function(l){return rt(ir(l))},Mn=r.strokeColor||"0 G";m.__private__.getStrokeColor=m.getDrawColor=function(){return Fn(Mn)},m.__private__.setStrokeColor=m.setDrawColor=function(l,v,F,B){return Mn=Tn({ch1:l,ch2:v,ch3:F,ch4:B,pdfColorType:"draw",precision:2}),M(Mn),this};var Hi=r.fillColor||"0 g";m.__private__.getFillColor=m.getFillColor=function(){return Fn(Hi)},m.__private__.setFillColor=m.setFillColor=function(l,v,F,B){return Hi=Tn({ch1:l,ch2:v,ch3:F,ch4:B,pdfColorType:"fill",precision:2}),M(Hi),this};var jr=r.textColor||"0 g",Go=m.__private__.getTextColor=m.getTextColor=function(){return Fn(jr)};m.__private__.setTextColor=m.setTextColor=function(l,v,F,B){return jr=Tn({ch1:l,ch2:v,ch3:F,ch4:B,pdfColorType:"text",precision:3}),this};var ai=r.charSpace,Yo=m.__private__.getCharSpace=m.getCharSpace=function(){return parseFloat(ai||0)};m.__private__.setCharSpace=m.setCharSpace=function(l){if(isNaN(l))throw new Error("Invalid argument passed to jsPDF.setCharSpace");return ai=l,this};var Vi=0;m.CapJoinStyles={0:0,butt:0,but:0,miter:0,1:1,round:1,rounded:1,circle:1,2:2,projecting:2,project:2,square:2,bevel:2},m.__private__.setLineCap=m.setLineCap=function(l){var v=m.CapJoinStyles[l];if(v===void 0)throw new Error("Line cap style of '"+l+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return Vi=v,M(v+" J"),this};var Wi=0;m.__private__.setLineJoin=m.setLineJoin=function(l){var v=m.CapJoinStyles[l];if(v===void 0)throw new Error("Line join style of '"+l+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return Wi=v,M(v+" j"),this},m.__private__.setLineMiterLimit=m.__private__.setMiterLimit=m.setLineMiterLimit=m.setMiterLimit=function(l){if(l=l||0,isNaN(l))throw new Error("Invalid argument passed to jsPDF.setLineMiterLimit");return M(rt(I(l))+" M"),this},m.GState=No,m.setGState=function(l){(l=typeof l=="string"?Se[Cn[l]]:Ea(null,l)).equals(er)||(M("/"+l.id+" gs"),er=l)};var Ea=function(l,v){if(!l||!Cn[l]){var F=!1;for(var B in Se)if(Se.hasOwnProperty(B)&&Se[B].equals(v)){F=!0;break}if(F)v=Se[B];else{var $="GS"+(Object.keys(Se).length+1).toString(10);Se[$]=v,v.id=$}return l&&(Cn[l]=v.id),ue.publish("addGState",v),v}};m.addGState=function(l,v){return Ea(l,v),this},m.saveGraphicsState=function(){return M("q"),Un.push({key:ne,size:xt,color:jr}),this},m.restoreGraphicsState=function(){M("Q");var l=Un.pop();return ne=l.key,xt=l.size,jr=l.color,er=null,this},m.setCurrentTransformationMatrix=function(l){return M(l.toString()+" cm"),this},m.comment=function(l){return M("#"+l),this};var oi=function(l,v){var F=l||0;Object.defineProperty(this,"x",{enumerable:!0,get:function(){return F},set:function(at){isNaN(at)||(F=parseFloat(at))}});var B=v||0;Object.defineProperty(this,"y",{enumerable:!0,get:function(){return B},set:function(at){isNaN(at)||(B=parseFloat(at))}});var $="pt";return Object.defineProperty(this,"type",{enumerable:!0,get:function(){return $},set:function(at){$=at.toString()}}),this},Gi=function(l,v,F,B){oi.call(this,l,v),this.type="rect";var $=F||0;Object.defineProperty(this,"w",{enumerable:!0,get:function(){return $},set:function(yt){isNaN(yt)||($=parseFloat(yt))}});var at=B||0;return Object.defineProperty(this,"h",{enumerable:!0,get:function(){return at},set:function(yt){isNaN(yt)||(at=parseFloat(yt))}}),this},Yi=function(){this.page=_e,this.currentPage=T,this.pages=Nt.slice(0),this.pagesContext=Kt.slice(0),this.x=Ke,this.y=se,this.matrix=Pn,this.width=Er(T),this.height=pr(T),this.outputDestination=kt,this.id="",this.objectNumber=-1};Yi.prototype.restore=function(){_e=this.page,T=this.currentPage,Kt=this.pagesContext,Nt=this.pages,Ke=this.x,se=this.y,Pn=this.matrix,$i(T,this.width),Ji(T,this.height),kt=this.outputDestination};var Oa=function(l,v,F,B,$){Hn.push(new Yi),_e=T=0,Nt=[],Ke=l,se=v,Pn=$,Bi([F,B])},$o=function(l){if(zn[l])Hn.pop().restore();else{var v=new Yi,F="Xo"+(Object.keys(Ye).length+1).toString(10);v.id=F,zn[l]=F,Ye[F]=v,ue.publish("addFormObject",v),Hn.pop().restore()}};for(var si in m.beginFormObject=function(l,v,F,B,$){return Oa(l,v,F,B,$),this},m.endFormObject=function(l){return $o(l),this},m.doFormObject=function(l,v){var F=Ye[zn[l]];return M("q"),M(v.toString()+" cm"),M("/"+F.id+" Do"),M("Q"),this},m.getFormObject=function(l){var v=Ye[zn[l]];return{x:v.x,y:v.y,width:v.width,height:v.height,matrix:v.matrix}},m.save=function(l,v){return l=l||"generated.pdf",(v=v||{}).returnPromise=v.returnPromise||!1,v.returnPromise===!1?(Hr(ei(rr()),l),typeof Hr.unload=="function"&&zt.setTimeout&&setTimeout(Hr.unload,911),this):new Promise(function(F,B){try{var $=Hr(ei(rr()),l);typeof Hr.unload=="function"&&zt.setTimeout&&setTimeout(Hr.unload,911),F($)}catch(at){B(at.message)}})},Vt.API)Vt.API.hasOwnProperty(si)&&(si==="events"&&Vt.API.events.length?function(l,v){var F,B,$;for($=v.length-1;$!==-1;$--)F=v[$][0],B=v[$][1],l.subscribe.apply(l,[F].concat(typeof B=="function"?[B]:B))}(ue,Vt.API.events):m[si]=Vt.API[si]);var Er=m.getPageWidth=function(l){return(Kt[l=l||T].mediaBox.topRightX-Kt[l].mediaBox.bottomLeftX)/Ft},$i=m.setPageWidth=function(l,v){Kt[l].mediaBox.topRightX=v*Ft+Kt[l].mediaBox.bottomLeftX},pr=m.getPageHeight=function(l){return(Kt[l=l||T].mediaBox.topRightY-Kt[l].mediaBox.bottomLeftY)/Ft},Ji=m.setPageHeight=function(l,v){Kt[l].mediaBox.topRightY=v*Ft+Kt[l].mediaBox.bottomLeftY};return m.internal={pdfEscape:ln,getStyle:ka,getFont:Vo,getFontSize:_t,getCharSpace:Yo,getTextColor:Go,getLineHeight:Da,getLineHeightFactor:ja,getLineWidth:ii,write:Yt,getHorizontalCoordinate:$n,getVerticalCoordinate:ir,getCoordinateString:Wo,getVerticalCoordinateString:dr,collections:{},newObject:Ue,newAdditionalObject:Yr,newObjectDeferred:Ee,newObjectDeferredBegin:pn,getFilters:Wn,putStream:yn,events:ue,scaleFactor:Ft,pageSize:{getWidth:function(){return Er(T)},setWidth:function(l){$i(T,l)},getHeight:function(){return pr(T)},setHeight:function(l){Ji(T,l)}},encryptionOptions:w,encryption:Xe,getEncryptor:Bo,output:ni,getNumberOfPages:Do,pages:Nt,out:M,f2:bt,f3:C,getPageInfo:Ia,getPageInfoByObjId:$t,getCurrentPageInfo:Ro,getPDFVersion:k,Point:oi,Rectangle:Gi,Matrix:Ut,hasHotfix:Ca},Object.defineProperty(m.internal.pageSize,"width",{get:function(){return Er(T)},set:function(l){$i(T,l)},enumerable:!0,configurable:!0}),Object.defineProperty(m.internal.pageSize,"height",{get:function(){return pr(T)},set:function(l){Ji(T,l)},enumerable:!0,configurable:!0}),Fo.call(m,wt),ne="F1",Sa(o,n),ue.publish("initialized"),m}Si.prototype.lsbFirstWord=function(r){return String.fromCharCode(r>>0&255,r>>8&255,r>>16&255,r>>24&255)},Si.prototype.toHexString=function(r){return r.split("").map(function(e){return("0"+(255&e.charCodeAt(0)).toString(16)).slice(-2)}).join("")},Si.prototype.hexToBytes=function(r){for(var e=[],n=0;n<r.length;n+=2)e.push(String.fromCharCode(parseInt(r.substr(n,2),16)));return e.join("")},Si.prototype.processOwnerPassword=function(r,e){return Is(Cs(e).substr(0,5),r)},Si.prototype.encryptor=function(r,e){var n=Cs(this.encryptionKey+String.fromCharCode(255&r,r>>8&255,r>>16&255,255&e,e>>8&255)).substr(0,10);return function(a){return Is(n,a)}},No.prototype.equals=function(r){var e,n="id,objectNumber,equals";if(!r||de(r)!==de(this))return!1;var a=0;for(e in this)if(!(n.indexOf(e)>=0)){if(this.hasOwnProperty(e)&&!r.hasOwnProperty(e)||this[e]!==r[e])return!1;a++}for(e in r)r.hasOwnProperty(e)&&n.indexOf(e)<0&&a--;return a===0},Vt.API={events:[]},Vt.version="3.0.0";var Ie=Vt.API,Es=1,Gr=function(r){return r.replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},Li=function(r){return r.replace(/\\\\/g,"\\").replace(/\\\(/g,"(").replace(/\\\)/g,")")},Qt=function(r){return r.toFixed(2)},_r=function(r){return r.toFixed(5)};Ie.__acroform__={};var dn=function(r,e){r.prototype=Object.create(e.prototype),r.prototype.constructor=r},Al=function(r){return r*Es},Xn=function(r){var e=new Yl,n=jt.internal.getHeight(r)||0,a=jt.internal.getWidth(r)||0;return e.BBox=[0,0,Number(Qt(a)),Number(Qt(n))],e},th=Ie.__acroform__.setBit=function(r,e){if(r=r||0,e=e||0,isNaN(r)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBit");return r|=1<<e},eh=Ie.__acroform__.clearBit=function(r,e){if(r=r||0,e=e||0,isNaN(r)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBit");return r&=~(1<<e)},nh=Ie.__acroform__.getBit=function(r,e){if(isNaN(r)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBit");return r&1<<e?1:0},De=Ie.__acroform__.getBitForPdf=function(r,e){if(isNaN(r)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBitForPdf");return nh(r,e-1)},Me=Ie.__acroform__.setBitForPdf=function(r,e){if(isNaN(r)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBitForPdf");return th(r,e-1)},je=Ie.__acroform__.clearBitForPdf=function(r,e){if(isNaN(r)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBitForPdf");return eh(r,e-1)},rh=Ie.__acroform__.calculateCoordinates=function(r,e){var n=e.internal.getHorizontalCoordinate,a=e.internal.getVerticalCoordinate,o=r[0],s=r[1],c=r[2],h=r[3],f={};return f.lowerLeft_X=n(o)||0,f.lowerLeft_Y=a(s+h)||0,f.upperRight_X=n(o+c)||0,f.upperRight_Y=a(s)||0,[Number(Qt(f.lowerLeft_X)),Number(Qt(f.lowerLeft_Y)),Number(Qt(f.upperRight_X)),Number(Qt(f.upperRight_Y))]},ih=function(r){if(r.appearanceStreamContent)return r.appearanceStreamContent;if(r.V||r.DV){var e=[],n=r._V||r.DV,a=ks(r,n),o=r.scope.internal.getFont(r.fontName,r.fontStyle).id;e.push("/Tx BMC"),e.push("q"),e.push("BT"),e.push(r.scope.__private__.encodeColorString(r.color)),e.push("/"+o+" "+Qt(a.fontSize)+" Tf"),e.push("1 0 0 1 0 0 Tm"),e.push(a.text),e.push("ET"),e.push("Q"),e.push("EMC");var s=Xn(r);return s.scope=r.scope,s.stream=e.join(`
`),s}},ks=function(r,e){var n=r.fontSize===0?r.maxFontSize:r.fontSize,a={text:"",fontSize:""},o=(e=(e=e.substr(0,1)=="("?e.substr(1):e).substr(e.length-1)==")"?e.substr(0,e.length-1):e).split(" ");o=r.multiline?o.map(function(C){return C.split(`
`)}):o.map(function(C){return[C]});var s=n,c=jt.internal.getHeight(r)||0;c=c<0?-c:c;var h=jt.internal.getWidth(r)||0;h=h<0?-h:h;var f=function(C,I,z){if(C+1<o.length){var R=I+" "+o[C+1][0];return uo(R,r,z).width<=h-4}return!1};s++;t:for(;s>0;){e="",s--;var p,w,L=uo("3",r,s).height,x=r.multiline?c-s:(c-L)/2,m=x+=2,D=0,k=0,O=0;if(s<=0){e=`(...) Tj
`,e+="% Width of Text: "+uo(e,r,s=12).width+", FieldWidth:"+h+`
`;break}for(var _="",j=0,J=0;J<o.length;J++)if(o.hasOwnProperty(J)){var st=!1;if(o[J].length!==1&&O!==o[J].length-1){if((L+2)*(j+2)+2>c)continue t;_+=o[J][O],st=!0,k=J,J--}else{_=(_+=o[J][O]+" ").substr(_.length-1)==" "?_.substr(0,_.length-1):_;var ft=parseInt(J),At=f(ft,_,s),rt=J>=o.length-1;if(At&&!rt){_+=" ",O=0;continue}if(At||rt){if(rt)k=ft;else if(r.multiline&&(L+2)*(j+2)+2>c)continue t}else{if(!r.multiline||(L+2)*(j+2)+2>c)continue t;k=ft}}for(var G="",vt=D;vt<=k;vt++){var bt=o[vt];if(r.multiline){if(vt===k){G+=bt[O]+" ",O=(O+1)%bt.length;continue}if(vt===D){G+=bt[bt.length-1]+" ";continue}}G+=bt[0]+" "}switch(G=G.substr(G.length-1)==" "?G.substr(0,G.length-1):G,w=uo(G,r,s).width,r.textAlign){case"right":p=h-w-2;break;case"center":p=(h-w)/2;break;case"left":default:p=2}e+=Qt(p)+" "+Qt(m)+` Td
`,e+="("+Gr(G)+`) Tj
`,e+=-Qt(p)+` 0 Td
`,m=-(s+2),w=0,D=st?k:k+1,j++,_=""}break}return a.text=e,a.fontSize=s,a},uo=function(r,e,n){var a=e.scope.internal.getFont(e.fontName,e.fontStyle),o=e.scope.getStringUnitWidth(r,{font:a,fontSize:parseFloat(n),charSpace:0})*parseFloat(n);return{height:e.scope.getStringUnitWidth("3",{font:a,fontSize:parseFloat(n),charSpace:0})*parseFloat(n)*1.5,width:o}},ah={fields:[],xForms:[],acroFormDictionaryRoot:null,printedOut:!1,internal:null,isInitialized:!1},oh=function(r,e){var n={type:"reference",object:r};e.internal.getPageInfo(r.page).pageContext.annotations.find(function(a){return a.type===n.type&&a.object===n.object})===void 0&&e.internal.getPageInfo(r.page).pageContext.annotations.push(n)},sh=function(r,e){for(var n in r)if(r.hasOwnProperty(n)){var a=n,o=r[n];e.internal.newObjectDeferredBegin(o.objId,!0),de(o)==="object"&&typeof o.putStream=="function"&&o.putStream(),delete r[a]}},lh=function(r,e){if(e.scope=r,r.internal!==void 0&&(r.internal.acroformPlugin===void 0||r.internal.acroformPlugin.isInitialized===!1)){if(Rn.FieldNum=0,r.internal.acroformPlugin=JSON.parse(JSON.stringify(ah)),r.internal.acroformPlugin.acroFormDictionaryRoot)throw new Error("Exception while creating AcroformDictionary");Es=r.internal.scaleFactor,r.internal.acroformPlugin.acroFormDictionaryRoot=new $l,r.internal.acroformPlugin.acroFormDictionaryRoot.scope=r,r.internal.acroformPlugin.acroFormDictionaryRoot._eventID=r.internal.events.subscribe("postPutResources",function(){(function(n){n.internal.events.unsubscribe(n.internal.acroformPlugin.acroFormDictionaryRoot._eventID),delete n.internal.acroformPlugin.acroFormDictionaryRoot._eventID,n.internal.acroformPlugin.printedOut=!0})(r)}),r.internal.events.subscribe("buildDocument",function(){(function(n){n.internal.acroformPlugin.acroFormDictionaryRoot.objId=void 0;var a=n.internal.acroformPlugin.acroFormDictionaryRoot.Fields;for(var o in a)if(a.hasOwnProperty(o)){var s=a[o];s.objId=void 0,s.hasAnnotation&&oh(s,n)}})(r)}),r.internal.events.subscribe("putCatalog",function(){(function(n){if(n.internal.acroformPlugin.acroFormDictionaryRoot===void 0)throw new Error("putCatalogCallback: Root missing.");n.internal.write("/AcroForm "+n.internal.acroformPlugin.acroFormDictionaryRoot.objId+" 0 R")})(r)}),r.internal.events.subscribe("postPutPages",function(n){(function(a,o){var s=!a;for(var c in a||(o.internal.newObjectDeferredBegin(o.internal.acroformPlugin.acroFormDictionaryRoot.objId,!0),o.internal.acroformPlugin.acroFormDictionaryRoot.putStream()),a=a||o.internal.acroformPlugin.acroFormDictionaryRoot.Kids)if(a.hasOwnProperty(c)){var h=a[c],f=[],p=h.Rect;if(h.Rect&&(h.Rect=rh(h.Rect,o)),o.internal.newObjectDeferredBegin(h.objId,!0),h.DA=jt.createDefaultAppearanceStream(h),de(h)==="object"&&typeof h.getKeyValueListForStream=="function"&&(f=h.getKeyValueListForStream()),h.Rect=p,h.hasAppearanceStream&&!h.appearanceStreamContent){var w=ih(h);f.push({key:"AP",value:"<</N "+w+">>"}),o.internal.acroformPlugin.xForms.push(w)}if(h.appearanceStreamContent){var L="";for(var x in h.appearanceStreamContent)if(h.appearanceStreamContent.hasOwnProperty(x)){var m=h.appearanceStreamContent[x];if(L+="/"+x+" ",L+="<<",Object.keys(m).length>=1||Array.isArray(m)){for(var c in m)if(m.hasOwnProperty(c)){var D=m[c];typeof D=="function"&&(D=D.call(o,h)),L+="/"+c+" "+D+" ",o.internal.acroformPlugin.xForms.indexOf(D)>=0||o.internal.acroformPlugin.xForms.push(D)}}else typeof(D=m)=="function"&&(D=D.call(o,h)),L+="/"+c+" "+D,o.internal.acroformPlugin.xForms.indexOf(D)>=0||o.internal.acroformPlugin.xForms.push(D);L+=">>"}f.push({key:"AP",value:`<<
`+L+">>"})}o.internal.putStream({additionalKeyValues:f,objectId:h.objId}),o.internal.out("endobj")}s&&sh(o.internal.acroformPlugin.xForms,o)})(n,r)}),r.internal.acroformPlugin.isInitialized=!0}},Gl=Ie.__acroform__.arrayToPdfArray=function(r,e,n){var a=function(c){return c};if(Array.isArray(r)){for(var o="[",s=0;s<r.length;s++)switch(s!==0&&(o+=" "),de(r[s])){case"boolean":case"number":case"object":o+=r[s].toString();break;case"string":r[s].substr(0,1)!=="/"?(e!==void 0&&n&&(a=n.internal.getEncryptor(e)),o+="("+Gr(a(r[s].toString()))+")"):o+=r[s].toString()}return o+="]"}throw new Error("Invalid argument passed to jsPDF.__acroform__.arrayToPdfArray")},Ns=function(r,e,n){var a=function(o){return o};return e!==void 0&&n&&(a=n.internal.getEncryptor(e)),(r=r||"").toString(),r="("+Gr(a(r))+")"},Zn=function(){this._objId=void 0,this._scope=void 0,Object.defineProperty(this,"objId",{get:function(){if(this._objId===void 0){if(this.scope===void 0)return;this._objId=this.scope.internal.newObjectDeferred()}return this._objId},set:function(r){this._objId=r}}),Object.defineProperty(this,"scope",{value:this._scope,writable:!0})};Zn.prototype.toString=function(){return this.objId+" 0 R"},Zn.prototype.putStream=function(){var r=this.getKeyValueListForStream();this.scope.internal.putStream({data:this.stream,additionalKeyValues:r,objectId:this.objId}),this.scope.internal.out("endobj")},Zn.prototype.getKeyValueListForStream=function(){var r=[],e=Object.getOwnPropertyNames(this).filter(function(s){return s!="content"&&s!="appearanceStreamContent"&&s!="scope"&&s!="objId"&&s.substring(0,1)!="_"});for(var n in e)if(Object.getOwnPropertyDescriptor(this,e[n]).configurable===!1){var a=e[n],o=this[a];o&&(Array.isArray(o)?r.push({key:a,value:Gl(o,this.objId,this.scope)}):o instanceof Zn?(o.scope=this.scope,r.push({key:a,value:o.objId+" 0 R"})):typeof o!="function"&&r.push({key:a,value:o}))}return r};var Yl=function(){Zn.call(this),Object.defineProperty(this,"Type",{value:"/XObject",configurable:!1,writable:!0}),Object.defineProperty(this,"Subtype",{value:"/Form",configurable:!1,writable:!0}),Object.defineProperty(this,"FormType",{value:1,configurable:!1,writable:!0});var r,e=[];Object.defineProperty(this,"BBox",{configurable:!1,get:function(){return e},set:function(n){e=n}}),Object.defineProperty(this,"Resources",{value:"2 0 R",configurable:!1,writable:!0}),Object.defineProperty(this,"stream",{enumerable:!1,configurable:!0,set:function(n){r=n.trim()},get:function(){return r||null}})};dn(Yl,Zn);var $l=function(){Zn.call(this);var r,e=[];Object.defineProperty(this,"Kids",{enumerable:!1,configurable:!0,get:function(){return e.length>0?e:void 0}}),Object.defineProperty(this,"Fields",{enumerable:!1,configurable:!1,get:function(){return e}}),Object.defineProperty(this,"DA",{enumerable:!1,configurable:!1,get:function(){if(r){var n=function(a){return a};return this.scope&&(n=this.scope.internal.getEncryptor(this.objId)),"("+Gr(n(r))+")"}},set:function(n){r=n}})};dn($l,Zn);var Rn=function r(){Zn.call(this);var e=4;Object.defineProperty(this,"F",{enumerable:!1,configurable:!1,get:function(){return e},set:function(_){if(isNaN(_))throw new Error('Invalid value "'+_+'" for attribute F supplied.');e=_}}),Object.defineProperty(this,"showWhenPrinted",{enumerable:!0,configurable:!0,get:function(){return!!De(e,3)},set:function(_){_?this.F=Me(e,3):this.F=je(e,3)}});var n=0;Object.defineProperty(this,"Ff",{enumerable:!1,configurable:!1,get:function(){return n},set:function(_){if(isNaN(_))throw new Error('Invalid value "'+_+'" for attribute Ff supplied.');n=_}});var a=[];Object.defineProperty(this,"Rect",{enumerable:!1,configurable:!1,get:function(){if(a.length!==0)return a},set:function(_){a=_!==void 0?_:[]}}),Object.defineProperty(this,"x",{enumerable:!0,configurable:!0,get:function(){return!a||isNaN(a[0])?0:a[0]},set:function(_){a[0]=_}}),Object.defineProperty(this,"y",{enumerable:!0,configurable:!0,get:function(){return!a||isNaN(a[1])?0:a[1]},set:function(_){a[1]=_}}),Object.defineProperty(this,"width",{enumerable:!0,configurable:!0,get:function(){return!a||isNaN(a[2])?0:a[2]},set:function(_){a[2]=_}}),Object.defineProperty(this,"height",{enumerable:!0,configurable:!0,get:function(){return!a||isNaN(a[3])?0:a[3]},set:function(_){a[3]=_}});var o="";Object.defineProperty(this,"FT",{enumerable:!0,configurable:!1,get:function(){return o},set:function(_){switch(_){case"/Btn":case"/Tx":case"/Ch":case"/Sig":o=_;break;default:throw new Error('Invalid value "'+_+'" for attribute FT supplied.')}}});var s=null;Object.defineProperty(this,"T",{enumerable:!0,configurable:!1,get:function(){if(!s||s.length<1){if(this instanceof Ao)return;s="FieldObject"+r.FieldNum++}var _=function(j){return j};return this.scope&&(_=this.scope.internal.getEncryptor(this.objId)),"("+Gr(_(s))+")"},set:function(_){s=_.toString()}}),Object.defineProperty(this,"fieldName",{configurable:!0,enumerable:!0,get:function(){return s},set:function(_){s=_}});var c="helvetica";Object.defineProperty(this,"fontName",{enumerable:!0,configurable:!0,get:function(){return c},set:function(_){c=_}});var h="normal";Object.defineProperty(this,"fontStyle",{enumerable:!0,configurable:!0,get:function(){return h},set:function(_){h=_}});var f=0;Object.defineProperty(this,"fontSize",{enumerable:!0,configurable:!0,get:function(){return f},set:function(_){f=_}});var p=void 0;Object.defineProperty(this,"maxFontSize",{enumerable:!0,configurable:!0,get:function(){return p===void 0?50/Es:p},set:function(_){p=_}});var w="black";Object.defineProperty(this,"color",{enumerable:!0,configurable:!0,get:function(){return w},set:function(_){w=_}});var L="/F1 0 Tf 0 g";Object.defineProperty(this,"DA",{enumerable:!0,configurable:!1,get:function(){if(!(!L||this instanceof Ao||this instanceof Wr))return Ns(L,this.objId,this.scope)},set:function(_){_=_.toString(),L=_}});var x=null;Object.defineProperty(this,"DV",{enumerable:!1,configurable:!1,get:function(){if(x)return this instanceof Ge?x:Ns(x,this.objId,this.scope)},set:function(_){_=_.toString(),x=this instanceof Ge?_:_.substr(0,1)==="("?Li(_.substr(1,_.length-2)):Li(_)}}),Object.defineProperty(this,"defaultValue",{enumerable:!0,configurable:!0,get:function(){return this instanceof Ge?Li(x.substr(1,x.length-1)):x},set:function(_){_=_.toString(),x=this instanceof Ge?"/"+_:_}});var m=null;Object.defineProperty(this,"_V",{enumerable:!1,configurable:!1,get:function(){if(m)return m},set:function(_){this.V=_}}),Object.defineProperty(this,"V",{enumerable:!1,configurable:!1,get:function(){if(m)return this instanceof Ge?m:Ns(m,this.objId,this.scope)},set:function(_){_=_.toString(),m=this instanceof Ge?_:_.substr(0,1)==="("?Li(_.substr(1,_.length-2)):Li(_)}}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,get:function(){return this instanceof Ge?Li(m.substr(1,m.length-1)):m},set:function(_){_=_.toString(),m=this instanceof Ge?"/"+_:_}}),Object.defineProperty(this,"hasAnnotation",{enumerable:!0,configurable:!0,get:function(){return this.Rect}}),Object.defineProperty(this,"Type",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Annot":null}}),Object.defineProperty(this,"Subtype",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Widget":null}});var D,k=!1;Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return k},set:function(_){_=!!_,k=_}}),Object.defineProperty(this,"page",{enumerable:!0,configurable:!0,get:function(){if(D)return D},set:function(_){D=_}}),Object.defineProperty(this,"readOnly",{enumerable:!0,configurable:!0,get:function(){return!!De(this.Ff,1)},set:function(_){_?this.Ff=Me(this.Ff,1):this.Ff=je(this.Ff,1)}}),Object.defineProperty(this,"required",{enumerable:!0,configurable:!0,get:function(){return!!De(this.Ff,2)},set:function(_){_?this.Ff=Me(this.Ff,2):this.Ff=je(this.Ff,2)}}),Object.defineProperty(this,"noExport",{enumerable:!0,configurable:!0,get:function(){return!!De(this.Ff,3)},set:function(_){_?this.Ff=Me(this.Ff,3):this.Ff=je(this.Ff,3)}});var O=null;Object.defineProperty(this,"Q",{enumerable:!0,configurable:!1,get:function(){if(O!==null)return O},set:function(_){if([0,1,2].indexOf(_)===-1)throw new Error('Invalid value "'+_+'" for attribute Q supplied.');O=_}}),Object.defineProperty(this,"textAlign",{get:function(){var _;switch(O){case 0:default:_="left";break;case 1:_="center";break;case 2:_="right"}return _},configurable:!0,enumerable:!0,set:function(_){switch(_){case"right":case 2:O=2;break;case"center":case 1:O=1;break;case"left":case 0:default:O=0}}})};dn(Rn,Zn);var Fi=function(){Rn.call(this),this.FT="/Ch",this.V="()",this.fontName="zapfdingbats";var r=0;Object.defineProperty(this,"TI",{enumerable:!0,configurable:!1,get:function(){return r},set:function(n){r=n}}),Object.defineProperty(this,"topIndex",{enumerable:!0,configurable:!0,get:function(){return r},set:function(n){r=n}});var e=[];Object.defineProperty(this,"Opt",{enumerable:!0,configurable:!1,get:function(){return Gl(e,this.objId,this.scope)},set:function(n){var a,o;o=[],typeof(a=n)=="string"&&(o=function(s,c,h){h||(h=1);for(var f,p=[];f=c.exec(s);)p.push(f[h]);return p}(a,/\((.*?)\)/g)),e=o}}),this.getOptions=function(){return e},this.setOptions=function(n){e=n,this.sort&&e.sort()},this.addOption=function(n){n=(n=n||"").toString(),e.push(n),this.sort&&e.sort()},this.removeOption=function(n,a){for(a=a||!1,n=(n=n||"").toString();e.indexOf(n)!==-1&&(e.splice(e.indexOf(n),1),a!==!1););},Object.defineProperty(this,"combo",{enumerable:!0,configurable:!0,get:function(){return!!De(this.Ff,18)},set:function(n){n?this.Ff=Me(this.Ff,18):this.Ff=je(this.Ff,18)}}),Object.defineProperty(this,"edit",{enumerable:!0,configurable:!0,get:function(){return!!De(this.Ff,19)},set:function(n){this.combo===!0&&(n?this.Ff=Me(this.Ff,19):this.Ff=je(this.Ff,19))}}),Object.defineProperty(this,"sort",{enumerable:!0,configurable:!0,get:function(){return!!De(this.Ff,20)},set:function(n){n?(this.Ff=Me(this.Ff,20),e.sort()):this.Ff=je(this.Ff,20)}}),Object.defineProperty(this,"multiSelect",{enumerable:!0,configurable:!0,get:function(){return!!De(this.Ff,22)},set:function(n){n?this.Ff=Me(this.Ff,22):this.Ff=je(this.Ff,22)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return!!De(this.Ff,23)},set:function(n){n?this.Ff=Me(this.Ff,23):this.Ff=je(this.Ff,23)}}),Object.defineProperty(this,"commitOnSelChange",{enumerable:!0,configurable:!0,get:function(){return!!De(this.Ff,27)},set:function(n){n?this.Ff=Me(this.Ff,27):this.Ff=je(this.Ff,27)}}),this.hasAppearanceStream=!1};dn(Fi,Rn);var Ti=function(){Fi.call(this),this.fontName="helvetica",this.combo=!1};dn(Ti,Fi);var Di=function(){Ti.call(this),this.combo=!0};dn(Di,Ti);var go=function(){Di.call(this),this.edit=!0};dn(go,Di);var Ge=function(){Rn.call(this),this.FT="/Btn",Object.defineProperty(this,"noToggleToOff",{enumerable:!0,configurable:!0,get:function(){return!!De(this.Ff,15)},set:function(n){n?this.Ff=Me(this.Ff,15):this.Ff=je(this.Ff,15)}}),Object.defineProperty(this,"radio",{enumerable:!0,configurable:!0,get:function(){return!!De(this.Ff,16)},set:function(n){n?this.Ff=Me(this.Ff,16):this.Ff=je(this.Ff,16)}}),Object.defineProperty(this,"pushButton",{enumerable:!0,configurable:!0,get:function(){return!!De(this.Ff,17)},set:function(n){n?this.Ff=Me(this.Ff,17):this.Ff=je(this.Ff,17)}}),Object.defineProperty(this,"radioIsUnison",{enumerable:!0,configurable:!0,get:function(){return!!De(this.Ff,26)},set:function(n){n?this.Ff=Me(this.Ff,26):this.Ff=je(this.Ff,26)}});var r,e={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var n=function(s){return s};if(this.scope&&(n=this.scope.internal.getEncryptor(this.objId)),Object.keys(e).length!==0){var a,o=[];for(a in o.push("<<"),e)o.push("/"+a+" ("+Gr(n(e[a]))+")");return o.push(">>"),o.join(`
`)}},set:function(n){de(n)==="object"&&(e=n)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return e.CA||""},set:function(n){typeof n=="string"&&(e.CA=n)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return r},set:function(n){r=n}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return r.substr(1,r.length-1)},set:function(n){r="/"+n}})};dn(Ge,Rn);var vo=function(){Ge.call(this),this.pushButton=!0};dn(vo,Ge);var Mi=function(){Ge.call(this),this.radio=!0,this.pushButton=!1;var r=[];Object.defineProperty(this,"Kids",{enumerable:!0,configurable:!1,get:function(){return r},set:function(e){r=e!==void 0?e:[]}})};dn(Mi,Ge);var Ao=function(){var r,e;Rn.call(this),Object.defineProperty(this,"Parent",{enumerable:!1,configurable:!1,get:function(){return r},set:function(o){r=o}}),Object.defineProperty(this,"optionName",{enumerable:!1,configurable:!0,get:function(){return e},set:function(o){e=o}});var n,a={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var o=function(h){return h};this.scope&&(o=this.scope.internal.getEncryptor(this.objId));var s,c=[];for(s in c.push("<<"),a)c.push("/"+s+" ("+Gr(o(a[s]))+")");return c.push(">>"),c.join(`
`)},set:function(o){de(o)==="object"&&(a=o)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return a.CA||""},set:function(o){typeof o=="string"&&(a.CA=o)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return n},set:function(o){n=o}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return n.substr(1,n.length-1)},set:function(o){n="/"+o}}),this.caption="l",this.appearanceState="Off",this._AppearanceType=jt.RadioButton.Circle,this.appearanceStreamContent=this._AppearanceType.createAppearanceStream(this.optionName)};dn(Ao,Rn),Mi.prototype.setAppearance=function(r){if(!("createAppearanceStream"in r)||!("getCA"in r))throw new Error("Couldn't assign Appearance to RadioButton. Appearance was Invalid!");for(var e in this.Kids)if(this.Kids.hasOwnProperty(e)){var n=this.Kids[e];n.appearanceStreamContent=r.createAppearanceStream(n.optionName),n.caption=r.getCA()}},Mi.prototype.createOption=function(r){var e=new Ao;return e.Parent=this,e.optionName=r,this.Kids.push(e),ch.call(this.scope,e),e};var bo=function(){Ge.call(this),this.fontName="zapfdingbats",this.caption="3",this.appearanceState="On",this.value="On",this.textAlign="center",this.appearanceStreamContent=jt.CheckBox.createAppearanceStream()};dn(bo,Ge);var Wr=function(){Rn.call(this),this.FT="/Tx",Object.defineProperty(this,"multiline",{enumerable:!0,configurable:!0,get:function(){return!!De(this.Ff,13)},set:function(e){e?this.Ff=Me(this.Ff,13):this.Ff=je(this.Ff,13)}}),Object.defineProperty(this,"fileSelect",{enumerable:!0,configurable:!0,get:function(){return!!De(this.Ff,21)},set:function(e){e?this.Ff=Me(this.Ff,21):this.Ff=je(this.Ff,21)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return!!De(this.Ff,23)},set:function(e){e?this.Ff=Me(this.Ff,23):this.Ff=je(this.Ff,23)}}),Object.defineProperty(this,"doNotScroll",{enumerable:!0,configurable:!0,get:function(){return!!De(this.Ff,24)},set:function(e){e?this.Ff=Me(this.Ff,24):this.Ff=je(this.Ff,24)}}),Object.defineProperty(this,"comb",{enumerable:!0,configurable:!0,get:function(){return!!De(this.Ff,25)},set:function(e){e?this.Ff=Me(this.Ff,25):this.Ff=je(this.Ff,25)}}),Object.defineProperty(this,"richText",{enumerable:!0,configurable:!0,get:function(){return!!De(this.Ff,26)},set:function(e){e?this.Ff=Me(this.Ff,26):this.Ff=je(this.Ff,26)}});var r=null;Object.defineProperty(this,"MaxLen",{enumerable:!0,configurable:!1,get:function(){return r},set:function(e){r=e}}),Object.defineProperty(this,"maxLength",{enumerable:!0,configurable:!0,get:function(){return r},set:function(e){Number.isInteger(e)&&(r=e)}}),Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return this.V||this.DV}})};dn(Wr,Rn);var yo=function(){Wr.call(this),Object.defineProperty(this,"password",{enumerable:!0,configurable:!0,get:function(){return!!De(this.Ff,14)},set:function(r){r?this.Ff=Me(this.Ff,14):this.Ff=je(this.Ff,14)}}),this.password=!0};dn(yo,Wr);var jt={CheckBox:{createAppearanceStream:function(){return{N:{On:jt.CheckBox.YesNormal},D:{On:jt.CheckBox.YesPushDown,Off:jt.CheckBox.OffPushDown}}},YesPushDown:function(r){var e=Xn(r);e.scope=r.scope;var n=[],a=r.scope.internal.getFont(r.fontName,r.fontStyle).id,o=r.scope.__private__.encodeColorString(r.color),s=ks(r,r.caption);return n.push("0.749023 g"),n.push("0 0 "+Qt(jt.internal.getWidth(r))+" "+Qt(jt.internal.getHeight(r))+" re"),n.push("f"),n.push("BMC"),n.push("q"),n.push("0 0 1 rg"),n.push("/"+a+" "+Qt(s.fontSize)+" Tf "+o),n.push("BT"),n.push(s.text),n.push("ET"),n.push("Q"),n.push("EMC"),e.stream=n.join(`
`),e},YesNormal:function(r){var e=Xn(r);e.scope=r.scope;var n=r.scope.internal.getFont(r.fontName,r.fontStyle).id,a=r.scope.__private__.encodeColorString(r.color),o=[],s=jt.internal.getHeight(r),c=jt.internal.getWidth(r),h=ks(r,r.caption);return o.push("1 g"),o.push("0 0 "+Qt(c)+" "+Qt(s)+" re"),o.push("f"),o.push("q"),o.push("0 0 1 rg"),o.push("0 0 "+Qt(c-1)+" "+Qt(s-1)+" re"),o.push("W"),o.push("n"),o.push("0 g"),o.push("BT"),o.push("/"+n+" "+Qt(h.fontSize)+" Tf "+a),o.push(h.text),o.push("ET"),o.push("Q"),e.stream=o.join(`
`),e},OffPushDown:function(r){var e=Xn(r);e.scope=r.scope;var n=[];return n.push("0.749023 g"),n.push("0 0 "+Qt(jt.internal.getWidth(r))+" "+Qt(jt.internal.getHeight(r))+" re"),n.push("f"),e.stream=n.join(`
`),e}},RadioButton:{Circle:{createAppearanceStream:function(r){var e={D:{Off:jt.RadioButton.Circle.OffPushDown},N:{}};return e.N[r]=jt.RadioButton.Circle.YesNormal,e.D[r]=jt.RadioButton.Circle.YesPushDown,e},getCA:function(){return"l"},YesNormal:function(r){var e=Xn(r);e.scope=r.scope;var n=[],a=jt.internal.getWidth(r)<=jt.internal.getHeight(r)?jt.internal.getWidth(r)/4:jt.internal.getHeight(r)/4;a=Number((.9*a).toFixed(5));var o=jt.internal.Bezier_C,s=Number((a*o).toFixed(5));return n.push("q"),n.push("1 0 0 1 "+_r(jt.internal.getWidth(r)/2)+" "+_r(jt.internal.getHeight(r)/2)+" cm"),n.push(a+" 0 m"),n.push(a+" "+s+" "+s+" "+a+" 0 "+a+" c"),n.push("-"+s+" "+a+" -"+a+" "+s+" -"+a+" 0 c"),n.push("-"+a+" -"+s+" -"+s+" -"+a+" 0 -"+a+" c"),n.push(s+" -"+a+" "+a+" -"+s+" "+a+" 0 c"),n.push("f"),n.push("Q"),e.stream=n.join(`
`),e},YesPushDown:function(r){var e=Xn(r);e.scope=r.scope;var n=[],a=jt.internal.getWidth(r)<=jt.internal.getHeight(r)?jt.internal.getWidth(r)/4:jt.internal.getHeight(r)/4;a=Number((.9*a).toFixed(5));var o=Number((2*a).toFixed(5)),s=Number((o*jt.internal.Bezier_C).toFixed(5)),c=Number((a*jt.internal.Bezier_C).toFixed(5));return n.push("0.749023 g"),n.push("q"),n.push("1 0 0 1 "+_r(jt.internal.getWidth(r)/2)+" "+_r(jt.internal.getHeight(r)/2)+" cm"),n.push(o+" 0 m"),n.push(o+" "+s+" "+s+" "+o+" 0 "+o+" c"),n.push("-"+s+" "+o+" -"+o+" "+s+" -"+o+" 0 c"),n.push("-"+o+" -"+s+" -"+s+" -"+o+" 0 -"+o+" c"),n.push(s+" -"+o+" "+o+" -"+s+" "+o+" 0 c"),n.push("f"),n.push("Q"),n.push("0 g"),n.push("q"),n.push("1 0 0 1 "+_r(jt.internal.getWidth(r)/2)+" "+_r(jt.internal.getHeight(r)/2)+" cm"),n.push(a+" 0 m"),n.push(a+" "+c+" "+c+" "+a+" 0 "+a+" c"),n.push("-"+c+" "+a+" -"+a+" "+c+" -"+a+" 0 c"),n.push("-"+a+" -"+c+" -"+c+" -"+a+" 0 -"+a+" c"),n.push(c+" -"+a+" "+a+" -"+c+" "+a+" 0 c"),n.push("f"),n.push("Q"),e.stream=n.join(`
`),e},OffPushDown:function(r){var e=Xn(r);e.scope=r.scope;var n=[],a=jt.internal.getWidth(r)<=jt.internal.getHeight(r)?jt.internal.getWidth(r)/4:jt.internal.getHeight(r)/4;a=Number((.9*a).toFixed(5));var o=Number((2*a).toFixed(5)),s=Number((o*jt.internal.Bezier_C).toFixed(5));return n.push("0.749023 g"),n.push("q"),n.push("1 0 0 1 "+_r(jt.internal.getWidth(r)/2)+" "+_r(jt.internal.getHeight(r)/2)+" cm"),n.push(o+" 0 m"),n.push(o+" "+s+" "+s+" "+o+" 0 "+o+" c"),n.push("-"+s+" "+o+" -"+o+" "+s+" -"+o+" 0 c"),n.push("-"+o+" -"+s+" -"+s+" -"+o+" 0 -"+o+" c"),n.push(s+" -"+o+" "+o+" -"+s+" "+o+" 0 c"),n.push("f"),n.push("Q"),e.stream=n.join(`
`),e}},Cross:{createAppearanceStream:function(r){var e={D:{Off:jt.RadioButton.Cross.OffPushDown},N:{}};return e.N[r]=jt.RadioButton.Cross.YesNormal,e.D[r]=jt.RadioButton.Cross.YesPushDown,e},getCA:function(){return"8"},YesNormal:function(r){var e=Xn(r);e.scope=r.scope;var n=[],a=jt.internal.calculateCross(r);return n.push("q"),n.push("1 1 "+Qt(jt.internal.getWidth(r)-2)+" "+Qt(jt.internal.getHeight(r)-2)+" re"),n.push("W"),n.push("n"),n.push(Qt(a.x1.x)+" "+Qt(a.x1.y)+" m"),n.push(Qt(a.x2.x)+" "+Qt(a.x2.y)+" l"),n.push(Qt(a.x4.x)+" "+Qt(a.x4.y)+" m"),n.push(Qt(a.x3.x)+" "+Qt(a.x3.y)+" l"),n.push("s"),n.push("Q"),e.stream=n.join(`
`),e},YesPushDown:function(r){var e=Xn(r);e.scope=r.scope;var n=jt.internal.calculateCross(r),a=[];return a.push("0.749023 g"),a.push("0 0 "+Qt(jt.internal.getWidth(r))+" "+Qt(jt.internal.getHeight(r))+" re"),a.push("f"),a.push("q"),a.push("1 1 "+Qt(jt.internal.getWidth(r)-2)+" "+Qt(jt.internal.getHeight(r)-2)+" re"),a.push("W"),a.push("n"),a.push(Qt(n.x1.x)+" "+Qt(n.x1.y)+" m"),a.push(Qt(n.x2.x)+" "+Qt(n.x2.y)+" l"),a.push(Qt(n.x4.x)+" "+Qt(n.x4.y)+" m"),a.push(Qt(n.x3.x)+" "+Qt(n.x3.y)+" l"),a.push("s"),a.push("Q"),e.stream=a.join(`
`),e},OffPushDown:function(r){var e=Xn(r);e.scope=r.scope;var n=[];return n.push("0.749023 g"),n.push("0 0 "+Qt(jt.internal.getWidth(r))+" "+Qt(jt.internal.getHeight(r))+" re"),n.push("f"),e.stream=n.join(`
`),e}}},createDefaultAppearanceStream:function(r){var e=r.scope.internal.getFont(r.fontName,r.fontStyle).id,n=r.scope.__private__.encodeColorString(r.color);return"/"+e+" "+r.fontSize+" Tf "+n}};jt.internal={Bezier_C:.551915024494,calculateCross:function(r){var e=jt.internal.getWidth(r),n=jt.internal.getHeight(r),a=Math.min(e,n);return{x1:{x:(e-a)/2,y:(n-a)/2+a},x2:{x:(e-a)/2+a,y:(n-a)/2},x3:{x:(e-a)/2,y:(n-a)/2},x4:{x:(e-a)/2+a,y:(n-a)/2+a}}}},jt.internal.getWidth=function(r){var e=0;return de(r)==="object"&&(e=Al(r.Rect[2])),e},jt.internal.getHeight=function(r){var e=0;return de(r)==="object"&&(e=Al(r.Rect[3])),e};var ch=Ie.addField=function(r){if(lh(this,r),!(r instanceof Rn))throw new Error("Invalid argument passed to jsPDF.addField.");var e;return(e=r).scope.internal.acroformPlugin.printedOut&&(e.scope.internal.acroformPlugin.printedOut=!1,e.scope.internal.acroformPlugin.acroFormDictionaryRoot=null),e.scope.internal.acroformPlugin.acroFormDictionaryRoot.Fields.push(e),r.page=r.scope.internal.getCurrentPageInfo().pageNumber,this};Ie.AcroFormChoiceField=Fi,Ie.AcroFormListBox=Ti,Ie.AcroFormComboBox=Di,Ie.AcroFormEditBox=go,Ie.AcroFormButton=Ge,Ie.AcroFormPushButton=vo,Ie.AcroFormRadioButton=Mi,Ie.AcroFormCheckBox=bo,Ie.AcroFormTextField=Wr,Ie.AcroFormPasswordField=yo,Ie.AcroFormAppearance=jt,Ie.AcroForm={ChoiceField:Fi,ListBox:Ti,ComboBox:Di,EditBox:go,Button:Ge,PushButton:vo,RadioButton:Mi,CheckBox:bo,TextField:Wr,PasswordField:yo,Appearance:jt},Vt.AcroForm={ChoiceField:Fi,ListBox:Ti,ComboBox:Di,EditBox:go,Button:Ge,PushButton:vo,RadioButton:Mi,CheckBox:bo,TextField:Wr,PasswordField:yo,Appearance:jt};function Jl(r){return r.reduce(function(e,n,a){return e[n]=a,e},{})}(function(r){r.__addimage__={};var e="UNKNOWN",n={PNG:[[137,80,78,71]],TIFF:[[77,77,0,42],[73,73,42,0]],JPEG:[[255,216,255,224,void 0,void 0,74,70,73,70,0],[255,216,255,225,void 0,void 0,69,120,105,102,0,0],[255,216,255,219],[255,216,255,238]],JPEG2000:[[0,0,0,12,106,80,32,32]],GIF87a:[[71,73,70,56,55,97]],GIF89a:[[71,73,70,56,57,97]],WEBP:[[82,73,70,70,void 0,void 0,void 0,void 0,87,69,66,80]],BMP:[[66,77],[66,65],[67,73],[67,80],[73,67],[80,84]]},a=r.__addimage__.getImageFileTypeByImageData=function(C,I){var z,R,lt,ot,mt,tt=e;if((I=I||e)==="RGBA"||C.data!==void 0&&C.data instanceof Uint8ClampedArray&&"height"in C&&"width"in C)return"RGBA";if(At(C))for(mt in n)for(lt=n[mt],z=0;z<lt.length;z+=1){for(ot=!0,R=0;R<lt[z].length;R+=1)if(lt[z][R]!==void 0&&lt[z][R]!==C[R]){ot=!1;break}if(ot===!0){tt=mt;break}}else for(mt in n)for(lt=n[mt],z=0;z<lt.length;z+=1){for(ot=!0,R=0;R<lt[z].length;R+=1)if(lt[z][R]!==void 0&&lt[z][R]!==C.charCodeAt(R)){ot=!1;break}if(ot===!0){tt=mt;break}}return tt===e&&I!==e&&(tt=I),tt},o=function C(I){for(var z=this.internal.write,R=this.internal.putStream,lt=(0,this.internal.getFilters)();lt.indexOf("FlateEncode")!==-1;)lt.splice(lt.indexOf("FlateEncode"),1);I.objectId=this.internal.newObject();var ot=[];if(ot.push({key:"Type",value:"/XObject"}),ot.push({key:"Subtype",value:"/Image"}),ot.push({key:"Width",value:I.width}),ot.push({key:"Height",value:I.height}),I.colorSpace===O.INDEXED?ot.push({key:"ColorSpace",value:"[/Indexed /DeviceRGB "+(I.palette.length/3-1)+" "+("sMask"in I&&I.sMask!==void 0?I.objectId+2:I.objectId+1)+" 0 R]"}):(ot.push({key:"ColorSpace",value:"/"+I.colorSpace}),I.colorSpace===O.DEVICE_CMYK&&ot.push({key:"Decode",value:"[1 0 1 0 1 0 1 0]"})),ot.push({key:"BitsPerComponent",value:I.bitsPerComponent}),"decodeParameters"in I&&I.decodeParameters!==void 0&&ot.push({key:"DecodeParms",value:"<<"+I.decodeParameters+">>"}),"transparency"in I&&Array.isArray(I.transparency)){for(var mt="",tt=0,pt=I.transparency.length;tt<pt;tt++)mt+=I.transparency[tt]+" "+I.transparency[tt]+" ";ot.push({key:"Mask",value:"["+mt+"]"})}I.sMask!==void 0&&ot.push({key:"SMask",value:I.objectId+1+" 0 R"});var ut=I.filter!==void 0?["/"+I.filter]:void 0;if(R({data:I.data,additionalKeyValues:ot,alreadyAppliedFilters:ut,objectId:I.objectId}),z("endobj"),"sMask"in I&&I.sMask!==void 0){var Ot="/Predictor "+I.predictor+" /Colors 1 /BitsPerComponent "+I.bitsPerComponent+" /Columns "+I.width,N={width:I.width,height:I.height,colorSpace:"DeviceGray",bitsPerComponent:I.bitsPerComponent,decodeParameters:Ot,data:I.sMask};"filter"in I&&(N.filter=I.filter),C.call(this,N)}if(I.colorSpace===O.INDEXED){var T=this.internal.newObject();R({data:G(new Uint8Array(I.palette)),objectId:T}),z("endobj")}},s=function(){var C=this.internal.collections.addImage_images;for(var I in C)o.call(this,C[I])},c=function(){var C,I=this.internal.collections.addImage_images,z=this.internal.write;for(var R in I)z("/I"+(C=I[R]).index,C.objectId,"0","R")},h=function(){this.internal.collections.addImage_images||(this.internal.collections.addImage_images={},this.internal.events.subscribe("putResources",s),this.internal.events.subscribe("putXobjectDict",c))},f=function(){var C=this.internal.collections.addImage_images;return h.call(this),C},p=function(){return Object.keys(this.internal.collections.addImage_images).length},w=function(C){return typeof r["process"+C.toUpperCase()]=="function"},L=function(C){return de(C)==="object"&&C.nodeType===1},x=function(C,I){if(C.nodeName==="IMG"&&C.hasAttribute("src")){var z=""+C.getAttribute("src");if(z.indexOf("data:image/")===0)return ga(unescape(z).split("base64,").pop());var R=r.loadFile(z,!0);if(R!==void 0)return R}if(C.nodeName==="CANVAS"){if(C.width===0||C.height===0)throw new Error("Given canvas must have data. Canvas width: "+C.width+", height: "+C.height);var lt;switch(I){case"PNG":lt="image/png";break;case"WEBP":lt="image/webp";break;case"JPEG":case"JPG":default:lt="image/jpeg"}return ga(C.toDataURL(lt,1).split("base64,").pop())}},m=function(C){var I=this.internal.collections.addImage_images;if(I){for(var z in I)if(C===I[z].alias)return I[z]}},D=function(C,I,z){return C||I||(C=-96,I=-96),C<0&&(C=-1*z.width*72/C/this.internal.scaleFactor),I<0&&(I=-1*z.height*72/I/this.internal.scaleFactor),C===0&&(C=I*z.width/z.height),I===0&&(I=C*z.height/z.width),[C,I]},k=function(C,I,z,R,lt,ot){var mt=D.call(this,z,R,lt),tt=this.internal.getCoordinateString,pt=this.internal.getVerticalCoordinateString,ut=f.call(this);if(z=mt[0],R=mt[1],ut[lt.index]=lt,ot){ot*=Math.PI/180;var Ot=Math.cos(ot),N=Math.sin(ot),T=function(V){return V.toFixed(4)},E=[T(Ot),T(N),T(-1*N),T(Ot),0,0,"cm"]}this.internal.write("q"),ot?(this.internal.write([1,"0","0",1,tt(C),pt(I+R),"cm"].join(" ")),this.internal.write(E.join(" ")),this.internal.write([tt(z),"0","0",tt(R),"0","0","cm"].join(" "))):this.internal.write([tt(z),"0","0",tt(R),tt(C),pt(I+R),"cm"].join(" ")),this.isAdvancedAPI()&&this.internal.write([1,0,0,-1,0,0,"cm"].join(" ")),this.internal.write("/I"+lt.index+" Do"),this.internal.write("Q")},O=r.color_spaces={DEVICE_RGB:"DeviceRGB",DEVICE_GRAY:"DeviceGray",DEVICE_CMYK:"DeviceCMYK",CAL_GREY:"CalGray",CAL_RGB:"CalRGB",LAB:"Lab",ICC_BASED:"ICCBased",INDEXED:"Indexed",PATTERN:"Pattern",SEPARATION:"Separation",DEVICE_N:"DeviceN"};r.decode={DCT_DECODE:"DCTDecode",FLATE_DECODE:"FlateDecode",LZW_DECODE:"LZWDecode",JPX_DECODE:"JPXDecode",JBIG2_DECODE:"JBIG2Decode",ASCII85_DECODE:"ASCII85Decode",ASCII_HEX_DECODE:"ASCIIHexDecode",RUN_LENGTH_DECODE:"RunLengthDecode",CCITT_FAX_DECODE:"CCITTFaxDecode"};var _=r.image_compression={NONE:"NONE",FAST:"FAST",MEDIUM:"MEDIUM",SLOW:"SLOW"},j=r.__addimage__.sHashCode=function(C){var I,z,R=0;if(typeof C=="string")for(z=C.length,I=0;I<z;I++)R=(R<<5)-R+C.charCodeAt(I),R|=0;else if(At(C))for(z=C.byteLength/2,I=0;I<z;I++)R=(R<<5)-R+C[I],R|=0;return R},J=r.__addimage__.validateStringAsBase64=function(C){(C=C||"").toString().trim();var I=!0;return C.length===0&&(I=!1),C.length%4!=0&&(I=!1),/^[A-Za-z0-9+/]+$/.test(C.substr(0,C.length-2))===!1&&(I=!1),/^[A-Za-z0-9/][A-Za-z0-9+/]|[A-Za-z0-9+/]=|==$/.test(C.substr(-2))===!1&&(I=!1),I},st=r.__addimage__.extractImageFromDataUrl=function(C){var I=(C=C||"").split("base64,"),z=null;if(I.length===2){var R=/^data:(\w*\/\w*);*(charset=(?!charset=)[\w=-]*)*;*$/.exec(I[0]);Array.isArray(R)&&(z={mimeType:R[1],charset:R[2],data:I[1]})}return z},ft=r.__addimage__.supportsArrayBuffer=function(){return typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"};r.__addimage__.isArrayBuffer=function(C){return ft()&&C instanceof ArrayBuffer};var At=r.__addimage__.isArrayBufferView=function(C){return ft()&&typeof Uint32Array<"u"&&(C instanceof Int8Array||C instanceof Uint8Array||typeof Uint8ClampedArray<"u"&&C instanceof Uint8ClampedArray||C instanceof Int16Array||C instanceof Uint16Array||C instanceof Int32Array||C instanceof Uint32Array||C instanceof Float32Array||C instanceof Float64Array)},rt=r.__addimage__.binaryStringToUint8Array=function(C){for(var I=C.length,z=new Uint8Array(I),R=0;R<I;R++)z[R]=C.charCodeAt(R);return z},G=r.__addimage__.arrayBufferToBinaryString=function(C){for(var I="",z=At(C)?C:new Uint8Array(C),R=0;R<z.length;R+=8192)I+=String.fromCharCode.apply(null,z.subarray(R,R+8192));return I};r.addImage=function(){var C,I,z,R,lt,ot,mt,tt,pt;if(typeof arguments[1]=="number"?(I=e,z=arguments[1],R=arguments[2],lt=arguments[3],ot=arguments[4],mt=arguments[5],tt=arguments[6],pt=arguments[7]):(I=arguments[1],z=arguments[2],R=arguments[3],lt=arguments[4],ot=arguments[5],mt=arguments[6],tt=arguments[7],pt=arguments[8]),de(C=arguments[0])==="object"&&!L(C)&&"imageData"in C){var ut=C;C=ut.imageData,I=ut.format||I||e,z=ut.x||z||0,R=ut.y||R||0,lt=ut.w||ut.width||lt,ot=ut.h||ut.height||ot,mt=ut.alias||mt,tt=ut.compression||tt,pt=ut.rotation||ut.angle||pt}var Ot=this.internal.getFilters();if(tt===void 0&&Ot.indexOf("FlateEncode")!==-1&&(tt="SLOW"),isNaN(z)||isNaN(R))throw new Error("Invalid coordinates passed to jsPDF.addImage");h.call(this);var N=vt.call(this,C,I,mt,tt);return k.call(this,z,R,lt,ot,N,pt),this};var vt=function(C,I,z,R){var lt,ot,mt;if(typeof C=="string"&&a(C)===e){C=unescape(C);var tt=bt(C,!1);(tt!==""||(tt=r.loadFile(C,!0))!==void 0)&&(C=tt)}if(L(C)&&(C=x(C,I)),I=a(C,I),!w(I))throw new Error("addImage does not support files of type '"+I+"', please ensure that a plugin for '"+I+"' support is added.");if(((mt=z)==null||mt.length===0)&&(z=function(pt){return typeof pt=="string"||At(pt)?j(pt):At(pt.data)?j(pt.data):null}(C)),(lt=m.call(this,z))||(ft()&&(C instanceof Uint8Array||I==="RGBA"||(ot=C,C=rt(C))),lt=this["process"+I.toUpperCase()](C,p.call(this),z,function(pt){return pt&&typeof pt=="string"&&(pt=pt.toUpperCase()),pt in r.image_compression?pt:_.NONE}(R),ot)),!lt)throw new Error("An unknown error occurred whilst processing the image.");return lt},bt=r.__addimage__.convertBase64ToBinaryString=function(C,I){var z;I=typeof I!="boolean"||I;var R,lt="";if(typeof C=="string"){R=(z=st(C))!==null?z.data:C;try{lt=ga(R)}catch(ot){if(I)throw J(R)?new Error("atob-Error in jsPDF.convertBase64ToBinaryString "+ot.message):new Error("Supplied Data is not a valid base64-String jsPDF.convertBase64ToBinaryString ")}}return lt};r.getImageProperties=function(C){var I,z,R="";if(L(C)&&(C=x(C)),typeof C=="string"&&a(C)===e&&((R=bt(C,!1))===""&&(R=r.loadFile(C)||""),C=R),z=a(C),!w(z))throw new Error("addImage does not support files of type '"+z+"', please ensure that a plugin for '"+z+"' support is added.");if(!ft()||C instanceof Uint8Array||(C=rt(C)),!(I=this["process"+z.toUpperCase()](C)))throw new Error("An unknown error occurred whilst processing the image");return I.fileType=z,I}})(Vt.API),function(r){var e=function(n){if(n!==void 0&&n!="")return!0};Vt.API.events.push(["addPage",function(n){this.internal.getPageInfo(n.pageNumber).pageContext.annotations=[]}]),r.events.push(["putPage",function(n){for(var a,o,s,c=this.internal.getCoordinateString,h=this.internal.getVerticalCoordinateString,f=this.internal.getPageInfoByObjId(n.objId),p=n.pageContext.annotations,w=!1,L=0;L<p.length&&!w;L++)switch((a=p[L]).type){case"link":(e(a.options.url)||e(a.options.pageNumber))&&(w=!0);break;case"reference":case"text":case"freetext":w=!0}if(w!=0){this.internal.write("/Annots [");for(var x=0;x<p.length;x++){a=p[x];var m=this.internal.pdfEscape,D=this.internal.getEncryptor(n.objId);switch(a.type){case"reference":this.internal.write(" "+a.object.objId+" 0 R ");break;case"text":var k=this.internal.newAdditionalObject(),O=this.internal.newAdditionalObject(),_=this.internal.getEncryptor(k.objId),j=a.title||"Note";s="<</Type /Annot /Subtype /Text "+(o="/Rect ["+c(a.bounds.x)+" "+h(a.bounds.y+a.bounds.h)+" "+c(a.bounds.x+a.bounds.w)+" "+h(a.bounds.y)+"] ")+"/Contents ("+m(_(a.contents))+")",s+=" /Popup "+O.objId+" 0 R",s+=" /P "+f.objId+" 0 R",s+=" /T ("+m(_(j))+") >>",k.content=s;var J=k.objId+" 0 R";s="<</Type /Annot /Subtype /Popup "+(o="/Rect ["+c(a.bounds.x+30)+" "+h(a.bounds.y+a.bounds.h)+" "+c(a.bounds.x+a.bounds.w+30)+" "+h(a.bounds.y)+"] ")+" /Parent "+J,a.open&&(s+=" /Open true"),s+=" >>",O.content=s,this.internal.write(k.objId,"0 R",O.objId,"0 R");break;case"freetext":o="/Rect ["+c(a.bounds.x)+" "+h(a.bounds.y)+" "+c(a.bounds.x+a.bounds.w)+" "+h(a.bounds.y+a.bounds.h)+"] ";var st=a.color||"#000000";s="<</Type /Annot /Subtype /FreeText "+o+"/Contents ("+m(D(a.contents))+")",s+=" /DS(font: Helvetica,sans-serif 12.0pt; text-align:left; color:#"+st+")",s+=" /Border [0 0 0]",s+=" >>",this.internal.write(s);break;case"link":if(a.options.name){var ft=this.annotations._nameMap[a.options.name];a.options.pageNumber=ft.page,a.options.top=ft.y}else a.options.top||(a.options.top=0);if(o="/Rect ["+a.finalBounds.x+" "+a.finalBounds.y+" "+a.finalBounds.w+" "+a.finalBounds.h+"] ",s="",a.options.url)s="<</Type /Annot /Subtype /Link "+o+"/Border [0 0 0] /A <</S /URI /URI ("+m(D(a.options.url))+") >>";else if(a.options.pageNumber)switch(s="<</Type /Annot /Subtype /Link "+o+"/Border [0 0 0] /Dest ["+this.internal.getPageInfo(a.options.pageNumber).objId+" 0 R",a.options.magFactor=a.options.magFactor||"XYZ",a.options.magFactor){case"Fit":s+=" /Fit]";break;case"FitH":s+=" /FitH "+a.options.top+"]";break;case"FitV":a.options.left=a.options.left||0,s+=" /FitV "+a.options.left+"]";break;case"XYZ":default:var At=h(a.options.top);a.options.left=a.options.left||0,a.options.zoom===void 0&&(a.options.zoom=0),s+=" /XYZ "+a.options.left+" "+At+" "+a.options.zoom+"]"}s!=""&&(s+=" >>",this.internal.write(s))}}this.internal.write("]")}}]),r.createAnnotation=function(n){var a=this.internal.getCurrentPageInfo();switch(n.type){case"link":this.link(n.bounds.x,n.bounds.y,n.bounds.w,n.bounds.h,n);break;case"text":case"freetext":a.pageContext.annotations.push(n)}},r.link=function(n,a,o,s,c){var h=this.internal.getCurrentPageInfo(),f=this.internal.getCoordinateString,p=this.internal.getVerticalCoordinateString;h.pageContext.annotations.push({finalBounds:{x:f(n),y:p(a),w:f(n+o),h:p(a+s)},options:c,type:"link"})},r.textWithLink=function(n,a,o,s){var c,h,f=this.getTextWidth(n),p=this.internal.getLineHeight()/this.internal.scaleFactor;if(s.maxWidth!==void 0){h=s.maxWidth;var w=this.splitTextToSize(n,h).length;c=Math.ceil(p*w)}else h=f,c=p;return this.text(n,a,o,s),o+=.2*p,s.align==="center"&&(a-=f/2),s.align==="right"&&(a-=f),this.link(a,o-p,h,c,s),f},r.getTextWidth=function(n){var a=this.internal.getFontSize();return this.getStringUnitWidth(n)*a/this.internal.scaleFactor}}(Vt.API),function(r){var e={1569:[65152],1570:[65153,65154],1571:[65155,65156],1572:[65157,65158],1573:[65159,65160],1574:[65161,65162,65163,65164],1575:[65165,65166],1576:[65167,65168,65169,65170],1577:[65171,65172],1578:[65173,65174,65175,65176],1579:[65177,65178,65179,65180],1580:[65181,65182,65183,65184],1581:[65185,65186,65187,65188],1582:[65189,65190,65191,65192],1583:[65193,65194],1584:[65195,65196],1585:[65197,65198],1586:[65199,65200],1587:[65201,65202,65203,65204],1588:[65205,65206,65207,65208],1589:[65209,65210,65211,65212],1590:[65213,65214,65215,65216],1591:[65217,65218,65219,65220],1592:[65221,65222,65223,65224],1593:[65225,65226,65227,65228],1594:[65229,65230,65231,65232],1601:[65233,65234,65235,65236],1602:[65237,65238,65239,65240],1603:[65241,65242,65243,65244],1604:[65245,65246,65247,65248],1605:[65249,65250,65251,65252],1606:[65253,65254,65255,65256],1607:[65257,65258,65259,65260],1608:[65261,65262],1609:[65263,65264,64488,64489],1610:[65265,65266,65267,65268],1649:[64336,64337],1655:[64477],1657:[64358,64359,64360,64361],1658:[64350,64351,64352,64353],1659:[64338,64339,64340,64341],1662:[64342,64343,64344,64345],1663:[64354,64355,64356,64357],1664:[64346,64347,64348,64349],1667:[64374,64375,64376,64377],1668:[64370,64371,64372,64373],1670:[64378,64379,64380,64381],1671:[64382,64383,64384,64385],1672:[64392,64393],1676:[64388,64389],1677:[64386,64387],1678:[64390,64391],1681:[64396,64397],1688:[64394,64395],1700:[64362,64363,64364,64365],1702:[64366,64367,64368,64369],1705:[64398,64399,64400,64401],1709:[64467,64468,64469,64470],1711:[64402,64403,64404,64405],1713:[64410,64411,64412,64413],1715:[64406,64407,64408,64409],1722:[64414,64415],1723:[64416,64417,64418,64419],1726:[64426,64427,64428,64429],1728:[64420,64421],1729:[64422,64423,64424,64425],1733:[64480,64481],1734:[64473,64474],1735:[64471,64472],1736:[64475,64476],1737:[64482,64483],1739:[64478,64479],1740:[64508,64509,64510,64511],1744:[64484,64485,64486,64487],1746:[64430,64431],1747:[64432,64433]},n={65247:{65154:65269,65156:65271,65160:65273,65166:65275},65248:{65154:65270,65156:65272,65160:65274,65166:65276},65165:{65247:{65248:{65258:65010}}},1617:{1612:64606,1613:64607,1614:64608,1615:64609,1616:64610}},a={1612:64606,1613:64607,1614:64608,1615:64609,1616:64610},o=[1570,1571,1573,1575];r.__arabicParser__={};var s=r.__arabicParser__.isInArabicSubstitutionA=function(k){return e[k.charCodeAt(0)]!==void 0},c=r.__arabicParser__.isArabicLetter=function(k){return typeof k=="string"&&/^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+$/.test(k)},h=r.__arabicParser__.isArabicEndLetter=function(k){return c(k)&&s(k)&&e[k.charCodeAt(0)].length<=2},f=r.__arabicParser__.isArabicAlfLetter=function(k){return c(k)&&o.indexOf(k.charCodeAt(0))>=0};r.__arabicParser__.arabicLetterHasIsolatedForm=function(k){return c(k)&&s(k)&&e[k.charCodeAt(0)].length>=1};var p=r.__arabicParser__.arabicLetterHasFinalForm=function(k){return c(k)&&s(k)&&e[k.charCodeAt(0)].length>=2};r.__arabicParser__.arabicLetterHasInitialForm=function(k){return c(k)&&s(k)&&e[k.charCodeAt(0)].length>=3};var w=r.__arabicParser__.arabicLetterHasMedialForm=function(k){return c(k)&&s(k)&&e[k.charCodeAt(0)].length==4},L=r.__arabicParser__.resolveLigatures=function(k){var O=0,_=n,j="",J=0;for(O=0;O<k.length;O+=1)_[k.charCodeAt(O)]!==void 0?(J++,typeof(_=_[k.charCodeAt(O)])=="number"&&(j+=String.fromCharCode(_),_=n,J=0),O===k.length-1&&(_=n,j+=k.charAt(O-(J-1)),O-=J-1,J=0)):(_=n,j+=k.charAt(O-J),O-=J,J=0);return j};r.__arabicParser__.isArabicDiacritic=function(k){return k!==void 0&&a[k.charCodeAt(0)]!==void 0};var x=r.__arabicParser__.getCorrectForm=function(k,O,_){return c(k)?s(k)===!1?-1:!p(k)||!c(O)&&!c(_)||!c(_)&&h(O)||h(k)&&!c(O)||h(k)&&f(O)||h(k)&&h(O)?0:w(k)&&c(O)&&!h(O)&&c(_)&&p(_)?3:h(k)||!c(_)?1:2:-1},m=function(k){var O=0,_=0,j=0,J="",st="",ft="",At=(k=k||"").split("\\s+"),rt=[];for(O=0;O<At.length;O+=1){for(rt.push(""),_=0;_<At[O].length;_+=1)J=At[O][_],st=At[O][_-1],ft=At[O][_+1],c(J)?(j=x(J,st,ft),rt[O]+=j!==-1?String.fromCharCode(e[J.charCodeAt(0)][j]):J):rt[O]+=J;rt[O]=L(rt[O])}return rt.join(" ")},D=r.__arabicParser__.processArabic=r.processArabic=function(){var k,O=typeof arguments[0]=="string"?arguments[0]:arguments[0].text,_=[];if(Array.isArray(O)){var j=0;for(_=[],j=0;j<O.length;j+=1)Array.isArray(O[j])?_.push([m(O[j][0]),O[j][1],O[j][2]]):_.push([m(O[j])]);k=_}else k=m(O);return typeof arguments[0]=="string"?k:(arguments[0].text=k,arguments[0])};r.events.push(["preProcessText",D])}(Vt.API),Vt.API.autoPrint=function(r){var e;switch((r=r||{}).variant=r.variant||"non-conform",r.variant){case"javascript":this.addJS("print({});");break;case"non-conform":default:this.internal.events.subscribe("postPutResources",function(){e=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /Named"),this.internal.out("/Type /Action"),this.internal.out("/N /Print"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){this.internal.out("/OpenAction "+e+" 0 R")})}return this},function(r){var e=function(){var n=void 0;Object.defineProperty(this,"pdf",{get:function(){return n},set:function(h){n=h}});var a=150;Object.defineProperty(this,"width",{get:function(){return a},set:function(h){a=isNaN(h)||Number.isInteger(h)===!1||h<0?150:h,this.getContext("2d").pageWrapXEnabled&&(this.getContext("2d").pageWrapX=a+1)}});var o=300;Object.defineProperty(this,"height",{get:function(){return o},set:function(h){o=isNaN(h)||Number.isInteger(h)===!1||h<0?300:h,this.getContext("2d").pageWrapYEnabled&&(this.getContext("2d").pageWrapY=o+1)}});var s=[];Object.defineProperty(this,"childNodes",{get:function(){return s},set:function(h){s=h}});var c={};Object.defineProperty(this,"style",{get:function(){return c},set:function(h){c=h}}),Object.defineProperty(this,"parentNode",{})};e.prototype.getContext=function(n,a){var o;if((n=n||"2d")!=="2d")return null;for(o in a)this.pdf.context2d.hasOwnProperty(o)&&(this.pdf.context2d[o]=a[o]);return this.pdf.context2d._canvas=this,this.pdf.context2d},e.prototype.toDataURL=function(){throw new Error("toDataURL is not implemented.")},r.events.push(["initialized",function(){this.canvas=new e,this.canvas.pdf=this}])}(Vt.API),function(r){var e={left:0,top:0,bottom:0,right:0},n=!1,a=function(){this.internal.__cell__===void 0&&(this.internal.__cell__={},this.internal.__cell__.padding=3,this.internal.__cell__.headerFunction=void 0,this.internal.__cell__.margins=Object.assign({},e),this.internal.__cell__.margins.width=this.getPageWidth(),o.call(this))},o=function(){this.internal.__cell__.lastCell=new s,this.internal.__cell__.pages=1},s=function(){var f=arguments[0];Object.defineProperty(this,"x",{enumerable:!0,get:function(){return f},set:function(k){f=k}});var p=arguments[1];Object.defineProperty(this,"y",{enumerable:!0,get:function(){return p},set:function(k){p=k}});var w=arguments[2];Object.defineProperty(this,"width",{enumerable:!0,get:function(){return w},set:function(k){w=k}});var L=arguments[3];Object.defineProperty(this,"height",{enumerable:!0,get:function(){return L},set:function(k){L=k}});var x=arguments[4];Object.defineProperty(this,"text",{enumerable:!0,get:function(){return x},set:function(k){x=k}});var m=arguments[5];Object.defineProperty(this,"lineNumber",{enumerable:!0,get:function(){return m},set:function(k){m=k}});var D=arguments[6];return Object.defineProperty(this,"align",{enumerable:!0,get:function(){return D},set:function(k){D=k}}),this};s.prototype.clone=function(){return new s(this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align)},s.prototype.toArray=function(){return[this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align]},r.setHeaderFunction=function(f){return a.call(this),this.internal.__cell__.headerFunction=typeof f=="function"?f:void 0,this},r.getTextDimensions=function(f,p){a.call(this);var w=(p=p||{}).fontSize||this.getFontSize(),L=p.font||this.getFont(),x=p.scaleFactor||this.internal.scaleFactor,m=0,D=0,k=0,O=this;if(!Array.isArray(f)&&typeof f!="string"){if(typeof f!="number")throw new Error("getTextDimensions expects text-parameter to be of type String or type Number or an Array of Strings.");f=String(f)}var _=p.maxWidth;_>0?typeof f=="string"?f=this.splitTextToSize(f,_):Object.prototype.toString.call(f)==="[object Array]"&&(f=f.reduce(function(J,st){return J.concat(O.splitTextToSize(st,_))},[])):f=Array.isArray(f)?f:[f];for(var j=0;j<f.length;j++)m<(k=this.getStringUnitWidth(f[j],{font:L})*w)&&(m=k);return m!==0&&(D=f.length),{w:m/=x,h:Math.max((D*w*this.getLineHeightFactor()-w*(this.getLineHeightFactor()-1))/x,0)}},r.cellAddPage=function(){a.call(this),this.addPage();var f=this.internal.__cell__.margins||e;return this.internal.__cell__.lastCell=new s(f.left,f.top,void 0,void 0),this.internal.__cell__.pages+=1,this};var c=r.cell=function(){var f;f=arguments[0]instanceof s?arguments[0]:new s(arguments[0],arguments[1],arguments[2],arguments[3],arguments[4],arguments[5]),a.call(this);var p=this.internal.__cell__.lastCell,w=this.internal.__cell__.padding,L=this.internal.__cell__.margins||e,x=this.internal.__cell__.tableHeaderRow,m=this.internal.__cell__.printHeaders;return p.lineNumber!==void 0&&(p.lineNumber===f.lineNumber?(f.x=(p.x||0)+(p.width||0),f.y=p.y||0):p.y+p.height+f.height+L.bottom>this.getPageHeight()?(this.cellAddPage(),f.y=L.top,m&&x&&(this.printHeaderRow(f.lineNumber,!0),f.y+=x[0].height)):f.y=p.y+p.height||f.y),f.text[0]!==void 0&&(this.rect(f.x,f.y,f.width,f.height,n===!0?"FD":void 0),f.align==="right"?this.text(f.text,f.x+f.width-w,f.y+w,{align:"right",baseline:"top"}):f.align==="center"?this.text(f.text,f.x+f.width/2,f.y+w,{align:"center",baseline:"top",maxWidth:f.width-w-w}):this.text(f.text,f.x+w,f.y+w,{align:"left",baseline:"top",maxWidth:f.width-w-w})),this.internal.__cell__.lastCell=f,this};r.table=function(f,p,w,L,x){if(a.call(this),!w)throw new Error("No data for PDF table.");var m,D,k,O,_=[],j=[],J=[],st={},ft={},At=[],rt=[],G=(x=x||{}).autoSize||!1,vt=x.printHeaders!==!1,bt=x.css&&x.css["font-size"]!==void 0?16*x.css["font-size"]:x.fontSize||12,C=x.margins||Object.assign({width:this.getPageWidth()},e),I=typeof x.padding=="number"?x.padding:3,z=x.headerBackgroundColor||"#c8c8c8",R=x.headerTextColor||"#000";if(o.call(this),this.internal.__cell__.printHeaders=vt,this.internal.__cell__.margins=C,this.internal.__cell__.table_font_size=bt,this.internal.__cell__.padding=I,this.internal.__cell__.headerBackgroundColor=z,this.internal.__cell__.headerTextColor=R,this.setFontSize(bt),L==null)j=_=Object.keys(w[0]),J=_.map(function(){return"left"});else if(Array.isArray(L)&&de(L[0])==="object")for(_=L.map(function(ut){return ut.name}),j=L.map(function(ut){return ut.prompt||ut.name||""}),J=L.map(function(ut){return ut.align||"left"}),m=0;m<L.length;m+=1)ft[L[m].name]=L[m].width*(19.049976/25.4);else Array.isArray(L)&&typeof L[0]=="string"&&(j=_=L,J=_.map(function(){return"left"}));if(G||Array.isArray(L)&&typeof L[0]=="string")for(m=0;m<_.length;m+=1){for(st[O=_[m]]=w.map(function(ut){return ut[O]}),this.setFont(void 0,"bold"),At.push(this.getTextDimensions(j[m],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w),D=st[O],this.setFont(void 0,"normal"),k=0;k<D.length;k+=1)At.push(this.getTextDimensions(D[k],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w);ft[O]=Math.max.apply(null,At)+I+I,At=[]}if(vt){var lt={};for(m=0;m<_.length;m+=1)lt[_[m]]={},lt[_[m]].text=j[m],lt[_[m]].align=J[m];var ot=h.call(this,lt,ft);rt=_.map(function(ut){return new s(f,p,ft[ut],ot,lt[ut].text,void 0,lt[ut].align)}),this.setTableHeaderRow(rt),this.printHeaderRow(1,!1)}var mt=L.reduce(function(ut,Ot){return ut[Ot.name]=Ot.align,ut},{});for(m=0;m<w.length;m+=1){"rowStart"in x&&x.rowStart instanceof Function&&x.rowStart({row:m,data:w[m]},this);var tt=h.call(this,w[m],ft);for(k=0;k<_.length;k+=1){var pt=w[m][_[k]];"cellStart"in x&&x.cellStart instanceof Function&&x.cellStart({row:m,col:k,data:pt},this),c.call(this,new s(f,p,ft[_[k]],tt,pt,m+2,mt[_[k]]))}}return this.internal.__cell__.table_x=f,this.internal.__cell__.table_y=p,this};var h=function(f,p){var w=this.internal.__cell__.padding,L=this.internal.__cell__.table_font_size,x=this.internal.scaleFactor;return Object.keys(f).map(function(m){var D=f[m];return this.splitTextToSize(D.hasOwnProperty("text")?D.text:D,p[m]-w-w)},this).map(function(m){return this.getLineHeightFactor()*m.length*L/x+w+w},this).reduce(function(m,D){return Math.max(m,D)},0)};r.setTableHeaderRow=function(f){a.call(this),this.internal.__cell__.tableHeaderRow=f},r.printHeaderRow=function(f,p){if(a.call(this),!this.internal.__cell__.tableHeaderRow)throw new Error("Property tableHeaderRow does not exist.");var w;if(n=!0,typeof this.internal.__cell__.headerFunction=="function"){var L=this.internal.__cell__.headerFunction(this,this.internal.__cell__.pages);this.internal.__cell__.lastCell=new s(L[0],L[1],L[2],L[3],void 0,-1)}this.setFont(void 0,"bold");for(var x=[],m=0;m<this.internal.__cell__.tableHeaderRow.length;m+=1){w=this.internal.__cell__.tableHeaderRow[m].clone(),p&&(w.y=this.internal.__cell__.margins.top||0,x.push(w)),w.lineNumber=f;var D=this.getTextColor();this.setTextColor(this.internal.__cell__.headerTextColor),this.setFillColor(this.internal.__cell__.headerBackgroundColor),c.call(this,w),this.setTextColor(D)}x.length>0&&this.setTableHeaderRow(x),this.setFont(void 0,"normal"),n=!1}}(Vt.API);var Kl={italic:["italic","oblique","normal"],oblique:["oblique","italic","normal"],normal:["normal","oblique","italic"]},Xl=["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded"],Fs=Jl(Xl),Ql=[100,200,300,400,500,600,700,800,900],uh=Jl(Ql);function Ts(r){var e=r.family.replace(/"|'/g,"").toLowerCase(),n=function(s){return Kl[s=s||"normal"]?s:"normal"}(r.style),a=function(s){if(!s)return 400;if(typeof s=="number")return s>=100&&s<=900&&s%100==0?s:400;if(/^\d00$/.test(s))return parseInt(s);switch(s){case"bold":return 700;case"normal":default:return 400}}(r.weight),o=function(s){return typeof Fs[s=s||"normal"]=="number"?s:"normal"}(r.stretch);return{family:e,style:n,weight:a,stretch:o,src:r.src||[],ref:r.ref||{name:e,style:[o,n,a].join(" ")}}}function Ll(r,e,n,a){var o;for(o=n;o>=0&&o<e.length;o+=a)if(r[e[o]])return r[e[o]];for(o=n;o>=0&&o<e.length;o-=a)if(r[e[o]])return r[e[o]]}var hh={"sans-serif":"helvetica",fixed:"courier",monospace:"courier",terminal:"courier",cursive:"times",fantasy:"times",serif:"times"},xl={caption:"times",icon:"times",menu:"times","message-box":"times","small-caption":"times","status-bar":"times"};function Sl(r){return[r.stretch,r.style,r.weight,r.family].join(" ")}function fh(r,e,n){for(var a=(n=n||{}).defaultFontFamily||"times",o=Object.assign({},hh,n.genericFontFamilies||{}),s=null,c=null,h=0;h<e.length;++h)if(o[(s=Ts(e[h])).family]&&(s.family=o[s.family]),r.hasOwnProperty(s.family)){c=r[s.family];break}if(!(c=c||r[a]))throw new Error("Could not find a font-family for the rule '"+Sl(s)+"' and default family '"+a+"'.");if(c=function(f,p){if(p[f])return p[f];var w=Fs[f],L=w<=Fs.normal?-1:1,x=Ll(p,Xl,w,L);if(!x)throw new Error("Could not find a matching font-stretch value for "+f);return x}(s.stretch,c),c=function(f,p){if(p[f])return p[f];for(var w=Kl[f],L=0;L<w.length;++L)if(p[w[L]])return p[w[L]];throw new Error("Could not find a matching font-style for "+f)}(s.style,c),!(c=function(f,p){if(p[f])return p[f];if(f===400&&p[500])return p[500];if(f===500&&p[400])return p[400];var w=uh[f],L=Ll(p,Ql,w,f<400?-1:1);if(!L)throw new Error("Could not find a matching font-weight for value "+f);return L}(s.weight,c)))throw new Error("Failed to resolve a font for the rule '"+Sl(s)+"'.");return c}function _l(r){return r.trimLeft()}function dh(r,e){for(var n=0;n<r.length;){if(r.charAt(n)===e)return[r.substring(0,n),r.substring(n+1)];n+=1}return null}function ph(r){var e=r.match(/^(-[a-z_]|[a-z_])[a-z0-9_-]*/i);return e===null?null:[e[0],r.substring(e[0].length)]}var ho,Pl,Cl,As=["times"];(function(r){var e,n,a,o,s,c,h,f,p,w=function(N){return N=N||{},this.isStrokeTransparent=N.isStrokeTransparent||!1,this.strokeOpacity=N.strokeOpacity||1,this.strokeStyle=N.strokeStyle||"#000000",this.fillStyle=N.fillStyle||"#000000",this.isFillTransparent=N.isFillTransparent||!1,this.fillOpacity=N.fillOpacity||1,this.font=N.font||"10px sans-serif",this.textBaseline=N.textBaseline||"alphabetic",this.textAlign=N.textAlign||"left",this.lineWidth=N.lineWidth||1,this.lineJoin=N.lineJoin||"miter",this.lineCap=N.lineCap||"butt",this.path=N.path||[],this.transform=N.transform!==void 0?N.transform.clone():new f,this.globalCompositeOperation=N.globalCompositeOperation||"normal",this.globalAlpha=N.globalAlpha||1,this.clip_path=N.clip_path||[],this.currentPoint=N.currentPoint||new c,this.miterLimit=N.miterLimit||10,this.lastPoint=N.lastPoint||new c,this.lineDashOffset=N.lineDashOffset||0,this.lineDash=N.lineDash||[],this.margin=N.margin||[0,0,0,0],this.prevPageLastElemOffset=N.prevPageLastElemOffset||0,this.ignoreClearRect=typeof N.ignoreClearRect!="boolean"||N.ignoreClearRect,this};r.events.push(["initialized",function(){this.context2d=new L(this),e=this.internal.f2,n=this.internal.getCoordinateString,a=this.internal.getVerticalCoordinateString,o=this.internal.getHorizontalCoordinate,s=this.internal.getVerticalCoordinate,c=this.internal.Point,h=this.internal.Rectangle,f=this.internal.Matrix,p=new w}]);var L=function(N){Object.defineProperty(this,"canvas",{get:function(){return{parentNode:!1,style:!1}}});var T=N;Object.defineProperty(this,"pdf",{get:function(){return T}});var E=!1;Object.defineProperty(this,"pageWrapXEnabled",{get:function(){return E},set:function(it){E=!!it}});var V=!1;Object.defineProperty(this,"pageWrapYEnabled",{get:function(){return V},set:function(it){V=!!it}});var Y=0;Object.defineProperty(this,"posX",{get:function(){return Y},set:function(it){isNaN(it)||(Y=it)}});var Q=0;Object.defineProperty(this,"posY",{get:function(){return Q},set:function(it){isNaN(it)||(Q=it)}}),Object.defineProperty(this,"margin",{get:function(){return p.margin},set:function(it){var M;typeof it=="number"?M=[it,it,it,it]:((M=new Array(4))[0]=it[0],M[1]=it.length>=2?it[1]:M[0],M[2]=it.length>=3?it[2]:M[0],M[3]=it.length>=4?it[3]:M[1]),p.margin=M}});var et=!1;Object.defineProperty(this,"autoPaging",{get:function(){return et},set:function(it){et=it}});var Z=0;Object.defineProperty(this,"lastBreak",{get:function(){return Z},set:function(it){Z=it}});var Lt=[];Object.defineProperty(this,"pageBreaks",{get:function(){return Lt},set:function(it){Lt=it}}),Object.defineProperty(this,"ctx",{get:function(){return p},set:function(it){it instanceof w&&(p=it)}}),Object.defineProperty(this,"path",{get:function(){return p.path},set:function(it){p.path=it}});var Nt=[];Object.defineProperty(this,"ctxStack",{get:function(){return Nt},set:function(it){Nt=it}}),Object.defineProperty(this,"fillStyle",{get:function(){return this.ctx.fillStyle},set:function(it){var M;M=x(it),this.ctx.fillStyle=M.style,this.ctx.isFillTransparent=M.a===0,this.ctx.fillOpacity=M.a,this.pdf.setFillColor(M.r,M.g,M.b,{a:M.a}),this.pdf.setTextColor(M.r,M.g,M.b,{a:M.a})}}),Object.defineProperty(this,"strokeStyle",{get:function(){return this.ctx.strokeStyle},set:function(it){var M=x(it);this.ctx.strokeStyle=M.style,this.ctx.isStrokeTransparent=M.a===0,this.ctx.strokeOpacity=M.a,M.a===0?this.pdf.setDrawColor(255,255,255):(M.a,this.pdf.setDrawColor(M.r,M.g,M.b))}}),Object.defineProperty(this,"lineCap",{get:function(){return this.ctx.lineCap},set:function(it){["butt","round","square"].indexOf(it)!==-1&&(this.ctx.lineCap=it,this.pdf.setLineCap(it))}}),Object.defineProperty(this,"lineWidth",{get:function(){return this.ctx.lineWidth},set:function(it){isNaN(it)||(this.ctx.lineWidth=it,this.pdf.setLineWidth(it))}}),Object.defineProperty(this,"lineJoin",{get:function(){return this.ctx.lineJoin},set:function(it){["bevel","round","miter"].indexOf(it)!==-1&&(this.ctx.lineJoin=it,this.pdf.setLineJoin(it))}}),Object.defineProperty(this,"miterLimit",{get:function(){return this.ctx.miterLimit},set:function(it){isNaN(it)||(this.ctx.miterLimit=it,this.pdf.setMiterLimit(it))}}),Object.defineProperty(this,"textBaseline",{get:function(){return this.ctx.textBaseline},set:function(it){this.ctx.textBaseline=it}}),Object.defineProperty(this,"textAlign",{get:function(){return this.ctx.textAlign},set:function(it){["right","end","center","left","start"].indexOf(it)!==-1&&(this.ctx.textAlign=it)}});var Tt=null;function kt(it,M){if(Tt===null){var Yt=function(Et){var wt=[];return Object.keys(Et).forEach(function(xt){Et[xt].forEach(function(Ct){var _t=null;switch(Ct){case"bold":_t={family:xt,weight:"bold"};break;case"italic":_t={family:xt,style:"italic"};break;case"bolditalic":_t={family:xt,weight:"bold",style:"italic"};break;case"":case"normal":_t={family:xt}}_t!==null&&(_t.ref={name:xt,style:Ct},wt.push(_t))})}),wt}(it.getFontList());Tt=function(Et){for(var wt={},xt=0;xt<Et.length;++xt){var Ct=Ts(Et[xt]),_t=Ct.family,Bt=Ct.stretch,Wt=Ct.style,Jt=Ct.weight;wt[_t]=wt[_t]||{},wt[_t][Bt]=wt[_t][Bt]||{},wt[_t][Bt][Wt]=wt[_t][Bt][Wt]||{},wt[_t][Bt][Wt][Jt]=Ct}return wt}(Yt.concat(M))}return Tt}var Ht=null;Object.defineProperty(this,"fontFaces",{get:function(){return Ht},set:function(it){Tt=null,Ht=it}}),Object.defineProperty(this,"font",{get:function(){return this.ctx.font},set:function(it){var M;if(this.ctx.font=it,(M=/^\s*(?=(?:(?:[-a-z]+\s*){0,2}(italic|oblique))?)(?=(?:(?:[-a-z]+\s*){0,2}(small-caps))?)(?=(?:(?:[-a-z]+\s*){0,2}(bold(?:er)?|lighter|[1-9]00))?)(?:(?:normal|\1|\2|\3)\s*){0,3}((?:xx?-)?(?:small|large)|medium|smaller|larger|[.\d]+(?:\%|in|[cem]m|ex|p[ctx]))(?:\s*\/\s*(normal|[.\d]+(?:\%|in|[cem]m|ex|p[ctx])))?\s*([-_,\"\'\sa-z]+?)\s*$/i.exec(it))!==null){var Yt=M[1],Et=(M[2],M[3]),wt=M[4],xt=(M[5],M[6]),Ct=/^([.\d]+)((?:%|in|[cem]m|ex|p[ctx]))$/i.exec(wt)[2];wt=Math.floor(Ct==="px"?parseFloat(wt)*this.pdf.internal.scaleFactor:Ct==="em"?parseFloat(wt)*this.pdf.getFontSize():parseFloat(wt)*this.pdf.internal.scaleFactor),this.pdf.setFontSize(wt);var _t=function(Gt){var ne,Ft,Ke=[],se=Gt.trim();if(se==="")return As;if(se in xl)return[xl[se]];for(;se!=="";){switch(Ft=null,ne=(se=_l(se)).charAt(0)){case'"':case"'":Ft=dh(se.substring(1),ne);break;default:Ft=ph(se)}if(Ft===null||(Ke.push(Ft[0]),(se=_l(Ft[1]))!==""&&se.charAt(0)!==","))return As;se=se.replace(/^,/,"")}return Ke}(xt);if(this.fontFaces){var Bt=fh(kt(this.pdf,this.fontFaces),_t.map(function(Gt){return{family:Gt,stretch:"normal",weight:Et,style:Yt}}));this.pdf.setFont(Bt.ref.name,Bt.ref.style)}else{var Wt="";(Et==="bold"||parseInt(Et,10)>=700||Yt==="bold")&&(Wt="bold"),Yt==="italic"&&(Wt+="italic"),Wt.length===0&&(Wt="normal");for(var Jt="",te={arial:"Helvetica",Arial:"Helvetica",verdana:"Helvetica",Verdana:"Helvetica",helvetica:"Helvetica",Helvetica:"Helvetica","sans-serif":"Helvetica",fixed:"Courier",monospace:"Courier",terminal:"Courier",cursive:"Times",fantasy:"Times",serif:"Times"},ie=0;ie<_t.length;ie++){if(this.pdf.internal.getFont(_t[ie],Wt,{noFallback:!0,disableWarning:!0})!==void 0){Jt=_t[ie];break}if(Wt==="bolditalic"&&this.pdf.internal.getFont(_t[ie],"bold",{noFallback:!0,disableWarning:!0})!==void 0)Jt=_t[ie],Wt="bold";else if(this.pdf.internal.getFont(_t[ie],"normal",{noFallback:!0,disableWarning:!0})!==void 0){Jt=_t[ie],Wt="normal";break}}if(Jt===""){for(var pe=0;pe<_t.length;pe++)if(te[_t[pe]]){Jt=te[_t[pe]];break}}Jt=Jt===""?"Times":Jt,this.pdf.setFont(Jt,Wt)}}}}),Object.defineProperty(this,"globalCompositeOperation",{get:function(){return this.ctx.globalCompositeOperation},set:function(it){this.ctx.globalCompositeOperation=it}}),Object.defineProperty(this,"globalAlpha",{get:function(){return this.ctx.globalAlpha},set:function(it){this.ctx.globalAlpha=it}}),Object.defineProperty(this,"lineDashOffset",{get:function(){return this.ctx.lineDashOffset},set:function(it){this.ctx.lineDashOffset=it,Ot.call(this)}}),Object.defineProperty(this,"lineDash",{get:function(){return this.ctx.lineDash},set:function(it){this.ctx.lineDash=it,Ot.call(this)}}),Object.defineProperty(this,"ignoreClearRect",{get:function(){return this.ctx.ignoreClearRect},set:function(it){this.ctx.ignoreClearRect=!!it}})};L.prototype.setLineDash=function(N){this.lineDash=N},L.prototype.getLineDash=function(){return this.lineDash.length%2?this.lineDash.concat(this.lineDash):this.lineDash.slice()},L.prototype.fill=function(){st.call(this,"fill",!1)},L.prototype.stroke=function(){st.call(this,"stroke",!1)},L.prototype.beginPath=function(){this.path=[{type:"begin"}]},L.prototype.moveTo=function(N,T){if(isNaN(N)||isNaN(T))throw be.error("jsPDF.context2d.moveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.moveTo");var E=this.ctx.transform.applyToPoint(new c(N,T));this.path.push({type:"mt",x:E.x,y:E.y}),this.ctx.lastPoint=new c(N,T)},L.prototype.closePath=function(){var N=new c(0,0),T=0;for(T=this.path.length-1;T!==-1;T--)if(this.path[T].type==="begin"&&de(this.path[T+1])==="object"&&typeof this.path[T+1].x=="number"){N=new c(this.path[T+1].x,this.path[T+1].y);break}this.path.push({type:"close"}),this.ctx.lastPoint=new c(N.x,N.y)},L.prototype.lineTo=function(N,T){if(isNaN(N)||isNaN(T))throw be.error("jsPDF.context2d.lineTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.lineTo");var E=this.ctx.transform.applyToPoint(new c(N,T));this.path.push({type:"lt",x:E.x,y:E.y}),this.ctx.lastPoint=new c(E.x,E.y)},L.prototype.clip=function(){this.ctx.clip_path=JSON.parse(JSON.stringify(this.path)),st.call(this,null,!0)},L.prototype.quadraticCurveTo=function(N,T,E,V){if(isNaN(E)||isNaN(V)||isNaN(N)||isNaN(T))throw be.error("jsPDF.context2d.quadraticCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.quadraticCurveTo");var Y=this.ctx.transform.applyToPoint(new c(E,V)),Q=this.ctx.transform.applyToPoint(new c(N,T));this.path.push({type:"qct",x1:Q.x,y1:Q.y,x:Y.x,y:Y.y}),this.ctx.lastPoint=new c(Y.x,Y.y)},L.prototype.bezierCurveTo=function(N,T,E,V,Y,Q){if(isNaN(Y)||isNaN(Q)||isNaN(N)||isNaN(T)||isNaN(E)||isNaN(V))throw be.error("jsPDF.context2d.bezierCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.bezierCurveTo");var et=this.ctx.transform.applyToPoint(new c(Y,Q)),Z=this.ctx.transform.applyToPoint(new c(N,T)),Lt=this.ctx.transform.applyToPoint(new c(E,V));this.path.push({type:"bct",x1:Z.x,y1:Z.y,x2:Lt.x,y2:Lt.y,x:et.x,y:et.y}),this.ctx.lastPoint=new c(et.x,et.y)},L.prototype.arc=function(N,T,E,V,Y,Q){if(isNaN(N)||isNaN(T)||isNaN(E)||isNaN(V)||isNaN(Y))throw be.error("jsPDF.context2d.arc: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.arc");if(Q=!!Q,!this.ctx.transform.isIdentity){var et=this.ctx.transform.applyToPoint(new c(N,T));N=et.x,T=et.y;var Z=this.ctx.transform.applyToPoint(new c(0,E)),Lt=this.ctx.transform.applyToPoint(new c(0,0));E=Math.sqrt(Math.pow(Z.x-Lt.x,2)+Math.pow(Z.y-Lt.y,2))}Math.abs(Y-V)>=2*Math.PI&&(V=0,Y=2*Math.PI),this.path.push({type:"arc",x:N,y:T,radius:E,startAngle:V,endAngle:Y,counterclockwise:Q})},L.prototype.arcTo=function(N,T,E,V,Y){throw new Error("arcTo not implemented.")},L.prototype.rect=function(N,T,E,V){if(isNaN(N)||isNaN(T)||isNaN(E)||isNaN(V))throw be.error("jsPDF.context2d.rect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rect");this.moveTo(N,T),this.lineTo(N+E,T),this.lineTo(N+E,T+V),this.lineTo(N,T+V),this.lineTo(N,T),this.lineTo(N+E,T),this.lineTo(N,T)},L.prototype.fillRect=function(N,T,E,V){if(isNaN(N)||isNaN(T)||isNaN(E)||isNaN(V))throw be.error("jsPDF.context2d.fillRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillRect");if(!m.call(this)){var Y={};this.lineCap!=="butt"&&(Y.lineCap=this.lineCap,this.lineCap="butt"),this.lineJoin!=="miter"&&(Y.lineJoin=this.lineJoin,this.lineJoin="miter"),this.beginPath(),this.rect(N,T,E,V),this.fill(),Y.hasOwnProperty("lineCap")&&(this.lineCap=Y.lineCap),Y.hasOwnProperty("lineJoin")&&(this.lineJoin=Y.lineJoin)}},L.prototype.strokeRect=function(N,T,E,V){if(isNaN(N)||isNaN(T)||isNaN(E)||isNaN(V))throw be.error("jsPDF.context2d.strokeRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeRect");D.call(this)||(this.beginPath(),this.rect(N,T,E,V),this.stroke())},L.prototype.clearRect=function(N,T,E,V){if(isNaN(N)||isNaN(T)||isNaN(E)||isNaN(V))throw be.error("jsPDF.context2d.clearRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.clearRect");this.ignoreClearRect||(this.fillStyle="#ffffff",this.fillRect(N,T,E,V))},L.prototype.save=function(N){N=typeof N!="boolean"||N;for(var T=this.pdf.internal.getCurrentPageInfo().pageNumber,E=0;E<this.pdf.internal.getNumberOfPages();E++)this.pdf.setPage(E+1),this.pdf.internal.out("q");if(this.pdf.setPage(T),N){this.ctx.fontSize=this.pdf.internal.getFontSize();var V=new w(this.ctx);this.ctxStack.push(this.ctx),this.ctx=V}},L.prototype.restore=function(N){N=typeof N!="boolean"||N;for(var T=this.pdf.internal.getCurrentPageInfo().pageNumber,E=0;E<this.pdf.internal.getNumberOfPages();E++)this.pdf.setPage(E+1),this.pdf.internal.out("Q");this.pdf.setPage(T),N&&this.ctxStack.length!==0&&(this.ctx=this.ctxStack.pop(),this.fillStyle=this.ctx.fillStyle,this.strokeStyle=this.ctx.strokeStyle,this.font=this.ctx.font,this.lineCap=this.ctx.lineCap,this.lineWidth=this.ctx.lineWidth,this.lineJoin=this.ctx.lineJoin,this.lineDash=this.ctx.lineDash,this.lineDashOffset=this.ctx.lineDashOffset)},L.prototype.toDataURL=function(){throw new Error("toDataUrl not implemented.")};var x=function(N){var T,E,V,Y;if(N.isCanvasGradient===!0&&(N=N.getColor()),!N)return{r:0,g:0,b:0,a:0,style:N};if(/transparent|rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*0+\s*\)/.test(N))T=0,E=0,V=0,Y=0;else{var Q=/rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/.exec(N);if(Q!==null)T=parseInt(Q[1]),E=parseInt(Q[2]),V=parseInt(Q[3]),Y=1;else if((Q=/rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)/.exec(N))!==null)T=parseInt(Q[1]),E=parseInt(Q[2]),V=parseInt(Q[3]),Y=parseFloat(Q[4]);else{if(Y=1,typeof N=="string"&&N.charAt(0)!=="#"){var et=new Hl(N);N=et.ok?et.toHex():"#000000"}N.length===4?(T=N.substring(1,2),T+=T,E=N.substring(2,3),E+=E,V=N.substring(3,4),V+=V):(T=N.substring(1,3),E=N.substring(3,5),V=N.substring(5,7)),T=parseInt(T,16),E=parseInt(E,16),V=parseInt(V,16)}}return{r:T,g:E,b:V,a:Y,style:N}},m=function(){return this.ctx.isFillTransparent||this.globalAlpha==0},D=function(){return!!(this.ctx.isStrokeTransparent||this.globalAlpha==0)};L.prototype.fillText=function(N,T,E,V){if(isNaN(T)||isNaN(E)||typeof N!="string")throw be.error("jsPDF.context2d.fillText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillText");if(V=isNaN(V)?void 0:V,!m.call(this)){var Y=tt(this.ctx.transform.rotation),Q=this.ctx.transform.scaleX;I.call(this,{text:N,x:T,y:E,scale:Q,angle:Y,align:this.textAlign,maxWidth:V})}},L.prototype.strokeText=function(N,T,E,V){if(isNaN(T)||isNaN(E)||typeof N!="string")throw be.error("jsPDF.context2d.strokeText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeText");if(!D.call(this)){V=isNaN(V)?void 0:V;var Y=tt(this.ctx.transform.rotation),Q=this.ctx.transform.scaleX;I.call(this,{text:N,x:T,y:E,scale:Q,renderingMode:"stroke",angle:Y,align:this.textAlign,maxWidth:V})}},L.prototype.measureText=function(N){if(typeof N!="string")throw be.error("jsPDF.context2d.measureText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.measureText");var T=this.pdf,E=this.pdf.internal.scaleFactor,V=T.internal.getFontSize(),Y=T.getStringUnitWidth(N)*V/T.internal.scaleFactor,Q=function(et){var Z=(et=et||{}).width||0;return Object.defineProperty(this,"width",{get:function(){return Z}}),this};return new Q({width:Y*=Math.round(96*E/72*1e4)/1e4})},L.prototype.scale=function(N,T){if(isNaN(N)||isNaN(T))throw be.error("jsPDF.context2d.scale: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.scale");var E=new f(N,0,0,T,0,0);this.ctx.transform=this.ctx.transform.multiply(E)},L.prototype.rotate=function(N){if(isNaN(N))throw be.error("jsPDF.context2d.rotate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rotate");var T=new f(Math.cos(N),Math.sin(N),-Math.sin(N),Math.cos(N),0,0);this.ctx.transform=this.ctx.transform.multiply(T)},L.prototype.translate=function(N,T){if(isNaN(N)||isNaN(T))throw be.error("jsPDF.context2d.translate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.translate");var E=new f(1,0,0,1,N,T);this.ctx.transform=this.ctx.transform.multiply(E)},L.prototype.transform=function(N,T,E,V,Y,Q){if(isNaN(N)||isNaN(T)||isNaN(E)||isNaN(V)||isNaN(Y)||isNaN(Q))throw be.error("jsPDF.context2d.transform: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.transform");var et=new f(N,T,E,V,Y,Q);this.ctx.transform=this.ctx.transform.multiply(et)},L.prototype.setTransform=function(N,T,E,V,Y,Q){N=isNaN(N)?1:N,T=isNaN(T)?0:T,E=isNaN(E)?0:E,V=isNaN(V)?1:V,Y=isNaN(Y)?0:Y,Q=isNaN(Q)?0:Q,this.ctx.transform=new f(N,T,E,V,Y,Q)};var k=function(){return this.margin[0]>0||this.margin[1]>0||this.margin[2]>0||this.margin[3]>0};L.prototype.drawImage=function(N,T,E,V,Y,Q,et,Z,Lt){var Nt=this.pdf.getImageProperties(N),Tt=1,kt=1,Ht=1,it=1;V!==void 0&&Z!==void 0&&(Ht=Z/V,it=Lt/Y,Tt=Nt.width/V*Z/V,kt=Nt.height/Y*Lt/Y),Q===void 0&&(Q=T,et=E,T=0,E=0),V!==void 0&&Z===void 0&&(Z=V,Lt=Y),V===void 0&&Z===void 0&&(Z=Nt.width,Lt=Nt.height);for(var M,Yt=this.ctx.transform.decompose(),Et=tt(Yt.rotate.shx),wt=new f,xt=(wt=(wt=(wt=wt.multiply(Yt.translate)).multiply(Yt.skew)).multiply(Yt.scale)).applyToRectangle(new h(Q-T*Ht,et-E*it,V*Tt,Y*kt)),Ct=O.call(this,xt),_t=[],Bt=0;Bt<Ct.length;Bt+=1)_t.indexOf(Ct[Bt])===-1&&_t.push(Ct[Bt]);if(J(_t),this.autoPaging)for(var Wt=_t[0],Jt=_t[_t.length-1],te=Wt;te<Jt+1;te++){this.pdf.setPage(te);var ie=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],pe=te===1?this.posY+this.margin[0]:this.margin[0],Gt=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],ne=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],Ft=te===1?0:Gt+(te-2)*ne;if(this.ctx.clip_path.length!==0){var Ke=this.path;M=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=j(M,this.posX+this.margin[3],-Ft+pe+this.ctx.prevPageLastElemOffset),ft.call(this,"fill",!0),this.path=Ke}var se=JSON.parse(JSON.stringify(xt));se=j([se],this.posX+this.margin[3],-Ft+pe+this.ctx.prevPageLastElemOffset)[0];var Pn=(te>Wt||te<Jt)&&k.call(this);Pn&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],ie,ne,null).clip().discardPath()),this.pdf.addImage(N,"JPEG",se.x,se.y,se.w,se.h,null,null,Et),Pn&&this.pdf.restoreGraphicsState()}else this.pdf.addImage(N,"JPEG",xt.x,xt.y,xt.w,xt.h,null,null,Et)};var O=function(N,T,E){var V=[];T=T||this.pdf.internal.pageSize.width,E=E||this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2];var Y=this.posY+this.ctx.prevPageLastElemOffset;switch(N.type){default:case"mt":case"lt":V.push(Math.floor((N.y+Y)/E)+1);break;case"arc":V.push(Math.floor((N.y+Y-N.radius)/E)+1),V.push(Math.floor((N.y+Y+N.radius)/E)+1);break;case"qct":var Q=pt(this.ctx.lastPoint.x,this.ctx.lastPoint.y,N.x1,N.y1,N.x,N.y);V.push(Math.floor((Q.y+Y)/E)+1),V.push(Math.floor((Q.y+Q.h+Y)/E)+1);break;case"bct":var et=ut(this.ctx.lastPoint.x,this.ctx.lastPoint.y,N.x1,N.y1,N.x2,N.y2,N.x,N.y);V.push(Math.floor((et.y+Y)/E)+1),V.push(Math.floor((et.y+et.h+Y)/E)+1);break;case"rect":V.push(Math.floor((N.y+Y)/E)+1),V.push(Math.floor((N.y+N.h+Y)/E)+1)}for(var Z=0;Z<V.length;Z+=1)for(;this.pdf.internal.getNumberOfPages()<V[Z];)_.call(this);return V},_=function(){var N=this.fillStyle,T=this.strokeStyle,E=this.font,V=this.lineCap,Y=this.lineWidth,Q=this.lineJoin;this.pdf.addPage(),this.fillStyle=N,this.strokeStyle=T,this.font=E,this.lineCap=V,this.lineWidth=Y,this.lineJoin=Q},j=function(N,T,E){for(var V=0;V<N.length;V++)switch(N[V].type){case"bct":N[V].x2+=T,N[V].y2+=E;case"qct":N[V].x1+=T,N[V].y1+=E;case"mt":case"lt":case"arc":default:N[V].x+=T,N[V].y+=E}return N},J=function(N){return N.sort(function(T,E){return T-E})},st=function(N,T){for(var E,V,Y=this.fillStyle,Q=this.strokeStyle,et=this.lineCap,Z=this.lineWidth,Lt=Math.abs(Z*this.ctx.transform.scaleX),Nt=this.lineJoin,Tt=JSON.parse(JSON.stringify(this.path)),kt=JSON.parse(JSON.stringify(this.path)),Ht=[],it=0;it<kt.length;it++)if(kt[it].x!==void 0)for(var M=O.call(this,kt[it]),Yt=0;Yt<M.length;Yt+=1)Ht.indexOf(M[Yt])===-1&&Ht.push(M[Yt]);for(var Et=0;Et<Ht.length;Et++)for(;this.pdf.internal.getNumberOfPages()<Ht[Et];)_.call(this);if(J(Ht),this.autoPaging)for(var wt=Ht[0],xt=Ht[Ht.length-1],Ct=wt;Ct<xt+1;Ct++){this.pdf.setPage(Ct),this.fillStyle=Y,this.strokeStyle=Q,this.lineCap=et,this.lineWidth=Lt,this.lineJoin=Nt;var _t=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],Bt=Ct===1?this.posY+this.margin[0]:this.margin[0],Wt=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],Jt=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],te=Ct===1?0:Wt+(Ct-2)*Jt;if(this.ctx.clip_path.length!==0){var ie=this.path;E=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=j(E,this.posX+this.margin[3],-te+Bt+this.ctx.prevPageLastElemOffset),ft.call(this,N,!0),this.path=ie}if(V=JSON.parse(JSON.stringify(Tt)),this.path=j(V,this.posX+this.margin[3],-te+Bt+this.ctx.prevPageLastElemOffset),T===!1||Ct===0){var pe=(Ct>wt||Ct<xt)&&k.call(this);pe&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],_t,Jt,null).clip().discardPath()),ft.call(this,N,T),pe&&this.pdf.restoreGraphicsState()}this.lineWidth=Z}else this.lineWidth=Lt,ft.call(this,N,T),this.lineWidth=Z;this.path=Tt},ft=function(N,T){if((N!=="stroke"||T||!D.call(this))&&(N==="stroke"||T||!m.call(this))){for(var E,V,Y=[],Q=this.path,et=0;et<Q.length;et++){var Z=Q[et];switch(Z.type){case"begin":Y.push({begin:!0});break;case"close":Y.push({close:!0});break;case"mt":Y.push({start:Z,deltas:[],abs:[]});break;case"lt":var Lt=Y.length;if(Q[et-1]&&!isNaN(Q[et-1].x)&&(E=[Z.x-Q[et-1].x,Z.y-Q[et-1].y],Lt>0)){for(;Lt>=0;Lt--)if(Y[Lt-1].close!==!0&&Y[Lt-1].begin!==!0){Y[Lt-1].deltas.push(E),Y[Lt-1].abs.push(Z);break}}break;case"bct":E=[Z.x1-Q[et-1].x,Z.y1-Q[et-1].y,Z.x2-Q[et-1].x,Z.y2-Q[et-1].y,Z.x-Q[et-1].x,Z.y-Q[et-1].y],Y[Y.length-1].deltas.push(E);break;case"qct":var Nt=Q[et-1].x+2/3*(Z.x1-Q[et-1].x),Tt=Q[et-1].y+2/3*(Z.y1-Q[et-1].y),kt=Z.x+2/3*(Z.x1-Z.x),Ht=Z.y+2/3*(Z.y1-Z.y),it=Z.x,M=Z.y;E=[Nt-Q[et-1].x,Tt-Q[et-1].y,kt-Q[et-1].x,Ht-Q[et-1].y,it-Q[et-1].x,M-Q[et-1].y],Y[Y.length-1].deltas.push(E);break;case"arc":Y.push({deltas:[],abs:[],arc:!0}),Array.isArray(Y[Y.length-1].abs)&&Y[Y.length-1].abs.push(Z)}}V=T?null:N==="stroke"?"stroke":"fill";for(var Yt=!1,Et=0;Et<Y.length;Et++)if(Y[Et].arc)for(var wt=Y[Et].abs,xt=0;xt<wt.length;xt++){var Ct=wt[xt];Ct.type==="arc"?G.call(this,Ct.x,Ct.y,Ct.radius,Ct.startAngle,Ct.endAngle,Ct.counterclockwise,void 0,T,!Yt):z.call(this,Ct.x,Ct.y),Yt=!0}else if(Y[Et].close===!0)this.pdf.internal.out("h"),Yt=!1;else if(Y[Et].begin!==!0){var _t=Y[Et].start.x,Bt=Y[Et].start.y;R.call(this,Y[Et].deltas,_t,Bt),Yt=!0}V&&vt.call(this,V),T&&bt.call(this)}},At=function(N){var T=this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor,E=T*(this.pdf.internal.getLineHeightFactor()-1);switch(this.ctx.textBaseline){case"bottom":return N-E;case"top":return N+T-E;case"hanging":return N+T-2*E;case"middle":return N+T/2-E;case"ideographic":return N;case"alphabetic":default:return N}},rt=function(N){return N+this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor*(this.pdf.internal.getLineHeightFactor()-1)};L.prototype.createLinearGradient=function(){var N=function(){};return N.colorStops=[],N.addColorStop=function(T,E){this.colorStops.push([T,E])},N.getColor=function(){return this.colorStops.length===0?"#000000":this.colorStops[0][1]},N.isCanvasGradient=!0,N},L.prototype.createPattern=function(){return this.createLinearGradient()},L.prototype.createRadialGradient=function(){return this.createLinearGradient()};var G=function(N,T,E,V,Y,Q,et,Z,Lt){for(var Nt=ot.call(this,E,V,Y,Q),Tt=0;Tt<Nt.length;Tt++){var kt=Nt[Tt];Tt===0&&(Lt?C.call(this,kt.x1+N,kt.y1+T):z.call(this,kt.x1+N,kt.y1+T)),lt.call(this,N,T,kt.x2,kt.y2,kt.x3,kt.y3,kt.x4,kt.y4)}Z?bt.call(this):vt.call(this,et)},vt=function(N){switch(N){case"stroke":this.pdf.internal.out("S");break;case"fill":this.pdf.internal.out("f")}},bt=function(){this.pdf.clip(),this.pdf.discardPath()},C=function(N,T){this.pdf.internal.out(n(N)+" "+a(T)+" m")},I=function(N){var T;switch(N.align){case"right":case"end":T="right";break;case"center":T="center";break;case"left":case"start":default:T="left"}var E=this.pdf.getTextDimensions(N.text),V=At.call(this,N.y),Y=rt.call(this,V)-E.h,Q=this.ctx.transform.applyToPoint(new c(N.x,V)),et=this.ctx.transform.decompose(),Z=new f;Z=(Z=(Z=Z.multiply(et.translate)).multiply(et.skew)).multiply(et.scale);for(var Lt,Nt,Tt,kt=this.ctx.transform.applyToRectangle(new h(N.x,V,E.w,E.h)),Ht=Z.applyToRectangle(new h(N.x,Y,E.w,E.h)),it=O.call(this,Ht),M=[],Yt=0;Yt<it.length;Yt+=1)M.indexOf(it[Yt])===-1&&M.push(it[Yt]);if(J(M),this.autoPaging)for(var Et=M[0],wt=M[M.length-1],xt=Et;xt<wt+1;xt++){this.pdf.setPage(xt);var Ct=xt===1?this.posY+this.margin[0]:this.margin[0],_t=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],Bt=this.pdf.internal.pageSize.height-this.margin[2],Wt=Bt-this.margin[0],Jt=this.pdf.internal.pageSize.width-this.margin[1],te=Jt-this.margin[3],ie=xt===1?0:_t+(xt-2)*Wt;if(this.ctx.clip_path.length!==0){var pe=this.path;Lt=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=j(Lt,this.posX+this.margin[3],-1*ie+Ct),ft.call(this,"fill",!0),this.path=pe}var Gt=j([JSON.parse(JSON.stringify(Ht))],this.posX+this.margin[3],-ie+Ct+this.ctx.prevPageLastElemOffset)[0];N.scale>=.01&&(Nt=this.pdf.internal.getFontSize(),this.pdf.setFontSize(Nt*N.scale),Tt=this.lineWidth,this.lineWidth=Tt*N.scale);var ne=this.autoPaging!=="text";if(ne||Gt.y+Gt.h<=Bt){if(ne||Gt.y>=Ct&&Gt.x<=Jt){var Ft=ne?N.text:this.pdf.splitTextToSize(N.text,N.maxWidth||Jt-Gt.x)[0],Ke=j([JSON.parse(JSON.stringify(kt))],this.posX+this.margin[3],-ie+Ct+this.ctx.prevPageLastElemOffset)[0],se=ne&&(xt>Et||xt<wt)&&k.call(this);se&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],te,Wt,null).clip().discardPath()),this.pdf.text(Ft,Ke.x,Ke.y,{angle:N.angle,align:T,renderingMode:N.renderingMode}),se&&this.pdf.restoreGraphicsState()}}else Gt.y<Bt&&(this.ctx.prevPageLastElemOffset+=Bt-Gt.y);N.scale>=.01&&(this.pdf.setFontSize(Nt),this.lineWidth=Tt)}else N.scale>=.01&&(Nt=this.pdf.internal.getFontSize(),this.pdf.setFontSize(Nt*N.scale),Tt=this.lineWidth,this.lineWidth=Tt*N.scale),this.pdf.text(N.text,Q.x+this.posX,Q.y+this.posY,{angle:N.angle,align:T,renderingMode:N.renderingMode,maxWidth:N.maxWidth}),N.scale>=.01&&(this.pdf.setFontSize(Nt),this.lineWidth=Tt)},z=function(N,T,E,V){E=E||0,V=V||0,this.pdf.internal.out(n(N+E)+" "+a(T+V)+" l")},R=function(N,T,E){return this.pdf.lines(N,T,E,null,null)},lt=function(N,T,E,V,Y,Q,et,Z){this.pdf.internal.out([e(o(E+N)),e(s(V+T)),e(o(Y+N)),e(s(Q+T)),e(o(et+N)),e(s(Z+T)),"c"].join(" "))},ot=function(N,T,E,V){for(var Y=2*Math.PI,Q=Math.PI/2;T>E;)T-=Y;var et=Math.abs(E-T);et<Y&&V&&(et=Y-et);for(var Z=[],Lt=V?-1:1,Nt=T;et>1e-5;){var Tt=Nt+Lt*Math.min(et,Q);Z.push(mt.call(this,N,Nt,Tt)),et-=Math.abs(Tt-Nt),Nt=Tt}return Z},mt=function(N,T,E){var V=(E-T)/2,Y=N*Math.cos(V),Q=N*Math.sin(V),et=Y,Z=-Q,Lt=et*et+Z*Z,Nt=Lt+et*Y+Z*Q,Tt=4/3*(Math.sqrt(2*Lt*Nt)-Nt)/(et*Q-Z*Y),kt=et-Tt*Z,Ht=Z+Tt*et,it=kt,M=-Ht,Yt=V+T,Et=Math.cos(Yt),wt=Math.sin(Yt);return{x1:N*Math.cos(T),y1:N*Math.sin(T),x2:kt*Et-Ht*wt,y2:kt*wt+Ht*Et,x3:it*Et-M*wt,y3:it*wt+M*Et,x4:N*Math.cos(E),y4:N*Math.sin(E)}},tt=function(N){return 180*N/Math.PI},pt=function(N,T,E,V,Y,Q){var et=N+.5*(E-N),Z=T+.5*(V-T),Lt=Y+.5*(E-Y),Nt=Q+.5*(V-Q),Tt=Math.min(N,Y,et,Lt),kt=Math.max(N,Y,et,Lt),Ht=Math.min(T,Q,Z,Nt),it=Math.max(T,Q,Z,Nt);return new h(Tt,Ht,kt-Tt,it-Ht)},ut=function(N,T,E,V,Y,Q,et,Z){var Lt,Nt,Tt,kt,Ht,it,M,Yt,Et,wt,xt,Ct,_t,Bt,Wt=E-N,Jt=V-T,te=Y-E,ie=Q-V,pe=et-Y,Gt=Z-Q;for(Nt=0;Nt<41;Nt++)Et=(M=(Tt=N+(Lt=Nt/40)*Wt)+Lt*((Ht=E+Lt*te)-Tt))+Lt*(Ht+Lt*(Y+Lt*pe-Ht)-M),wt=(Yt=(kt=T+Lt*Jt)+Lt*((it=V+Lt*ie)-kt))+Lt*(it+Lt*(Q+Lt*Gt-it)-Yt),Nt==0?(xt=Et,Ct=wt,_t=Et,Bt=wt):(xt=Math.min(xt,Et),Ct=Math.min(Ct,wt),_t=Math.max(_t,Et),Bt=Math.max(Bt,wt));return new h(Math.round(xt),Math.round(Ct),Math.round(_t-xt),Math.round(Bt-Ct))},Ot=function(){if(this.prevLineDash||this.ctx.lineDash.length||this.ctx.lineDashOffset){var N,T,E=(N=this.ctx.lineDash,T=this.ctx.lineDashOffset,JSON.stringify({lineDash:N,lineDashOffset:T}));this.prevLineDash!==E&&(this.pdf.setLineDash(this.ctx.lineDash,this.ctx.lineDashOffset),this.prevLineDash=E)}}})(Vt.API),function(r){var e=function(s){var c,h,f,p,w,L,x,m,D,k;for(h=[],f=0,p=(s+=c="\0\0\0\0".slice(s.length%4||4)).length;p>f;f+=4)(w=(s.charCodeAt(f)<<24)+(s.charCodeAt(f+1)<<16)+(s.charCodeAt(f+2)<<8)+s.charCodeAt(f+3))!==0?(L=(w=((w=((w=((w=(w-(k=w%85))/85)-(D=w%85))/85)-(m=w%85))/85)-(x=w%85))/85)%85,h.push(L+33,x+33,m+33,D+33,k+33)):h.push(122);return function(O,_){for(var j=_;j>0;j--)O.pop()}(h,c.length),String.fromCharCode.apply(String,h)+"~>"},n=function(s){var c,h,f,p,w,L=String,x="length",m=255,D="charCodeAt",k="slice",O="replace";for(s[k](-2),s=s[k](0,-2)[O](/\s/g,"")[O]("z","!!!!!"),f=[],p=0,w=(s+=c="uuuuu"[k](s[x]%5||5))[x];w>p;p+=5)h=52200625*(s[D](p)-33)+614125*(s[D](p+1)-33)+7225*(s[D](p+2)-33)+85*(s[D](p+3)-33)+(s[D](p+4)-33),f.push(m&h>>24,m&h>>16,m&h>>8,m&h);return function(_,j){for(var J=j;J>0;J--)_.pop()}(f,c[x]),L.fromCharCode.apply(L,f)},a=function(s){var c=new RegExp(/^([0-9A-Fa-f]{2})+$/);if((s=s.replace(/\s/g,"")).indexOf(">")!==-1&&(s=s.substr(0,s.indexOf(">"))),s.length%2&&(s+="0"),c.test(s)===!1)return"";for(var h="",f=0;f<s.length;f+=2)h+=String.fromCharCode("0x"+(s[f]+s[f+1]));return h},o=function(s){for(var c=new Uint8Array(s.length),h=s.length;h--;)c[h]=s.charCodeAt(h);return s=(c=_s(c)).reduce(function(f,p){return f+String.fromCharCode(p)},"")};r.processDataByFilters=function(s,c){var h=0,f=s||"",p=[];for(typeof(c=c||[])=="string"&&(c=[c]),h=0;h<c.length;h+=1)switch(c[h]){case"ASCII85Decode":case"/ASCII85Decode":f=n(f),p.push("/ASCII85Encode");break;case"ASCII85Encode":case"/ASCII85Encode":f=e(f),p.push("/ASCII85Decode");break;case"ASCIIHexDecode":case"/ASCIIHexDecode":f=a(f),p.push("/ASCIIHexEncode");break;case"ASCIIHexEncode":case"/ASCIIHexEncode":f=f.split("").map(function(w){return("0"+w.charCodeAt().toString(16)).slice(-2)}).join("")+">",p.push("/ASCIIHexDecode");break;case"FlateEncode":case"/FlateEncode":f=o(f),p.push("/FlateDecode");break;default:throw new Error('The filter: "'+c[h]+'" is not implemented')}return{data:f,reverseChain:p.reverse().join(" ")}}}(Vt.API),function(r){r.loadFile=function(e,n,a){return function(o,s,c){s=s!==!1,c=typeof c=="function"?c:function(){};var h=void 0;try{h=function(f,p,w){var L=new XMLHttpRequest,x=0,m=function(D){var k=D.length,O=[],_=String.fromCharCode;for(x=0;x<k;x+=1)O.push(_(255&D.charCodeAt(x)));return O.join("")};if(L.open("GET",f,!p),L.overrideMimeType("text/plain; charset=x-user-defined"),p===!1&&(L.onload=function(){L.status===200?w(m(this.responseText)):w(void 0)}),L.send(null),p&&L.status===200)return m(L.responseText)}(o,s,c)}catch{}return h}(e,n,a)},r.loadImageFile=r.loadFile}(Vt.API),function(r){function e(){return(zt.html2canvas?Promise.resolve(zt.html2canvas):Ss(()=>import("./html2canvas.esm-cf66fc0f.js").then(c=>c.a),[])).catch(function(c){return Promise.reject(new Error("Could not load html2canvas: "+c))}).then(function(c){return c.default?c.default:c})}function n(){return(zt.DOMPurify?Promise.resolve(zt.DOMPurify):Ss(()=>import("./purify.es-3fb4e735.js"),[])).catch(function(c){return Promise.reject(new Error("Could not load dompurify: "+c))}).then(function(c){return c.default?c.default:c})}var a=function(c){var h=de(c);return h==="undefined"?"undefined":h==="string"||c instanceof String?"string":h==="number"||c instanceof Number?"number":h==="function"||c instanceof Function?"function":c&&c.constructor===Array?"array":c&&c.nodeType===1?"element":h==="object"?"object":"unknown"},o=function(c,h){var f=document.createElement(c);for(var p in h.className&&(f.className=h.className),h.innerHTML&&h.dompurify&&(f.innerHTML=h.dompurify.sanitize(h.innerHTML)),h.style)f.style[p]=h.style[p];return f},s=function c(h){var f=Object.assign(c.convert(Promise.resolve()),JSON.parse(JSON.stringify(c.template))),p=c.convert(Promise.resolve(),f);return p=(p=p.setProgress(1,c,1,[c])).set(h)};(s.prototype=Object.create(Promise.prototype)).constructor=s,s.convert=function(c,h){return c.__proto__=h||s.prototype,c},s.template={prop:{src:null,container:null,overlay:null,canvas:null,img:null,pdf:null,pageSize:null,callback:function(){}},progress:{val:0,state:null,n:0,stack:[]},opt:{filename:"file.pdf",margin:[0,0,0,0],enableLinks:!0,x:0,y:0,html2canvas:{},jsPDF:{},backgroundColor:"transparent"}},s.prototype.from=function(c,h){return this.then(function(){switch(h=h||function(f){switch(a(f)){case"string":return"string";case"element":return f.nodeName.toLowerCase()==="canvas"?"canvas":"element";default:return"unknown"}}(c)){case"string":return this.then(n).then(function(f){return this.set({src:o("div",{innerHTML:c,dompurify:f})})});case"element":return this.set({src:c});case"canvas":return this.set({canvas:c});case"img":return this.set({img:c});default:return this.error("Unknown source type.")}})},s.prototype.to=function(c){switch(c){case"container":return this.toContainer();case"canvas":return this.toCanvas();case"img":return this.toImg();case"pdf":return this.toPdf();default:return this.error("Invalid target.")}},s.prototype.toContainer=function(){return this.thenList([function(){return this.prop.src||this.error("Cannot duplicate - no source HTML.")},function(){return this.prop.pageSize||this.setPageSize()}]).then(function(){var c={position:"relative",display:"inline-block",width:(typeof this.opt.width!="number"||isNaN(this.opt.width)||typeof this.opt.windowWidth!="number"||isNaN(this.opt.windowWidth)?Math.max(this.prop.src.clientWidth,this.prop.src.scrollWidth,this.prop.src.offsetWidth):this.opt.windowWidth)+"px",left:0,right:0,top:0,margin:"auto",backgroundColor:this.opt.backgroundColor},h=function f(p,w){for(var L=p.nodeType===3?document.createTextNode(p.nodeValue):p.cloneNode(!1),x=p.firstChild;x;x=x.nextSibling)w!==!0&&x.nodeType===1&&x.nodeName==="SCRIPT"||L.appendChild(f(x,w));return p.nodeType===1&&(p.nodeName==="CANVAS"?(L.width=p.width,L.height=p.height,L.getContext("2d").drawImage(p,0,0)):p.nodeName!=="TEXTAREA"&&p.nodeName!=="SELECT"||(L.value=p.value),L.addEventListener("load",function(){L.scrollTop=p.scrollTop,L.scrollLeft=p.scrollLeft},!0)),L}(this.prop.src,this.opt.html2canvas.javascriptEnabled);h.tagName==="BODY"&&(c.height=Math.max(document.body.scrollHeight,document.body.offsetHeight,document.documentElement.clientHeight,document.documentElement.scrollHeight,document.documentElement.offsetHeight)+"px"),this.prop.overlay=o("div",{className:"html2pdf__overlay",style:{position:"fixed",overflow:"hidden",zIndex:1e3,left:"-100000px",right:0,bottom:0,top:0}}),this.prop.container=o("div",{className:"html2pdf__container",style:c}),this.prop.container.appendChild(h),this.prop.container.firstChild.appendChild(o("div",{style:{clear:"both",border:"0 none transparent",margin:0,padding:0,height:0}})),this.prop.container.style.float="none",this.prop.overlay.appendChild(this.prop.container),document.body.appendChild(this.prop.overlay),this.prop.container.firstChild.style.position="relative",this.prop.container.height=Math.max(this.prop.container.firstChild.clientHeight,this.prop.container.firstChild.scrollHeight,this.prop.container.firstChild.offsetHeight)+"px"})},s.prototype.toCanvas=function(){var c=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(c).then(e).then(function(h){var f=Object.assign({},this.opt.html2canvas);return delete f.onrendered,h(this.prop.container,f)}).then(function(h){(this.opt.html2canvas.onrendered||function(){})(h),this.prop.canvas=h,document.body.removeChild(this.prop.overlay)})},s.prototype.toContext2d=function(){var c=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(c).then(e).then(function(h){var f=this.opt.jsPDF,p=this.opt.fontFaces,w=typeof this.opt.width!="number"||isNaN(this.opt.width)||typeof this.opt.windowWidth!="number"||isNaN(this.opt.windowWidth)?1:this.opt.width/this.opt.windowWidth,L=Object.assign({async:!0,allowTaint:!0,scale:w,scrollX:this.opt.scrollX||0,scrollY:this.opt.scrollY||0,backgroundColor:"#ffffff",imageTimeout:15e3,logging:!0,proxy:null,removeContainer:!0,foreignObjectRendering:!1,useCORS:!1},this.opt.html2canvas);if(delete L.onrendered,f.context2d.autoPaging=this.opt.autoPaging===void 0||this.opt.autoPaging,f.context2d.posX=this.opt.x,f.context2d.posY=this.opt.y,f.context2d.margin=this.opt.margin,f.context2d.fontFaces=p,p)for(var x=0;x<p.length;++x){var m=p[x],D=m.src.find(function(k){return k.format==="truetype"});D&&f.addFont(D.url,m.ref.name,m.ref.style)}return L.windowHeight=L.windowHeight||0,L.windowHeight=L.windowHeight==0?Math.max(this.prop.container.clientHeight,this.prop.container.scrollHeight,this.prop.container.offsetHeight):L.windowHeight,f.context2d.save(!0),h(this.prop.container,L)}).then(function(h){this.opt.jsPDF.context2d.restore(!0),(this.opt.html2canvas.onrendered||function(){})(h),this.prop.canvas=h,document.body.removeChild(this.prop.overlay)})},s.prototype.toImg=function(){return this.thenList([function(){return this.prop.canvas||this.toCanvas()}]).then(function(){var c=this.prop.canvas.toDataURL("image/"+this.opt.image.type,this.opt.image.quality);this.prop.img=document.createElement("img"),this.prop.img.src=c})},s.prototype.toPdf=function(){return this.thenList([function(){return this.toContext2d()}]).then(function(){this.prop.pdf=this.prop.pdf||this.opt.jsPDF})},s.prototype.output=function(c,h,f){return(f=f||"pdf").toLowerCase()==="img"||f.toLowerCase()==="image"?this.outputImg(c,h):this.outputPdf(c,h)},s.prototype.outputPdf=function(c,h){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){return this.prop.pdf.output(c,h)})},s.prototype.outputImg=function(c){return this.thenList([function(){return this.prop.img||this.toImg()}]).then(function(){switch(c){case void 0:case"img":return this.prop.img;case"datauristring":case"dataurlstring":return this.prop.img.src;case"datauri":case"dataurl":return document.location.href=this.prop.img.src;default:throw'Image output type "'+c+'" is not supported.'}})},s.prototype.save=function(c){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).set(c?{filename:c}:null).then(function(){this.prop.pdf.save(this.opt.filename)})},s.prototype.doCallback=function(){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){this.prop.callback(this.prop.pdf)})},s.prototype.set=function(c){if(a(c)!=="object")return this;var h=Object.keys(c||{}).map(function(f){if(f in s.template.prop)return function(){this.prop[f]=c[f]};switch(f){case"margin":return this.setMargin.bind(this,c.margin);case"jsPDF":return function(){return this.opt.jsPDF=c.jsPDF,this.setPageSize()};case"pageSize":return this.setPageSize.bind(this,c.pageSize);default:return function(){this.opt[f]=c[f]}}},this);return this.then(function(){return this.thenList(h)})},s.prototype.get=function(c,h){return this.then(function(){var f=c in s.template.prop?this.prop[c]:this.opt[c];return h?h(f):f})},s.prototype.setMargin=function(c){return this.then(function(){switch(a(c)){case"number":c=[c,c,c,c];case"array":if(c.length===2&&(c=[c[0],c[1],c[0],c[1]]),c.length===4)break;default:return this.error("Invalid margin array.")}this.opt.margin=c}).then(this.setPageSize)},s.prototype.setPageSize=function(c){function h(f,p){return Math.floor(f*p/72*96)}return this.then(function(){(c=c||Vt.getPageSize(this.opt.jsPDF)).hasOwnProperty("inner")||(c.inner={width:c.width-this.opt.margin[1]-this.opt.margin[3],height:c.height-this.opt.margin[0]-this.opt.margin[2]},c.inner.px={width:h(c.inner.width,c.k),height:h(c.inner.height,c.k)},c.inner.ratio=c.inner.height/c.inner.width),this.prop.pageSize=c})},s.prototype.setProgress=function(c,h,f,p){return c!=null&&(this.progress.val=c),h!=null&&(this.progress.state=h),f!=null&&(this.progress.n=f),p!=null&&(this.progress.stack=p),this.progress.ratio=this.progress.val/this.progress.state,this},s.prototype.updateProgress=function(c,h,f,p){return this.setProgress(c?this.progress.val+c:null,h||null,f?this.progress.n+f:null,p?this.progress.stack.concat(p):null)},s.prototype.then=function(c,h){var f=this;return this.thenCore(c,h,function(p,w){return f.updateProgress(null,null,1,[p]),Promise.prototype.then.call(this,function(L){return f.updateProgress(null,p),L}).then(p,w).then(function(L){return f.updateProgress(1),L})})},s.prototype.thenCore=function(c,h,f){f=f||Promise.prototype.then,c&&(c=c.bind(this)),h&&(h=h.bind(this));var p=Promise.toString().indexOf("[native code]")!==-1&&Promise.name==="Promise"?this:s.convert(Object.assign({},this),Promise.prototype),w=f.call(p,c,h);return s.convert(w,this.__proto__)},s.prototype.thenExternal=function(c,h){return Promise.prototype.then.call(this,c,h)},s.prototype.thenList=function(c){var h=this;return c.forEach(function(f){h=h.thenCore(f)}),h},s.prototype.catch=function(c){c&&(c=c.bind(this));var h=Promise.prototype.catch.call(this,c);return s.convert(h,this)},s.prototype.catchExternal=function(c){return Promise.prototype.catch.call(this,c)},s.prototype.error=function(c){return this.then(function(){throw new Error(c)})},s.prototype.using=s.prototype.set,s.prototype.saveAs=s.prototype.save,s.prototype.export=s.prototype.output,s.prototype.run=s.prototype.then,Vt.getPageSize=function(c,h,f){if(de(c)==="object"){var p=c;c=p.orientation,h=p.unit||h,f=p.format||f}h=h||"mm",f=f||"a4",c=(""+(c||"P")).toLowerCase();var w,L=(""+f).toLowerCase(),x={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};switch(h){case"pt":w=1;break;case"mm":w=72/25.4;break;case"cm":w=72/2.54;break;case"in":w=72;break;case"px":w=.75;break;case"pc":case"em":w=12;break;case"ex":w=6;break;default:throw"Invalid unit: "+h}var m,D=0,k=0;if(x.hasOwnProperty(L))D=x[L][1]/w,k=x[L][0]/w;else try{D=f[1],k=f[0]}catch{throw new Error("Invalid format: "+f)}if(c==="p"||c==="portrait")c="p",k>D&&(m=k,k=D,D=m);else{if(c!=="l"&&c!=="landscape")throw"Invalid orientation: "+c;c="l",D>k&&(m=k,k=D,D=m)}return{width:k,height:D,unit:h,k:w,orientation:c}},r.html=function(c,h){(h=h||{}).callback=h.callback||function(){},h.html2canvas=h.html2canvas||{},h.html2canvas.canvas=h.html2canvas.canvas||this.canvas,h.jsPDF=h.jsPDF||this,h.fontFaces=h.fontFaces?h.fontFaces.map(Ts):null;var f=new s(h);return h.worker?f:f.from(c).doCallback()}}(Vt.API),Vt.API.addJS=function(r){return Cl=r,this.internal.events.subscribe("postPutResources",function(){ho=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/Names [(EmbeddedJS) "+(ho+1)+" 0 R]"),this.internal.out(">>"),this.internal.out("endobj"),Pl=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /JavaScript"),this.internal.out("/JS ("+Cl+")"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){ho!==void 0&&Pl!==void 0&&this.internal.out("/Names <</JavaScript "+ho+" 0 R>>")}),this},function(r){var e;r.events.push(["postPutResources",function(){var n=this,a=/^(\d+) 0 obj$/;if(this.outline.root.children.length>0)for(var o=n.outline.render().split(/\r\n/),s=0;s<o.length;s++){var c=o[s],h=a.exec(c);if(h!=null){var f=h[1];n.internal.newObjectDeferredBegin(f,!1)}n.internal.write(c)}if(this.outline.createNamedDestinations){var p=this.internal.pages.length,w=[];for(s=0;s<p;s++){var L=n.internal.newObject();w.push(L);var x=n.internal.getPageInfo(s+1);n.internal.write("<< /D["+x.objId+" 0 R /XYZ null null null]>> endobj")}var m=n.internal.newObject();for(n.internal.write("<< /Names [ "),s=0;s<w.length;s++)n.internal.write("(page_"+(s+1)+")"+w[s]+" 0 R");n.internal.write(" ] >>","endobj"),e=n.internal.newObject(),n.internal.write("<< /Dests "+m+" 0 R"),n.internal.write(">>","endobj")}}]),r.events.push(["putCatalog",function(){this.outline.root.children.length>0&&(this.internal.write("/Outlines",this.outline.makeRef(this.outline.root)),this.outline.createNamedDestinations&&this.internal.write("/Names "+e+" 0 R"))}]),r.events.push(["initialized",function(){var n=this;n.outline={createNamedDestinations:!1,root:{children:[]}},n.outline.add=function(a,o,s){var c={title:o,options:s,children:[]};return a==null&&(a=this.root),a.children.push(c),c},n.outline.render=function(){return this.ctx={},this.ctx.val="",this.ctx.pdf=n,this.genIds_r(this.root),this.renderRoot(this.root),this.renderItems(this.root),this.ctx.val},n.outline.genIds_r=function(a){a.id=n.internal.newObjectDeferred();for(var o=0;o<a.children.length;o++)this.genIds_r(a.children[o])},n.outline.renderRoot=function(a){this.objStart(a),this.line("/Type /Outlines"),a.children.length>0&&(this.line("/First "+this.makeRef(a.children[0])),this.line("/Last "+this.makeRef(a.children[a.children.length-1]))),this.line("/Count "+this.count_r({count:0},a)),this.objEnd()},n.outline.renderItems=function(a){for(var o=this.ctx.pdf.internal.getVerticalCoordinateString,s=0;s<a.children.length;s++){var c=a.children[s];this.objStart(c),this.line("/Title "+this.makeString(c.title)),this.line("/Parent "+this.makeRef(a)),s>0&&this.line("/Prev "+this.makeRef(a.children[s-1])),s<a.children.length-1&&this.line("/Next "+this.makeRef(a.children[s+1])),c.children.length>0&&(this.line("/First "+this.makeRef(c.children[0])),this.line("/Last "+this.makeRef(c.children[c.children.length-1])));var h=this.count=this.count_r({count:0},c);if(h>0&&this.line("/Count "+h),c.options&&c.options.pageNumber){var f=n.internal.getPageInfo(c.options.pageNumber);this.line("/Dest ["+f.objId+" 0 R /XYZ 0 "+o(0)+" 0]")}this.objEnd()}for(var p=0;p<a.children.length;p++)this.renderItems(a.children[p])},n.outline.line=function(a){this.ctx.val+=a+`\r
`},n.outline.makeRef=function(a){return a.id+" 0 R"},n.outline.makeString=function(a){return"("+n.internal.pdfEscape(a)+")"},n.outline.objStart=function(a){this.ctx.val+=`\r
`+a.id+` 0 obj\r
<<\r
`},n.outline.objEnd=function(){this.ctx.val+=`>> \r
endobj\r
`},n.outline.count_r=function(a,o){for(var s=0;s<o.children.length;s++)a.count++,this.count_r(a,o.children[s]);return a.count}}])}(Vt.API),function(r){var e=[192,193,194,195,196,197,198,199];r.processJPEG=function(n,a,o,s,c,h){var f,p=this.decode.DCT_DECODE,w=null;if(typeof n=="string"||this.__addimage__.isArrayBuffer(n)||this.__addimage__.isArrayBufferView(n)){switch(n=c||n,n=this.__addimage__.isArrayBuffer(n)?new Uint8Array(n):n,(f=function(L){for(var x,m=256*L.charCodeAt(4)+L.charCodeAt(5),D=L.length,k={width:0,height:0,numcomponents:1},O=4;O<D;O+=2){if(O+=m,e.indexOf(L.charCodeAt(O+1))!==-1){x=256*L.charCodeAt(O+5)+L.charCodeAt(O+6),k={width:256*L.charCodeAt(O+7)+L.charCodeAt(O+8),height:x,numcomponents:L.charCodeAt(O+9)};break}m=256*L.charCodeAt(O+2)+L.charCodeAt(O+3)}return k}(n=this.__addimage__.isArrayBufferView(n)?this.__addimage__.arrayBufferToBinaryString(n):n)).numcomponents){case 1:h=this.color_spaces.DEVICE_GRAY;break;case 4:h=this.color_spaces.DEVICE_CMYK;break;case 3:h=this.color_spaces.DEVICE_RGB}w={data:n,width:f.width,height:f.height,colorSpace:h,bitsPerComponent:8,filter:p,index:a,alias:o}}return w}}(Vt.API);var xi,fo,Il,kl,Fl,mh=function(){var r,e,n;function a(s){var c,h,f,p,w,L,x,m,D,k,O,_,j,J;for(this.data=s,this.pos=8,this.palette=[],this.imgData=[],this.transparency={},this.animation=null,this.text={},L=null;;){switch(c=this.readUInt32(),D=(function(){var st,ft;for(ft=[],st=0;st<4;++st)ft.push(String.fromCharCode(this.data[this.pos++]));return ft}).call(this).join("")){case"IHDR":this.width=this.readUInt32(),this.height=this.readUInt32(),this.bits=this.data[this.pos++],this.colorType=this.data[this.pos++],this.compressionMethod=this.data[this.pos++],this.filterMethod=this.data[this.pos++],this.interlaceMethod=this.data[this.pos++];break;case"acTL":this.animation={numFrames:this.readUInt32(),numPlays:this.readUInt32()||1/0,frames:[]};break;case"PLTE":this.palette=this.read(c);break;case"fcTL":L&&this.animation.frames.push(L),this.pos+=4,L={width:this.readUInt32(),height:this.readUInt32(),xOffset:this.readUInt32(),yOffset:this.readUInt32()},w=this.readUInt16(),p=this.readUInt16()||100,L.delay=1e3*w/p,L.disposeOp=this.data[this.pos++],L.blendOp=this.data[this.pos++],L.data=[];break;case"IDAT":case"fdAT":for(D==="fdAT"&&(this.pos+=4,c-=4),s=(L!=null?L.data:void 0)||this.imgData,_=0;0<=c?_<c:_>c;0<=c?++_:--_)s.push(this.data[this.pos++]);break;case"tRNS":switch(this.transparency={},this.colorType){case 3:if(f=this.palette.length/3,this.transparency.indexed=this.read(c),this.transparency.indexed.length>f)throw new Error("More transparent colors than palette size");if((k=f-this.transparency.indexed.length)>0)for(j=0;0<=k?j<k:j>k;0<=k?++j:--j)this.transparency.indexed.push(255);break;case 0:this.transparency.grayscale=this.read(c)[0];break;case 2:this.transparency.rgb=this.read(c)}break;case"tEXt":x=(O=this.read(c)).indexOf(0),m=String.fromCharCode.apply(String,O.slice(0,x)),this.text[m]=String.fromCharCode.apply(String,O.slice(x+1));break;case"IEND":return L&&this.animation.frames.push(L),this.colors=(function(){switch(this.colorType){case 0:case 3:case 4:return 1;case 2:case 6:return 3}}).call(this),this.hasAlphaChannel=(J=this.colorType)===4||J===6,h=this.colors+(this.hasAlphaChannel?1:0),this.pixelBitlength=this.bits*h,this.colorSpace=(function(){switch(this.colors){case 1:return"DeviceGray";case 3:return"DeviceRGB"}}).call(this),void(this.imgData=new Uint8Array(this.imgData));default:this.pos+=c}if(this.pos+=4,this.pos>this.data.length)throw new Error("Incomplete or corrupt PNG file")}}a.prototype.read=function(s){var c,h;for(h=[],c=0;0<=s?c<s:c>s;0<=s?++c:--c)h.push(this.data[this.pos++]);return h},a.prototype.readUInt32=function(){return this.data[this.pos++]<<24|this.data[this.pos++]<<16|this.data[this.pos++]<<8|this.data[this.pos++]},a.prototype.readUInt16=function(){return this.data[this.pos++]<<8|this.data[this.pos++]},a.prototype.decodePixels=function(s){var c=this.pixelBitlength/8,h=new Uint8Array(this.width*this.height*c),f=0,p=this;if(s==null&&(s=this.imgData),s.length===0)return new Uint8Array(0);function w(L,x,m,D){var k,O,_,j,J,st,ft,At,rt,G,vt,bt,C,I,z,R,lt,ot,mt,tt,pt,ut=Math.ceil((p.width-L)/m),Ot=Math.ceil((p.height-x)/D),N=p.width==ut&&p.height==Ot;for(I=c*ut,bt=N?h:new Uint8Array(I*Ot),st=s.length,C=0,O=0;C<Ot&&f<st;){switch(s[f++]){case 0:for(j=lt=0;lt<I;j=lt+=1)bt[O++]=s[f++];break;case 1:for(j=ot=0;ot<I;j=ot+=1)k=s[f++],J=j<c?0:bt[O-c],bt[O++]=(k+J)%256;break;case 2:for(j=mt=0;mt<I;j=mt+=1)k=s[f++],_=(j-j%c)/c,z=C&&bt[(C-1)*I+_*c+j%c],bt[O++]=(z+k)%256;break;case 3:for(j=tt=0;tt<I;j=tt+=1)k=s[f++],_=(j-j%c)/c,J=j<c?0:bt[O-c],z=C&&bt[(C-1)*I+_*c+j%c],bt[O++]=(k+Math.floor((J+z)/2))%256;break;case 4:for(j=pt=0;pt<I;j=pt+=1)k=s[f++],_=(j-j%c)/c,J=j<c?0:bt[O-c],C===0?z=R=0:(z=bt[(C-1)*I+_*c+j%c],R=_&&bt[(C-1)*I+(_-1)*c+j%c]),ft=J+z-R,At=Math.abs(ft-J),G=Math.abs(ft-z),vt=Math.abs(ft-R),rt=At<=G&&At<=vt?J:G<=vt?z:R,bt[O++]=(k+rt)%256;break;default:throw new Error("Invalid filter algorithm: "+s[f-1])}if(!N){var T=((x+C*D)*p.width+L)*c,E=C*I;for(j=0;j<ut;j+=1){for(var V=0;V<c;V+=1)h[T++]=bt[E++];T+=(m-1)*c}}C++}}return s=du(s),p.interlaceMethod==1?(w(0,0,8,8),w(4,0,8,8),w(0,4,4,8),w(2,0,4,4),w(0,2,2,4),w(1,0,2,2),w(0,1,1,2)):w(0,0,1,1),h},a.prototype.decodePalette=function(){var s,c,h,f,p,w,L,x,m;for(h=this.palette,w=this.transparency.indexed||[],p=new Uint8Array((w.length||0)+h.length),f=0,s=0,c=L=0,x=h.length;L<x;c=L+=3)p[f++]=h[c],p[f++]=h[c+1],p[f++]=h[c+2],p[f++]=(m=w[s++])!=null?m:255;return p},a.prototype.copyToImageData=function(s,c){var h,f,p,w,L,x,m,D,k,O,_;if(f=this.colors,k=null,h=this.hasAlphaChannel,this.palette.length&&(k=(_=this._decodedPalette)!=null?_:this._decodedPalette=this.decodePalette(),f=4,h=!0),D=(p=s.data||s).length,L=k||c,w=x=0,f===1)for(;w<D;)m=k?4*c[w/4]:x,O=L[m++],p[w++]=O,p[w++]=O,p[w++]=O,p[w++]=h?L[m++]:255,x=m;else for(;w<D;)m=k?4*c[w/4]:x,p[w++]=L[m++],p[w++]=L[m++],p[w++]=L[m++],p[w++]=h?L[m++]:255,x=m},a.prototype.decode=function(){var s;return s=new Uint8Array(this.width*this.height*4),this.copyToImageData(s,this.decodePixels()),s};var o=function(){if(Object.prototype.toString.call(zt)==="[object Window]"){try{e=zt.document.createElement("canvas"),n=e.getContext("2d")}catch{return!1}return!0}return!1};return o(),r=function(s){var c;if(o()===!0)return n.width=s.width,n.height=s.height,n.clearRect(0,0,s.width,s.height),n.putImageData(s,0,0),(c=new Image).src=e.toDataURL(),c;throw new Error("This method requires a Browser with Canvas-capability.")},a.prototype.decodeFrames=function(s){var c,h,f,p,w,L,x,m;if(this.animation){for(m=[],h=w=0,L=(x=this.animation.frames).length;w<L;h=++w)c=x[h],f=s.createImageData(c.width,c.height),p=this.decodePixels(new Uint8Array(c.data)),this.copyToImageData(f,p),c.imageData=f,m.push(c.image=r(f));return m}},a.prototype.renderFrame=function(s,c){var h,f,p;return h=(f=this.animation.frames)[c],p=f[c-1],c===0&&s.clearRect(0,0,this.width,this.height),(p!=null?p.disposeOp:void 0)===1?s.clearRect(p.xOffset,p.yOffset,p.width,p.height):(p!=null?p.disposeOp:void 0)===2&&s.putImageData(p.imageData,p.xOffset,p.yOffset),h.blendOp===0&&s.clearRect(h.xOffset,h.yOffset,h.width,h.height),s.drawImage(h.image,h.xOffset,h.yOffset)},a.prototype.animate=function(s){var c,h,f,p,w,L,x=this;return h=0,L=this.animation,p=L.numFrames,f=L.frames,w=L.numPlays,(c=function(){var m,D;if(m=h++%p,D=f[m],x.renderFrame(s,m),p>1&&h/p<w)return x.animation._timeout=setTimeout(c,D.delay)})()},a.prototype.stopAnimation=function(){var s;return clearTimeout((s=this.animation)!=null?s._timeout:void 0)},a.prototype.render=function(s){var c,h;return s._png&&s._png.stopAnimation(),s._png=this,s.width=this.width,s.height=this.height,c=s.getContext("2d"),this.animation?(this.decodeFrames(c),this.animate(c)):(h=c.createImageData(this.width,this.height),this.copyToImageData(h,this.decodePixels()),c.putImageData(h,0,0))},a}();/**
 * @license
 *
 * Copyright (c) 2014 James Robb, https://github.com/jamesbrobb
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 * ====================================================================
 *//**
 * @license
 * (c) Dean McNamee <<EMAIL>>, 2013.
 *
 * https://github.com/deanm/omggif
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to
 * deal in the Software without restriction, including without limitation the
 * rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
 * sell copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
 * IN THE SOFTWARE.
 *
 * omggif is a JavaScript implementation of a GIF 89a encoder and decoder,
 * including animation and compression.  It does not rely on any specific
 * underlying system, so should run in the browser, Node, or Plask.
 */function gh(r){var e=0;if(r[e++]!==71||r[e++]!==73||r[e++]!==70||r[e++]!==56||(r[e++]+1&253)!=56||r[e++]!==97)throw new Error("Invalid GIF 87a/89a header.");var n=r[e++]|r[e++]<<8,a=r[e++]|r[e++]<<8,o=r[e++],s=o>>7,c=1<<(7&o)+1;r[e++],r[e++];var h=null,f=null;s&&(h=e,f=c,e+=3*c);var p=!0,w=[],L=0,x=null,m=0,D=null;for(this.width=n,this.height=a;p&&e<r.length;)switch(r[e++]){case 33:switch(r[e++]){case 255:if(r[e]!==11||r[e+1]==78&&r[e+2]==69&&r[e+3]==84&&r[e+4]==83&&r[e+5]==67&&r[e+6]==65&&r[e+7]==80&&r[e+8]==69&&r[e+9]==50&&r[e+10]==46&&r[e+11]==48&&r[e+12]==3&&r[e+13]==1&&r[e+16]==0)e+=14,D=r[e++]|r[e++]<<8,e++;else for(e+=12;;){if(!((C=r[e++])>=0))throw Error("Invalid block size");if(C===0)break;e+=C}break;case 249:if(r[e++]!==4||r[e+4]!==0)throw new Error("Invalid graphics extension block.");var k=r[e++];L=r[e++]|r[e++]<<8,x=r[e++],!(1&k)&&(x=null),m=k>>2&7,e++;break;case 254:for(;;){if(!((C=r[e++])>=0))throw Error("Invalid block size");if(C===0)break;e+=C}break;default:throw new Error("Unknown graphic control label: 0x"+r[e-1].toString(16))}break;case 44:var O=r[e++]|r[e++]<<8,_=r[e++]|r[e++]<<8,j=r[e++]|r[e++]<<8,J=r[e++]|r[e++]<<8,st=r[e++],ft=st>>6&1,At=1<<(7&st)+1,rt=h,G=f,vt=!1;st>>7&&(vt=!0,rt=e,G=At,e+=3*At);var bt=e;for(e++;;){var C;if(!((C=r[e++])>=0))throw Error("Invalid block size");if(C===0)break;e+=C}w.push({x:O,y:_,width:j,height:J,has_local_palette:vt,palette_offset:rt,palette_size:G,data_offset:bt,data_length:e-bt,transparent_index:x,interlaced:!!ft,delay:L,disposal:m});break;case 59:p=!1;break;default:throw new Error("Unknown gif block: 0x"+r[e-1].toString(16))}this.numFrames=function(){return w.length},this.loopCount=function(){return D},this.frameInfo=function(I){if(I<0||I>=w.length)throw new Error("Frame index out of range.");return w[I]},this.decodeAndBlitFrameBGRA=function(I,z){var R=this.frameInfo(I),lt=R.width*R.height,ot=new Uint8Array(lt);Tl(r,R.data_offset,ot,lt);var mt=R.palette_offset,tt=R.transparent_index;tt===null&&(tt=256);var pt=R.width,ut=n-pt,Ot=pt,N=4*(R.y*n+R.x),T=4*((R.y+R.height)*n+R.x),E=N,V=4*ut;R.interlaced===!0&&(V+=4*n*7);for(var Y=8,Q=0,et=ot.length;Q<et;++Q){var Z=ot[Q];if(Ot===0&&(Ot=pt,(E+=V)>=T&&(V=4*ut+4*n*(Y-1),E=N+(pt+ut)*(Y<<1),Y>>=1)),Z===tt)E+=4;else{var Lt=r[mt+3*Z],Nt=r[mt+3*Z+1],Tt=r[mt+3*Z+2];z[E++]=Tt,z[E++]=Nt,z[E++]=Lt,z[E++]=255}--Ot}},this.decodeAndBlitFrameRGBA=function(I,z){var R=this.frameInfo(I),lt=R.width*R.height,ot=new Uint8Array(lt);Tl(r,R.data_offset,ot,lt);var mt=R.palette_offset,tt=R.transparent_index;tt===null&&(tt=256);var pt=R.width,ut=n-pt,Ot=pt,N=4*(R.y*n+R.x),T=4*((R.y+R.height)*n+R.x),E=N,V=4*ut;R.interlaced===!0&&(V+=4*n*7);for(var Y=8,Q=0,et=ot.length;Q<et;++Q){var Z=ot[Q];if(Ot===0&&(Ot=pt,(E+=V)>=T&&(V=4*ut+4*n*(Y-1),E=N+(pt+ut)*(Y<<1),Y>>=1)),Z===tt)E+=4;else{var Lt=r[mt+3*Z],Nt=r[mt+3*Z+1],Tt=r[mt+3*Z+2];z[E++]=Lt,z[E++]=Nt,z[E++]=Tt,z[E++]=255}--Ot}}}function Tl(r,e,n,a){for(var o=r[e++],s=1<<o,c=s+1,h=c+1,f=o+1,p=(1<<f)-1,w=0,L=0,x=0,m=r[e++],D=new Int32Array(4096),k=null;;){for(;w<16&&m!==0;)L|=r[e++]<<w,w+=8,m===1?m=r[e++]:--m;if(w<f)break;var O=L&p;if(L>>=f,w-=f,O!==s){if(O===c)break;for(var _=O<h?O:k,j=0,J=_;J>s;)J=D[J]>>8,++j;var st=J;if(x+j+(_!==O?1:0)>a)return void be.log("Warning, gif stream longer than expected.");n[x++]=st;var ft=x+=j;for(_!==O&&(n[x++]=st),J=_;j--;)J=D[J],n[--ft]=255&J,J>>=8;k!==null&&h<4096&&(D[h++]=k<<8|st,h>=p+1&&f<12&&(++f,p=p<<1|1)),k=O}else h=c+1,p=(1<<(f=o+1))-1,k=null}return x!==a&&be.log("Warning, gif stream shorter than expected."),n}/**
 * @license
  Copyright (c) 2008, Adobe Systems Incorporated
  All rights reserved.

  Redistribution and use in source and binary forms, with or without 
  modification, are permitted provided that the following conditions are
  met:

  * Redistributions of source code must retain the above copyright notice, 
    this list of conditions and the following disclaimer.
  
  * Redistributions in binary form must reproduce the above copyright
    notice, this list of conditions and the following disclaimer in the 
    documentation and/or other materials provided with the distribution.
  
  * Neither the name of Adobe Systems Incorporated nor the names of its 
    contributors may be used to endorse or promote products derived from 
    this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
  IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
  THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
  PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR 
  CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
  PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
  LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
  NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/function Ls(r){var e,n,a,o,s,c=Math.floor,h=new Array(64),f=new Array(64),p=new Array(64),w=new Array(64),L=new Array(65535),x=new Array(65535),m=new Array(64),D=new Array(64),k=[],O=0,_=7,j=new Array(64),J=new Array(64),st=new Array(64),ft=new Array(256),At=new Array(2048),rt=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],G=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],vt=[0,1,2,3,4,5,6,7,8,9,10,11],bt=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],C=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],I=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],z=[0,1,2,3,4,5,6,7,8,9,10,11],R=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],lt=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function ot(N,T){for(var E=0,V=0,Y=new Array,Q=1;Q<=16;Q++){for(var et=1;et<=N[Q];et++)Y[T[V]]=[],Y[T[V]][0]=E,Y[T[V]][1]=Q,V++,E++;E*=2}return Y}function mt(N){for(var T=N[0],E=N[1]-1;E>=0;)T&1<<E&&(O|=1<<_),E--,--_<0&&(O==255?(tt(255),tt(0)):tt(O),_=7,O=0)}function tt(N){k.push(N)}function pt(N){tt(N>>8&255),tt(255&N)}function ut(N,T,E,V,Y){for(var Q,et=Y[0],Z=Y[240],Lt=function(wt,xt){var Ct,_t,Bt,Wt,Jt,te,ie,pe,Gt,ne,Ft=0;for(Gt=0;Gt<8;++Gt){Ct=wt[Ft],_t=wt[Ft+1],Bt=wt[Ft+2],Wt=wt[Ft+3],Jt=wt[Ft+4],te=wt[Ft+5],ie=wt[Ft+6];var Ke=Ct+(pe=wt[Ft+7]),se=Ct-pe,Pn=_t+ie,ge=_t-ie,Ae=Bt+te,Un=Bt-te,ce=Wt+Jt,kr=Wt-Jt,Se=Ke+ce,Cn=Ke-ce,er=Pn+Ae,_e=Pn-Ae;wt[Ft]=Se+er,wt[Ft+4]=Se-er;var Kt=.707106781*(_e+Cn);wt[Ft+2]=Cn+Kt,wt[Ft+6]=Cn-Kt;var ue=.382683433*((Se=kr+Un)-(_e=ge+se)),Fr=.5411961*Se+ue,Ye=1.306562965*_e+ue,zn=.707106781*(er=Un+ge),Hn=se+zn,Ut=se-zn;wt[Ft+5]=Ut+Fr,wt[Ft+3]=Ut-Fr,wt[Ft+1]=Hn+Ye,wt[Ft+7]=Hn-Ye,Ft+=8}for(Ft=0,Gt=0;Gt<8;++Gt){Ct=wt[Ft],_t=wt[Ft+8],Bt=wt[Ft+16],Wt=wt[Ft+24],Jt=wt[Ft+32],te=wt[Ft+40],ie=wt[Ft+48];var In=Ct+(pe=wt[Ft+56]),Vn=Ct-pe,sn=_t+ie,Ue=_t-ie,Ee=Bt+te,pn=Bt-te,Yr=Wt+Jt,nr=Wt-Jt,kn=In+Yr,Fn=In-Yr,Tn=sn+Ee,Wn=sn-Ee;wt[Ft]=kn+Tn,wt[Ft+32]=kn-Tn;var yn=.707106781*(Wn+Fn);wt[Ft+16]=Fn+yn,wt[Ft+48]=Fn-yn;var Gn=.382683433*((kn=nr+pn)-(Wn=Ue+Vn)),Tr=.5411961*kn+Gn,$r=1.306562965*Wn+Gn,Jr=.707106781*(Tn=pn+Ue),Kr=Vn+Jr,Xr=Vn-Jr;wt[Ft+40]=Xr+Tr,wt[Ft+24]=Xr-Tr,wt[Ft+8]=Kr+$r,wt[Ft+56]=Kr-$r,Ft++}for(Gt=0;Gt<64;++Gt)ne=wt[Gt]*xt[Gt],m[Gt]=ne>0?ne+.5|0:ne-.5|0;return m}(N,T),Nt=0;Nt<64;++Nt)D[rt[Nt]]=Lt[Nt];var Tt=D[0]-E;E=D[0],Tt==0?mt(V[0]):(mt(V[x[Q=32767+Tt]]),mt(L[Q]));for(var kt=63;kt>0&&D[kt]==0;)kt--;if(kt==0)return mt(et),E;for(var Ht,it=1;it<=kt;){for(var M=it;D[it]==0&&it<=kt;)++it;var Yt=it-M;if(Yt>=16){Ht=Yt>>4;for(var Et=1;Et<=Ht;++Et)mt(Z);Yt&=15}Q=32767+D[it],mt(Y[(Yt<<4)+x[Q]]),mt(L[Q]),it++}return kt!=63&&mt(et),E}function Ot(N){N=Math.min(Math.max(N,1),100),s!=N&&(function(T){for(var E=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],V=0;V<64;V++){var Y=c((E[V]*T+50)/100);Y=Math.min(Math.max(Y,1),255),h[rt[V]]=Y}for(var Q=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],et=0;et<64;et++){var Z=c((Q[et]*T+50)/100);Z=Math.min(Math.max(Z,1),255),f[rt[et]]=Z}for(var Lt=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],Nt=0,Tt=0;Tt<8;Tt++)for(var kt=0;kt<8;kt++)p[Nt]=1/(h[rt[Nt]]*Lt[Tt]*Lt[kt]*8),w[Nt]=1/(f[rt[Nt]]*Lt[Tt]*Lt[kt]*8),Nt++}(N<50?Math.floor(5e3/N):Math.floor(200-2*N)),s=N)}this.encode=function(N,T){T&&Ot(T),k=new Array,O=0,_=7,pt(65496),pt(65504),pt(16),tt(74),tt(70),tt(73),tt(70),tt(0),tt(1),tt(1),tt(0),pt(1),pt(1),tt(0),tt(0),function(){pt(65499),pt(132),tt(0);for(var _t=0;_t<64;_t++)tt(h[_t]);tt(1);for(var Bt=0;Bt<64;Bt++)tt(f[Bt])}(),function(_t,Bt){pt(65472),pt(17),tt(8),pt(Bt),pt(_t),tt(3),tt(1),tt(17),tt(0),tt(2),tt(17),tt(1),tt(3),tt(17),tt(1)}(N.width,N.height),function(){pt(65476),pt(418),tt(0);for(var _t=0;_t<16;_t++)tt(G[_t+1]);for(var Bt=0;Bt<=11;Bt++)tt(vt[Bt]);tt(16);for(var Wt=0;Wt<16;Wt++)tt(bt[Wt+1]);for(var Jt=0;Jt<=161;Jt++)tt(C[Jt]);tt(1);for(var te=0;te<16;te++)tt(I[te+1]);for(var ie=0;ie<=11;ie++)tt(z[ie]);tt(17);for(var pe=0;pe<16;pe++)tt(R[pe+1]);for(var Gt=0;Gt<=161;Gt++)tt(lt[Gt])}(),pt(65498),pt(12),tt(3),tt(1),tt(0),tt(2),tt(17),tt(3),tt(17),tt(0),tt(63),tt(0);var E=0,V=0,Y=0;O=0,_=7,this.encode.displayName="_encode_";for(var Q,et,Z,Lt,Nt,Tt,kt,Ht,it,M=N.data,Yt=N.width,Et=N.height,wt=4*Yt,xt=0;xt<Et;){for(Q=0;Q<wt;){for(Nt=wt*xt+Q,kt=-1,Ht=0,it=0;it<64;it++)Tt=Nt+(Ht=it>>3)*wt+(kt=4*(7&it)),xt+Ht>=Et&&(Tt-=wt*(xt+1+Ht-Et)),Q+kt>=wt&&(Tt-=Q+kt-wt+4),et=M[Tt++],Z=M[Tt++],Lt=M[Tt++],j[it]=(At[et]+At[Z+256>>0]+At[Lt+512>>0]>>16)-128,J[it]=(At[et+768>>0]+At[Z+1024>>0]+At[Lt+1280>>0]>>16)-128,st[it]=(At[et+1280>>0]+At[Z+1536>>0]+At[Lt+1792>>0]>>16)-128;E=ut(j,p,E,e,a),V=ut(J,w,V,n,o),Y=ut(st,w,Y,n,o),Q+=32}xt+=8}if(_>=0){var Ct=[];Ct[1]=_+1,Ct[0]=(1<<_+1)-1,mt(Ct)}return pt(65497),new Uint8Array(k)},r=r||50,function(){for(var N=String.fromCharCode,T=0;T<256;T++)ft[T]=N(T)}(),e=ot(G,vt),n=ot(I,z),a=ot(bt,C),o=ot(R,lt),function(){for(var N=1,T=2,E=1;E<=15;E++){for(var V=N;V<T;V++)x[32767+V]=E,L[32767+V]=[],L[32767+V][1]=E,L[32767+V][0]=V;for(var Y=-(T-1);Y<=-N;Y++)x[32767+Y]=E,L[32767+Y]=[],L[32767+Y][1]=E,L[32767+Y][0]=T-1+Y;N<<=1,T<<=1}}(),function(){for(var N=0;N<256;N++)At[N]=19595*N,At[N+256>>0]=38470*N,At[N+512>>0]=7471*N+32768,At[N+768>>0]=-11059*N,At[N+1024>>0]=-21709*N,At[N+1280>>0]=32768*N+8421375,At[N+1536>>0]=-27439*N,At[N+1792>>0]=-5329*N}(),Ot(r)}/**
 * @license
 * Copyright (c) 2017 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */function Bn(r,e){if(this.pos=0,this.buffer=r,this.datav=new DataView(r.buffer),this.is_with_alpha=!!e,this.bottom_up=!0,this.flag=String.fromCharCode(this.buffer[0])+String.fromCharCode(this.buffer[1]),this.pos+=2,["BM","BA","CI","CP","IC","PT"].indexOf(this.flag)===-1)throw new Error("Invalid BMP File");this.parseHeader(),this.parseBGR()}function Dl(r){function e(G){if(!G)throw Error("assert :P")}function n(G,vt,bt){for(var C=0;4>C;C++)if(G[vt+C]!=bt.charCodeAt(C))return!0;return!1}function a(G,vt,bt,C,I){for(var z=0;z<I;z++)G[vt+z]=bt[C+z]}function o(G,vt,bt,C){for(var I=0;I<C;I++)G[vt+I]=bt}function s(G){return new Int32Array(G)}function c(G,vt){for(var bt=[],C=0;C<G;C++)bt.push(new vt);return bt}function h(G,vt){var bt=[];return function C(I,z,R){for(var lt=R[z],ot=0;ot<lt&&(I.push(R.length>z+1?[]:new vt),!(R.length<z+1));ot++)C(I[ot],z+1,R)}(bt,0,G),bt}var f=function(){var G=this;function vt(t,i){for(var u=1<<i-1>>>0;t&u;)u>>>=1;return u?(t&u-1)+u:t}function bt(t,i,u,d,g){e(!(d%u));do t[i+(d-=u)]=g;while(0<d)}function C(t,i,u,d,g){if(e(2328>=g),512>=g)var b=s(512);else if((b=s(g))==null)return 0;return function(y,A,S,P,q,K){var X,W,dt=A,nt=1<<S,U=s(16),H=s(16);for(e(q!=0),e(P!=null),e(y!=null),e(0<S),W=0;W<q;++W){if(15<P[W])return 0;++U[P[W]]}if(U[0]==q)return 0;for(H[1]=0,X=1;15>X;++X){if(U[X]>1<<X)return 0;H[X+1]=H[X]+U[X]}for(W=0;W<q;++W)X=P[W],0<P[W]&&(K[H[X]++]=W);if(H[15]==1)return(P=new I).g=0,P.value=K[0],bt(y,dt,1,nt,P),nt;var ct,gt=-1,ht=nt-1,Mt=0,St=1,qt=1,Pt=1<<S;for(W=0,X=1,q=2;X<=S;++X,q<<=1){if(St+=qt<<=1,0>(qt-=U[X]))return 0;for(;0<U[X];--U[X])(P=new I).g=X,P.value=K[W++],bt(y,dt+Mt,q,Pt,P),Mt=vt(Mt,X)}for(X=S+1,q=2;15>=X;++X,q<<=1){if(St+=qt<<=1,0>(qt-=U[X]))return 0;for(;0<U[X];--U[X]){if(P=new I,(Mt&ht)!=gt){for(dt+=Pt,ct=1<<(gt=X)-S;15>gt&&!(0>=(ct-=U[gt]));)++gt,ct<<=1;nt+=Pt=1<<(ct=gt-S),y[A+(gt=Mt&ht)].g=ct+S,y[A+gt].value=dt-A-gt}P.g=X-S,P.value=K[W++],bt(y,dt+(Mt>>S),q,Pt,P),Mt=vt(Mt,X)}}return St!=2*H[15]-1?0:nt}(t,i,u,d,g,b)}function I(){this.value=this.g=0}function z(){this.value=this.g=0}function R(){this.G=c(5,I),this.H=s(5),this.jc=this.Qb=this.qb=this.nd=0,this.pd=c($e,z)}function lt(t,i,u,d){e(t!=null),e(i!=null),e(2147483648>d),t.Ca=254,t.I=0,t.b=-8,t.Ka=0,t.oa=i,t.pa=u,t.Jd=i,t.Yc=u+d,t.Zc=4<=d?u+d-4+1:u,Q(t)}function ot(t,i){for(var u=0;0<i--;)u|=Z(t,128)<<i;return u}function mt(t,i){var u=ot(t,i);return et(t)?-u:u}function tt(t,i,u,d){var g,b=0;for(e(t!=null),e(i!=null),e(4294967288>d),t.Sb=d,t.Ra=0,t.u=0,t.h=0,4<d&&(d=4),g=0;g<d;++g)b+=i[u+g]<<8*g;t.Ra=b,t.bb=d,t.oa=i,t.pa=u}function pt(t){for(;8<=t.u&&t.bb<t.Sb;)t.Ra>>>=8,t.Ra+=t.oa[t.pa+t.bb]<<fi-8>>>0,++t.bb,t.u-=8;E(t)&&(t.h=1,t.u=0)}function ut(t,i){if(e(0<=i),!t.h&&i<=hi){var u=T(t)&ui[i];return t.u+=i,pt(t),u}return t.h=1,t.u=0}function Ot(){this.b=this.Ca=this.I=0,this.oa=[],this.pa=0,this.Jd=[],this.Yc=0,this.Zc=[],this.Ka=0}function N(){this.Ra=0,this.oa=[],this.h=this.u=this.bb=this.Sb=this.pa=0}function T(t){return t.Ra>>>(t.u&fi-1)>>>0}function E(t){return e(t.bb<=t.Sb),t.h||t.bb==t.Sb&&t.u>fi}function V(t,i){t.u=i,t.h=E(t)}function Y(t){t.u>=ta&&(e(t.u>=ta),pt(t))}function Q(t){e(t!=null&&t.oa!=null),t.pa<t.Zc?(t.I=(t.oa[t.pa++]|t.I<<8)>>>0,t.b+=8):(e(t!=null&&t.oa!=null),t.pa<t.Yc?(t.b+=8,t.I=t.oa[t.pa++]|t.I<<8):t.Ka?t.b=0:(t.I<<=8,t.b+=8,t.Ka=1))}function et(t){return ot(t,1)}function Z(t,i){var u=t.Ca;0>t.b&&Q(t);var d=t.b,g=u*i>>>8,b=(t.I>>>d>g)+0;for(b?(u-=g,t.I-=g+1<<d>>>0):u=g+1,d=u,g=0;256<=d;)g+=8,d>>=8;return d=7^g+cn[d],t.b-=d,t.Ca=(u<<d)-1,b}function Lt(t,i,u){t[i+0]=u>>24&255,t[i+1]=u>>16&255,t[i+2]=u>>8&255,t[i+3]=u>>0&255}function Nt(t,i){return t[i+0]<<0|t[i+1]<<8}function Tt(t,i){return Nt(t,i)|t[i+2]<<16}function kt(t,i){return Nt(t,i)|Nt(t,i+2)<<16}function Ht(t,i){var u=1<<i;return e(t!=null),e(0<i),t.X=s(u),t.X==null?0:(t.Mb=32-i,t.Xa=i,1)}function it(t,i){e(t!=null),e(i!=null),e(t.Xa==i.Xa),a(i.X,0,t.X,0,1<<i.Xa)}function M(){this.X=[],this.Xa=this.Mb=0}function Yt(t,i,u,d){e(u!=null),e(d!=null);var g=u[0],b=d[0];return g==0&&(g=(t*b+i/2)/i),b==0&&(b=(i*g+t/2)/t),0>=g||0>=b?0:(u[0]=g,d[0]=b,1)}function Et(t,i){return t+(1<<i)-1>>>i}function wt(t,i){return((4278255360&t)+(4278255360&i)>>>0&4278255360)+((16711935&t)+(16711935&i)>>>0&16711935)>>>0}function xt(t,i){G[i]=function(u,d,g,b,y,A,S){var P;for(P=0;P<y;++P){var q=G[t](A[S+P-1],g,b+P);A[S+P]=wt(u[d+P],q)}}}function Ct(){this.ud=this.hd=this.jd=0}function _t(t,i){return((4278124286&(t^i))>>>1)+(t&i)>>>0}function Bt(t){return 0<=t&&256>t?t:0>t?0:255<t?255:void 0}function Wt(t,i){return Bt(t+(t-i+.5>>1))}function Jt(t,i,u){return Math.abs(i-u)-Math.abs(t-u)}function te(t,i,u,d,g,b,y){for(d=b[y-1],u=0;u<g;++u)b[y+u]=d=wt(t[i+u],d)}function ie(t,i,u,d,g){var b;for(b=0;b<u;++b){var y=t[i+b],A=y>>8&255,S=16711935&(S=(S=16711935&y)+((A<<16)+A));d[g+b]=(4278255360&y)+S>>>0}}function pe(t,i){i.jd=t>>0&255,i.hd=t>>8&255,i.ud=t>>16&255}function Gt(t,i,u,d,g,b){var y;for(y=0;y<d;++y){var A=i[u+y],S=A>>>8,P=A,q=255&(q=(q=A>>>16)+((t.jd<<24>>24)*(S<<24>>24)>>>5));P=255&(P=(P=P+((t.hd<<24>>24)*(S<<24>>24)>>>5))+((t.ud<<24>>24)*(q<<24>>24)>>>5)),g[b+y]=(4278255360&A)+(q<<16)+P}}function ne(t,i,u,d,g){G[i]=function(b,y,A,S,P,q,K,X,W){for(S=K;S<X;++S)for(K=0;K<W;++K)P[q++]=g(A[d(b[y++])])},G[t]=function(b,y,A,S,P,q,K){var X=8>>b.b,W=b.Ea,dt=b.K[0],nt=b.w;if(8>X)for(b=(1<<b.b)-1,nt=(1<<X)-1;y<A;++y){var U,H=0;for(U=0;U<W;++U)U&b||(H=d(S[P++])),q[K++]=g(dt[H&nt]),H>>=X}else G["VP8LMapColor"+u](S,P,dt,nt,q,K,y,A,W)}}function Ft(t,i,u,d,g){for(u=i+u;i<u;){var b=t[i++];d[g++]=b>>16&255,d[g++]=b>>8&255,d[g++]=b>>0&255}}function Ke(t,i,u,d,g){for(u=i+u;i<u;){var b=t[i++];d[g++]=b>>16&255,d[g++]=b>>8&255,d[g++]=b>>0&255,d[g++]=b>>24&255}}function se(t,i,u,d,g){for(u=i+u;i<u;){var b=(y=t[i++])>>16&240|y>>12&15,y=y>>0&240|y>>28&15;d[g++]=b,d[g++]=y}}function Pn(t,i,u,d,g){for(u=i+u;i<u;){var b=(y=t[i++])>>16&248|y>>13&7,y=y>>5&224|y>>3&31;d[g++]=b,d[g++]=y}}function ge(t,i,u,d,g){for(u=i+u;i<u;){var b=t[i++];d[g++]=b>>0&255,d[g++]=b>>8&255,d[g++]=b>>16&255}}function Ae(t,i,u,d,g,b){if(b==0)for(u=i+u;i<u;)Lt(d,((b=t[i++])[0]>>24|b[1]>>8&65280|b[2]<<8&16711680|b[3]<<24)>>>0),g+=32;else a(d,g,t,i,u)}function Un(t,i){G[i][0]=G[t+"0"],G[i][1]=G[t+"1"],G[i][2]=G[t+"2"],G[i][3]=G[t+"3"],G[i][4]=G[t+"4"],G[i][5]=G[t+"5"],G[i][6]=G[t+"6"],G[i][7]=G[t+"7"],G[i][8]=G[t+"8"],G[i][9]=G[t+"9"],G[i][10]=G[t+"10"],G[i][11]=G[t+"11"],G[i][12]=G[t+"12"],G[i][13]=G[t+"13"],G[i][14]=G[t+"0"],G[i][15]=G[t+"0"]}function ce(t){return t==Qo||t==Zo||t==Ja||t==ts}function kr(){this.eb=[],this.size=this.A=this.fb=0}function Se(){this.y=[],this.f=[],this.ea=[],this.F=[],this.Tc=this.Ed=this.Cd=this.Fd=this.lb=this.Db=this.Ab=this.fa=this.J=this.W=this.N=this.O=0}function Cn(){this.Rd=this.height=this.width=this.S=0,this.f={},this.f.RGBA=new kr,this.f.kb=new Se,this.sd=null}function er(){this.width=[0],this.height=[0],this.Pd=[0],this.Qd=[0],this.format=[0]}function _e(){this.Id=this.fd=this.Md=this.hb=this.ib=this.da=this.bd=this.cd=this.j=this.v=this.Da=this.Sd=this.ob=0}function Kt(t){return alert("todo:WebPSamplerProcessPlane"),t.T}function ue(t,i){var u=t.T,d=i.ba.f.RGBA,g=d.eb,b=d.fb+t.ka*d.A,y=Sn[i.ba.S],A=t.y,S=t.O,P=t.f,q=t.N,K=t.ea,X=t.W,W=i.cc,dt=i.dc,nt=i.Mc,U=i.Nc,H=t.ka,ct=t.ka+t.T,gt=t.U,ht=gt+1>>1;for(H==0?y(A,S,null,null,P,q,K,X,P,q,K,X,g,b,null,null,gt):(y(i.ec,i.fc,A,S,W,dt,nt,U,P,q,K,X,g,b-d.A,g,b,gt),++u);H+2<ct;H+=2)W=P,dt=q,nt=K,U=X,q+=t.Rc,X+=t.Rc,b+=2*d.A,y(A,(S+=2*t.fa)-t.fa,A,S,W,dt,nt,U,P,q,K,X,g,b-d.A,g,b,gt);return S+=t.fa,t.j+ct<t.o?(a(i.ec,i.fc,A,S,gt),a(i.cc,i.dc,P,q,ht),a(i.Mc,i.Nc,K,X,ht),u--):1&ct||y(A,S,null,null,P,q,K,X,P,q,K,X,g,b+d.A,null,null,gt),u}function Fr(t,i,u){var d=t.F,g=[t.J];if(d!=null){var b=t.U,y=i.ba.S,A=y==$a||y==Ja;i=i.ba.f.RGBA;var S=[0],P=t.ka;S[0]=t.T,t.Kb&&(P==0?--S[0]:(--P,g[0]-=t.width),t.j+t.ka+t.T==t.o&&(S[0]=t.o-t.j-P));var q=i.eb;P=i.fb+P*i.A,t=Ne(d,g[0],t.width,b,S,q,P+(A?0:3),i.A),e(u==S),t&&ce(y)&&Ln(q,P,A,b,S,i.A)}return 0}function Ye(t){var i=t.ma,u=i.ba.S,d=11>u,g=u==Ga||u==Ya||u==$a||u==Xo||u==12||ce(u);if(i.memory=null,i.Ib=null,i.Jb=null,i.Nd=null,!Qi(i.Oa,t,g?11:12))return 0;if(g&&ce(u)&&yt(),t.da)alert("todo:use_scaling");else{if(d){if(i.Ib=Kt,t.Kb){if(u=t.U+1>>1,i.memory=s(t.U+2*u),i.memory==null)return 0;i.ec=i.memory,i.fc=0,i.cc=i.ec,i.dc=i.fc+t.U,i.Mc=i.cc,i.Nc=i.dc+u,i.Ib=ue,yt()}}else alert("todo:EmitYUV");g&&(i.Jb=Fr,d&&$())}if(d&&!Ys){for(t=0;256>t;++t)yc[t]=89858*(t-128)+Xa>>Ka,Ac[t]=-22014*(t-128)+Xa,Nc[t]=-45773*(t-128),wc[t]=113618*(t-128)+Xa>>Ka;for(t=sa;t<rs;++t)i=76283*(t-16)+Xa>>Ka,Lc[t-sa]=mn(i,255),xc[t-sa]=mn(i+8>>4,15);Ys=1}return 1}function zn(t){var i=t.ma,u=t.U,d=t.T;return e(!(1&t.ka)),0>=u||0>=d?0:(u=i.Ib(t,i),i.Jb!=null&&i.Jb(t,i,u),i.Dc+=u,1)}function Hn(t){t.ma.memory=null}function Ut(t,i,u,d){return ut(t,8)!=47?0:(i[0]=ut(t,14)+1,u[0]=ut(t,14)+1,d[0]=ut(t,1),ut(t,3)!=0?0:!t.h)}function In(t,i){if(4>t)return t+1;var u=t-2>>1;return(2+(1&t)<<u)+ut(i,u)+1}function Vn(t,i){return 120<i?i-120:1<=(u=((u=ac[i-1])>>4)*t+(8-(15&u)))?u:1;var u}function sn(t,i,u){var d=T(u),g=t[i+=255&d].g-8;return 0<g&&(V(u,u.u+8),d=T(u),i+=t[i].value,i+=d&(1<<g)-1),V(u,u.u+t[i].g),t[i].value}function Ue(t,i,u){return u.g+=t.g,u.value+=t.value<<i>>>0,e(8>=u.g),t.g}function Ee(t,i,u){var d=t.xc;return e((i=d==0?0:t.vc[t.md*(u>>d)+(i>>d)])<t.Wb),t.Ya[i]}function pn(t,i,u,d){var g=t.ab,b=t.c*i,y=t.C;i=y+i;var A=u,S=d;for(d=t.Ta,u=t.Ua;0<g--;){var P=t.gc[g],q=y,K=i,X=A,W=S,dt=(S=d,A=u,P.Ea);switch(e(q<K),e(K<=P.nc),P.hc){case 2:Ra(X,W,(K-q)*dt,S,A);break;case 0:var nt=q,U=K,H=S,ct=A,gt=(Pt=P).Ea;nt==0&&(Jo(X,W,null,null,1,H,ct),te(X,W+1,0,0,gt-1,H,ct+1),W+=gt,ct+=gt,++nt);for(var ht=1<<Pt.b,Mt=ht-1,St=Et(gt,Pt.b),qt=Pt.K,Pt=Pt.w+(nt>>Pt.b)*St;nt<U;){var le=qt,he=Pt,oe=1;for(ea(X,W,H,ct-gt,1,H,ct);oe<gt;){var re=(oe&~Mt)+ht;re>gt&&(re=gt),(0,mr[le[he++]>>8&15])(X,W+ +oe,H,ct+oe-gt,re-oe,H,ct+oe),oe=re}W+=gt,ct+=gt,++nt&Mt||(Pt+=St)}K!=P.nc&&a(S,A-dt,S,A+(K-q-1)*dt,dt);break;case 1:for(dt=X,U=W,gt=(X=P.Ea)-(ct=X&~(H=(W=1<<P.b)-1)),nt=Et(X,P.b),ht=P.K,P=P.w+(q>>P.b)*nt;q<K;){for(Mt=ht,St=P,qt=new Ct,Pt=U+ct,le=U+X;U<Pt;)pe(Mt[St++],qt),Or(qt,dt,U,W,S,A),U+=W,A+=W;U<le&&(pe(Mt[St++],qt),Or(qt,dt,U,gt,S,A),U+=gt,A+=gt),++q&H||(P+=nt)}break;case 3:if(X==S&&W==A&&0<P.b){for(U=S,X=dt=A+(K-q)*dt-(ct=(K-q)*Et(P.Ea,P.b)),W=S,H=A,nt=[],ct=(gt=ct)-1;0<=ct;--ct)nt[ct]=W[H+ct];for(ct=gt-1;0<=ct;--ct)U[X+ct]=nt[ct];wn(P,q,K,S,dt,S,A)}else wn(P,q,K,X,W,S,A)}A=d,S=u}S!=u&&a(d,u,A,S,b)}function Yr(t,i){var u=t.V,d=t.Ba+t.c*t.C,g=i-t.C;if(e(i<=t.l.o),e(16>=g),0<g){var b=t.l,y=t.Ta,A=t.Ua,S=b.width;if(pn(t,g,u,d),g=A=[A],e((u=t.C)<(d=i)),e(b.v<b.va),d>b.o&&(d=b.o),u<b.j){var P=b.j-u;u=b.j,g[0]+=P*S}if(u>=d?u=0:(g[0]+=4*b.v,b.ka=u-b.j,b.U=b.va-b.v,b.T=d-u,u=1),u){if(A=A[0],11>(u=t.ca).S){var q=u.f.RGBA,K=(d=u.S,g=b.U,b=b.T,P=q.eb,q.A),X=b;for(q=q.fb+t.Ma*q.A;0<X--;){var W=y,dt=A,nt=g,U=P,H=q;switch(d){case Wa:un(W,dt,nt,U,H);break;case Ga:rn(W,dt,nt,U,H);break;case Qo:rn(W,dt,nt,U,H),Ln(U,H,0,nt,1,0);break;case Os:or(W,dt,nt,U,H);break;case Ya:Ae(W,dt,nt,U,H,1);break;case Zo:Ae(W,dt,nt,U,H,1),Ln(U,H,0,nt,1,0);break;case $a:Ae(W,dt,nt,U,H,0);break;case Ja:Ae(W,dt,nt,U,H,0),Ln(U,H,1,nt,1,0);break;case Xo:gr(W,dt,nt,U,H);break;case ts:gr(W,dt,nt,U,H),we(U,H,nt,1,0);break;case Bs:ar(W,dt,nt,U,H);break;default:e(0)}A+=S,q+=K}t.Ma+=b}else alert("todo:EmitRescaledRowsYUVA");e(t.Ma<=u.height)}}t.C=i,e(t.C<=t.i)}function nr(t){var i;if(0<t.ua)return 0;for(i=0;i<t.Wb;++i){var u=t.Ya[i].G,d=t.Ya[i].H;if(0<u[1][d[1]+0].g||0<u[2][d[2]+0].g||0<u[3][d[3]+0].g)return 0}return 1}function kn(t,i,u,d,g,b){if(t.Z!=0){var y=t.qd,A=t.rd;for(e(wr[t.Z]!=null);i<u;++i)wr[t.Z](y,A,d,g,d,g,b),y=d,A=g,g+=b;t.qd=y,t.rd=A}}function Fn(t,i){var u=t.l.ma,d=u.Z==0||u.Z==1?t.l.j:t.C;if(d=t.C<d?d:t.C,e(i<=t.l.o),i>d){var g=t.l.width,b=u.ca,y=u.tb+g*d,A=t.V,S=t.Ba+t.c*d,P=t.gc;e(t.ab==1),e(P[0].hc==3),qa(P[0],d,i,A,S,b,y),kn(u,d,i,b,y,g)}t.C=t.Ma=i}function Tn(t,i,u,d,g,b,y){var A=t.$/d,S=t.$%d,P=t.m,q=t.s,K=u+t.$,X=K;g=u+d*g;var W=u+d*b,dt=280+q.ua,nt=t.Pb?A:16777216,U=0<q.ua?q.Wa:null,H=q.wc,ct=K<W?Ee(q,S,A):null;e(t.C<b),e(W<=g);var gt=!1;t:for(;;){for(;gt||K<W;){var ht=0;if(A>=nt){var Mt=K-u;e((nt=t).Pb),nt.wd=nt.m,nt.xd=Mt,0<nt.s.ua&&it(nt.s.Wa,nt.s.vb),nt=A+sc}if(S&H||(ct=Ee(q,S,A)),e(ct!=null),ct.Qb&&(i[K]=ct.qb,gt=!0),!gt)if(Y(P),ct.jc){ht=P,Mt=i;var St=K,qt=ct.pd[T(ht)&$e-1];e(ct.jc),256>qt.g?(V(ht,ht.u+qt.g),Mt[St]=qt.value,ht=0):(V(ht,ht.u+qt.g-256),e(256<=qt.value),ht=qt.value),ht==0&&(gt=!0)}else ht=sn(ct.G[0],ct.H[0],P);if(P.h)break;if(gt||256>ht){if(!gt)if(ct.nd)i[K]=(ct.qb|ht<<8)>>>0;else{if(Y(P),gt=sn(ct.G[1],ct.H[1],P),Y(P),Mt=sn(ct.G[2],ct.H[2],P),St=sn(ct.G[3],ct.H[3],P),P.h)break;i[K]=(St<<24|gt<<16|ht<<8|Mt)>>>0}if(gt=!1,++K,++S>=d&&(S=0,++A,y!=null&&A<=b&&!(A%16)&&y(t,A),U!=null))for(;X<K;)ht=i[X++],U.X[(506832829*ht&**********)>>>U.Mb]=ht}else if(280>ht){if(ht=In(ht-256,P),Mt=sn(ct.G[4],ct.H[4],P),Y(P),Mt=Vn(d,Mt=In(Mt,P)),P.h)break;if(K-u<Mt||g-K<ht)break t;for(St=0;St<ht;++St)i[K+St]=i[K+St-Mt];for(K+=ht,S+=ht;S>=d;)S-=d,++A,y!=null&&A<=b&&!(A%16)&&y(t,A);if(e(K<=g),S&H&&(ct=Ee(q,S,A)),U!=null)for(;X<K;)ht=i[X++],U.X[(506832829*ht&**********)>>>U.Mb]=ht}else{if(!(ht<dt))break t;for(gt=ht-280,e(U!=null);X<K;)ht=i[X++],U.X[(506832829*ht&**********)>>>U.Mb]=ht;ht=K,e(!(gt>>>(Mt=U).Xa)),i[ht]=Mt.X[gt],gt=!0}gt||e(P.h==E(P))}if(t.Pb&&P.h&&K<g)e(t.m.h),t.a=5,t.m=t.wd,t.$=t.xd,0<t.s.ua&&it(t.s.vb,t.s.Wa);else{if(P.h)break t;y!=null&&y(t,A>b?b:A),t.a=0,t.$=K-u}return 1}return t.a=3,0}function Wn(t){e(t!=null),t.vc=null,t.yc=null,t.Ya=null;var i=t.Wa;i!=null&&(i.X=null),t.vb=null,e(t!=null)}function yn(){var t=new $o;return t==null?null:(t.a=0,t.xb=Us,Un("Predictor","VP8LPredictors"),Un("Predictor","VP8LPredictors_C"),Un("PredictorAdd","VP8LPredictorsAdd"),Un("PredictorAdd","VP8LPredictorsAdd_C"),Ra=ie,Or=Gt,un=Ft,rn=Ke,gr=se,ar=Pn,or=ge,G.VP8LMapColor32b=di,G.VP8LMapColor8b=Ua,t)}function Gn(t,i,u,d,g){var b=1,y=[t],A=[i],S=d.m,P=d.s,q=null,K=0;t:for(;;){if(u)for(;b&&ut(S,1);){var X=y,W=A,dt=d,nt=1,U=dt.m,H=dt.gc[dt.ab],ct=ut(U,2);if(dt.Oc&1<<ct)b=0;else{switch(dt.Oc|=1<<ct,H.hc=ct,H.Ea=X[0],H.nc=W[0],H.K=[null],++dt.ab,e(4>=dt.ab),ct){case 0:case 1:H.b=ut(U,3)+2,nt=Gn(Et(H.Ea,H.b),Et(H.nc,H.b),0,dt,H.K),H.K=H.K[0];break;case 3:var gt,ht=ut(U,8)+1,Mt=16<ht?0:4<ht?1:2<ht?2:3;if(X[0]=Et(H.Ea,Mt),H.b=Mt,gt=nt=Gn(ht,1,0,dt,H.K)){var St,qt=ht,Pt=H,le=1<<(8>>Pt.b),he=s(le);if(he==null)gt=0;else{var oe=Pt.K[0],re=Pt.w;for(he[0]=Pt.K[0][0],St=1;St<1*qt;++St)he[St]=wt(oe[re+St],he[St-1]);for(;St<4*le;++St)he[St]=0;Pt.K[0]=null,Pt.K[0]=he,gt=1}}nt=gt;break;case 2:break;default:e(0)}b=nt}}if(y=y[0],A=A[0],b&&ut(S,1)&&!(b=1<=(K=ut(S,4))&&11>=K)){d.a=3;break t}var ve;if(ve=b)e:{var me,ee,Be,hn=d,Re=y,fn=A,fe=K,vn=u,bn=hn.m,Ve=hn.s,Je=[null],on=1,_n=0,Kn=oc[fe];n:for(;;){if(vn&&ut(bn,1)){var We=ut(bn,3)+2,ur=Et(Re,We),zr=Et(fn,We),bi=ur*zr;if(!Gn(ur,zr,0,hn,Je))break n;for(Je=Je[0],Ve.xc=We,me=0;me<bi;++me){var Nr=Je[me]>>8&65535;Je[me]=Nr,Nr>=on&&(on=Nr+1)}}if(bn.h)break n;for(ee=0;5>ee;++ee){var Ce=Rs[ee];!ee&&0<fe&&(Ce+=1<<fe),_n<Ce&&(_n=Ce)}var is=c(on*Kn,I),Ks=on,Xs=c(Ks,R);if(Xs==null)var Za=null;else e(65536>=Ks),Za=Xs;var la=s(_n);if(Za==null||la==null||is==null){hn.a=1;break n}var to=is;for(me=Be=0;me<on;++me){var On=Za[me],yi=On.G,wi=On.H,Qs=0,eo=1,Zs=0;for(ee=0;5>ee;++ee){Ce=Rs[ee],yi[ee]=to,wi[ee]=Be,!ee&&0<fe&&(Ce+=1<<fe);i:{var no,as=Ce,ro=hn,ca=la,Pc=to,Cc=Be,os=0,Ar=ro.m,Ic=ut(Ar,1);if(o(ca,0,0,as),Ic){var kc=ut(Ar,1)+1,Fc=ut(Ar,1),tl=ut(Ar,Fc==0?1:8);ca[tl]=1,kc==2&&(ca[tl=ut(Ar,8)]=1);var io=1}else{var el=s(19),nl=ut(Ar,4)+4;if(19<nl){ro.a=3;var ao=0;break i}for(no=0;no<nl;++no)el[ic[no]]=ut(Ar,3);var ss=void 0,ua=void 0,rl=ro,Tc=el,oo=as,il=ca,ls=0,Lr=rl.m,al=8,ol=c(128,I);r:for(;C(ol,0,7,Tc,19);){if(ut(Lr,1)){var Dc=2+2*ut(Lr,3);if((ss=2+ut(Lr,Dc))>oo)break r}else ss=oo;for(ua=0;ua<oo&&ss--;){Y(Lr);var sl=ol[0+(127&T(Lr))];V(Lr,Lr.u+sl.g);var Ni=sl.value;if(16>Ni)il[ua++]=Ni,Ni!=0&&(al=Ni);else{var Mc=Ni==16,ll=Ni-16,jc=nc[ll],cl=ut(Lr,ec[ll])+jc;if(ua+cl>oo)break r;for(var Ec=Mc?al:0;0<cl--;)il[ua++]=Ec}}ls=1;break r}ls||(rl.a=3),io=ls}(io=io&&!Ar.h)&&(os=C(Pc,Cc,8,ca,as)),io&&os!=0?ao=os:(ro.a=3,ao=0)}if(ao==0)break n;if(eo&&rc[ee]==1&&(eo=to[Be].g==0),Qs+=to[Be].g,Be+=ao,3>=ee){var ha,cs=la[0];for(ha=1;ha<Ce;++ha)la[ha]>cs&&(cs=la[ha]);Zs+=cs}}if(On.nd=eo,On.Qb=0,eo&&(On.qb=(yi[3][wi[3]+0].value<<24|yi[1][wi[1]+0].value<<16|yi[2][wi[2]+0].value)>>>0,Qs==0&&256>yi[0][wi[0]+0].value&&(On.Qb=1,On.qb+=yi[0][wi[0]+0].value<<8)),On.jc=!On.Qb&&6>Zs,On.jc){var so,hr=On;for(so=0;so<$e;++so){var xr=so,Sr=hr.pd[xr],lo=hr.G[0][hr.H[0]+xr];256<=lo.value?(Sr.g=lo.g+256,Sr.value=lo.value):(Sr.g=0,Sr.value=0,xr>>=Ue(lo,8,Sr),xr>>=Ue(hr.G[1][hr.H[1]+xr],16,Sr),xr>>=Ue(hr.G[2][hr.H[2]+xr],0,Sr),Ue(hr.G[3][hr.H[3]+xr],24,Sr))}}}Ve.vc=Je,Ve.Wb=on,Ve.Ya=Za,Ve.yc=is,ve=1;break e}ve=0}if(!(b=ve)){d.a=3;break t}if(0<K){if(P.ua=1<<K,!Ht(P.Wa,K)){d.a=1,b=0;break t}}else P.ua=0;var us=d,ul=y,Oc=A,hs=us.s,fs=hs.xc;if(us.c=ul,us.i=Oc,hs.md=Et(ul,fs),hs.wc=fs==0?-1:(1<<fs)-1,u){d.xb=pc;break t}if((q=s(y*A))==null){d.a=1,b=0;break t}b=(b=Tn(d,q,0,y,A,A,null))&&!S.h;break t}return b?(g!=null?g[0]=q:(e(q==null),e(u)),d.$=0,u||Wn(P)):Wn(P),b}function Tr(t,i){var u=t.c*t.i,d=u+i+16*i;return e(t.c<=i),t.V=s(d),t.V==null?(t.Ta=null,t.Ua=0,t.a=1,0):(t.Ta=t.V,t.Ua=t.Ba+u+i,1)}function $r(t,i){var u=t.C,d=i-u,g=t.V,b=t.Ba+t.c*u;for(e(i<=t.l.o);0<d;){var y=16<d?16:d,A=t.l.ma,S=t.l.width,P=S*y,q=A.ca,K=A.tb+S*u,X=t.Ta,W=t.Ua;pn(t,y,g,b),Te(X,W,q,K,P),kn(A,u,u+y,q,K,S),d-=y,g+=y*t.c,u+=y}e(u==i),t.C=t.Ma=i}function Jr(){this.ub=this.yd=this.td=this.Rb=0}function Kr(){this.Kd=this.Ld=this.Ud=this.Td=this.i=this.c=0}function Xr(){this.Fb=this.Bb=this.Cb=0,this.Zb=s(4),this.Lb=s(4)}function ya(){this.Yb=function(){var t=[];return function i(u,d,g){for(var b=g[d],y=0;y<b&&(u.push(g.length>d+1?[]:0),!(g.length<d+1));y++)i(u[y],d+1,g)}(t,0,[3,11]),t}()}function So(){this.jb=s(3),this.Wc=h([4,8],ya),this.Xc=h([4,17],ya)}function _o(){this.Pc=this.wb=this.Tb=this.zd=0,this.vd=new s(4),this.od=new s(4)}function Qr(){this.ld=this.La=this.dd=this.tc=0}function wa(){this.Na=this.la=0}function Po(){this.Sc=[0,0],this.Eb=[0,0],this.Qc=[0,0],this.ia=this.lc=0}function Ei(){this.ad=s(384),this.Za=0,this.Ob=s(16),this.$b=this.Ad=this.ia=this.Gc=this.Hc=this.Dd=0}function Co(){this.uc=this.M=this.Nb=0,this.wa=Array(new Qr),this.Y=0,this.ya=Array(new Ei),this.aa=0,this.l=new Zr}function Na(){this.y=s(16),this.f=s(8),this.ea=s(8)}function Io(){this.cb=this.a=0,this.sc="",this.m=new Ot,this.Od=new Jr,this.Kc=new Kr,this.ed=new _o,this.Qa=new Xr,this.Ic=this.$c=this.Aa=0,this.D=new Co,this.Xb=this.Va=this.Hb=this.zb=this.yb=this.Ub=this.za=0,this.Jc=c(8,Ot),this.ia=0,this.pb=c(4,Po),this.Pa=new So,this.Bd=this.kc=0,this.Ac=[],this.Bc=0,this.zc=[0,0,0,0],this.Gd=Array(new Na),this.Hd=0,this.rb=Array(new wa),this.sb=0,this.wa=Array(new Qr),this.Y=0,this.oc=[],this.pc=0,this.sa=[],this.ta=0,this.qa=[],this.ra=0,this.Ha=[],this.B=this.R=this.Ia=0,this.Ec=[],this.M=this.ja=this.Vb=this.Fc=0,this.ya=Array(new Ei),this.L=this.aa=0,this.gd=h([4,2],Qr),this.ga=null,this.Fa=[],this.Cc=this.qc=this.P=0,this.Gb=[],this.Uc=0,this.mb=[],this.nb=0,this.rc=[],this.Ga=this.Vc=0}function Zr(){this.T=this.U=this.ka=this.height=this.width=0,this.y=[],this.f=[],this.ea=[],this.Rc=this.fa=this.W=this.N=this.O=0,this.ma="void",this.put="VP8IoPutHook",this.ac="VP8IoSetupHook",this.bc="VP8IoTeardownHook",this.ha=this.Kb=0,this.data=[],this.hb=this.ib=this.da=this.o=this.j=this.va=this.v=this.Da=this.ob=this.w=0,this.F=[],this.J=0}function ko(){var t=new Io;return t!=null&&(t.a=0,t.sc="OK",t.cb=0,t.Xb=0,oa||(oa=xa)),t}function ke(t,i,u){return t.a==0&&(t.a=i,t.sc=u,t.cb=0),0}function Aa(t,i,u){return 3<=u&&t[i+0]==157&&t[i+1]==1&&t[i+2]==42}function La(t,i){if(t==null)return 0;if(t.a=0,t.sc="OK",i==null)return ke(t,2,"null VP8Io passed to VP8GetHeaders()");var u=i.data,d=i.w,g=i.ha;if(4>g)return ke(t,7,"Truncated header.");var b=u[d+0]|u[d+1]<<8|u[d+2]<<16,y=t.Od;if(y.Rb=!(1&b),y.td=b>>1&7,y.yd=b>>4&1,y.ub=b>>5,3<y.td)return ke(t,3,"Incorrect keyframe parameters.");if(!y.yd)return ke(t,4,"Frame not displayable.");d+=3,g-=3;var A=t.Kc;if(y.Rb){if(7>g)return ke(t,7,"cannot parse picture header");if(!Aa(u,d,g))return ke(t,3,"Bad code word");A.c=16383&(u[d+4]<<8|u[d+3]),A.Td=u[d+4]>>6,A.i=16383&(u[d+6]<<8|u[d+5]),A.Ud=u[d+6]>>6,d+=7,g-=7,t.za=A.c+15>>4,t.Ub=A.i+15>>4,i.width=A.c,i.height=A.i,i.Da=0,i.j=0,i.v=0,i.va=i.width,i.o=i.height,i.da=0,i.ib=i.width,i.hb=i.height,i.U=i.width,i.T=i.height,o((b=t.Pa).jb,0,255,b.jb.length),e((b=t.Qa)!=null),b.Cb=0,b.Bb=0,b.Fb=1,o(b.Zb,0,0,b.Zb.length),o(b.Lb,0,0,b.Lb)}if(y.ub>g)return ke(t,7,"bad partition length");lt(b=t.m,u,d,y.ub),d+=y.ub,g-=y.ub,y.Rb&&(A.Ld=et(b),A.Kd=et(b)),A=t.Qa;var S,P=t.Pa;if(e(b!=null),e(A!=null),A.Cb=et(b),A.Cb){if(A.Bb=et(b),et(b)){for(A.Fb=et(b),S=0;4>S;++S)A.Zb[S]=et(b)?mt(b,7):0;for(S=0;4>S;++S)A.Lb[S]=et(b)?mt(b,6):0}if(A.Bb)for(S=0;3>S;++S)P.jb[S]=et(b)?ot(b,8):255}else A.Bb=0;if(b.Ka)return ke(t,3,"cannot parse segment header");if((A=t.ed).zd=et(b),A.Tb=ot(b,6),A.wb=ot(b,3),A.Pc=et(b),A.Pc&&et(b)){for(P=0;4>P;++P)et(b)&&(A.vd[P]=mt(b,6));for(P=0;4>P;++P)et(b)&&(A.od[P]=mt(b,6))}if(t.L=A.Tb==0?0:A.zd?1:2,b.Ka)return ke(t,3,"cannot parse filter header");var q=g;if(g=S=d,d=S+q,A=q,t.Xb=(1<<ot(t.m,2))-1,q<3*(P=t.Xb))u=7;else{for(S+=3*P,A-=3*P,q=0;q<P;++q){var K=u[g+0]|u[g+1]<<8|u[g+2]<<16;K>A&&(K=A),lt(t.Jc[+q],u,S,K),S+=K,A-=K,g+=3}lt(t.Jc[+P],u,S,A),u=S<d?0:5}if(u!=0)return ke(t,u,"cannot parse partitions");for(u=ot(S=t.m,7),g=et(S)?mt(S,4):0,d=et(S)?mt(S,4):0,A=et(S)?mt(S,4):0,P=et(S)?mt(S,4):0,S=et(S)?mt(S,4):0,q=t.Qa,K=0;4>K;++K){if(q.Cb){var X=q.Zb[K];q.Fb||(X+=u)}else{if(0<K){t.pb[K]=t.pb[0];continue}X=u}var W=t.pb[K];W.Sc[0]=es[mn(X+g,127)],W.Sc[1]=ns[mn(X+0,127)],W.Eb[0]=2*es[mn(X+d,127)],W.Eb[1]=101581*ns[mn(X+A,127)]>>16,8>W.Eb[1]&&(W.Eb[1]=8),W.Qc[0]=es[mn(X+P,117)],W.Qc[1]=ns[mn(X+S,127)],W.lc=X+S}if(!y.Rb)return ke(t,4,"Not a key frame.");for(et(b),y=t.Pa,u=0;4>u;++u){for(g=0;8>g;++g)for(d=0;3>d;++d)for(A=0;11>A;++A)P=Z(b,fc[u][g][d][A])?ot(b,8):uc[u][g][d][A],y.Wc[u][g].Yb[d][A]=P;for(g=0;17>g;++g)y.Xc[u][g]=y.Wc[u][dc[g]]}return t.kc=et(b),t.kc&&(t.Bd=ot(b,8)),t.cb=1}function xa(t,i,u,d,g,b,y){var A=i[g].Yb[u];for(u=0;16>g;++g){if(!Z(t,A[u+0]))return g;for(;!Z(t,A[u+1]);)if(A=i[++g].Yb[0],u=0,g==16)return 16;var S=i[g+1].Yb;if(Z(t,A[u+2])){var P=t,q=0;if(Z(P,(X=A)[(K=u)+3]))if(Z(P,X[K+6])){for(A=0,K=2*(q=Z(P,X[K+8]))+(X=Z(P,X[K+9+q])),q=0,X=lc[K];X[A];++A)q+=q+Z(P,X[A]);q+=3+(8<<K)}else Z(P,X[K+7])?(q=7+2*Z(P,165),q+=Z(P,145)):q=5+Z(P,159);else q=Z(P,X[K+4])?3+Z(P,X[K+5]):2;A=S[2]}else q=1,A=S[1];S=y+cc[g],0>(P=t).b&&Q(P);var K,X=P.b,W=(K=P.Ca>>1)-(P.I>>X)>>31;--P.b,P.Ca+=W,P.Ca|=1,P.I-=(K+1&W)<<X,b[S]=((q^W)-W)*d[(0<g)+0]}return 16}function Oi(t){var i=t.rb[t.sb-1];i.la=0,i.Na=0,o(t.zc,0,0,t.zc.length),t.ja=0}function Fo(t,i){if(t==null)return 0;if(i==null)return ke(t,2,"NULL VP8Io parameter in VP8Decode().");if(!t.cb&&!La(t,i))return 0;if(e(t.cb),i.ac==null||i.ac(i)){i.ob&&(t.L=0);var u=Qa[t.L];if(t.L==2?(t.yb=0,t.zb=0):(t.yb=i.v-u>>4,t.zb=i.j-u>>4,0>t.yb&&(t.yb=0),0>t.zb&&(t.zb=0)),t.Va=i.o+15+u>>4,t.Hb=i.va+15+u>>4,t.Hb>t.za&&(t.Hb=t.za),t.Va>t.Ub&&(t.Va=t.Ub),0<t.L){var d=t.ed;for(u=0;4>u;++u){var g;if(t.Qa.Cb){var b=t.Qa.Lb[u];t.Qa.Fb||(b+=d.Tb)}else b=d.Tb;for(g=0;1>=g;++g){var y=t.gd[u][g],A=b;if(d.Pc&&(A+=d.vd[0],g&&(A+=d.od[0])),0<(A=0>A?0:63<A?63:A)){var S=A;0<d.wb&&(S=4<d.wb?S>>2:S>>1)>9-d.wb&&(S=9-d.wb),1>S&&(S=1),y.dd=S,y.tc=2*A+S,y.ld=40<=A?2:15<=A?1:0}else y.tc=0;y.La=g}}}u=0}else ke(t,6,"Frame setup failed"),u=t.a;if(u=u==0){if(u){t.$c=0,0<t.Aa||(t.Ic=_c);t:{u=t.Ic,d=4*(S=t.za);var P=32*S,q=S+1,K=0<t.L?S*(0<t.Aa?2:1):0,X=(t.Aa==2?2:1)*S;if((y=d+832+(g=3*(16*u+Qa[t.L])/2*P)+(b=t.Fa!=null&&0<t.Fa.length?t.Kc.c*t.Kc.i:0))!=y)u=0;else{if(y>t.Vb){if(t.Vb=0,t.Ec=s(y),t.Fc=0,t.Ec==null){u=ke(t,1,"no memory during frame initialization.");break t}t.Vb=y}y=t.Ec,A=t.Fc,t.Ac=y,t.Bc=A,A+=d,t.Gd=c(P,Na),t.Hd=0,t.rb=c(q+1,wa),t.sb=1,t.wa=K?c(K,Qr):null,t.Y=0,t.D.Nb=0,t.D.wa=t.wa,t.D.Y=t.Y,0<t.Aa&&(t.D.Y+=S),e(!0),t.oc=y,t.pc=A,A+=832,t.ya=c(X,Ei),t.aa=0,t.D.ya=t.ya,t.D.aa=t.aa,t.Aa==2&&(t.D.aa+=S),t.R=16*S,t.B=8*S,S=(P=Qa[t.L])*t.R,P=P/2*t.B,t.sa=y,t.ta=A+S,t.qa=t.sa,t.ra=t.ta+16*u*t.R+P,t.Ha=t.qa,t.Ia=t.ra+8*u*t.B+P,t.$c=0,A+=g,t.mb=b?y:null,t.nb=b?A:null,e(A+b<=t.Fc+t.Vb),Oi(t),o(t.Ac,t.Bc,0,d),u=1}}if(u){if(i.ka=0,i.y=t.sa,i.O=t.ta,i.f=t.qa,i.N=t.ra,i.ea=t.Ha,i.Vd=t.Ia,i.fa=t.R,i.Rc=t.B,i.F=null,i.J=0,!Ha){for(u=-255;255>=u;++u)ze[255+u]=0>u?-u:u;for(u=-1020;1020>=u;++u)lr[1020+u]=-128>u?-128:127<u?127:u;for(u=-112;112>=u;++u)aa[112+u]=-16>u?-16:15<u?15:u;for(u=-255;510>=u;++u)vi[255+u]=0>u?0:255<u?255:u;Ha=1}pi=Mo,sr=To,na=_a,an=Do,Nn=Pa,Fe=Sa,mi=Hi,za=jr,ra=Yo,Br=Vi,Rr=Go,vr=ai,qr=Wi,gi=Ea,Ur=ja,br=$n,ia=ir,An=Wo,En[0]=Yn,En[1]=jo,En[2]=Ro,En[3]=qo,En[4]=ka,En[5]=ri,En[6]=Fa,En[7]=qi,En[8]=zo,En[9]=Uo,yr[0]=Ca,yr[1]=Oo,yr[2]=rr,yr[3]=ei,yr[4]=Xe,yr[5]=Bo,yr[6]=Ia,cr[0]=fr,cr[1]=Eo,cr[2]=Ho,cr[3]=Ui,cr[4]=Mr,cr[5]=Vo,cr[6]=zi,u=1}else u=0}u&&(u=function(W,dt){for(W.M=0;W.M<W.Va;++W.M){var nt,U=W.Jc[W.M&W.Xb],H=W.m,ct=W;for(nt=0;nt<ct.za;++nt){var gt=H,ht=ct,Mt=ht.Ac,St=ht.Bc+4*nt,qt=ht.zc,Pt=ht.ya[ht.aa+nt];if(ht.Qa.Bb?Pt.$b=Z(gt,ht.Pa.jb[0])?2+Z(gt,ht.Pa.jb[2]):Z(gt,ht.Pa.jb[1]):Pt.$b=0,ht.kc&&(Pt.Ad=Z(gt,ht.Bd)),Pt.Za=!Z(gt,145)+0,Pt.Za){var le=Pt.Ob,he=0;for(ht=0;4>ht;++ht){var oe,re=qt[0+ht];for(oe=0;4>oe;++oe){re=hc[Mt[St+oe]][re];for(var ve=qs[Z(gt,re[0])];0<ve;)ve=qs[2*ve+Z(gt,re[ve])];re=-ve,Mt[St+oe]=re}a(le,he,Mt,St,4),he+=4,qt[0+ht]=re}}else re=Z(gt,156)?Z(gt,128)?1:3:Z(gt,163)?2:0,Pt.Ob[0]=re,o(Mt,St,re,4),o(qt,0,re,4);Pt.Dd=Z(gt,142)?Z(gt,114)?Z(gt,183)?1:3:2:0}if(ct.m.Ka)return ke(W,7,"Premature end-of-partition0 encountered.");for(;W.ja<W.za;++W.ja){if(ct=U,gt=(H=W).rb[H.sb-1],Mt=H.rb[H.sb+H.ja],nt=H.ya[H.aa+H.ja],St=H.kc?nt.Ad:0)gt.la=Mt.la=0,nt.Za||(gt.Na=Mt.Na=0),nt.Hc=0,nt.Gc=0,nt.ia=0;else{var me,ee;if(gt=Mt,Mt=ct,St=H.Pa.Xc,qt=H.ya[H.aa+H.ja],Pt=H.pb[qt.$b],ht=qt.ad,le=0,he=H.rb[H.sb-1],re=oe=0,o(ht,le,0,384),qt.Za)var Be=0,hn=St[3];else{ve=s(16);var Re=gt.Na+he.Na;if(Re=oa(Mt,St[1],Re,Pt.Eb,0,ve,0),gt.Na=he.Na=(0<Re)+0,1<Re)pi(ve,0,ht,le);else{var fn=ve[0]+3>>3;for(ve=0;256>ve;ve+=16)ht[le+ve]=fn}Be=1,hn=St[0]}var fe=15&gt.la,vn=15&he.la;for(ve=0;4>ve;++ve){var bn=1&vn;for(fn=ee=0;4>fn;++fn)fe=fe>>1|(bn=(Re=oa(Mt,hn,Re=bn+(1&fe),Pt.Sc,Be,ht,le))>Be)<<7,ee=ee<<2|(3<Re?3:1<Re?2:ht[le+0]!=0),le+=16;fe>>=4,vn=vn>>1|bn<<7,oe=(oe<<8|ee)>>>0}for(hn=fe,Be=vn>>4,me=0;4>me;me+=2){for(ee=0,fe=gt.la>>4+me,vn=he.la>>4+me,ve=0;2>ve;++ve){for(bn=1&vn,fn=0;2>fn;++fn)Re=bn+(1&fe),fe=fe>>1|(bn=0<(Re=oa(Mt,St[2],Re,Pt.Qc,0,ht,le)))<<3,ee=ee<<2|(3<Re?3:1<Re?2:ht[le+0]!=0),le+=16;fe>>=2,vn=vn>>1|bn<<5}re|=ee<<4*me,hn|=fe<<4<<me,Be|=(240&vn)<<me}gt.la=hn,he.la=Be,qt.Hc=oe,qt.Gc=re,qt.ia=43690&re?0:Pt.ia,St=!(oe|re)}if(0<H.L&&(H.wa[H.Y+H.ja]=H.gd[nt.$b][nt.Za],H.wa[H.Y+H.ja].La|=!St),ct.Ka)return ke(W,7,"Premature end-of-file encountered.")}if(Oi(W),H=dt,ct=1,nt=(U=W).D,gt=0<U.L&&U.M>=U.zb&&U.M<=U.Va,U.Aa==0)t:{if(nt.M=U.M,nt.uc=gt,Xi(U,nt),ct=1,nt=(ee=U.D).Nb,gt=(re=Qa[U.L])*U.R,Mt=re/2*U.B,ve=16*nt*U.R,fn=8*nt*U.B,St=U.sa,qt=U.ta-gt+ve,Pt=U.qa,ht=U.ra-Mt+fn,le=U.Ha,he=U.Ia-Mt+fn,vn=(fe=ee.M)==0,oe=fe>=U.Va-1,U.Aa==2&&Xi(U,ee),ee.uc)for(bn=(Re=U).D.M,e(Re.D.uc),ee=Re.yb;ee<Re.Hb;++ee){Be=ee,hn=bn;var Ve=(Je=(Ce=Re).D).Nb;me=Ce.R;var Je=Je.wa[Je.Y+Be],on=Ce.sa,_n=Ce.ta+16*Ve*me+16*Be,Kn=Je.dd,We=Je.tc;if(We!=0)if(e(3<=We),Ce.L==1)0<Be&&br(on,_n,me,We+4),Je.La&&An(on,_n,me,We),0<hn&&Ur(on,_n,me,We+4),Je.La&&ia(on,_n,me,We);else{var ur=Ce.B,zr=Ce.qa,bi=Ce.ra+8*Ve*ur+8*Be,Nr=Ce.Ha,Ce=Ce.Ia+8*Ve*ur+8*Be;Ve=Je.ld,0<Be&&(za(on,_n,me,We+4,Kn,Ve),Br(zr,bi,Nr,Ce,ur,We+4,Kn,Ve)),Je.La&&(vr(on,_n,me,We,Kn,Ve),gi(zr,bi,Nr,Ce,ur,We,Kn,Ve)),0<hn&&(mi(on,_n,me,We+4,Kn,Ve),ra(zr,bi,Nr,Ce,ur,We+4,Kn,Ve)),Je.La&&(Rr(on,_n,me,We,Kn,Ve),qr(zr,bi,Nr,Ce,ur,We,Kn,Ve))}}if(U.ia&&alert("todo:DitherRow"),H.put!=null){if(ee=16*fe,fe=16*(fe+1),vn?(H.y=U.sa,H.O=U.ta+ve,H.f=U.qa,H.N=U.ra+fn,H.ea=U.Ha,H.W=U.Ia+fn):(ee-=re,H.y=St,H.O=qt,H.f=Pt,H.N=ht,H.ea=le,H.W=he),oe||(fe-=re),fe>H.o&&(fe=H.o),H.F=null,H.J=null,U.Fa!=null&&0<U.Fa.length&&ee<fe&&(H.J=Ji(U,H,ee,fe-ee),H.F=U.mb,H.F==null&&H.F.length==0)){ct=ke(U,3,"Could not decode alpha data.");break t}ee<H.j&&(re=H.j-ee,ee=H.j,e(!(1&re)),H.O+=U.R*re,H.N+=U.B*(re>>1),H.W+=U.B*(re>>1),H.F!=null&&(H.J+=H.width*re)),ee<fe&&(H.O+=H.v,H.N+=H.v>>1,H.W+=H.v>>1,H.F!=null&&(H.J+=H.v),H.ka=ee-H.j,H.U=H.va-H.v,H.T=fe-ee,ct=H.put(H))}nt+1!=U.Ic||oe||(a(U.sa,U.ta-gt,St,qt+16*U.R,gt),a(U.qa,U.ra-Mt,Pt,ht+8*U.B,Mt),a(U.Ha,U.Ia-Mt,le,he+8*U.B,Mt))}if(!ct)return ke(W,6,"Output aborted.")}return 1}(t,i)),i.bc!=null&&i.bc(i),u&=1}return u?(t.cb=0,u):0}function Dn(t,i,u,d,g){g=t[i+u+32*d]+(g>>3),t[i+u+32*d]=-256&g?0>g?0:255:g}function ti(t,i,u,d,g,b){Dn(t,i,0,u,d+g),Dn(t,i,1,u,d+b),Dn(t,i,2,u,d-b),Dn(t,i,3,u,d-g)}function ln(t){return(20091*t>>16)+t}function Bi(t,i,u,d){var g,b=0,y=s(16);for(g=0;4>g;++g){var A=t[i+0]+t[i+8],S=t[i+0]-t[i+8],P=(35468*t[i+4]>>16)-ln(t[i+12]),q=ln(t[i+4])+(35468*t[i+12]>>16);y[b+0]=A+q,y[b+1]=S+P,y[b+2]=S-P,y[b+3]=A-q,b+=4,i++}for(g=b=0;4>g;++g)A=(t=y[b+0]+4)+y[b+8],S=t-y[b+8],P=(35468*y[b+4]>>16)-ln(y[b+12]),Dn(u,d,0,0,A+(q=ln(y[b+4])+(35468*y[b+12]>>16))),Dn(u,d,1,0,S+P),Dn(u,d,2,0,S-P),Dn(u,d,3,0,A-q),b++,d+=32}function Sa(t,i,u,d){var g=t[i+0]+4,b=35468*t[i+4]>>16,y=ln(t[i+4]),A=35468*t[i+1]>>16;ti(u,d,0,g+y,t=ln(t[i+1]),A),ti(u,d,1,g+b,t,A),ti(u,d,2,g-b,t,A),ti(u,d,3,g-y,t,A)}function To(t,i,u,d,g){Bi(t,i,u,d),g&&Bi(t,i+16,u,d+4)}function _a(t,i,u,d){sr(t,i+0,u,d,1),sr(t,i+32,u,d+128,1)}function Do(t,i,u,d){var g;for(t=t[i+0]+4,g=0;4>g;++g)for(i=0;4>i;++i)Dn(u,d,i,g,t)}function Pa(t,i,u,d){t[i+0]&&an(t,i+0,u,d),t[i+16]&&an(t,i+16,u,d+4),t[i+32]&&an(t,i+32,u,d+128),t[i+48]&&an(t,i+48,u,d+128+4)}function Mo(t,i,u,d){var g,b=s(16);for(g=0;4>g;++g){var y=t[i+0+g]+t[i+12+g],A=t[i+4+g]+t[i+8+g],S=t[i+4+g]-t[i+8+g],P=t[i+0+g]-t[i+12+g];b[0+g]=y+A,b[8+g]=y-A,b[4+g]=P+S,b[12+g]=P-S}for(g=0;4>g;++g)y=(t=b[0+4*g]+3)+b[3+4*g],A=b[1+4*g]+b[2+4*g],S=b[1+4*g]-b[2+4*g],P=t-b[3+4*g],u[d+0]=y+A>>3,u[d+16]=P+S>>3,u[d+32]=y-A>>3,u[d+48]=P-S>>3,d+=64}function Ri(t,i,u){var d,g=i-32,b=gn,y=255-t[g-1];for(d=0;d<u;++d){var A,S=b,P=y+t[i-1];for(A=0;A<u;++A)t[i+A]=S[P+t[g+A]];i+=32}}function jo(t,i){Ri(t,i,4)}function Eo(t,i){Ri(t,i,8)}function Oo(t,i){Ri(t,i,16)}function rr(t,i){var u;for(u=0;16>u;++u)a(t,i+32*u,t,i-32,16)}function ei(t,i){var u;for(u=16;0<u;--u)o(t,i,t[i-1],16),i+=32}function ni(t,i,u){var d;for(d=0;16>d;++d)o(i,u+32*d,t,16)}function Ca(t,i){var u,d=16;for(u=0;16>u;++u)d+=t[i-1+32*u]+t[i+u-32];ni(d>>5,t,i)}function Xe(t,i){var u,d=8;for(u=0;16>u;++u)d+=t[i-1+32*u];ni(d>>4,t,i)}function Bo(t,i){var u,d=8;for(u=0;16>u;++u)d+=t[i+u-32];ni(d>>4,t,i)}function Ia(t,i){ni(128,t,i)}function $t(t,i,u){return t+2*i+u+2>>2}function Ro(t,i){var u,d=i-32;for(d=new Uint8Array([$t(t[d-1],t[d+0],t[d+1]),$t(t[d+0],t[d+1],t[d+2]),$t(t[d+1],t[d+2],t[d+3]),$t(t[d+2],t[d+3],t[d+4])]),u=0;4>u;++u)a(t,i+32*u,d,0,d.length)}function qo(t,i){var u=t[i-1],d=t[i-1+32],g=t[i-1+64],b=t[i-1+96];Lt(t,i+0,16843009*$t(t[i-1-32],u,d)),Lt(t,i+32,16843009*$t(u,d,g)),Lt(t,i+64,16843009*$t(d,g,b)),Lt(t,i+96,16843009*$t(g,b,b))}function Yn(t,i){var u,d=4;for(u=0;4>u;++u)d+=t[i+u-32]+t[i-1+32*u];for(d>>=3,u=0;4>u;++u)o(t,i+32*u,d,4)}function ka(t,i){var u=t[i-1+0],d=t[i-1+32],g=t[i-1+64],b=t[i-1-32],y=t[i+0-32],A=t[i+1-32],S=t[i+2-32],P=t[i+3-32];t[i+0+96]=$t(d,g,t[i-1+96]),t[i+1+96]=t[i+0+64]=$t(u,d,g),t[i+2+96]=t[i+1+64]=t[i+0+32]=$t(b,u,d),t[i+3+96]=t[i+2+64]=t[i+1+32]=t[i+0+0]=$t(y,b,u),t[i+3+64]=t[i+2+32]=t[i+1+0]=$t(A,y,b),t[i+3+32]=t[i+2+0]=$t(S,A,y),t[i+3+0]=$t(P,S,A)}function Fa(t,i){var u=t[i+1-32],d=t[i+2-32],g=t[i+3-32],b=t[i+4-32],y=t[i+5-32],A=t[i+6-32],S=t[i+7-32];t[i+0+0]=$t(t[i+0-32],u,d),t[i+1+0]=t[i+0+32]=$t(u,d,g),t[i+2+0]=t[i+1+32]=t[i+0+64]=$t(d,g,b),t[i+3+0]=t[i+2+32]=t[i+1+64]=t[i+0+96]=$t(g,b,y),t[i+3+32]=t[i+2+64]=t[i+1+96]=$t(b,y,A),t[i+3+64]=t[i+2+96]=$t(y,A,S),t[i+3+96]=$t(A,S,S)}function ri(t,i){var u=t[i-1+0],d=t[i-1+32],g=t[i-1+64],b=t[i-1-32],y=t[i+0-32],A=t[i+1-32],S=t[i+2-32],P=t[i+3-32];t[i+0+0]=t[i+1+64]=b+y+1>>1,t[i+1+0]=t[i+2+64]=y+A+1>>1,t[i+2+0]=t[i+3+64]=A+S+1>>1,t[i+3+0]=S+P+1>>1,t[i+0+96]=$t(g,d,u),t[i+0+64]=$t(d,u,b),t[i+0+32]=t[i+1+96]=$t(u,b,y),t[i+1+32]=t[i+2+96]=$t(b,y,A),t[i+2+32]=t[i+3+96]=$t(y,A,S),t[i+3+32]=$t(A,S,P)}function qi(t,i){var u=t[i+0-32],d=t[i+1-32],g=t[i+2-32],b=t[i+3-32],y=t[i+4-32],A=t[i+5-32],S=t[i+6-32],P=t[i+7-32];t[i+0+0]=u+d+1>>1,t[i+1+0]=t[i+0+64]=d+g+1>>1,t[i+2+0]=t[i+1+64]=g+b+1>>1,t[i+3+0]=t[i+2+64]=b+y+1>>1,t[i+0+32]=$t(u,d,g),t[i+1+32]=t[i+0+96]=$t(d,g,b),t[i+2+32]=t[i+1+96]=$t(g,b,y),t[i+3+32]=t[i+2+96]=$t(b,y,A),t[i+3+64]=$t(y,A,S),t[i+3+96]=$t(A,S,P)}function Uo(t,i){var u=t[i-1+0],d=t[i-1+32],g=t[i-1+64],b=t[i-1+96];t[i+0+0]=u+d+1>>1,t[i+2+0]=t[i+0+32]=d+g+1>>1,t[i+2+32]=t[i+0+64]=g+b+1>>1,t[i+1+0]=$t(u,d,g),t[i+3+0]=t[i+1+32]=$t(d,g,b),t[i+3+32]=t[i+1+64]=$t(g,b,b),t[i+3+64]=t[i+2+64]=t[i+0+96]=t[i+1+96]=t[i+2+96]=t[i+3+96]=b}function zo(t,i){var u=t[i-1+0],d=t[i-1+32],g=t[i-1+64],b=t[i-1+96],y=t[i-1-32],A=t[i+0-32],S=t[i+1-32],P=t[i+2-32];t[i+0+0]=t[i+2+32]=u+y+1>>1,t[i+0+32]=t[i+2+64]=d+u+1>>1,t[i+0+64]=t[i+2+96]=g+d+1>>1,t[i+0+96]=b+g+1>>1,t[i+3+0]=$t(A,S,P),t[i+2+0]=$t(y,A,S),t[i+1+0]=t[i+3+32]=$t(u,y,A),t[i+1+32]=t[i+3+64]=$t(d,u,y),t[i+1+64]=t[i+3+96]=$t(g,d,u),t[i+1+96]=$t(b,g,d)}function Ho(t,i){var u;for(u=0;8>u;++u)a(t,i+32*u,t,i-32,8)}function Ui(t,i){var u;for(u=0;8>u;++u)o(t,i,t[i-1],8),i+=32}function Dr(t,i,u){var d;for(d=0;8>d;++d)o(i,u+32*d,t,8)}function fr(t,i){var u,d=8;for(u=0;8>u;++u)d+=t[i+u-32]+t[i-1+32*u];Dr(d>>4,t,i)}function Vo(t,i){var u,d=4;for(u=0;8>u;++u)d+=t[i+u-32];Dr(d>>3,t,i)}function Mr(t,i){var u,d=4;for(u=0;8>u;++u)d+=t[i-1+32*u];Dr(d>>3,t,i)}function zi(t,i){Dr(128,t,i)}function ii(t,i,u){var d=t[i-u],g=t[i+0],b=3*(g-d)+Ko[1020+t[i-2*u]-t[i+u]],y=Va[112+(b+4>>3)];t[i-u]=gn[255+d+Va[112+(b+3>>3)]],t[i+0]=gn[255+g-y]}function Ta(t,i,u,d){var g=t[i+0],b=t[i+u];return xn[255+t[i-2*u]-t[i-u]]>d||xn[255+b-g]>d}function Da(t,i,u,d){return 4*xn[255+t[i-u]-t[i+0]]+xn[255+t[i-2*u]-t[i+u]]<=d}function Ma(t,i,u,d,g){var b=t[i-3*u],y=t[i-2*u],A=t[i-u],S=t[i+0],P=t[i+u],q=t[i+2*u],K=t[i+3*u];return 4*xn[255+A-S]+xn[255+y-P]>d?0:xn[255+t[i-4*u]-b]<=g&&xn[255+b-y]<=g&&xn[255+y-A]<=g&&xn[255+K-q]<=g&&xn[255+q-P]<=g&&xn[255+P-S]<=g}function ja(t,i,u,d){var g=2*d+1;for(d=0;16>d;++d)Da(t,i+d,u,g)&&ii(t,i+d,u)}function $n(t,i,u,d){var g=2*d+1;for(d=0;16>d;++d)Da(t,i+d*u,1,g)&&ii(t,i+d*u,1)}function ir(t,i,u,d){var g;for(g=3;0<g;--g)ja(t,i+=4*u,u,d)}function Wo(t,i,u,d){var g;for(g=3;0<g;--g)$n(t,i+=4,u,d)}function dr(t,i,u,d,g,b,y,A){for(b=2*b+1;0<g--;){if(Ma(t,i,u,b,y))if(Ta(t,i,u,A))ii(t,i,u);else{var S=t,P=i,q=u,K=S[P-2*q],X=S[P-q],W=S[P+0],dt=S[P+q],nt=S[P+2*q],U=27*(ct=Ko[1020+3*(W-X)+Ko[1020+K-dt]])+63>>7,H=18*ct+63>>7,ct=9*ct+63>>7;S[P-3*q]=gn[255+S[P-3*q]+ct],S[P-2*q]=gn[255+K+H],S[P-q]=gn[255+X+U],S[P+0]=gn[255+W-U],S[P+q]=gn[255+dt-H],S[P+2*q]=gn[255+nt-ct]}i+=d}}function Mn(t,i,u,d,g,b,y,A){for(b=2*b+1;0<g--;){if(Ma(t,i,u,b,y))if(Ta(t,i,u,A))ii(t,i,u);else{var S=t,P=i,q=u,K=S[P-q],X=S[P+0],W=S[P+q],dt=Va[112+((nt=3*(X-K))+4>>3)],nt=Va[112+(nt+3>>3)],U=dt+1>>1;S[P-2*q]=gn[255+S[P-2*q]+U],S[P-q]=gn[255+K+nt],S[P+0]=gn[255+X-dt],S[P+q]=gn[255+W-U]}i+=d}}function Hi(t,i,u,d,g,b){dr(t,i,u,1,16,d,g,b)}function jr(t,i,u,d,g,b){dr(t,i,1,u,16,d,g,b)}function Go(t,i,u,d,g,b){var y;for(y=3;0<y;--y)Mn(t,i+=4*u,u,1,16,d,g,b)}function ai(t,i,u,d,g,b){var y;for(y=3;0<y;--y)Mn(t,i+=4,1,u,16,d,g,b)}function Yo(t,i,u,d,g,b,y,A){dr(t,i,g,1,8,b,y,A),dr(u,d,g,1,8,b,y,A)}function Vi(t,i,u,d,g,b,y,A){dr(t,i,1,g,8,b,y,A),dr(u,d,1,g,8,b,y,A)}function Wi(t,i,u,d,g,b,y,A){Mn(t,i+4*g,g,1,8,b,y,A),Mn(u,d+4*g,g,1,8,b,y,A)}function Ea(t,i,u,d,g,b,y,A){Mn(t,i+4,1,g,8,b,y,A),Mn(u,d+4,1,g,8,b,y,A)}function oi(){this.ba=new Cn,this.ec=[],this.cc=[],this.Mc=[],this.Dc=this.Nc=this.dc=this.fc=0,this.Oa=new _e,this.memory=0,this.Ib="OutputFunc",this.Jb="OutputAlphaFunc",this.Nd="OutputRowFunc"}function Gi(){this.data=[],this.offset=this.kd=this.ha=this.w=0,this.na=[],this.xa=this.gb=this.Ja=this.Sa=this.P=0}function Yi(){this.nc=this.Ea=this.b=this.hc=0,this.K=[],this.w=0}function Oa(){this.ua=0,this.Wa=new M,this.vb=new M,this.md=this.xc=this.wc=0,this.vc=[],this.Wb=0,this.Ya=new R,this.yc=new I}function $o(){this.xb=this.a=0,this.l=new Zr,this.ca=new Cn,this.V=[],this.Ba=0,this.Ta=[],this.Ua=0,this.m=new N,this.Pb=0,this.wd=new N,this.Ma=this.$=this.C=this.i=this.c=this.xd=0,this.s=new Oa,this.ab=0,this.gc=c(4,Yi),this.Oc=0}function si(){this.Lc=this.Z=this.$a=this.i=this.c=0,this.l=new Zr,this.ic=0,this.ca=[],this.tb=0,this.qd=null,this.rd=0}function Er(t,i,u,d,g,b,y){for(t=t==null?0:t[i+0],i=0;i<y;++i)g[b+i]=t+u[d+i]&255,t=g[b+i]}function $i(t,i,u,d,g,b,y){var A;if(t==null)Er(null,null,u,d,g,b,y);else for(A=0;A<y;++A)g[b+A]=t[i+A]+u[d+A]&255}function pr(t,i,u,d,g,b,y){if(t==null)Er(null,null,u,d,g,b,y);else{var A,S=t[i+0],P=S,q=S;for(A=0;A<y;++A)P=q+(S=t[i+A])-P,q=u[d+A]+(-256&P?0>P?0:255:P)&255,P=S,g[b+A]=q}}function Ji(t,i,u,d){var g=i.width,b=i.o;if(e(t!=null&&i!=null),0>u||0>=d||u+d>b)return null;if(!t.Cc){if(t.ga==null){var y;if(t.ga=new si,(y=t.ga==null)||(y=i.width*i.o,e(t.Gb.length==0),t.Gb=s(y),t.Uc=0,t.Gb==null?y=0:(t.mb=t.Gb,t.nb=t.Uc,t.rc=null,y=1),y=!y),!y){y=t.ga;var A=t.Fa,S=t.P,P=t.qc,q=t.mb,K=t.nb,X=S+1,W=P-1,dt=y.l;if(e(A!=null&&q!=null&&i!=null),wr[0]=null,wr[1]=Er,wr[2]=$i,wr[3]=pr,y.ca=q,y.tb=K,y.c=i.width,y.i=i.height,e(0<y.c&&0<y.i),1>=P)i=0;else if(y.$a=A[S+0]>>0&3,y.Z=A[S+0]>>2&3,y.Lc=A[S+0]>>4&3,S=A[S+0]>>6&3,0>y.$a||1<y.$a||4<=y.Z||1<y.Lc||S)i=0;else if(dt.put=zn,dt.ac=Ye,dt.bc=Hn,dt.ma=y,dt.width=i.width,dt.height=i.height,dt.Da=i.Da,dt.v=i.v,dt.va=i.va,dt.j=i.j,dt.o=i.o,y.$a)t:{e(y.$a==1),i=yn();e:for(;;){if(i==null){i=0;break t}if(e(y!=null),y.mc=i,i.c=y.c,i.i=y.i,i.l=y.l,i.l.ma=y,i.l.width=y.c,i.l.height=y.i,i.a=0,tt(i.m,A,X,W),!Gn(y.c,y.i,1,i,null)||(i.ab==1&&i.gc[0].hc==3&&nr(i.s)?(y.ic=1,A=i.c*i.i,i.Ta=null,i.Ua=0,i.V=s(A),i.Ba=0,i.V==null?(i.a=1,i=0):i=1):(y.ic=0,i=Tr(i,y.c)),!i))break e;i=1;break t}y.mc=null,i=0}else i=W>=y.c*y.i;y=!i}if(y)return null;t.ga.Lc!=1?t.Ga=0:d=b-u}e(t.ga!=null),e(u+d<=b);t:{if(i=(A=t.ga).c,b=A.l.o,A.$a==0){if(X=t.rc,W=t.Vc,dt=t.Fa,S=t.P+1+u*i,P=t.mb,q=t.nb+u*i,e(S<=t.P+t.qc),A.Z!=0)for(e(wr[A.Z]!=null),y=0;y<d;++y)wr[A.Z](X,W,dt,S,P,q,i),X=P,W=q,q+=i,S+=i;else for(y=0;y<d;++y)a(P,q,dt,S,i),X=P,W=q,q+=i,S+=i;t.rc=X,t.Vc=W}else{if(e(A.mc!=null),i=u+d,e((y=A.mc)!=null),e(i<=y.i),y.C>=i)i=1;else if(A.ic||$(),A.ic){A=y.V,X=y.Ba,W=y.c;var nt=y.i,U=(dt=1,S=y.$/W,P=y.$%W,q=y.m,K=y.s,y.$),H=W*nt,ct=W*i,gt=K.wc,ht=U<ct?Ee(K,P,S):null;e(U<=H),e(i<=nt),e(nr(K));e:for(;;){for(;!q.h&&U<ct;){if(P&gt||(ht=Ee(K,P,S)),e(ht!=null),Y(q),256>(nt=sn(ht.G[0],ht.H[0],q)))A[X+U]=nt,++U,++P>=W&&(P=0,++S<=i&&!(S%16)&&Fn(y,S));else{if(!(280>nt)){dt=0;break e}nt=In(nt-256,q);var Mt,St=sn(ht.G[4],ht.H[4],q);if(Y(q),!(U>=(St=Vn(W,St=In(St,q)))&&H-U>=nt)){dt=0;break e}for(Mt=0;Mt<nt;++Mt)A[X+U+Mt]=A[X+U+Mt-St];for(U+=nt,P+=nt;P>=W;)P-=W,++S<=i&&!(S%16)&&Fn(y,S);U<ct&&P&gt&&(ht=Ee(K,P,S))}e(q.h==E(q))}Fn(y,S>i?i:S);break e}!dt||q.h&&U<H?(dt=0,y.a=q.h?5:3):y.$=U,i=dt}else i=Tn(y,y.V,y.Ba,y.c,y.i,i,$r);if(!i){d=0;break t}}u+d>=b&&(t.Cc=1),d=1}if(!d)return null;if(t.Cc&&((d=t.ga)!=null&&(d.mc=null),t.ga=null,0<t.Ga))return alert("todo:WebPDequantizeLevels"),null}return t.nb+u*g}function l(t,i,u,d,g,b){for(;0<g--;){var y,A=t,S=i+(u?1:0),P=t,q=i+(u?0:3);for(y=0;y<d;++y){var K=P[q+4*y];K!=255&&(K*=32897,A[S+4*y+0]=A[S+4*y+0]*K>>23,A[S+4*y+1]=A[S+4*y+1]*K>>23,A[S+4*y+2]=A[S+4*y+2]*K>>23)}i+=b}}function v(t,i,u,d,g){for(;0<d--;){var b;for(b=0;b<u;++b){var y=t[i+2*b+0],A=15&(P=t[i+2*b+1]),S=4369*A,P=(240&P|P>>4)*S>>16;t[i+2*b+0]=(240&y|y>>4)*S>>16&240|(15&y|y<<4)*S>>16>>4&15,t[i+2*b+1]=240&P|A}i+=g}}function F(t,i,u,d,g,b,y,A){var S,P,q=255;for(P=0;P<g;++P){for(S=0;S<d;++S){var K=t[i+S];b[y+4*S]=K,q&=K}i+=u,y+=A}return q!=255}function B(t,i,u,d,g){var b;for(b=0;b<g;++b)u[d+b]=t[i+b]>>8}function $(){Ln=l,we=v,Ne=F,Te=B}function at(t,i,u){G[t]=function(d,g,b,y,A,S,P,q,K,X,W,dt,nt,U,H,ct,gt){var ht,Mt=gt-1>>1,St=A[S+0]|P[q+0]<<16,qt=K[X+0]|W[dt+0]<<16;e(d!=null);var Pt=3*St+qt+131074>>2;for(i(d[g+0],255&Pt,Pt>>16,nt,U),b!=null&&(Pt=3*qt+St+131074>>2,i(b[y+0],255&Pt,Pt>>16,H,ct)),ht=1;ht<=Mt;++ht){var le=A[S+ht]|P[q+ht]<<16,he=K[X+ht]|W[dt+ht]<<16,oe=St+le+qt+he+524296,re=oe+2*(le+qt)>>3;Pt=re+St>>1,St=(oe=oe+2*(St+he)>>3)+le>>1,i(d[g+2*ht-1],255&Pt,Pt>>16,nt,U+(2*ht-1)*u),i(d[g+2*ht-0],255&St,St>>16,nt,U+(2*ht-0)*u),b!=null&&(Pt=oe+qt>>1,St=re+he>>1,i(b[y+2*ht-1],255&Pt,Pt>>16,H,ct+(2*ht-1)*u),i(b[y+2*ht+0],255&St,St>>16,H,ct+(2*ht+0)*u)),St=le,qt=he}1&gt||(Pt=3*St+qt+131074>>2,i(d[g+gt-1],255&Pt,Pt>>16,nt,U+(gt-1)*u),b!=null&&(Pt=3*qt+St+131074>>2,i(b[y+gt-1],255&Pt,Pt>>16,H,ct+(gt-1)*u)))}}function yt(){Sn[Wa]=mc,Sn[Ga]=zs,Sn[Os]=gc,Sn[Ya]=Hs,Sn[$a]=Vs,Sn[Xo]=Ws,Sn[Bs]=vc,Sn[Qo]=zs,Sn[Zo]=Hs,Sn[Ja]=Vs,Sn[ts]=Ws}function Dt(t){return t&~bc?0>t?0:255:t>>Gs}function Rt(t,i){return Dt((19077*t>>8)+(26149*i>>8)-14234)}function Zt(t,i,u){return Dt((19077*t>>8)-(6419*i>>8)-(13320*u>>8)+8708)}function Xt(t,i){return Dt((19077*t>>8)+(33050*i>>8)-17685)}function ae(t,i,u,d,g){d[g+0]=Rt(t,u),d[g+1]=Zt(t,i,u),d[g+2]=Xt(t,i)}function Le(t,i,u,d,g){d[g+0]=Xt(t,i),d[g+1]=Zt(t,i,u),d[g+2]=Rt(t,u)}function Pe(t,i,u,d,g){var b=Zt(t,i,u);i=b<<3&224|Xt(t,i)>>3,d[g+0]=248&Rt(t,u)|b>>5,d[g+1]=i}function Oe(t,i,u,d,g){var b=240&Xt(t,i)|15;d[g+0]=240&Rt(t,u)|Zt(t,i,u)>>4,d[g+1]=b}function Qe(t,i,u,d,g){d[g+0]=255,ae(t,i,u,d,g+1)}function He(t,i,u,d,g){Le(t,i,u,d,g),d[g+3]=255}function jn(t,i,u,d,g){ae(t,i,u,d,g),d[g+3]=255}function mn(t,i){return 0>t?0:t>i?i:t}function Jn(t,i,u){G[t]=function(d,g,b,y,A,S,P,q,K){for(var X=q+(-2&K)*u;q!=X;)i(d[g+0],b[y+0],A[S+0],P,q),i(d[g+1],b[y+0],A[S+0],P,q+u),g+=2,++y,++S,q+=2*u;1&K&&i(d[g+0],b[y+0],A[S+0],P,q)}}function Ba(t,i,u){return u==0?t==0?i==0?6:5:i==0?4:0:u}function Ki(t,i,u,d,g){switch(t>>>30){case 3:sr(i,u,d,g,0);break;case 2:Fe(i,u,d,g);break;case 1:an(i,u,d,g)}}function Xi(t,i){var u,d,g=i.M,b=i.Nb,y=t.oc,A=t.pc+40,S=t.oc,P=t.pc+584,q=t.oc,K=t.pc+600;for(u=0;16>u;++u)y[A+32*u-1]=129;for(u=0;8>u;++u)S[P+32*u-1]=129,q[K+32*u-1]=129;for(0<g?y[A-1-32]=S[P-1-32]=q[K-1-32]=129:(o(y,A-32-1,127,21),o(S,P-32-1,127,9),o(q,K-32-1,127,9)),d=0;d<t.za;++d){var X=i.ya[i.aa+d];if(0<d){for(u=-1;16>u;++u)a(y,A+32*u-4,y,A+32*u+12,4);for(u=-1;8>u;++u)a(S,P+32*u-4,S,P+32*u+4,4),a(q,K+32*u-4,q,K+32*u+4,4)}var W=t.Gd,dt=t.Hd+d,nt=X.ad,U=X.Hc;if(0<g&&(a(y,A-32,W[dt].y,0,16),a(S,P-32,W[dt].f,0,8),a(q,K-32,W[dt].ea,0,8)),X.Za){var H=y,ct=A-32+16;for(0<g&&(d>=t.za-1?o(H,ct,W[dt].y[15],4):a(H,ct,W[dt+1].y,0,4)),u=0;4>u;u++)H[ct+128+u]=H[ct+256+u]=H[ct+384+u]=H[ct+0+u];for(u=0;16>u;++u,U<<=2)H=y,ct=A+$s[u],En[X.Ob[u]](H,ct),Ki(U,nt,16*+u,H,ct)}else if(H=Ba(d,g,X.Ob[0]),yr[H](y,A),U!=0)for(u=0;16>u;++u,U<<=2)Ki(U,nt,16*+u,y,A+$s[u]);for(u=X.Gc,H=Ba(d,g,X.Dd),cr[H](S,P),cr[H](q,K),U=nt,H=S,ct=P,255&(X=u>>0)&&(170&X?na(U,256,H,ct):Nn(U,256,H,ct)),X=q,U=K,255&(u>>=8)&&(170&u?na(nt,320,X,U):Nn(nt,320,X,U)),g<t.Ub-1&&(a(W[dt].y,0,y,A+480,16),a(W[dt].f,0,S,P+224,8),a(W[dt].ea,0,q,K+224,8)),u=8*b*t.B,W=t.sa,dt=t.ta+16*d+16*b*t.R,nt=t.qa,X=t.ra+8*d+u,U=t.Ha,H=t.Ia+8*d+u,u=0;16>u;++u)a(W,dt+u*t.R,y,A+32*u,16);for(u=0;8>u;++u)a(nt,X+u*t.B,S,P+32*u,8),a(U,H+u*t.B,q,K+32*u,8)}}function li(t,i,u,d,g,b,y,A,S){var P=[0],q=[0],K=0,X=S!=null?S.kd:0,W=S??new Gi;if(t==null||12>u)return 7;W.data=t,W.w=i,W.ha=u,i=[i],u=[u],W.gb=[W.gb];t:{var dt=i,nt=u,U=W.gb;if(e(t!=null),e(nt!=null),e(U!=null),U[0]=0,12<=nt[0]&&!n(t,dt[0],"RIFF")){if(n(t,dt[0]+8,"WEBP")){U=3;break t}var H=kt(t,dt[0]+4);if(12>H||4294967286<H){U=3;break t}if(X&&H>nt[0]-8){U=7;break t}U[0]=H,dt[0]+=12,nt[0]-=12}U=0}if(U!=0)return U;for(H=0<W.gb[0],u=u[0];;){t:{var ct=t;nt=i,U=u;var gt=P,ht=q,Mt=dt=[0];if((Pt=K=[K])[0]=0,8>U[0])U=7;else{if(!n(ct,nt[0],"VP8X")){if(kt(ct,nt[0]+4)!=10){U=3;break t}if(18>U[0]){U=7;break t}var St=kt(ct,nt[0]+8),qt=1+Tt(ct,nt[0]+12);if(2147483648<=qt*(ct=1+Tt(ct,nt[0]+15))){U=3;break t}Mt!=null&&(Mt[0]=St),gt!=null&&(gt[0]=qt),ht!=null&&(ht[0]=ct),nt[0]+=18,U[0]-=18,Pt[0]=1}U=0}}if(K=K[0],dt=dt[0],U!=0)return U;if(nt=!!(2&dt),!H&&K)return 3;if(b!=null&&(b[0]=!!(16&dt)),y!=null&&(y[0]=nt),A!=null&&(A[0]=0),y=P[0],dt=q[0],K&&nt&&S==null){U=0;break}if(4>u){U=7;break}if(H&&K||!H&&!K&&!n(t,i[0],"ALPH")){u=[u],W.na=[W.na],W.P=[W.P],W.Sa=[W.Sa];t:{St=t,U=i,H=u;var Pt=W.gb;gt=W.na,ht=W.P,Mt=W.Sa,qt=22,e(St!=null),e(H!=null),ct=U[0];var le=H[0];for(e(gt!=null),e(Mt!=null),gt[0]=null,ht[0]=null,Mt[0]=0;;){if(U[0]=ct,H[0]=le,8>le){U=7;break t}var he=kt(St,ct+4);if(4294967286<he){U=3;break t}var oe=8+he+1&-2;if(qt+=oe,0<Pt&&qt>Pt){U=3;break t}if(!n(St,ct,"VP8 ")||!n(St,ct,"VP8L")){U=0;break t}if(le[0]<oe){U=7;break t}n(St,ct,"ALPH")||(gt[0]=St,ht[0]=ct+8,Mt[0]=he),ct+=oe,le-=oe}}if(u=u[0],W.na=W.na[0],W.P=W.P[0],W.Sa=W.Sa[0],U!=0)break}u=[u],W.Ja=[W.Ja],W.xa=[W.xa];t:if(Pt=t,U=i,H=u,gt=W.gb[0],ht=W.Ja,Mt=W.xa,St=U[0],ct=!n(Pt,St,"VP8 "),qt=!n(Pt,St,"VP8L"),e(Pt!=null),e(H!=null),e(ht!=null),e(Mt!=null),8>H[0])U=7;else{if(ct||qt){if(Pt=kt(Pt,St+4),12<=gt&&Pt>gt-12){U=3;break t}if(X&&Pt>H[0]-8){U=7;break t}ht[0]=Pt,U[0]+=8,H[0]-=8,Mt[0]=qt}else Mt[0]=5<=H[0]&&Pt[St+0]==47&&!(Pt[St+4]>>5),ht[0]=H[0];U=0}if(u=u[0],W.Ja=W.Ja[0],W.xa=W.xa[0],i=i[0],U!=0)break;if(4294967286<W.Ja)return 3;if(A==null||nt||(A[0]=W.xa?2:1),y=[y],dt=[dt],W.xa){if(5>u){U=7;break}A=y,X=dt,nt=b,t==null||5>u?t=0:5<=u&&t[i+0]==47&&!(t[i+4]>>5)?(H=[0],Pt=[0],gt=[0],tt(ht=new N,t,i,u),Ut(ht,H,Pt,gt)?(A!=null&&(A[0]=H[0]),X!=null&&(X[0]=Pt[0]),nt!=null&&(nt[0]=gt[0]),t=1):t=0):t=0}else{if(10>u){U=7;break}A=dt,t==null||10>u||!Aa(t,i+3,u-3)?t=0:(X=t[i+0]|t[i+1]<<8|t[i+2]<<16,nt=16383&(t[i+7]<<8|t[i+6]),t=16383&(t[i+9]<<8|t[i+8]),1&X||3<(X>>1&7)||!(X>>4&1)||X>>5>=W.Ja||!nt||!t?t=0:(y&&(y[0]=nt),A&&(A[0]=t),t=1))}if(!t||(y=y[0],dt=dt[0],K&&(P[0]!=y||q[0]!=dt)))return 3;S!=null&&(S[0]=W,S.offset=i-S.w,e(4294967286>i-S.w),e(S.offset==S.ha-u));break}return U==0||U==7&&K&&S==null?(b!=null&&(b[0]|=W.na!=null&&0<W.na.length),d!=null&&(d[0]=y),g!=null&&(g[0]=dt),0):U}function Qi(t,i,u){var d=i.width,g=i.height,b=0,y=0,A=d,S=g;if(i.Da=t!=null&&0<t.Da,i.Da&&(A=t.cd,S=t.bd,b=t.v,y=t.j,11>u||(b&=-2,y&=-2),0>b||0>y||0>=A||0>=S||b+A>d||y+S>g))return 0;if(i.v=b,i.j=y,i.va=b+A,i.o=y+S,i.U=A,i.T=S,i.da=t!=null&&0<t.da,i.da){if(!Yt(A,S,u=[t.ib],b=[t.hb]))return 0;i.ib=u[0],i.hb=b[0]}return i.ob=t!=null&&t.ob,i.Kb=t==null||!t.Sd,i.da&&(i.ob=i.ib<3*d/4&&i.hb<3*g/4,i.Kb=0),1}function Zi(t){if(t==null)return 2;if(11>t.S){var i=t.f.RGBA;i.fb+=(t.height-1)*i.A,i.A=-i.A}else i=t.f.kb,t=t.height,i.O+=(t-1)*i.fa,i.fa=-i.fa,i.N+=(t-1>>1)*i.Ab,i.Ab=-i.Ab,i.W+=(t-1>>1)*i.Db,i.Db=-i.Db,i.F!=null&&(i.J+=(t-1)*i.lb,i.lb=-i.lb);return 0}function ci(t,i,u,d){if(d==null||0>=t||0>=i)return 2;if(u!=null){if(u.Da){var g=u.cd,b=u.bd,y=-2&u.v,A=-2&u.j;if(0>y||0>A||0>=g||0>=b||y+g>t||A+b>i)return 2;t=g,i=b}if(u.da){if(!Yt(t,i,g=[u.ib],b=[u.hb]))return 2;t=g[0],i=b[0]}}d.width=t,d.height=i;t:{var S=d.width,P=d.height;if(t=d.S,0>=S||0>=P||!(t>=Wa&&13>t))t=2;else{if(0>=d.Rd&&d.sd==null){y=b=g=i=0;var q=(A=S*Js[t])*P;if(11>t||(b=(P+1)/2*(i=(S+1)/2),t==12&&(y=(g=S)*P)),(P=s(q+2*b+y))==null){t=1;break t}d.sd=P,11>t?((S=d.f.RGBA).eb=P,S.fb=0,S.A=A,S.size=q):((S=d.f.kb).y=P,S.O=0,S.fa=A,S.Fd=q,S.f=P,S.N=0+q,S.Ab=i,S.Cd=b,S.ea=P,S.W=0+q+b,S.Db=i,S.Ed=b,t==12&&(S.F=P,S.J=0+q+2*b),S.Tc=y,S.lb=g)}if(i=1,g=d.S,b=d.width,y=d.height,g>=Wa&&13>g)if(11>g)t=d.f.RGBA,i&=(A=Math.abs(t.A))*(y-1)+b<=t.size,i&=A>=b*Js[g],i&=t.eb!=null;else{t=d.f.kb,A=(b+1)/2,q=(y+1)/2,S=Math.abs(t.fa),P=Math.abs(t.Ab);var K=Math.abs(t.Db),X=Math.abs(t.lb),W=X*(y-1)+b;i&=S*(y-1)+b<=t.Fd,i&=P*(q-1)+A<=t.Cd,i=(i&=K*(q-1)+A<=t.Ed)&S>=b&P>=A&K>=A,i&=t.y!=null,i&=t.f!=null,i&=t.ea!=null,g==12&&(i&=X>=b,i&=W<=t.Tc,i&=t.F!=null)}else i=0;t=i?0:2}}return t!=0||u!=null&&u.fd&&(t=Zi(d)),t}var $e=64,ui=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535,131071,262143,524287,1048575,2097151,4194303,8388607,16777215],hi=24,fi=32,ta=8,cn=[0,0,1,1,2,2,2,2,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7];xt("Predictor0","PredictorAdd0"),G.Predictor0=function(){return **********},G.Predictor1=function(t){return t},G.Predictor2=function(t,i,u){return i[u+0]},G.Predictor3=function(t,i,u){return i[u+1]},G.Predictor4=function(t,i,u){return i[u-1]},G.Predictor5=function(t,i,u){return _t(_t(t,i[u+1]),i[u+0])},G.Predictor6=function(t,i,u){return _t(t,i[u-1])},G.Predictor7=function(t,i,u){return _t(t,i[u+0])},G.Predictor8=function(t,i,u){return _t(i[u-1],i[u+0])},G.Predictor9=function(t,i,u){return _t(i[u+0],i[u+1])},G.Predictor10=function(t,i,u){return _t(_t(t,i[u-1]),_t(i[u+0],i[u+1]))},G.Predictor11=function(t,i,u){var d=i[u+0];return 0>=Jt(d>>24&255,t>>24&255,(i=i[u-1])>>24&255)+Jt(d>>16&255,t>>16&255,i>>16&255)+Jt(d>>8&255,t>>8&255,i>>8&255)+Jt(255&d,255&t,255&i)?d:t},G.Predictor12=function(t,i,u){var d=i[u+0];return(Bt((t>>24&255)+(d>>24&255)-((i=i[u-1])>>24&255))<<24|Bt((t>>16&255)+(d>>16&255)-(i>>16&255))<<16|Bt((t>>8&255)+(d>>8&255)-(i>>8&255))<<8|Bt((255&t)+(255&d)-(255&i)))>>>0},G.Predictor13=function(t,i,u){var d=i[u-1];return(Wt((t=_t(t,i[u+0]))>>24&255,d>>24&255)<<24|Wt(t>>16&255,d>>16&255)<<16|Wt(t>>8&255,d>>8&255)<<8|Wt(t>>0&255,d>>0&255))>>>0};var Jo=G.PredictorAdd0;G.PredictorAdd1=te,xt("Predictor2","PredictorAdd2"),xt("Predictor3","PredictorAdd3"),xt("Predictor4","PredictorAdd4"),xt("Predictor5","PredictorAdd5"),xt("Predictor6","PredictorAdd6"),xt("Predictor7","PredictorAdd7"),xt("Predictor8","PredictorAdd8"),xt("Predictor9","PredictorAdd9"),xt("Predictor10","PredictorAdd10"),xt("Predictor11","PredictorAdd11"),xt("Predictor12","PredictorAdd12"),xt("Predictor13","PredictorAdd13");var ea=G.PredictorAdd2;ne("ColorIndexInverseTransform","MapARGB","32b",function(t){return t>>8&255},function(t){return t}),ne("VP8LColorIndexInverseTransformAlpha","MapAlpha","8b",function(t){return t},function(t){return t>>8&255});var Ra,wn=G.ColorIndexInverseTransform,di=G.MapARGB,qa=G.VP8LColorIndexInverseTransformAlpha,Ua=G.MapAlpha,mr=G.VP8LPredictorsAdd=[];mr.length=16,(G.VP8LPredictors=[]).length=16,(G.VP8LPredictorsAdd_C=[]).length=16,(G.VP8LPredictors_C=[]).length=16;var Or,un,rn,gr,ar,or,pi,sr,Fe,na,an,Nn,mi,za,ra,Br,Rr,vr,qr,gi,Ur,br,ia,An,Ln,we,Ne,Te,ze=s(511),lr=s(2041),aa=s(225),vi=s(767),Ha=0,Ko=lr,Va=aa,gn=vi,xn=ze,Wa=0,Ga=1,Os=2,Ya=3,$a=4,Xo=5,Bs=6,Qo=7,Zo=8,Ja=9,ts=10,ec=[2,3,7],nc=[3,3,11],Rs=[280,256,256,256,40],rc=[0,1,1,1,0],ic=[17,18,0,1,2,3,4,5,16,6,7,8,9,10,11,12,13,14,15],ac=[24,7,23,25,40,6,39,41,22,26,38,42,56,5,55,57,21,27,54,58,37,43,72,4,71,73,20,28,53,59,70,74,36,44,88,69,75,52,60,3,87,89,19,29,86,90,35,45,68,76,85,91,51,61,104,2,103,105,18,30,102,106,34,46,84,92,67,77,101,107,50,62,120,1,119,121,83,93,17,31,100,108,66,78,118,122,33,47,117,123,49,63,99,109,82,94,0,116,124,65,79,16,32,98,110,48,115,125,81,95,64,114,126,97,111,80,113,127,96,112],oc=[2954,2956,2958,2962,2970,2986,3018,3082,3212,3468,3980,5004],sc=8,es=[4,5,6,7,8,9,10,10,11,12,13,14,15,16,17,17,18,19,20,20,21,21,22,22,23,23,24,25,25,26,27,28,29,30,31,32,33,34,35,36,37,37,38,39,40,41,42,43,44,45,46,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,76,77,78,79,80,81,82,83,84,85,86,87,88,89,91,93,95,96,98,100,101,102,104,106,108,110,112,114,116,118,122,124,126,128,130,132,134,136,138,140,143,145,148,151,154,157],ns=[4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,60,62,64,66,68,70,72,74,76,78,80,82,84,86,88,90,92,94,96,98,100,102,104,106,108,110,112,114,116,119,122,125,128,131,134,137,140,143,146,149,152,155,158,161,164,167,170,173,177,181,185,189,193,197,201,205,209,213,217,221,225,229,234,239,245,249,254,259,264,269,274,279,284],oa=null,lc=[[173,148,140,0],[176,155,140,135,0],[180,157,141,134,130,0],[254,254,243,230,196,177,153,140,133,130,129,0]],cc=[0,1,4,8,5,2,3,6,9,12,13,10,7,11,14,15],qs=[-0,1,-1,2,-2,3,4,6,-3,5,-4,-5,-6,7,-7,8,-8,-9],uc=[[[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]],[[253,136,254,255,228,219,128,128,128,128,128],[189,129,242,255,227,213,255,219,128,128,128],[106,126,227,252,214,209,255,255,128,128,128]],[[1,98,248,255,236,226,255,255,128,128,128],[181,133,238,254,221,234,255,154,128,128,128],[78,134,202,247,198,180,255,219,128,128,128]],[[1,185,249,255,243,255,128,128,128,128,128],[184,150,247,255,236,224,128,128,128,128,128],[77,110,216,255,236,230,128,128,128,128,128]],[[1,101,251,255,241,255,128,128,128,128,128],[170,139,241,252,236,209,255,255,128,128,128],[37,116,196,243,228,255,255,255,128,128,128]],[[1,204,254,255,245,255,128,128,128,128,128],[207,160,250,255,238,128,128,128,128,128,128],[102,103,231,255,211,171,128,128,128,128,128]],[[1,152,252,255,240,255,128,128,128,128,128],[177,135,243,255,234,225,128,128,128,128,128],[80,129,211,255,194,224,128,128,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[246,1,255,128,128,128,128,128,128,128,128],[255,128,128,128,128,128,128,128,128,128,128]]],[[[198,35,237,223,193,187,162,160,145,155,62],[131,45,198,221,172,176,220,157,252,221,1],[68,47,146,208,149,167,221,162,255,223,128]],[[1,149,241,255,221,224,255,255,128,128,128],[184,141,234,253,222,220,255,199,128,128,128],[81,99,181,242,176,190,249,202,255,255,128]],[[1,129,232,253,214,197,242,196,255,255,128],[99,121,210,250,201,198,255,202,128,128,128],[23,91,163,242,170,187,247,210,255,255,128]],[[1,200,246,255,234,255,128,128,128,128,128],[109,178,241,255,231,245,255,255,128,128,128],[44,130,201,253,205,192,255,255,128,128,128]],[[1,132,239,251,219,209,255,165,128,128,128],[94,136,225,251,218,190,255,255,128,128,128],[22,100,174,245,186,161,255,199,128,128,128]],[[1,182,249,255,232,235,128,128,128,128,128],[124,143,241,255,227,234,128,128,128,128,128],[35,77,181,251,193,211,255,205,128,128,128]],[[1,157,247,255,236,231,255,255,128,128,128],[121,141,235,255,225,227,255,255,128,128,128],[45,99,188,251,195,217,255,224,128,128,128]],[[1,1,251,255,213,255,128,128,128,128,128],[203,1,248,255,255,128,128,128,128,128,128],[137,1,177,255,224,255,128,128,128,128,128]]],[[[253,9,248,251,207,208,255,192,128,128,128],[175,13,224,243,193,185,249,198,255,255,128],[73,17,171,221,161,179,236,167,255,234,128]],[[1,95,247,253,212,183,255,255,128,128,128],[239,90,244,250,211,209,255,255,128,128,128],[155,77,195,248,188,195,255,255,128,128,128]],[[1,24,239,251,218,219,255,205,128,128,128],[201,51,219,255,196,186,128,128,128,128,128],[69,46,190,239,201,218,255,228,128,128,128]],[[1,191,251,255,255,128,128,128,128,128,128],[223,165,249,255,213,255,128,128,128,128,128],[141,124,248,255,255,128,128,128,128,128,128]],[[1,16,248,255,255,128,128,128,128,128,128],[190,36,230,255,236,255,128,128,128,128,128],[149,1,255,128,128,128,128,128,128,128,128]],[[1,226,255,128,128,128,128,128,128,128,128],[247,192,255,128,128,128,128,128,128,128,128],[240,128,255,128,128,128,128,128,128,128,128]],[[1,134,252,255,255,128,128,128,128,128,128],[213,62,250,255,255,128,128,128,128,128,128],[55,93,255,128,128,128,128,128,128,128,128]],[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]]],[[[202,24,213,235,186,191,220,160,240,175,255],[126,38,182,232,169,184,228,174,255,187,128],[61,46,138,219,151,178,240,170,255,216,128]],[[1,112,230,250,199,191,247,159,255,255,128],[166,109,228,252,211,215,255,174,128,128,128],[39,77,162,232,172,180,245,178,255,255,128]],[[1,52,220,246,198,199,249,220,255,255,128],[124,74,191,243,183,193,250,221,255,255,128],[24,71,130,219,154,170,243,182,255,255,128]],[[1,182,225,249,219,240,255,224,128,128,128],[149,150,226,252,216,205,255,171,128,128,128],[28,108,170,242,183,194,254,223,255,255,128]],[[1,81,230,252,204,203,255,192,128,128,128],[123,102,209,247,188,196,255,233,128,128,128],[20,95,153,243,164,173,255,203,128,128,128]],[[1,222,248,255,216,213,128,128,128,128,128],[168,175,246,252,235,205,255,255,128,128,128],[47,116,215,255,211,212,255,255,128,128,128]],[[1,121,236,253,212,214,255,255,128,128,128],[141,84,213,252,201,202,255,219,128,128,128],[42,80,160,240,162,185,255,205,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[244,1,255,128,128,128,128,128,128,128,128],[238,1,255,128,128,128,128,128,128,128,128]]]],hc=[[[231,120,48,89,115,113,120,152,112],[152,179,64,126,170,118,46,70,95],[175,69,143,80,85,82,72,155,103],[56,58,10,171,218,189,17,13,152],[114,26,17,163,44,195,21,10,173],[121,24,80,195,26,62,44,64,85],[144,71,10,38,171,213,144,34,26],[170,46,55,19,136,160,33,206,71],[63,20,8,114,114,208,12,9,226],[81,40,11,96,182,84,29,16,36]],[[134,183,89,137,98,101,106,165,148],[72,187,100,130,157,111,32,75,80],[66,102,167,99,74,62,40,234,128],[41,53,9,178,241,141,26,8,107],[74,43,26,146,73,166,49,23,157],[65,38,105,160,51,52,31,115,128],[104,79,12,27,217,255,87,17,7],[87,68,71,44,114,51,15,186,23],[47,41,14,110,182,183,21,17,194],[66,45,25,102,197,189,23,18,22]],[[88,88,147,150,42,46,45,196,205],[43,97,183,117,85,38,35,179,61],[39,53,200,87,26,21,43,232,171],[56,34,51,104,114,102,29,93,77],[39,28,85,171,58,165,90,98,64],[34,22,116,206,23,34,43,166,73],[107,54,32,26,51,1,81,43,31],[68,25,106,22,64,171,36,225,114],[34,19,21,102,132,188,16,76,124],[62,18,78,95,85,57,50,48,51]],[[193,101,35,159,215,111,89,46,111],[60,148,31,172,219,228,21,18,111],[112,113,77,85,179,255,38,120,114],[40,42,1,196,245,209,10,25,109],[88,43,29,140,166,213,37,43,154],[61,63,30,155,67,45,68,1,209],[100,80,8,43,154,1,51,26,71],[142,78,78,16,255,128,34,197,171],[41,40,5,102,211,183,4,1,221],[51,50,17,168,209,192,23,25,82]],[[138,31,36,171,27,166,38,44,229],[67,87,58,169,82,115,26,59,179],[63,59,90,180,59,166,93,73,154],[40,40,21,116,143,209,34,39,175],[47,15,16,183,34,223,49,45,183],[46,17,33,183,6,98,15,32,183],[57,46,22,24,128,1,54,17,37],[65,32,73,115,28,128,23,128,205],[40,3,9,115,51,192,18,6,223],[87,37,9,115,59,77,64,21,47]],[[104,55,44,218,9,54,53,130,226],[64,90,70,205,40,41,23,26,57],[54,57,112,184,5,41,38,166,213],[30,34,26,133,152,116,10,32,134],[39,19,53,221,26,114,32,73,255],[31,9,65,234,2,15,1,118,73],[75,32,12,51,192,255,160,43,51],[88,31,35,67,102,85,55,186,85],[56,21,23,111,59,205,45,37,192],[55,38,70,124,73,102,1,34,98]],[[125,98,42,88,104,85,117,175,82],[95,84,53,89,128,100,113,101,45],[75,79,123,47,51,128,81,171,1],[57,17,5,71,102,57,53,41,49],[38,33,13,121,57,73,26,1,85],[41,10,67,138,77,110,90,47,114],[115,21,2,10,102,255,166,23,6],[101,29,16,10,85,128,101,196,26],[57,18,10,102,102,213,34,20,43],[117,20,15,36,163,128,68,1,26]],[[102,61,71,37,34,53,31,243,192],[69,60,71,38,73,119,28,222,37],[68,45,128,34,1,47,11,245,171],[62,17,19,70,146,85,55,62,70],[37,43,37,154,100,163,85,160,1],[63,9,92,136,28,64,32,201,85],[75,15,9,9,64,255,184,119,16],[86,6,28,5,64,255,25,248,1],[56,8,17,132,137,255,55,116,128],[58,15,20,82,135,57,26,121,40]],[[164,50,31,137,154,133,25,35,218],[51,103,44,131,131,123,31,6,158],[86,40,64,135,148,224,45,183,128],[22,26,17,131,240,154,14,1,209],[45,16,21,91,64,222,7,1,197],[56,21,39,155,60,138,23,102,213],[83,12,13,54,192,255,68,47,28],[85,26,85,85,128,128,32,146,171],[18,11,7,63,144,171,4,4,246],[35,27,10,146,174,171,12,26,128]],[[190,80,35,99,180,80,126,54,45],[85,126,47,87,176,51,41,20,32],[101,75,128,139,118,146,116,128,85],[56,41,15,176,236,85,37,9,62],[71,30,17,119,118,255,17,18,138],[101,38,60,138,55,70,43,26,142],[146,36,19,30,171,255,97,27,20],[138,45,61,62,219,1,81,188,64],[32,41,20,117,151,142,20,21,163],[112,19,12,61,195,128,48,4,24]]],fc=[[[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[176,246,255,255,255,255,255,255,255,255,255],[223,241,252,255,255,255,255,255,255,255,255],[249,253,253,255,255,255,255,255,255,255,255]],[[255,244,252,255,255,255,255,255,255,255,255],[234,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255]],[[255,246,254,255,255,255,255,255,255,255,255],[239,253,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[251,255,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[251,254,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,254,253,255,254,255,255,255,255,255,255],[250,255,254,255,254,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[217,255,255,255,255,255,255,255,255,255,255],[225,252,241,253,255,255,254,255,255,255,255],[234,250,241,250,253,255,253,254,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[223,254,254,255,255,255,255,255,255,255,255],[238,253,254,254,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[249,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,255,255,255,255,255,255,255,255,255],[247,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[252,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[186,251,250,255,255,255,255,255,255,255,255],[234,251,244,254,255,255,255,255,255,255,255],[251,251,243,253,254,255,254,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[236,253,254,255,255,255,255,255,255,255,255],[251,253,253,254,254,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[254,254,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[254,254,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[248,255,255,255,255,255,255,255,255,255,255],[250,254,252,254,255,255,255,255,255,255,255],[248,254,249,253,255,255,255,255,255,255,255]],[[255,253,253,255,255,255,255,255,255,255,255],[246,253,253,255,255,255,255,255,255,255,255],[252,254,251,254,254,255,255,255,255,255,255]],[[255,254,252,255,255,255,255,255,255,255,255],[248,254,253,255,255,255,255,255,255,255,255],[253,255,254,254,255,255,255,255,255,255,255]],[[255,251,254,255,255,255,255,255,255,255,255],[245,251,254,255,255,255,255,255,255,255,255],[253,253,254,255,255,255,255,255,255,255,255]],[[255,251,253,255,255,255,255,255,255,255,255],[252,253,254,255,255,255,255,255,255,255,255],[255,254,255,255,255,255,255,255,255,255,255]],[[255,252,255,255,255,255,255,255,255,255,255],[249,255,254,255,255,255,255,255,255,255,255],[255,255,254,255,255,255,255,255,255,255,255]],[[255,255,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]]],dc=[0,1,2,3,6,4,5,6,6,6,6,6,6,6,6,7,0],yr=[],En=[],cr=[],pc=1,Us=2,wr=[],Sn=[];at("UpsampleRgbLinePair",ae,3),at("UpsampleBgrLinePair",Le,3),at("UpsampleRgbaLinePair",jn,4),at("UpsampleBgraLinePair",He,4),at("UpsampleArgbLinePair",Qe,4),at("UpsampleRgba4444LinePair",Oe,2),at("UpsampleRgb565LinePair",Pe,2);var mc=G.UpsampleRgbLinePair,gc=G.UpsampleBgrLinePair,zs=G.UpsampleRgbaLinePair,Hs=G.UpsampleBgraLinePair,Vs=G.UpsampleArgbLinePair,Ws=G.UpsampleRgba4444LinePair,vc=G.UpsampleRgb565LinePair,Ka=16,Xa=1<<Ka-1,sa=-227,rs=482,Gs=6,bc=(256<<Gs)-1,Ys=0,yc=s(256),wc=s(256),Nc=s(256),Ac=s(256),Lc=s(rs-sa),xc=s(rs-sa);Jn("YuvToRgbRow",ae,3),Jn("YuvToBgrRow",Le,3),Jn("YuvToRgbaRow",jn,4),Jn("YuvToBgraRow",He,4),Jn("YuvToArgbRow",Qe,4),Jn("YuvToRgba4444Row",Oe,2),Jn("YuvToRgb565Row",Pe,2);var $s=[0,4,8,12,128,132,136,140,256,260,264,268,384,388,392,396],Qa=[0,2,8],Sc=[8,7,6,4,4,2,2,2,1,1,1,1],_c=1;this.WebPDecodeRGBA=function(t,i,u,d,g){var b=Ga,y=new oi,A=new Cn;y.ba=A,A.S=b,A.width=[A.width],A.height=[A.height];var S=A.width,P=A.height,q=new er;if(q==null||t==null)var K=2;else e(q!=null),K=li(t,i,u,q.width,q.height,q.Pd,q.Qd,q.format,null);if(K!=0?S=0:(S!=null&&(S[0]=q.width[0]),P!=null&&(P[0]=q.height[0]),S=1),S){A.width=A.width[0],A.height=A.height[0],d!=null&&(d[0]=A.width),g!=null&&(g[0]=A.height);t:{if(d=new Zr,(g=new Gi).data=t,g.w=i,g.ha=u,g.kd=1,i=[0],e(g!=null),((t=li(g.data,g.w,g.ha,null,null,null,i,null,g))==0||t==7)&&i[0]&&(t=4),(i=t)==0){if(e(y!=null),d.data=g.data,d.w=g.w+g.offset,d.ha=g.ha-g.offset,d.put=zn,d.ac=Ye,d.bc=Hn,d.ma=y,g.xa){if((t=yn())==null){y=1;break t}if(function(X,W){var dt=[0],nt=[0],U=[0];e:for(;;){if(X==null)return 0;if(W==null)return X.a=2,0;if(X.l=W,X.a=0,tt(X.m,W.data,W.w,W.ha),!Ut(X.m,dt,nt,U)){X.a=3;break e}if(X.xb=Us,W.width=dt[0],W.height=nt[0],!Gn(dt[0],nt[0],1,X,null))break e;return 1}return e(X.a!=0),0}(t,d)){if(d=(i=ci(d.width,d.height,y.Oa,y.ba))==0){e:{d=t;n:for(;;){if(d==null){d=0;break e}if(e(d.s.yc!=null),e(d.s.Ya!=null),e(0<d.s.Wb),e((u=d.l)!=null),e((g=u.ma)!=null),d.xb!=0){if(d.ca=g.ba,d.tb=g.tb,e(d.ca!=null),!Qi(g.Oa,u,Ya)){d.a=2;break n}if(!Tr(d,u.width)||u.da)break n;if((u.da||ce(d.ca.S))&&$(),11>d.ca.S||(alert("todo:WebPInitConvertARGBToYUV"),d.ca.f.kb.F!=null&&$()),d.Pb&&0<d.s.ua&&d.s.vb.X==null&&!Ht(d.s.vb,d.s.Wa.Xa)){d.a=1;break n}d.xb=0}if(!Tn(d,d.V,d.Ba,d.c,d.i,u.o,Yr))break n;g.Dc=d.Ma,d=1;break e}e(d.a!=0),d=0}d=!d}d&&(i=t.a)}else i=t.a}else{if((t=new ko)==null){y=1;break t}if(t.Fa=g.na,t.P=g.P,t.qc=g.Sa,La(t,d)){if((i=ci(d.width,d.height,y.Oa,y.ba))==0){if(t.Aa=0,u=y.Oa,e((g=t)!=null),u!=null){if(0<(S=0>(S=u.Md)?0:100<S?255:255*S/100)){for(P=q=0;4>P;++P)12>(K=g.pb[P]).lc&&(K.ia=S*Sc[0>K.lc?0:K.lc]>>3),q|=K.ia;q&&(alert("todo:VP8InitRandom"),g.ia=1)}g.Ga=u.Id,100<g.Ga?g.Ga=100:0>g.Ga&&(g.Ga=0)}Fo(t,d)||(i=t.a)}}else i=t.a}i==0&&y.Oa!=null&&y.Oa.fd&&(i=Zi(y.ba))}y=i}b=y!=0?null:11>b?A.f.RGBA.eb:A.f.kb.y}else b=null;return b};var Js=[3,4,3,4,4,2,2,4,4,4,2,1,1]};function p(G,vt){for(var bt="",C=0;C<4;C++)bt+=String.fromCharCode(G[vt++]);return bt}function w(G,vt){return(G[vt+0]<<0|G[vt+1]<<8|G[vt+2]<<16)>>>0}function L(G,vt){return(G[vt+0]<<0|G[vt+1]<<8|G[vt+2]<<16|G[vt+3]<<24)>>>0}new f;var x=[0],m=[0],D=[],k=new f,O=r,_=function(G,vt){var bt={},C=0,I=!1,z=0,R=0;if(bt.frames=[],!function(T,E,V,Y){for(var Q=0;Q<Y;Q++)if(T[E+Q]!=V.charCodeAt(Q))return!0;return!1}(G,vt,"RIFF",4)){var lt,ot;for(L(G,vt+=4),vt+=8;vt<G.length;){var mt=p(G,vt),tt=L(G,vt+=4);vt+=4;var pt=tt+(1&tt);switch(mt){case"VP8 ":case"VP8L":bt.frames[C]===void 0&&(bt.frames[C]={}),(N=bt.frames[C]).src_off=I?R:vt-8,N.src_size=z+tt+8,C++,I&&(I=!1,z=0,R=0);break;case"VP8X":(N=bt.header={}).feature_flags=G[vt];var ut=vt+4;N.canvas_width=1+w(G,ut),ut+=3,N.canvas_height=1+w(G,ut),ut+=3;break;case"ALPH":I=!0,z=pt+8,R=vt-8;break;case"ANIM":(N=bt.header).bgcolor=L(G,vt),ut=vt+4,N.loop_count=(lt=G)[(ot=ut)+0]<<0|lt[ot+1]<<8,ut+=2;break;case"ANMF":var Ot,N;(N=bt.frames[C]={}).offset_x=2*w(G,vt),vt+=3,N.offset_y=2*w(G,vt),vt+=3,N.width=1+w(G,vt),vt+=3,N.height=1+w(G,vt),vt+=3,N.duration=w(G,vt),vt+=3,Ot=G[vt++],N.dispose=1&Ot,N.blend=Ot>>1&1}mt!="ANMF"&&(vt+=pt)}return bt}}(O,0);_.response=O,_.rgbaoutput=!0,_.dataurl=!1;var j=_.header?_.header:null,J=_.frames?_.frames:null;if(j){j.loop_counter=j.loop_count,x=[j.canvas_height],m=[j.canvas_width];for(var st=0;st<J.length&&J[st].blend!=0;st++);}var ft=J[0],At=k.WebPDecodeRGBA(O,ft.src_off,ft.src_size,m,x);ft.rgba=At,ft.imgwidth=m[0],ft.imgheight=x[0];for(var rt=0;rt<m[0]*x[0]*4;rt++)D[rt]=At[rt];return this.width=m,this.height=x,this.data=D,this}(function(r){var e=function(){return typeof _s=="function"},n=function(x,m,D,k){var O=4,_=c;switch(k){case r.image_compression.FAST:O=1,_=s;break;case r.image_compression.MEDIUM:O=6,_=h;break;case r.image_compression.SLOW:O=9,_=f}x=a(x,m,D,_);var j=_s(x,{level:O});return r.__addimage__.arrayBufferToBinaryString(j)},a=function(x,m,D,k){for(var O,_,j,J=x.length/m,st=new Uint8Array(x.length+J),ft=w(),At=0;At<J;At+=1){if(j=At*m,O=x.subarray(j,j+m),k)st.set(k(O,D,_),j+At);else{for(var rt,G=ft.length,vt=[];rt<G;rt+=1)vt[rt]=ft[rt](O,D,_);var bt=L(vt.concat());st.set(vt[bt],j+At)}_=O}return st},o=function(x){var m=Array.apply([],x);return m.unshift(0),m},s=function(x,m){var D,k=[],O=x.length;k[0]=1;for(var _=0;_<O;_+=1)D=x[_-m]||0,k[_+1]=x[_]-D+256&255;return k},c=function(x,m,D){var k,O=[],_=x.length;O[0]=2;for(var j=0;j<_;j+=1)k=D&&D[j]||0,O[j+1]=x[j]-k+256&255;return O},h=function(x,m,D){var k,O,_=[],j=x.length;_[0]=3;for(var J=0;J<j;J+=1)k=x[J-m]||0,O=D&&D[J]||0,_[J+1]=x[J]+256-(k+O>>>1)&255;return _},f=function(x,m,D){var k,O,_,j,J=[],st=x.length;J[0]=4;for(var ft=0;ft<st;ft+=1)k=x[ft-m]||0,O=D&&D[ft]||0,_=D&&D[ft-m]||0,j=p(k,O,_),J[ft+1]=x[ft]-j+256&255;return J},p=function(x,m,D){if(x===m&&m===D)return x;var k=Math.abs(m-D),O=Math.abs(x-D),_=Math.abs(x+m-D-D);return k<=O&&k<=_?x:O<=_?m:D},w=function(){return[o,s,c,h,f]},L=function(x){var m=x.map(function(D){return D.reduce(function(k,O){return k+Math.abs(O)},0)});return m.indexOf(Math.min.apply(null,m))};r.processPNG=function(x,m,D,k){var O,_,j,J,st,ft,At,rt,G,vt,bt,C,I,z,R,lt=this.decode.FLATE_DECODE,ot="";if(this.__addimage__.isArrayBuffer(x)&&(x=new Uint8Array(x)),this.__addimage__.isArrayBufferView(x)){if(x=(j=new mh(x)).imgData,_=j.bits,O=j.colorSpace,st=j.colors,[4,6].indexOf(j.colorType)!==-1){if(j.bits===8){G=(rt=j.pixelBitlength==32?new Uint32Array(j.decodePixels().buffer):j.pixelBitlength==16?new Uint16Array(j.decodePixels().buffer):new Uint8Array(j.decodePixels().buffer)).length,bt=new Uint8Array(G*j.colors),vt=new Uint8Array(G);var mt,tt=j.pixelBitlength-j.bits;for(z=0,R=0;z<G;z++){for(I=rt[z],mt=0;mt<tt;)bt[R++]=I>>>mt&255,mt+=j.bits;vt[z]=I>>>mt&255}}if(j.bits===16){G=(rt=new Uint32Array(j.decodePixels().buffer)).length,bt=new Uint8Array(G*(32/j.pixelBitlength)*j.colors),vt=new Uint8Array(G*(32/j.pixelBitlength)),C=j.colors>1,z=0,R=0;for(var pt=0;z<G;)I=rt[z++],bt[R++]=I>>>0&255,C&&(bt[R++]=I>>>16&255,I=rt[z++],bt[R++]=I>>>0&255),vt[pt++]=I>>>16&255;_=8}k!==r.image_compression.NONE&&e()?(x=n(bt,j.width*j.colors,j.colors,k),At=n(vt,j.width,1,k)):(x=bt,At=vt,lt=void 0)}if(j.colorType===3&&(O=this.color_spaces.INDEXED,ft=j.palette,j.transparency.indexed)){var ut=j.transparency.indexed,Ot=0;for(z=0,G=ut.length;z<G;++z)Ot+=ut[z];if((Ot/=255)===G-1&&ut.indexOf(0)!==-1)J=[ut.indexOf(0)];else if(Ot!==G){for(rt=j.decodePixels(),vt=new Uint8Array(rt.length),z=0,G=rt.length;z<G;z++)vt[z]=ut[rt[z]];At=n(vt,j.width,1)}}var N=function(T){var E;switch(T){case r.image_compression.FAST:E=11;break;case r.image_compression.MEDIUM:E=13;break;case r.image_compression.SLOW:E=14;break;default:E=12}return E}(k);return lt===this.decode.FLATE_DECODE&&(ot="/Predictor "+N+" "),ot+="/Colors "+st+" /BitsPerComponent "+_+" /Columns "+j.width,(this.__addimage__.isArrayBuffer(x)||this.__addimage__.isArrayBufferView(x))&&(x=this.__addimage__.arrayBufferToBinaryString(x)),(At&&this.__addimage__.isArrayBuffer(At)||this.__addimage__.isArrayBufferView(At))&&(At=this.__addimage__.arrayBufferToBinaryString(At)),{alias:D,data:x,index:m,filter:lt,decodeParameters:ot,transparency:J,palette:ft,sMask:At,predictor:N,width:j.width,height:j.height,bitsPerComponent:_,colorSpace:O}}}})(Vt.API),function(r){r.processGIF89A=function(e,n,a,o){var s=new gh(e),c=s.width,h=s.height,f=[];s.decodeAndBlitFrameRGBA(0,f);var p={data:f,width:c,height:h},w=new Ls(100).encode(p,100);return r.processJPEG.call(this,w,n,a,o)},r.processGIF87A=r.processGIF89A}(Vt.API),Bn.prototype.parseHeader=function(){if(this.fileSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.reserved=this.datav.getUint32(this.pos,!0),this.pos+=4,this.offset=this.datav.getUint32(this.pos,!0),this.pos+=4,this.headerSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.width=this.datav.getUint32(this.pos,!0),this.pos+=4,this.height=this.datav.getInt32(this.pos,!0),this.pos+=4,this.planes=this.datav.getUint16(this.pos,!0),this.pos+=2,this.bitPP=this.datav.getUint16(this.pos,!0),this.pos+=2,this.compress=this.datav.getUint32(this.pos,!0),this.pos+=4,this.rawSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.hr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.vr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.colors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.importantColors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.bitPP===16&&this.is_with_alpha&&(this.bitPP=15),this.bitPP<15){var r=this.colors===0?1<<this.bitPP:this.colors;this.palette=new Array(r);for(var e=0;e<r;e++){var n=this.datav.getUint8(this.pos++,!0),a=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0),s=this.datav.getUint8(this.pos++,!0);this.palette[e]={red:o,green:a,blue:n,quad:s}}}this.height<0&&(this.height*=-1,this.bottom_up=!1)},Bn.prototype.parseBGR=function(){this.pos=this.offset;try{var r="bit"+this.bitPP,e=this.width*this.height*4;this.data=new Uint8Array(e),this[r]()}catch(n){be.log("bit decode error:"+n)}},Bn.prototype.bit1=function(){var r,e=Math.ceil(this.width/8),n=e%4;for(r=this.height-1;r>=0;r--){for(var a=this.bottom_up?r:this.height-1-r,o=0;o<e;o++)for(var s=this.datav.getUint8(this.pos++,!0),c=a*this.width*4+8*o*4,h=0;h<8&&8*o+h<this.width;h++){var f=this.palette[s>>7-h&1];this.data[c+4*h]=f.blue,this.data[c+4*h+1]=f.green,this.data[c+4*h+2]=f.red,this.data[c+4*h+3]=255}n!==0&&(this.pos+=4-n)}},Bn.prototype.bit4=function(){for(var r=Math.ceil(this.width/2),e=r%4,n=this.height-1;n>=0;n--){for(var a=this.bottom_up?n:this.height-1-n,o=0;o<r;o++){var s=this.datav.getUint8(this.pos++,!0),c=a*this.width*4+2*o*4,h=s>>4,f=15&s,p=this.palette[h];if(this.data[c]=p.blue,this.data[c+1]=p.green,this.data[c+2]=p.red,this.data[c+3]=255,2*o+1>=this.width)break;p=this.palette[f],this.data[c+4]=p.blue,this.data[c+4+1]=p.green,this.data[c+4+2]=p.red,this.data[c+4+3]=255}e!==0&&(this.pos+=4-e)}},Bn.prototype.bit8=function(){for(var r=this.width%4,e=this.height-1;e>=0;e--){for(var n=this.bottom_up?e:this.height-1-e,a=0;a<this.width;a++){var o=this.datav.getUint8(this.pos++,!0),s=n*this.width*4+4*a;if(o<this.palette.length){var c=this.palette[o];this.data[s]=c.red,this.data[s+1]=c.green,this.data[s+2]=c.blue,this.data[s+3]=255}else this.data[s]=255,this.data[s+1]=255,this.data[s+2]=255,this.data[s+3]=255}r!==0&&(this.pos+=4-r)}},Bn.prototype.bit15=function(){for(var r=this.width%3,e=parseInt("11111",2),n=this.height-1;n>=0;n--){for(var a=this.bottom_up?n:this.height-1-n,o=0;o<this.width;o++){var s=this.datav.getUint16(this.pos,!0);this.pos+=2;var c=(s&e)/e*255|0,h=(s>>5&e)/e*255|0,f=(s>>10&e)/e*255|0,p=s>>15?255:0,w=a*this.width*4+4*o;this.data[w]=f,this.data[w+1]=h,this.data[w+2]=c,this.data[w+3]=p}this.pos+=r}},Bn.prototype.bit16=function(){for(var r=this.width%3,e=parseInt("11111",2),n=parseInt("111111",2),a=this.height-1;a>=0;a--){for(var o=this.bottom_up?a:this.height-1-a,s=0;s<this.width;s++){var c=this.datav.getUint16(this.pos,!0);this.pos+=2;var h=(c&e)/e*255|0,f=(c>>5&n)/n*255|0,p=(c>>11)/e*255|0,w=o*this.width*4+4*s;this.data[w]=p,this.data[w+1]=f,this.data[w+2]=h,this.data[w+3]=255}this.pos+=r}},Bn.prototype.bit24=function(){for(var r=this.height-1;r>=0;r--){for(var e=this.bottom_up?r:this.height-1-r,n=0;n<this.width;n++){var a=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0),s=this.datav.getUint8(this.pos++,!0),c=e*this.width*4+4*n;this.data[c]=s,this.data[c+1]=o,this.data[c+2]=a,this.data[c+3]=255}this.pos+=this.width%4}},Bn.prototype.bit32=function(){for(var r=this.height-1;r>=0;r--)for(var e=this.bottom_up?r:this.height-1-r,n=0;n<this.width;n++){var a=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0),s=this.datav.getUint8(this.pos++,!0),c=this.datav.getUint8(this.pos++,!0),h=e*this.width*4+4*n;this.data[h]=s,this.data[h+1]=o,this.data[h+2]=a,this.data[h+3]=c}},Bn.prototype.getData=function(){return this.data},function(r){r.processBMP=function(e,n,a,o){var s=new Bn(e,!1),c=s.width,h=s.height,f={data:s.getData(),width:c,height:h},p=new Ls(100).encode(f,100);return r.processJPEG.call(this,p,n,a,o)}}(Vt.API),Dl.prototype.getData=function(){return this.data},function(r){r.processWEBP=function(e,n,a,o){var s=new Dl(e),c=s.width,h=s.height,f={data:s.getData(),width:c,height:h},p=new Ls(100).encode(f,100);return r.processJPEG.call(this,p,n,a,o)}}(Vt.API),Vt.API.processRGBA=function(r,e,n){for(var a=r.data,o=a.length,s=new Uint8Array(o/4*3),c=new Uint8Array(o/4),h=0,f=0,p=0;p<o;p+=4){var w=a[p],L=a[p+1],x=a[p+2],m=a[p+3];s[h++]=w,s[h++]=L,s[h++]=x,c[f++]=m}var D=this.__addimage__.arrayBufferToBinaryString(s);return{alpha:this.__addimage__.arrayBufferToBinaryString(c),data:D,index:e,alias:n,colorSpace:"DeviceRGB",bitsPerComponent:8,width:r.width,height:r.height}},Vt.API.setLanguage=function(r){return this.internal.languageSettings===void 0&&(this.internal.languageSettings={},this.internal.languageSettings.isSubscribed=!1),{af:"Afrikaans",sq:"Albanian",ar:"Arabic (Standard)","ar-DZ":"Arabic (Algeria)","ar-BH":"Arabic (Bahrain)","ar-EG":"Arabic (Egypt)","ar-IQ":"Arabic (Iraq)","ar-JO":"Arabic (Jordan)","ar-KW":"Arabic (Kuwait)","ar-LB":"Arabic (Lebanon)","ar-LY":"Arabic (Libya)","ar-MA":"Arabic (Morocco)","ar-OM":"Arabic (Oman)","ar-QA":"Arabic (Qatar)","ar-SA":"Arabic (Saudi Arabia)","ar-SY":"Arabic (Syria)","ar-TN":"Arabic (Tunisia)","ar-AE":"Arabic (U.A.E.)","ar-YE":"Arabic (Yemen)",an:"Aragonese",hy:"Armenian",as:"Assamese",ast:"Asturian",az:"Azerbaijani",eu:"Basque",be:"Belarusian",bn:"Bengali",bs:"Bosnian",br:"Breton",bg:"Bulgarian",my:"Burmese",ca:"Catalan",ch:"Chamorro",ce:"Chechen",zh:"Chinese","zh-HK":"Chinese (Hong Kong)","zh-CN":"Chinese (PRC)","zh-SG":"Chinese (Singapore)","zh-TW":"Chinese (Taiwan)",cv:"Chuvash",co:"Corsican",cr:"Cree",hr:"Croatian",cs:"Czech",da:"Danish",nl:"Dutch (Standard)","nl-BE":"Dutch (Belgian)",en:"English","en-AU":"English (Australia)","en-BZ":"English (Belize)","en-CA":"English (Canada)","en-IE":"English (Ireland)","en-JM":"English (Jamaica)","en-NZ":"English (New Zealand)","en-PH":"English (Philippines)","en-ZA":"English (South Africa)","en-TT":"English (Trinidad & Tobago)","en-GB":"English (United Kingdom)","en-US":"English (United States)","en-ZW":"English (Zimbabwe)",eo:"Esperanto",et:"Estonian",fo:"Faeroese",fj:"Fijian",fi:"Finnish",fr:"French (Standard)","fr-BE":"French (Belgium)","fr-CA":"French (Canada)","fr-FR":"French (France)","fr-LU":"French (Luxembourg)","fr-MC":"French (Monaco)","fr-CH":"French (Switzerland)",fy:"Frisian",fur:"Friulian",gd:"Gaelic (Scots)","gd-IE":"Gaelic (Irish)",gl:"Galacian",ka:"Georgian",de:"German (Standard)","de-AT":"German (Austria)","de-DE":"German (Germany)","de-LI":"German (Liechtenstein)","de-LU":"German (Luxembourg)","de-CH":"German (Switzerland)",el:"Greek",gu:"Gujurati",ht:"Haitian",he:"Hebrew",hi:"Hindi",hu:"Hungarian",is:"Icelandic",id:"Indonesian",iu:"Inuktitut",ga:"Irish",it:"Italian (Standard)","it-CH":"Italian (Switzerland)",ja:"Japanese",kn:"Kannada",ks:"Kashmiri",kk:"Kazakh",km:"Khmer",ky:"Kirghiz",tlh:"Klingon",ko:"Korean","ko-KP":"Korean (North Korea)","ko-KR":"Korean (South Korea)",la:"Latin",lv:"Latvian",lt:"Lithuanian",lb:"Luxembourgish",mk:"North Macedonia",ms:"Malay",ml:"Malayalam",mt:"Maltese",mi:"Maori",mr:"Marathi",mo:"Moldavian",nv:"Navajo",ng:"Ndonga",ne:"Nepali",no:"Norwegian",nb:"Norwegian (Bokmal)",nn:"Norwegian (Nynorsk)",oc:"Occitan",or:"Oriya",om:"Oromo",fa:"Persian","fa-IR":"Persian/Iran",pl:"Polish",pt:"Portuguese","pt-BR":"Portuguese (Brazil)",pa:"Punjabi","pa-IN":"Punjabi (India)","pa-PK":"Punjabi (Pakistan)",qu:"Quechua",rm:"Rhaeto-Romanic",ro:"Romanian","ro-MO":"Romanian (Moldavia)",ru:"Russian","ru-MO":"Russian (Moldavia)",sz:"Sami (Lappish)",sg:"Sango",sa:"Sanskrit",sc:"Sardinian",sd:"Sindhi",si:"Singhalese",sr:"Serbian",sk:"Slovak",sl:"Slovenian",so:"Somani",sb:"Sorbian",es:"Spanish","es-AR":"Spanish (Argentina)","es-BO":"Spanish (Bolivia)","es-CL":"Spanish (Chile)","es-CO":"Spanish (Colombia)","es-CR":"Spanish (Costa Rica)","es-DO":"Spanish (Dominican Republic)","es-EC":"Spanish (Ecuador)","es-SV":"Spanish (El Salvador)","es-GT":"Spanish (Guatemala)","es-HN":"Spanish (Honduras)","es-MX":"Spanish (Mexico)","es-NI":"Spanish (Nicaragua)","es-PA":"Spanish (Panama)","es-PY":"Spanish (Paraguay)","es-PE":"Spanish (Peru)","es-PR":"Spanish (Puerto Rico)","es-ES":"Spanish (Spain)","es-UY":"Spanish (Uruguay)","es-VE":"Spanish (Venezuela)",sx:"Sutu",sw:"Swahili",sv:"Swedish","sv-FI":"Swedish (Finland)","sv-SV":"Swedish (Sweden)",ta:"Tamil",tt:"Tatar",te:"Teluga",th:"Thai",tig:"Tigre",ts:"Tsonga",tn:"Tswana",tr:"Turkish",tk:"Turkmen",uk:"Ukrainian",hsb:"Upper Sorbian",ur:"Urdu",ve:"Venda",vi:"Vietnamese",vo:"Volapuk",wa:"Walloon",cy:"Welsh",xh:"Xhosa",ji:"Yiddish",zu:"Zulu"}[r]!==void 0&&(this.internal.languageSettings.languageCode=r,this.internal.languageSettings.isSubscribed===!1&&(this.internal.events.subscribe("putCatalog",function(){this.internal.write("/Lang ("+this.internal.languageSettings.languageCode+")")}),this.internal.languageSettings.isSubscribed=!0)),this},xi=Vt.API,fo=xi.getCharWidthsArray=function(r,e){var n,a,o=(e=e||{}).font||this.internal.getFont(),s=e.fontSize||this.internal.getFontSize(),c=e.charSpace||this.internal.getCharSpace(),h=e.widths?e.widths:o.metadata.Unicode.widths,f=h.fof?h.fof:1,p=e.kerning?e.kerning:o.metadata.Unicode.kerning,w=p.fof?p.fof:1,L=e.doKerning!==!1,x=0,m=r.length,D=0,k=h[0]||f,O=[];for(n=0;n<m;n++)a=r.charCodeAt(n),typeof o.metadata.widthOfString=="function"?O.push((o.metadata.widthOfGlyph(o.metadata.characterToGlyph(a))+c*(1e3/s)||0)/1e3):(x=L&&de(p[a])==="object"&&!isNaN(parseInt(p[a][D],10))?p[a][D]/w:0,O.push((h[a]||k)/f+x)),D=a;return O},Il=xi.getStringUnitWidth=function(r,e){var n=(e=e||{}).fontSize||this.internal.getFontSize(),a=e.font||this.internal.getFont(),o=e.charSpace||this.internal.getCharSpace();return xi.processArabic&&(r=xi.processArabic(r)),typeof a.metadata.widthOfString=="function"?a.metadata.widthOfString(r,n,o)/n:fo.apply(this,arguments).reduce(function(s,c){return s+c},0)},kl=function(r,e,n,a){for(var o=[],s=0,c=r.length,h=0;s!==c&&h+e[s]<n;)h+=e[s],s++;o.push(r.slice(0,s));var f=s;for(h=0;s!==c;)h+e[s]>a&&(o.push(r.slice(f,s)),h=0,f=s),h+=e[s],s++;return f!==s&&o.push(r.slice(f,s)),o},Fl=function(r,e,n){n||(n={});var a,o,s,c,h,f,p,w=[],L=[w],x=n.textIndent||0,m=0,D=0,k=r.split(" "),O=fo.apply(this,[" ",n])[0];if(f=n.lineIndent===-1?k[0].length+2:n.lineIndent||0){var _=Array(f).join(" "),j=[];k.map(function(st){(st=st.split(/\s*\n/)).length>1?j=j.concat(st.map(function(ft,At){return(At&&ft.length?`
`:"")+ft})):j.push(st[0])}),k=j,f=Il.apply(this,[_,n])}for(s=0,c=k.length;s<c;s++){var J=0;if(a=k[s],f&&a[0]==`
`&&(a=a.substr(1),J=1),x+m+(D=(o=fo.apply(this,[a,n])).reduce(function(st,ft){return st+ft},0))>e||J){if(D>e){for(h=kl.apply(this,[a,o,e-(x+m),e]),w.push(h.shift()),w=[h.pop()];h.length;)L.push([h.shift()]);D=o.slice(a.length-(w[0]?w[0].length:0)).reduce(function(st,ft){return st+ft},0)}else w=[a];L.push(w),x=D+f,m=O}else w.push(a),x+=m+D,m=O}return p=f?function(st,ft){return(ft?_:"")+st.join(" ")}:function(st){return st.join(" ")},L.map(p)},xi.splitTextToSize=function(r,e,n){var a,o=(n=n||{}).fontSize||this.internal.getFontSize(),s=(function(w){if(w.widths&&w.kerning)return{widths:w.widths,kerning:w.kerning};var L=this.internal.getFont(w.fontName,w.fontStyle);return L.metadata.Unicode?{widths:L.metadata.Unicode.widths||{0:1},kerning:L.metadata.Unicode.kerning||{}}:{font:L.metadata,fontSize:this.internal.getFontSize(),charSpace:this.internal.getCharSpace()}}).call(this,n);a=Array.isArray(r)?r:String(r).split(/\r?\n/);var c=1*this.internal.scaleFactor*e/o;s.textIndent=n.textIndent?1*n.textIndent*this.internal.scaleFactor/o:0,s.lineIndent=n.lineIndent;var h,f,p=[];for(h=0,f=a.length;h<f;h++)p=p.concat(Fl.apply(this,[a[h],c,s]));return p},function(r){r.__fontmetrics__=r.__fontmetrics__||{};for(var e="klmnopqrstuvwxyz",n={},a={},o=0;o<e.length;o++)n[e[o]]="0123456789abcdef"[o],a["0123456789abcdef"[o]]=e[o];var s=function(L){return"0x"+parseInt(L,10).toString(16)},c=r.__fontmetrics__.compress=function(L){var x,m,D,k,O=["{"];for(var _ in L){if(x=L[_],isNaN(parseInt(_,10))?m="'"+_+"'":(_=parseInt(_,10),m=(m=s(_).slice(2)).slice(0,-1)+a[m.slice(-1)]),typeof x=="number")x<0?(D=s(x).slice(3),k="-"):(D=s(x).slice(2),k=""),D=k+D.slice(0,-1)+a[D.slice(-1)];else{if(de(x)!=="object")throw new Error("Don't know what to do with value type "+de(x)+".");D=c(x)}O.push(m+D)}return O.push("}"),O.join("")},h=r.__fontmetrics__.uncompress=function(L){if(typeof L!="string")throw new Error("Invalid argument passed to uncompress.");for(var x,m,D,k,O={},_=1,j=O,J=[],st="",ft="",At=L.length-1,rt=1;rt<At;rt+=1)(k=L[rt])=="'"?x?(D=x.join(""),x=void 0):x=[]:x?x.push(k):k=="{"?(J.push([j,D]),j={},D=void 0):k=="}"?((m=J.pop())[0][m[1]]=j,D=void 0,j=m[0]):k=="-"?_=-1:D===void 0?n.hasOwnProperty(k)?(st+=n[k],D=parseInt(st,16)*_,_=1,st=""):st+=k:n.hasOwnProperty(k)?(ft+=n[k],j[D]=parseInt(ft,16)*_,_=1,D=void 0,ft=""):ft+=k;return O},f={codePages:["WinAnsiEncoding"],WinAnsiEncoding:h("{19m8n201n9q201o9r201s9l201t9m201u8m201w9n201x9o201y8o202k8q202l8r202m9p202q8p20aw8k203k8t203t8v203u9v2cq8s212m9t15m8w15n9w2dw9s16k8u16l9u17s9z17x8y17y9y}")},p={Unicode:{Courier:f,"Courier-Bold":f,"Courier-BoldOblique":f,"Courier-Oblique":f,Helvetica:f,"Helvetica-Bold":f,"Helvetica-BoldOblique":f,"Helvetica-Oblique":f,"Times-Roman":f,"Times-Bold":f,"Times-BoldItalic":f,"Times-Italic":f}},w={Unicode:{"Courier-Oblique":h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-BoldItalic":h("{'widths'{k3o2q4ycx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2r202m2n2n3m2o3m2p5n202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5n4l4m4m4m4n4m4o4s4p4m4q4m4r4s4s4y4t2r4u3m4v4m4w3x4x5t4y4s4z4s5k3x5l4s5m4m5n3r5o3x5p4s5q4m5r5t5s4m5t3x5u3x5v2l5w1w5x2l5y3t5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q2l6r3m6s3r6t1w6u1w6v3m6w1w6x4y6y3r6z3m7k3m7l3m7m2r7n2r7o1w7p3r7q2w7r4m7s3m7t2w7u2r7v2n7w1q7x2n7y3t202l3mcl4mal2ram3man3mao3map3mar3mas2lat4uau1uav3maw3way4uaz2lbk2sbl3t'fof'6obo2lbp3tbq3mbr1tbs2lbu1ybv3mbz3mck4m202k3mcm4mcn4mco4mcp4mcq5ycr4mcs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz2w203k6o212m6o2dw2l2cq2l3t3m3u2l17s3x19m3m}'kerning'{cl{4qu5kt5qt5rs17ss5ts}201s{201ss}201t{cks4lscmscnscoscpscls2wu2yu201ts}201x{2wu2yu}2k{201ts}2w{4qx5kx5ou5qx5rs17su5tu}2x{17su5tu5ou}2y{4qx5kx5ou5qx5rs17ss5ts}'fof'-6ofn{17sw5tw5ou5qw5rs}7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qs}3v{17su5tu5os5qs}7p{17su5tu}ck{4qu5kt5qt5rs17ss5ts}4l{4qu5kt5qt5rs17ss5ts}cm{4qu5kt5qt5rs17ss5ts}cn{4qu5kt5qt5rs17ss5ts}co{4qu5kt5qt5rs17ss5ts}cp{4qu5kt5qt5rs17ss5ts}6l{4qu5ou5qw5rt17su5tu}5q{ckuclucmucnucoucpu4lu}5r{ckuclucmucnucoucpu4lu}7q{cksclscmscnscoscps4ls}6p{4qu5ou5qw5rt17sw5tw}ek{4qu5ou5qw5rt17su5tu}el{4qu5ou5qw5rt17su5tu}em{4qu5ou5qw5rt17su5tu}en{4qu5ou5qw5rt17su5tu}eo{4qu5ou5qw5rt17su5tu}ep{4qu5ou5qw5rt17su5tu}es{17ss5ts5qs4qu}et{4qu5ou5qw5rt17sw5tw}eu{4qu5ou5qw5rt17ss5ts}ev{17ss5ts5qs4qu}6z{17sw5tw5ou5qw5rs}fm{17sw5tw5ou5qw5rs}7n{201ts}fo{17sw5tw5ou5qw5rs}fp{17sw5tw5ou5qw5rs}fq{17sw5tw5ou5qw5rs}7r{cksclscmscnscoscps4ls}fs{17sw5tw5ou5qw5rs}ft{17su5tu}fu{17su5tu}fv{17su5tu}fw{17su5tu}fz{cksclscmscnscoscps4ls}}}"),"Helvetica-Bold":h("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),Courier:h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-BoldOblique":h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Bold":h("{'widths'{k3q2q5ncx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2l202m2n2n3m2o3m2p6o202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5x4l4s4m4m4n4s4o4s4p4m4q3x4r4y4s4y4t2r4u3m4v4y4w4m4x5y4y4s4z4y5k3x5l4y5m4s5n3r5o4m5p4s5q4s5r6o5s4s5t4s5u4m5v2l5w1w5x2l5y3u5z3m6k2l6l3m6m3r6n2w6o3r6p2w6q2l6r3m6s3r6t1w6u2l6v3r6w1w6x5n6y3r6z3m7k3r7l3r7m2w7n2r7o2l7p3r7q3m7r4s7s3m7t3m7u2w7v2r7w1q7x2r7y3o202l3mcl4sal2lam3man3mao3map3mar3mas2lat4uau1yav3maw3tay4uaz2lbk2sbl3t'fof'6obo2lbp3rbr1tbs2lbu2lbv3mbz3mck4s202k3mcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3rek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3m3u2l17s4s19m3m}'kerning'{cl{4qt5ks5ot5qy5rw17sv5tv}201t{cks4lscmscnscoscpscls4wv}2k{201ts}2w{4qu5ku7mu5os5qx5ru17su5tu}2x{17su5tu5ou5qs}2y{4qv5kv7mu5ot5qz5ru17su5tu}'fof'-6o7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qu}3v{17su5tu5os5qu}fu{17su5tu5ou5qu}7p{17su5tu5ou5qu}ck{4qt5ks5ot5qy5rw17sv5tv}4l{4qt5ks5ot5qy5rw17sv5tv}cm{4qt5ks5ot5qy5rw17sv5tv}cn{4qt5ks5ot5qy5rw17sv5tv}co{4qt5ks5ot5qy5rw17sv5tv}cp{4qt5ks5ot5qy5rw17sv5tv}6l{17st5tt5ou5qu}17s{ckuclucmucnucoucpu4lu4wu}5o{ckuclucmucnucoucpu4lu4wu}5q{ckzclzcmzcnzcozcpz4lz4wu}5r{ckxclxcmxcnxcoxcpx4lx4wu}5t{ckuclucmucnucoucpu4lu4wu}7q{ckuclucmucnucoucpu4lu}6p{17sw5tw5ou5qu}ek{17st5tt5qu}el{17st5tt5ou5qu}em{17st5tt5qu}en{17st5tt5qu}eo{17st5tt5qu}ep{17st5tt5ou5qu}es{17ss5ts5qu}et{17sw5tw5ou5qu}eu{17sw5tw5ou5qu}ev{17ss5ts5qu}6z{17sw5tw5ou5qu5rs}fm{17sw5tw5ou5qu5rs}fn{17sw5tw5ou5qu5rs}fo{17sw5tw5ou5qu5rs}fp{17sw5tw5ou5qu5rs}fq{17sw5tw5ou5qu5rs}7r{cktcltcmtcntcotcpt4lt5os}fs{17sw5tw5ou5qu5rs}ft{17su5tu5ou5qu}7m{5os}fv{17su5tu5ou5qu}fw{17su5tu5ou5qu}fz{cksclscmscnscoscps4ls}}}"),Symbol:h("{'widths'{k3uaw4r19m3m2k1t2l2l202m2y2n3m2p5n202q6o3k3m2s2l2t2l2v3r2w1t3m3m2y1t2z1wbk2sbl3r'fof'6o3n3m3o3m3p3m3q3m3r3m3s3m3t3m3u1w3v1w3w3r3x3r3y3r3z2wbp3t3l3m5v2l5x2l5z3m2q4yfr3r7v3k7w1o7x3k}'kerning'{'fof'-6o}}"),Helvetica:h("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}"),"Helvetica-BoldOblique":h("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),ZapfDingbats:h("{'widths'{k4u2k1w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-Bold":h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Italic":h("{'widths'{k3n2q4ycx2l201n3m201o5t201s2l201t2l201u2l201w3r201x3r201y3r2k1t2l2l202m2n2n3m2o3m2p5n202q5t2r1p2s2l2t2l2u3m2v4n2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w4n3x4n3y4n3z3m4k5w4l3x4m3x4n4m4o4s4p3x4q3x4r4s4s4s4t2l4u2w4v4m4w3r4x5n4y4m4z4s5k3x5l4s5m3x5n3m5o3r5p4s5q3x5r5n5s3x5t3r5u3r5v2r5w1w5x2r5y2u5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q1w6r3m6s3m6t1w6u1w6v2w6w1w6x4s6y3m6z3m7k3m7l3m7m2r7n2r7o1w7p3m7q2w7r4m7s2w7t2w7u2r7v2s7w1v7x2s7y3q202l3mcl3xal2ram3man3mao3map3mar3mas2lat4wau1vav3maw4nay4waz2lbk2sbl4n'fof'6obo2lbp3mbq3obr1tbs2lbu1zbv3mbz3mck3x202k3mcm3xcn3xco3xcp3xcq5tcr4mcs3xct3xcu3xcv3xcw2l2m2ucy2lcz2ldl4mdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr4nfs3mft3mfu3mfv3mfw3mfz2w203k6o212m6m2dw2l2cq2l3t3m3u2l17s3r19m3m}'kerning'{cl{5kt4qw}201s{201sw}201t{201tw2wy2yy6q-t}201x{2wy2yy}2k{201tw}2w{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}2x{17ss5ts5os}2y{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}'fof'-6o6t{17ss5ts5qs}7t{5os}3v{5qs}7p{17su5tu5qs}ck{5kt4qw}4l{5kt4qw}cm{5kt4qw}cn{5kt4qw}co{5kt4qw}cp{5kt4qw}6l{4qs5ks5ou5qw5ru17su5tu}17s{2ks}5q{ckvclvcmvcnvcovcpv4lv}5r{ckuclucmucnucoucpu4lu}5t{2ks}6p{4qs5ks5ou5qw5ru17su5tu}ek{4qs5ks5ou5qw5ru17su5tu}el{4qs5ks5ou5qw5ru17su5tu}em{4qs5ks5ou5qw5ru17su5tu}en{4qs5ks5ou5qw5ru17su5tu}eo{4qs5ks5ou5qw5ru17su5tu}ep{4qs5ks5ou5qw5ru17su5tu}es{5ks5qs4qs}et{4qs5ks5ou5qw5ru17su5tu}eu{4qs5ks5qw5ru17su5tu}ev{5ks5qs4qs}ex{17ss5ts5qs}6z{4qv5ks5ou5qw5ru17su5tu}fm{4qv5ks5ou5qw5ru17su5tu}fn{4qv5ks5ou5qw5ru17su5tu}fo{4qv5ks5ou5qw5ru17su5tu}fp{4qv5ks5ou5qw5ru17su5tu}fq{4qv5ks5ou5qw5ru17su5tu}7r{5os}fs{4qv5ks5ou5qw5ru17su5tu}ft{17su5tu5qs}fu{17su5tu5qs}fv{17su5tu5qs}fw{17su5tu5qs}}}"),"Times-Roman":h("{'widths'{k3n2q4ycx2l201n3m201o6o201s2l201t2l201u2l201w2w201x2w201y2w2k1t2l2l202m2n2n3m2o3m2p5n202q6o2r1m2s2l2t2l2u3m2v3s2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v1w3w3s3x3s3y3s3z2w4k5w4l4s4m4m4n4m4o4s4p3x4q3r4r4s4s4s4t2l4u2r4v4s4w3x4x5t4y4s4z4s5k3r5l4s5m4m5n3r5o3x5p4s5q4s5r5y5s4s5t4s5u3x5v2l5w1w5x2l5y2z5z3m6k2l6l2w6m3m6n2w6o3m6p2w6q2l6r3m6s3m6t1w6u1w6v3m6w1w6x4y6y3m6z3m7k3m7l3m7m2l7n2r7o1w7p3m7q3m7r4s7s3m7t3m7u2w7v3k7w1o7x3k7y3q202l3mcl4sal2lam3man3mao3map3mar3mas2lat4wau1vav3maw3say4waz2lbk2sbl3s'fof'6obo2lbp3mbq2xbr1tbs2lbu1zbv3mbz2wck4s202k3mcm4scn4sco4scp4scq5tcr4mcs3xct3xcu3xcv3xcw2l2m2tcy2lcz2ldl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek2wel2wem2wen2weo2wep2weq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr3sfs3mft3mfu3mfv3mfw3mfz3m203k6o212m6m2dw2l2cq2l3t3m3u1w17s4s19m3m}'kerning'{cl{4qs5ku17sw5ou5qy5rw201ss5tw201ws}201s{201ss}201t{ckw4lwcmwcnwcowcpwclw4wu201ts}2k{201ts}2w{4qs5kw5os5qx5ru17sx5tx}2x{17sw5tw5ou5qu}2y{4qs5kw5os5qx5ru17sx5tx}'fof'-6o7t{ckuclucmucnucoucpu4lu5os5rs}3u{17su5tu5qs}3v{17su5tu5qs}7p{17sw5tw5qs}ck{4qs5ku17sw5ou5qy5rw201ss5tw201ws}4l{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cm{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cn{4qs5ku17sw5ou5qy5rw201ss5tw201ws}co{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cp{4qs5ku17sw5ou5qy5rw201ss5tw201ws}6l{17su5tu5os5qw5rs}17s{2ktclvcmvcnvcovcpv4lv4wuckv}5o{ckwclwcmwcnwcowcpw4lw4wu}5q{ckyclycmycnycoycpy4ly4wu5ms}5r{cktcltcmtcntcotcpt4lt4ws}5t{2ktclvcmvcnvcovcpv4lv4wuckv}7q{cksclscmscnscoscps4ls}6p{17su5tu5qw5rs}ek{5qs5rs}el{17su5tu5os5qw5rs}em{17su5tu5os5qs5rs}en{17su5qs5rs}eo{5qs5rs}ep{17su5tu5os5qw5rs}es{5qs}et{17su5tu5qw5rs}eu{17su5tu5qs5rs}ev{5qs}6z{17sv5tv5os5qx5rs}fm{5os5qt5rs}fn{17sv5tv5os5qx5rs}fo{17sv5tv5os5qx5rs}fp{5os5qt5rs}fq{5os5qt5rs}7r{ckuclucmucnucoucpu4lu5os}fs{17sv5tv5os5qx5rs}ft{17ss5ts5qs}fu{17sw5tw5qs}fv{17sw5tw5qs}fw{17ss5ts5qs}fz{ckuclucmucnucoucpu4lu5os5rs}}}"),"Helvetica-Oblique":h("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}")}};r.events.push(["addFont",function(L){var x=L.font,m=w.Unicode[x.postScriptName];m&&(x.metadata.Unicode={},x.metadata.Unicode.widths=m.widths,x.metadata.Unicode.kerning=m.kerning);var D=p.Unicode[x.postScriptName];D&&(x.metadata.Unicode.encoding=D,x.encoding=D.codePages[0])}])}(Vt.API),function(r){var e=function(n){for(var a=n.length,o=new Uint8Array(a),s=0;s<a;s++)o[s]=n.charCodeAt(s);return o};r.API.events.push(["addFont",function(n){var a=void 0,o=n.font,s=n.instance;if(!o.isStandardFont){if(s===void 0)throw new Error("Font does not exist in vFS, import fonts or remove declaration doc.addFont('"+o.postScriptName+"').");if(typeof(a=s.existsFileInVFS(o.postScriptName)===!1?s.loadFile(o.postScriptName):s.getFileFromVFS(o.postScriptName))!="string")throw new Error("Font is not stored as string-data in vFS, import fonts or remove declaration doc.addFont('"+o.postScriptName+"').");(function(c,h){h=/^\x00\x01\x00\x00/.test(h)?e(h):e(ga(h)),c.metadata=r.API.TTFFont.open(h),c.metadata.Unicode=c.metadata.Unicode||{encoding:{},kerning:{},widths:[]},c.metadata.glyIdsUsed=[0]})(o,a)}}])}(Vt),function(r){function e(){return(zt.canvg?Promise.resolve(zt.canvg):Ss(()=>import("./index.es-74ddacaf.js"),["assets/index.es-74ddacaf.js","assets/index-a2fbd71b.js","assets/index-83400b99.css","assets/index-fe3402a7.js","assets/isUndefined-aa0326a0.js","assets/index-68054860.js","assets/index-350461d4.js","assets/index-ac58700d.js","assets/strings-cc725bd5.js","assets/isEqual-c950930c.js","assets/index-4e9a4449.js","assets/index-96be5bee.js","assets/castArray-2ae6cabb.js","assets/_plugin-vue_export-helper-c27b6911.js","assets/TicketInfoPopover-9ac1ad15.js","assets/dictApi-14e8db9b.js","assets/add-2f19224f.js","assets/_createMathOperation-9624aa42.js","assets/TicketPopItem.vue_vue_type_script_setup_true_lang-eb783de8.js","assets/theme-light_empty-0081a108.js","assets/index-ff93035e.js","assets/index-e5c2c3ff.js","assets/index-d88b3135.js","assets/refs-b36b7130.js","assets/ticketOperationApi-4e3e854b.js","assets/time-04300430.js","assets/regular-crs-531dcd3f.js","assets/config-6a4e2d9f.js","assets/TicketPopItem-d9c892dc.css","assets/index-8ff7f67e.js","assets/index-a42e5d8f.js","assets/index-35a05a15.js","assets/dropdown-7f4180b8.js","assets/TicketInfoPopover-df3606c8.css","assets/browser-6cfa1fde.js"])).catch(function(n){return Promise.reject(new Error("Could not load canvg: "+n))}).then(function(n){return n.default?n.default:n})}Vt.API.addSvgAsImage=function(n,a,o,s,c,h,f,p){if(isNaN(a)||isNaN(o))throw be.error("jsPDF.addSvgAsImage: Invalid coordinates",arguments),new Error("Invalid coordinates passed to jsPDF.addSvgAsImage");if(isNaN(s)||isNaN(c))throw be.error("jsPDF.addSvgAsImage: Invalid measurements",arguments),new Error("Invalid measurements (width and/or height) passed to jsPDF.addSvgAsImage");var w=document.createElement("canvas");w.width=s,w.height=c;var L=w.getContext("2d");L.fillStyle="#fff",L.fillRect(0,0,w.width,w.height);var x={ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0},m=this;return e().then(function(D){return D.fromString(L,n,x)},function(){return Promise.reject(new Error("Could not load canvg."))}).then(function(D){return D.render(x)}).then(function(){m.addImage(w.toDataURL("image/jpeg",1),a,o,s,c,f,p)})}}(),Vt.API.putTotalPages=function(r){var e,n=0;parseInt(this.internal.getFont().id.substr(1),10)<15?(e=new RegExp(r,"g"),n=this.internal.getNumberOfPages()):(e=new RegExp(this.pdfEscape16(r,this.internal.getFont()),"g"),n=this.pdfEscape16(this.internal.getNumberOfPages()+"",this.internal.getFont()));for(var a=1;a<=this.internal.getNumberOfPages();a++)for(var o=0;o<this.internal.pages[a].length;o++)this.internal.pages[a][o]=this.internal.pages[a][o].replace(e,n);return this},Vt.API.viewerPreferences=function(r,e){var n;r=r||{},e=e||!1;var a,o,s,c={HideToolbar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideMenubar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideWindowUI:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},FitWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},CenterWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},DisplayDocTitle:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.4},NonFullScreenPageMode:{defaultValue:"UseNone",value:"UseNone",type:"name",explicitSet:!1,valueSet:["UseNone","UseOutlines","UseThumbs","UseOC"],pdfVersion:1.3},Direction:{defaultValue:"L2R",value:"L2R",type:"name",explicitSet:!1,valueSet:["L2R","R2L"],pdfVersion:1.3},ViewArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},ViewClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintScaling:{defaultValue:"AppDefault",value:"AppDefault",type:"name",explicitSet:!1,valueSet:["AppDefault","None"],pdfVersion:1.6},Duplex:{defaultValue:"",value:"none",type:"name",explicitSet:!1,valueSet:["Simplex","DuplexFlipShortEdge","DuplexFlipLongEdge","none"],pdfVersion:1.7},PickTrayByPDFSize:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.7},PrintPageRange:{defaultValue:"",value:"",type:"array",explicitSet:!1,valueSet:null,pdfVersion:1.7},NumCopies:{defaultValue:1,value:1,type:"integer",explicitSet:!1,valueSet:null,pdfVersion:1.7}},h=Object.keys(c),f=[],p=0,w=0,L=0;function x(D,k){var O,_=!1;for(O=0;O<D.length;O+=1)D[O]===k&&(_=!0);return _}if(this.internal.viewerpreferences===void 0&&(this.internal.viewerpreferences={},this.internal.viewerpreferences.configuration=JSON.parse(JSON.stringify(c)),this.internal.viewerpreferences.isSubscribed=!1),n=this.internal.viewerpreferences.configuration,r==="reset"||e===!0){var m=h.length;for(L=0;L<m;L+=1)n[h[L]].value=n[h[L]].defaultValue,n[h[L]].explicitSet=!1}if(de(r)==="object"){for(o in r)if(s=r[o],x(h,o)&&s!==void 0){if(n[o].type==="boolean"&&typeof s=="boolean")n[o].value=s;else if(n[o].type==="name"&&x(n[o].valueSet,s))n[o].value=s;else if(n[o].type==="integer"&&Number.isInteger(s))n[o].value=s;else if(n[o].type==="array"){for(p=0;p<s.length;p+=1)if(a=!0,s[p].length===1&&typeof s[p][0]=="number")f.push(String(s[p]-1));else if(s[p].length>1){for(w=0;w<s[p].length;w+=1)typeof s[p][w]!="number"&&(a=!1);a===!0&&f.push([s[p][0]-1,s[p][1]-1].join(" "))}n[o].value="["+f.join(" ")+"]"}else n[o].value=n[o].defaultValue;n[o].explicitSet=!0}}return this.internal.viewerpreferences.isSubscribed===!1&&(this.internal.events.subscribe("putCatalog",function(){var D,k=[];for(D in n)n[D].explicitSet===!0&&(n[D].type==="name"?k.push("/"+D+" /"+n[D].value):k.push("/"+D+" "+n[D].value));k.length!==0&&this.internal.write(`/ViewerPreferences
<<
`+k.join(`
`)+`
>>`)}),this.internal.viewerpreferences.isSubscribed=!0),this.internal.viewerpreferences.configuration=n,this},function(r){var e=function(){var a='<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"><rdf:Description rdf:about="" xmlns:jspdf="'+this.internal.__metadata__.namespaceuri+'"><jspdf:metadata>',o=unescape(encodeURIComponent('<x:xmpmeta xmlns:x="adobe:ns:meta/">')),s=unescape(encodeURIComponent(a)),c=unescape(encodeURIComponent(this.internal.__metadata__.metadata)),h=unescape(encodeURIComponent("</jspdf:metadata></rdf:Description></rdf:RDF>")),f=unescape(encodeURIComponent("</x:xmpmeta>")),p=s.length+c.length+h.length+o.length+f.length;this.internal.__metadata__.metadata_object_number=this.internal.newObject(),this.internal.write("<< /Type /Metadata /Subtype /XML /Length "+p+" >>"),this.internal.write("stream"),this.internal.write(o+s+c+h+f),this.internal.write("endstream"),this.internal.write("endobj")},n=function(){this.internal.__metadata__.metadata_object_number&&this.internal.write("/Metadata "+this.internal.__metadata__.metadata_object_number+" 0 R")};r.addMetadata=function(a,o){return this.internal.__metadata__===void 0&&(this.internal.__metadata__={metadata:a,namespaceuri:o||"http://jspdf.default.namespaceuri/"},this.internal.events.subscribe("putCatalog",n),this.internal.events.subscribe("postPutResources",e)),this}}(Vt.API),function(r){var e=r.API,n=e.pdfEscape16=function(s,c){for(var h,f=c.metadata.Unicode.widths,p=["","0","00","000","0000"],w=[""],L=0,x=s.length;L<x;++L){if(h=c.metadata.characterToGlyph(s.charCodeAt(L)),c.metadata.glyIdsUsed.push(h),c.metadata.toUnicode[h]=s.charCodeAt(L),f.indexOf(h)==-1&&(f.push(h),f.push([parseInt(c.metadata.widthOfGlyph(h),10)])),h=="0")return w.join("");h=h.toString(16),w.push(p[4-h.length],h)}return w.join("")},a=function(s){var c,h,f,p,w,L,x;for(w=`/CIDInit /ProcSet findresource begin
12 dict begin
begincmap
/CIDSystemInfo <<
  /Registry (Adobe)
  /Ordering (UCS)
  /Supplement 0
>> def
/CMapName /Adobe-Identity-UCS def
/CMapType 2 def
1 begincodespacerange
<0000><ffff>
endcodespacerange`,f=[],L=0,x=(h=Object.keys(s).sort(function(m,D){return m-D})).length;L<x;L++)c=h[L],f.length>=100&&(w+=`
`+f.length+` beginbfchar
`+f.join(`
`)+`
endbfchar`,f=[]),s[c]!==void 0&&s[c]!==null&&typeof s[c].toString=="function"&&(p=("0000"+s[c].toString(16)).slice(-4),c=("0000"+(+c).toString(16)).slice(-4),f.push("<"+c+"><"+p+">"));return f.length&&(w+=`
`+f.length+` beginbfchar
`+f.join(`
`)+`
endbfchar
`),w+=`endcmap
CMapName currentdict /CMap defineresource pop
end
end`};e.events.push(["putFont",function(s){(function(c){var h=c.font,f=c.out,p=c.newObject,w=c.putStream;if(h.metadata instanceof r.API.TTFFont&&h.encoding==="Identity-H"){for(var L=h.metadata.Unicode.widths,x=h.metadata.subset.encode(h.metadata.glyIdsUsed,1),m="",D=0;D<x.length;D++)m+=String.fromCharCode(x[D]);var k=p();w({data:m,addLength1:!0,objectId:k}),f("endobj");var O=p();w({data:a(h.metadata.toUnicode),addLength1:!0,objectId:O}),f("endobj");var _=p();f("<<"),f("/Type /FontDescriptor"),f("/FontName /"+_i(h.fontName)),f("/FontFile2 "+k+" 0 R"),f("/FontBBox "+r.API.PDFObject.convert(h.metadata.bbox)),f("/Flags "+h.metadata.flags),f("/StemV "+h.metadata.stemV),f("/ItalicAngle "+h.metadata.italicAngle),f("/Ascent "+h.metadata.ascender),f("/Descent "+h.metadata.decender),f("/CapHeight "+h.metadata.capHeight),f(">>"),f("endobj");var j=p();f("<<"),f("/Type /Font"),f("/BaseFont /"+_i(h.fontName)),f("/FontDescriptor "+_+" 0 R"),f("/W "+r.API.PDFObject.convert(L)),f("/CIDToGIDMap /Identity"),f("/DW 1000"),f("/Subtype /CIDFontType2"),f("/CIDSystemInfo"),f("<<"),f("/Supplement 0"),f("/Registry (Adobe)"),f("/Ordering ("+h.encoding+")"),f(">>"),f(">>"),f("endobj"),h.objectNumber=p(),f("<<"),f("/Type /Font"),f("/Subtype /Type0"),f("/ToUnicode "+O+" 0 R"),f("/BaseFont /"+_i(h.fontName)),f("/Encoding /"+h.encoding),f("/DescendantFonts ["+j+" 0 R]"),f(">>"),f("endobj"),h.isAlreadyPutted=!0}})(s)}]),e.events.push(["putFont",function(s){(function(c){var h=c.font,f=c.out,p=c.newObject,w=c.putStream;if(h.metadata instanceof r.API.TTFFont&&h.encoding==="WinAnsiEncoding"){for(var L=h.metadata.rawData,x="",m=0;m<L.length;m++)x+=String.fromCharCode(L[m]);var D=p();w({data:x,addLength1:!0,objectId:D}),f("endobj");var k=p();w({data:a(h.metadata.toUnicode),addLength1:!0,objectId:k}),f("endobj");var O=p();f("<<"),f("/Descent "+h.metadata.decender),f("/CapHeight "+h.metadata.capHeight),f("/StemV "+h.metadata.stemV),f("/Type /FontDescriptor"),f("/FontFile2 "+D+" 0 R"),f("/Flags 96"),f("/FontBBox "+r.API.PDFObject.convert(h.metadata.bbox)),f("/FontName /"+_i(h.fontName)),f("/ItalicAngle "+h.metadata.italicAngle),f("/Ascent "+h.metadata.ascender),f(">>"),f("endobj"),h.objectNumber=p();for(var _=0;_<h.metadata.hmtx.widths.length;_++)h.metadata.hmtx.widths[_]=parseInt(h.metadata.hmtx.widths[_]*(1e3/h.metadata.head.unitsPerEm));f("<</Subtype/TrueType/Type/Font/ToUnicode "+k+" 0 R/BaseFont/"+_i(h.fontName)+"/FontDescriptor "+O+" 0 R/Encoding/"+h.encoding+" /FirstChar 29 /LastChar 255 /Widths "+r.API.PDFObject.convert(h.metadata.hmtx.widths)+">>"),f("endobj"),h.isAlreadyPutted=!0}})(s)}]);var o=function(s){var c,h=s.text||"",f=s.x,p=s.y,w=s.options||{},L=s.mutex||{},x=L.pdfEscape,m=L.activeFontKey,D=L.fonts,k=m,O="",_=0,j="",J=D[k].encoding;if(D[k].encoding!=="Identity-H")return{text:h,x:f,y:p,options:w,mutex:L};for(j=h,k=m,Array.isArray(h)&&(j=h[0]),_=0;_<j.length;_+=1)D[k].metadata.hasOwnProperty("cmap")&&(c=D[k].metadata.cmap.unicode.codeMap[j[_].charCodeAt(0)]),c||j[_].charCodeAt(0)<256&&D[k].metadata.hasOwnProperty("Unicode")?O+=j[_]:O+="";var st="";return parseInt(k.slice(1))<14||J==="WinAnsiEncoding"?st=x(O,k).split("").map(function(ft){return ft.charCodeAt(0).toString(16)}).join(""):J==="Identity-H"&&(st=n(O,D[k])),L.isHex=!0,{text:st,x:f,y:p,options:w,mutex:L}};e.events.push(["postProcessText",function(s){var c=s.text||"",h=[],f={text:c,x:s.x,y:s.y,options:s.options,mutex:s.mutex};if(Array.isArray(c)){var p=0;for(p=0;p<c.length;p+=1)Array.isArray(c[p])&&c[p].length===3?h.push([o(Object.assign({},f,{text:c[p][0]})).text,c[p][1],c[p][2]]):h.push(o(Object.assign({},f,{text:c[p]})).text);s.text=h}else s.text=o(Object.assign({},f,{text:c})).text}])}(Vt),function(r){var e=function(){return this.internal.vFS===void 0&&(this.internal.vFS={}),!0};r.existsFileInVFS=function(n){return e.call(this),this.internal.vFS[n]!==void 0},r.addFileToVFS=function(n,a){return e.call(this),this.internal.vFS[n]=a,this},r.getFileFromVFS=function(n){return e.call(this),this.internal.vFS[n]!==void 0?this.internal.vFS[n]:null}}(Vt.API),function(r){r.__bidiEngine__=r.prototype.__bidiEngine__=function(a){var o,s,c,h,f,p,w,L=e,x=[[0,3,0,1,0,0,0],[0,3,0,1,2,2,0],[0,3,0,17,2,0,1],[0,3,5,5,4,1,0],[0,3,21,21,4,0,1],[0,3,5,5,4,2,0]],m=[[2,0,1,1,0,1,0],[2,0,1,1,0,2,0],[2,0,2,1,3,2,0],[2,0,2,33,3,1,1]],D={L:0,R:1,EN:2,AN:3,N:4,B:5,S:6},k={0:0,5:1,6:2,7:3,32:4,251:5,254:6,255:7},O=["(",")","(","<",">","<","[","]","[","{","}","{","«","»","«","‹","›","‹","⁅","⁆","⁅","⁽","⁾","⁽","₍","₎","₍","≤","≥","≤","〈","〉","〈","﹙","﹚","﹙","﹛","﹜","﹛","﹝","﹞","﹝","﹤","﹥","﹤"],_=new RegExp(/^([1-4|9]|1[0-9]|2[0-9]|3[0168]|4[04589]|5[012]|7[78]|159|16[0-9]|17[0-2]|21[569]|22[03489]|250)$/),j=!1,J=0;this.__bidiEngine__={};var st=function(C){var I=C.charCodeAt(),z=I>>8,R=k[z];return R!==void 0?L[256*R+(255&I)]:z===252||z===253?"AL":_.test(z)?"L":z===8?"R":"N"},ft=function(C){for(var I,z=0;z<C.length;z++){if((I=st(C.charAt(z)))==="L")return!1;if(I==="R")return!0}return!1},At=function(C,I,z,R){var lt,ot,mt,tt,pt=I[R];switch(pt){case"L":case"R":j=!1;break;case"N":case"AN":break;case"EN":j&&(pt="AN");break;case"AL":j=!0,pt="R";break;case"WS":pt="N";break;case"CS":R<1||R+1>=I.length||(lt=z[R-1])!=="EN"&&lt!=="AN"||(ot=I[R+1])!=="EN"&&ot!=="AN"?pt="N":j&&(ot="AN"),pt=ot===lt?ot:"N";break;case"ES":pt=(lt=R>0?z[R-1]:"B")==="EN"&&R+1<I.length&&I[R+1]==="EN"?"EN":"N";break;case"ET":if(R>0&&z[R-1]==="EN"){pt="EN";break}if(j){pt="N";break}for(mt=R+1,tt=I.length;mt<tt&&I[mt]==="ET";)mt++;pt=mt<tt&&I[mt]==="EN"?"EN":"N";break;case"NSM":if(c&&!h){for(tt=I.length,mt=R+1;mt<tt&&I[mt]==="NSM";)mt++;if(mt<tt){var ut=C[R],Ot=ut>=1425&&ut<=2303||ut===64286;if(lt=I[mt],Ot&&(lt==="R"||lt==="AL")){pt="R";break}}}pt=R<1||(lt=I[R-1])==="B"?"N":z[R-1];break;case"B":j=!1,o=!0,pt=J;break;case"S":s=!0,pt="N";break;case"LRE":case"RLE":case"LRO":case"RLO":case"PDF":j=!1;break;case"BN":pt="N"}return pt},rt=function(C,I,z){var R=C.split("");return z&&G(R,z,{hiLevel:J}),R.reverse(),I&&I.reverse(),R.join("")},G=function(C,I,z){var R,lt,ot,mt,tt,pt=-1,ut=C.length,Ot=0,N=[],T=J?m:x,E=[];for(j=!1,o=!1,s=!1,lt=0;lt<ut;lt++)E[lt]=st(C[lt]);for(ot=0;ot<ut;ot++){if(tt=Ot,N[ot]=At(C,E,N,ot),R=240&(Ot=T[tt][D[N[ot]]]),Ot&=15,I[ot]=mt=T[Ot][5],R>0)if(R===16){for(lt=pt;lt<ot;lt++)I[lt]=1;pt=-1}else pt=-1;if(T[Ot][6])pt===-1&&(pt=ot);else if(pt>-1){for(lt=pt;lt<ot;lt++)I[lt]=mt;pt=-1}E[ot]==="B"&&(I[ot]=0),z.hiLevel|=mt}s&&function(V,Y,Q){for(var et=0;et<Q;et++)if(V[et]==="S"){Y[et]=J;for(var Z=et-1;Z>=0&&V[Z]==="WS";Z--)Y[Z]=J}}(E,I,ut)},vt=function(C,I,z,R,lt){if(!(lt.hiLevel<C)){if(C===1&&J===1&&!o)return I.reverse(),void(z&&z.reverse());for(var ot,mt,tt,pt,ut=I.length,Ot=0;Ot<ut;){if(R[Ot]>=C){for(tt=Ot+1;tt<ut&&R[tt]>=C;)tt++;for(pt=Ot,mt=tt-1;pt<mt;pt++,mt--)ot=I[pt],I[pt]=I[mt],I[mt]=ot,z&&(ot=z[pt],z[pt]=z[mt],z[mt]=ot);Ot=tt}Ot++}}},bt=function(C,I,z){var R=C.split(""),lt={hiLevel:J};return z||(z=[]),G(R,z,lt),function(ot,mt,tt){if(tt.hiLevel!==0&&w)for(var pt,ut=0;ut<ot.length;ut++)mt[ut]===1&&(pt=O.indexOf(ot[ut]))>=0&&(ot[ut]=O[pt+1])}(R,z,lt),vt(2,R,I,z,lt),vt(1,R,I,z,lt),R.join("")};return this.__bidiEngine__.doBidiReorder=function(C,I,z){if(function(lt,ot){if(ot)for(var mt=0;mt<lt.length;mt++)ot[mt]=mt;h===void 0&&(h=ft(lt)),p===void 0&&(p=ft(lt))}(C,I),c||!f||p)if(c&&f&&h^p)J=h?1:0,C=rt(C,I,z);else if(!c&&f&&p)J=h?1:0,C=bt(C,I,z),C=rt(C,I);else if(!c||h||f||p){if(c&&!f&&h^p)C=rt(C,I),h?(J=0,C=bt(C,I,z)):(J=1,C=bt(C,I,z),C=rt(C,I));else if(c&&h&&!f&&p)J=1,C=bt(C,I,z),C=rt(C,I);else if(!c&&!f&&h^p){var R=w;h?(J=1,C=bt(C,I,z),J=0,w=!1,C=bt(C,I,z),w=R):(J=0,C=bt(C,I,z),C=rt(C,I),J=1,w=!1,C=bt(C,I,z),w=R,C=rt(C,I))}}else J=0,C=bt(C,I,z);else J=h?1:0,C=bt(C,I,z);return C},this.__bidiEngine__.setOptions=function(C){C&&(c=C.isInputVisual,f=C.isOutputVisual,h=C.isInputRtl,p=C.isOutputRtl,w=C.isSymmetricSwapping)},this.__bidiEngine__.setOptions(a),this.__bidiEngine__};var e=["BN","BN","BN","BN","BN","BN","BN","BN","BN","S","B","S","WS","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","B","B","B","S","WS","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","BN","BN","BN","BN","BN","BN","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","CS","N","ET","ET","ET","ET","N","N","N","N","L","N","N","BN","N","N","ET","ET","EN","EN","N","L","N","N","N","EN","L","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","N","N","N","N","N","ET","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","NSM","R","NSM","NSM","R","NSM","NSM","R","NSM","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","N","N","N","N","N","R","R","R","R","R","N","N","N","N","N","N","N","N","N","N","N","AN","AN","AN","AN","AN","AN","N","N","AL","ET","ET","AL","CS","AL","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","AN","AN","AN","AN","AN","AN","AN","AN","AN","ET","AN","AN","AL","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","N","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","NSM","NSM","N","NSM","NSM","NSM","NSM","AL","AL","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","R","N","N","N","N","R","N","N","N","N","N","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","BN","BN","BN","L","R","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","B","LRE","RLE","PDF","LRO","RLO","CS","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","BN","BN","BN","BN","BN","N","LRI","RLI","FSI","PDI","BN","BN","BN","BN","BN","BN","EN","L","N","N","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","L","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","N","N","N","N","N","R","NSM","R","R","R","R","R","R","R","R","R","R","ES","R","R","R","R","R","R","R","R","R","R","R","R","R","N","R","R","R","R","R","N","R","N","R","R","N","R","R","N","R","R","R","R","R","R","R","R","R","R","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","CS","N","N","CS","N","N","N","N","N","N","N","N","N","ET","N","N","ES","ES","N","N","N","N","N","ET","ET","N","N","N","N","N","AL","AL","AL","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","BN","N","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","N","N","N","ET","ET","N","N","N","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N"],n=new r.__bidiEngine__({isInputVisual:!0});r.API.events.push(["postProcessText",function(a){var o=a.text,s=(a.x,a.y,a.options||{}),c=(a.mutex,s.lang,[]);if(s.isInputVisual=typeof s.isInputVisual!="boolean"||s.isInputVisual,n.setOptions(s),Object.prototype.toString.call(o)==="[object Array]"){var h=0;for(c=[],h=0;h<o.length;h+=1)Object.prototype.toString.call(o[h])==="[object Array]"?c.push([n.doBidiReorder(o[h][0]),o[h][1],o[h][2]]):c.push([n.doBidiReorder(o[h])]);a.text=c}else a.text=n.doBidiReorder(o);n.setOptions({isInputVisual:!0})}])}(Vt),Vt.API.TTFFont=function(){function r(e){var n;if(this.rawData=e,n=this.contents=new Ir(e),this.contents.pos=4,n.readString(4)==="ttcf")throw new Error("TTCF not supported.");n.pos=0,this.parse(),this.subset=new Fh(this),this.registerTTF()}return r.open=function(e){return new r(e)},r.prototype.parse=function(){return this.directory=new vh(this.contents),this.head=new yh(this),this.name=new xh(this),this.cmap=new Zl(this),this.toUnicode={},this.hhea=new wh(this),this.maxp=new Sh(this),this.hmtx=new _h(this),this.post=new Ah(this),this.os2=new Nh(this),this.loca=new kh(this),this.glyf=new Ph(this),this.ascender=this.os2.exists&&this.os2.ascender||this.hhea.ascender,this.decender=this.os2.exists&&this.os2.decender||this.hhea.decender,this.lineGap=this.os2.exists&&this.os2.lineGap||this.hhea.lineGap,this.bbox=[this.head.xMin,this.head.yMin,this.head.xMax,this.head.yMax]},r.prototype.registerTTF=function(){var e,n,a,o,s;if(this.scaleFactor=1e3/this.head.unitsPerEm,this.bbox=(function(){var c,h,f,p;for(p=[],c=0,h=(f=this.bbox).length;c<h;c++)e=f[c],p.push(Math.round(e*this.scaleFactor));return p}).call(this),this.stemV=0,this.post.exists?(a=255&(o=this.post.italic_angle),32768&(n=o>>16)&&(n=-(1+(65535^n))),this.italicAngle=+(n+"."+a)):this.italicAngle=0,this.ascender=Math.round(this.ascender*this.scaleFactor),this.decender=Math.round(this.decender*this.scaleFactor),this.lineGap=Math.round(this.lineGap*this.scaleFactor),this.capHeight=this.os2.exists&&this.os2.capHeight||this.ascender,this.xHeight=this.os2.exists&&this.os2.xHeight||0,this.familyClass=(this.os2.exists&&this.os2.familyClass||0)>>8,this.isSerif=(s=this.familyClass)===1||s===2||s===3||s===4||s===5||s===7,this.isScript=this.familyClass===10,this.flags=0,this.post.isFixedPitch&&(this.flags|=1),this.isSerif&&(this.flags|=2),this.isScript&&(this.flags|=8),this.italicAngle!==0&&(this.flags|=64),this.flags|=32,!this.cmap.unicode)throw new Error("No unicode cmap for font")},r.prototype.characterToGlyph=function(e){var n;return((n=this.cmap.unicode)!=null?n.codeMap[e]:void 0)||0},r.prototype.widthOfGlyph=function(e){var n;return n=1e3/this.head.unitsPerEm,this.hmtx.forGlyph(e).advance*n},r.prototype.widthOfString=function(e,n,a){var o,s,c,h;for(c=0,s=0,h=(e=""+e).length;0<=h?s<h:s>h;s=0<=h?++s:--s)o=e.charCodeAt(s),c+=this.widthOfGlyph(this.characterToGlyph(o))+a*(1e3/n)||0;return c*(n/1e3)},r.prototype.lineHeight=function(e,n){var a;return n==null&&(n=!1),a=n?this.lineGap:0,(this.ascender+a-this.decender)/1e3*e},r}();var qn,Ir=function(){function r(e){this.data=e??[],this.pos=0,this.length=this.data.length}return r.prototype.readByte=function(){return this.data[this.pos++]},r.prototype.writeByte=function(e){return this.data[this.pos++]=e},r.prototype.readUInt32=function(){return 16777216*this.readByte()+(this.readByte()<<16)+(this.readByte()<<8)+this.readByte()},r.prototype.writeUInt32=function(e){return this.writeByte(e>>>24&255),this.writeByte(e>>16&255),this.writeByte(e>>8&255),this.writeByte(255&e)},r.prototype.readInt32=function(){var e;return(e=this.readUInt32())>=2147483648?e-4294967296:e},r.prototype.writeInt32=function(e){return e<0&&(e+=4294967296),this.writeUInt32(e)},r.prototype.readUInt16=function(){return this.readByte()<<8|this.readByte()},r.prototype.writeUInt16=function(e){return this.writeByte(e>>8&255),this.writeByte(255&e)},r.prototype.readInt16=function(){var e;return(e=this.readUInt16())>=32768?e-65536:e},r.prototype.writeInt16=function(e){return e<0&&(e+=65536),this.writeUInt16(e)},r.prototype.readString=function(e){var n,a;for(a=[],n=0;0<=e?n<e:n>e;n=0<=e?++n:--n)a[n]=String.fromCharCode(this.readByte());return a.join("")},r.prototype.writeString=function(e){var n,a,o;for(o=[],n=0,a=e.length;0<=a?n<a:n>a;n=0<=a?++n:--n)o.push(this.writeByte(e.charCodeAt(n)));return o},r.prototype.readShort=function(){return this.readInt16()},r.prototype.writeShort=function(e){return this.writeInt16(e)},r.prototype.readLongLong=function(){var e,n,a,o,s,c,h,f;return e=this.readByte(),n=this.readByte(),a=this.readByte(),o=this.readByte(),s=this.readByte(),c=this.readByte(),h=this.readByte(),f=this.readByte(),128&e?-1*(72057594037927940*(255^e)+281474976710656*(255^n)+1099511627776*(255^a)+4294967296*(255^o)+16777216*(255^s)+65536*(255^c)+256*(255^h)+(255^f)+1):72057594037927940*e+281474976710656*n+1099511627776*a+4294967296*o+16777216*s+65536*c+256*h+f},r.prototype.writeLongLong=function(e){var n,a;return n=Math.floor(e/4294967296),a=**********&e,this.writeByte(n>>24&255),this.writeByte(n>>16&255),this.writeByte(n>>8&255),this.writeByte(255&n),this.writeByte(a>>24&255),this.writeByte(a>>16&255),this.writeByte(a>>8&255),this.writeByte(255&a)},r.prototype.readInt=function(){return this.readInt32()},r.prototype.writeInt=function(e){return this.writeInt32(e)},r.prototype.read=function(e){var n,a;for(n=[],a=0;0<=e?a<e:a>e;a=0<=e?++a:--a)n.push(this.readByte());return n},r.prototype.write=function(e){var n,a,o,s;for(s=[],a=0,o=e.length;a<o;a++)n=e[a],s.push(this.writeByte(n));return s},r}(),vh=function(){var r;function e(n){var a,o,s;for(this.scalarType=n.readInt(),this.tableCount=n.readShort(),this.searchRange=n.readShort(),this.entrySelector=n.readShort(),this.rangeShift=n.readShort(),this.tables={},o=0,s=this.tableCount;0<=s?o<s:o>s;o=0<=s?++o:--o)a={tag:n.readString(4),checksum:n.readInt(),offset:n.readInt(),length:n.readInt()},this.tables[a.tag]=a}return e.prototype.encode=function(n){var a,o,s,c,h,f,p,w,L,x,m,D,k;for(k in m=Object.keys(n).length,f=Math.log(2),L=16*Math.floor(Math.log(m)/f),c=Math.floor(L/f),w=16*m-L,(o=new Ir).writeInt(this.scalarType),o.writeShort(m),o.writeShort(L),o.writeShort(c),o.writeShort(w),s=16*m,p=o.pos+s,h=null,D=[],n)for(x=n[k],o.writeString(k),o.writeInt(r(x)),o.writeInt(p),o.writeInt(x.length),D=D.concat(x),k==="head"&&(h=p),p+=x.length;p%4;)D.push(0),p++;return o.write(D),a=2981146554-r(o.data),o.pos=h+8,o.writeUInt32(a),o.data},r=function(n){var a,o,s,c;for(n=tc.call(n);n.length%4;)n.push(0);for(s=new Ir(n),o=0,a=0,c=n.length;a<c;a=a+=4)o+=s.readUInt32();return **********&o},e}(),bh={}.hasOwnProperty,tr=function(r,e){for(var n in e)bh.call(e,n)&&(r[n]=e[n]);function a(){this.constructor=r}return a.prototype=e.prototype,r.prototype=new a,r.__super__=e.prototype,r};qn=function(){function r(e){var n;this.file=e,n=this.file.directory.tables[this.tag],this.exists=!!n,n&&(this.offset=n.offset,this.length=n.length,this.parse(this.file.contents))}return r.prototype.parse=function(){},r.prototype.encode=function(){},r.prototype.raw=function(){return this.exists?(this.file.contents.pos=this.offset,this.file.contents.read(this.length)):null},r}();var yh=function(r){function e(){return e.__super__.constructor.apply(this,arguments)}return tr(e,qn),e.prototype.tag="head",e.prototype.parse=function(n){return n.pos=this.offset,this.version=n.readInt(),this.revision=n.readInt(),this.checkSumAdjustment=n.readInt(),this.magicNumber=n.readInt(),this.flags=n.readShort(),this.unitsPerEm=n.readShort(),this.created=n.readLongLong(),this.modified=n.readLongLong(),this.xMin=n.readShort(),this.yMin=n.readShort(),this.xMax=n.readShort(),this.yMax=n.readShort(),this.macStyle=n.readShort(),this.lowestRecPPEM=n.readShort(),this.fontDirectionHint=n.readShort(),this.indexToLocFormat=n.readShort(),this.glyphDataFormat=n.readShort()},e.prototype.encode=function(n){var a;return(a=new Ir).writeInt(this.version),a.writeInt(this.revision),a.writeInt(this.checkSumAdjustment),a.writeInt(this.magicNumber),a.writeShort(this.flags),a.writeShort(this.unitsPerEm),a.writeLongLong(this.created),a.writeLongLong(this.modified),a.writeShort(this.xMin),a.writeShort(this.yMin),a.writeShort(this.xMax),a.writeShort(this.yMax),a.writeShort(this.macStyle),a.writeShort(this.lowestRecPPEM),a.writeShort(this.fontDirectionHint),a.writeShort(n),a.writeShort(this.glyphDataFormat),a.data},e}(),Ml=function(){function r(e,n){var a,o,s,c,h,f,p,w,L,x,m,D,k,O,_,j,J;switch(this.platformID=e.readUInt16(),this.encodingID=e.readShort(),this.offset=n+e.readInt(),L=e.pos,e.pos=this.offset,this.format=e.readUInt16(),this.length=e.readUInt16(),this.language=e.readUInt16(),this.isUnicode=this.platformID===3&&this.encodingID===1&&this.format===4||this.platformID===0&&this.format===4,this.codeMap={},this.format){case 0:for(f=0;f<256;++f)this.codeMap[f]=e.readByte();break;case 4:for(m=e.readUInt16(),x=m/2,e.pos+=6,s=function(){var st,ft;for(ft=[],f=st=0;0<=x?st<x:st>x;f=0<=x?++st:--st)ft.push(e.readUInt16());return ft}(),e.pos+=2,k=function(){var st,ft;for(ft=[],f=st=0;0<=x?st<x:st>x;f=0<=x?++st:--st)ft.push(e.readUInt16());return ft}(),p=function(){var st,ft;for(ft=[],f=st=0;0<=x?st<x:st>x;f=0<=x?++st:--st)ft.push(e.readUInt16());return ft}(),w=function(){var st,ft;for(ft=[],f=st=0;0<=x?st<x:st>x;f=0<=x?++st:--st)ft.push(e.readUInt16());return ft}(),o=(this.length-e.pos+this.offset)/2,h=function(){var st,ft;for(ft=[],f=st=0;0<=o?st<o:st>o;f=0<=o?++st:--st)ft.push(e.readUInt16());return ft}(),f=_=0,J=s.length;_<J;f=++_)for(O=s[f],a=j=D=k[f];D<=O?j<=O:j>=O;a=D<=O?++j:--j)w[f]===0?c=a+p[f]:(c=h[w[f]/2+(a-D)-(x-f)]||0)!==0&&(c+=p[f]),this.codeMap[a]=65535&c}e.pos=L}return r.encode=function(e,n){var a,o,s,c,h,f,p,w,L,x,m,D,k,O,_,j,J,st,ft,At,rt,G,vt,bt,C,I,z,R,lt,ot,mt,tt,pt,ut,Ot,N,T,E,V,Y,Q,et,Z,Lt,Nt,Tt;switch(R=new Ir,c=Object.keys(e).sort(function(kt,Ht){return kt-Ht}),n){case"macroman":for(k=0,O=function(){var kt=[];for(D=0;D<256;++D)kt.push(0);return kt}(),j={0:0},s={},lt=0,pt=c.length;lt<pt;lt++)j[Z=e[o=c[lt]]]==null&&(j[Z]=++k),s[o]={old:e[o],new:j[e[o]]},O[o]=j[e[o]];return R.writeUInt16(1),R.writeUInt16(0),R.writeUInt32(12),R.writeUInt16(0),R.writeUInt16(262),R.writeUInt16(0),R.write(O),{charMap:s,subtable:R.data,maxGlyphID:k+1};case"unicode":for(I=[],L=[],J=0,j={},a={},_=p=null,ot=0,ut=c.length;ot<ut;ot++)j[ft=e[o=c[ot]]]==null&&(j[ft]=++J),a[o]={old:ft,new:j[ft]},h=j[ft]-o,_!=null&&h===p||(_&&L.push(_),I.push(o),p=h),_=o;for(_&&L.push(_),L.push(65535),I.push(65535),bt=2*(vt=I.length),G=2*Math.pow(Math.log(vt)/Math.LN2,2),x=Math.log(G/2)/Math.LN2,rt=2*vt-G,f=[],At=[],m=[],D=mt=0,Ot=I.length;mt<Ot;D=++mt){if(C=I[D],w=L[D],C===65535){f.push(0),At.push(0);break}if(C-(z=a[C].new)>=32768)for(f.push(0),At.push(2*(m.length+vt-D)),o=tt=C;C<=w?tt<=w:tt>=w;o=C<=w?++tt:--tt)m.push(a[o].new);else f.push(z-C),At.push(0)}for(R.writeUInt16(3),R.writeUInt16(1),R.writeUInt32(12),R.writeUInt16(4),R.writeUInt16(16+8*vt+2*m.length),R.writeUInt16(0),R.writeUInt16(bt),R.writeUInt16(G),R.writeUInt16(x),R.writeUInt16(rt),Q=0,N=L.length;Q<N;Q++)o=L[Q],R.writeUInt16(o);for(R.writeUInt16(0),et=0,T=I.length;et<T;et++)o=I[et],R.writeUInt16(o);for(Lt=0,E=f.length;Lt<E;Lt++)h=f[Lt],R.writeUInt16(h);for(Nt=0,V=At.length;Nt<V;Nt++)st=At[Nt],R.writeUInt16(st);for(Tt=0,Y=m.length;Tt<Y;Tt++)k=m[Tt],R.writeUInt16(k);return{charMap:a,subtable:R.data,maxGlyphID:J+1}}},r}(),Zl=function(r){function e(){return e.__super__.constructor.apply(this,arguments)}return tr(e,qn),e.prototype.tag="cmap",e.prototype.parse=function(n){var a,o,s;for(n.pos=this.offset,this.version=n.readUInt16(),s=n.readUInt16(),this.tables=[],this.unicode=null,o=0;0<=s?o<s:o>s;o=0<=s?++o:--o)a=new Ml(n,this.offset),this.tables.push(a),a.isUnicode&&this.unicode==null&&(this.unicode=a);return!0},e.encode=function(n,a){var o,s;return a==null&&(a="macroman"),o=Ml.encode(n,a),(s=new Ir).writeUInt16(0),s.writeUInt16(1),o.table=s.data.concat(o.subtable),o},e}(),wh=function(r){function e(){return e.__super__.constructor.apply(this,arguments)}return tr(e,qn),e.prototype.tag="hhea",e.prototype.parse=function(n){return n.pos=this.offset,this.version=n.readInt(),this.ascender=n.readShort(),this.decender=n.readShort(),this.lineGap=n.readShort(),this.advanceWidthMax=n.readShort(),this.minLeftSideBearing=n.readShort(),this.minRightSideBearing=n.readShort(),this.xMaxExtent=n.readShort(),this.caretSlopeRise=n.readShort(),this.caretSlopeRun=n.readShort(),this.caretOffset=n.readShort(),n.pos+=8,this.metricDataFormat=n.readShort(),this.numberOfMetrics=n.readUInt16()},e}(),Nh=function(r){function e(){return e.__super__.constructor.apply(this,arguments)}return tr(e,qn),e.prototype.tag="OS/2",e.prototype.parse=function(n){if(n.pos=this.offset,this.version=n.readUInt16(),this.averageCharWidth=n.readShort(),this.weightClass=n.readUInt16(),this.widthClass=n.readUInt16(),this.type=n.readShort(),this.ySubscriptXSize=n.readShort(),this.ySubscriptYSize=n.readShort(),this.ySubscriptXOffset=n.readShort(),this.ySubscriptYOffset=n.readShort(),this.ySuperscriptXSize=n.readShort(),this.ySuperscriptYSize=n.readShort(),this.ySuperscriptXOffset=n.readShort(),this.ySuperscriptYOffset=n.readShort(),this.yStrikeoutSize=n.readShort(),this.yStrikeoutPosition=n.readShort(),this.familyClass=n.readShort(),this.panose=function(){var a,o;for(o=[],a=0;a<10;++a)o.push(n.readByte());return o}(),this.charRange=function(){var a,o;for(o=[],a=0;a<4;++a)o.push(n.readInt());return o}(),this.vendorID=n.readString(4),this.selection=n.readShort(),this.firstCharIndex=n.readShort(),this.lastCharIndex=n.readShort(),this.version>0&&(this.ascent=n.readShort(),this.descent=n.readShort(),this.lineGap=n.readShort(),this.winAscent=n.readShort(),this.winDescent=n.readShort(),this.codePageRange=function(){var a,o;for(o=[],a=0;a<2;a=++a)o.push(n.readInt());return o}(),this.version>1))return this.xHeight=n.readShort(),this.capHeight=n.readShort(),this.defaultChar=n.readShort(),this.breakChar=n.readShort(),this.maxContext=n.readShort()},e}(),Ah=function(r){function e(){return e.__super__.constructor.apply(this,arguments)}return tr(e,qn),e.prototype.tag="post",e.prototype.parse=function(n){var a,o,s;switch(n.pos=this.offset,this.format=n.readInt(),this.italicAngle=n.readInt(),this.underlinePosition=n.readShort(),this.underlineThickness=n.readShort(),this.isFixedPitch=n.readInt(),this.minMemType42=n.readInt(),this.maxMemType42=n.readInt(),this.minMemType1=n.readInt(),this.maxMemType1=n.readInt(),this.format){case 65536:break;case 131072:var c;for(o=n.readUInt16(),this.glyphNameIndex=[],c=0;0<=o?c<o:c>o;c=0<=o?++c:--c)this.glyphNameIndex.push(n.readUInt16());for(this.names=[],s=[];n.pos<this.offset+this.length;)a=n.readByte(),s.push(this.names.push(n.readString(a)));return s;case 151552:return o=n.readUInt16(),this.offsets=n.read(o);case 196608:break;case 262144:return this.map=(function(){var h,f,p;for(p=[],c=h=0,f=this.file.maxp.numGlyphs;0<=f?h<f:h>f;c=0<=f?++h:--h)p.push(n.readUInt32());return p}).call(this)}},e}(),Lh=function(r,e){this.raw=r,this.length=r.length,this.platformID=e.platformID,this.encodingID=e.encodingID,this.languageID=e.languageID},xh=function(r){function e(){return e.__super__.constructor.apply(this,arguments)}return tr(e,qn),e.prototype.tag="name",e.prototype.parse=function(n){var a,o,s,c,h,f,p,w,L,x,m;for(n.pos=this.offset,n.readShort(),a=n.readShort(),f=n.readShort(),o=[],c=0;0<=a?c<a:c>a;c=0<=a?++c:--c)o.push({platformID:n.readShort(),encodingID:n.readShort(),languageID:n.readShort(),nameID:n.readShort(),length:n.readShort(),offset:this.offset+f+n.readShort()});for(p={},c=L=0,x=o.length;L<x;c=++L)s=o[c],n.pos=s.offset,w=n.readString(s.length),h=new Lh(w,s),p[m=s.nameID]==null&&(p[m]=[]),p[s.nameID].push(h);this.strings=p,this.copyright=p[0],this.fontFamily=p[1],this.fontSubfamily=p[2],this.uniqueSubfamily=p[3],this.fontName=p[4],this.version=p[5];try{this.postscriptName=p[6][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}catch{this.postscriptName=p[4][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}return this.trademark=p[7],this.manufacturer=p[8],this.designer=p[9],this.description=p[10],this.vendorUrl=p[11],this.designerUrl=p[12],this.license=p[13],this.licenseUrl=p[14],this.preferredFamily=p[15],this.preferredSubfamily=p[17],this.compatibleFull=p[18],this.sampleText=p[19]},e}(),Sh=function(r){function e(){return e.__super__.constructor.apply(this,arguments)}return tr(e,qn),e.prototype.tag="maxp",e.prototype.parse=function(n){return n.pos=this.offset,this.version=n.readInt(),this.numGlyphs=n.readUInt16(),this.maxPoints=n.readUInt16(),this.maxContours=n.readUInt16(),this.maxCompositePoints=n.readUInt16(),this.maxComponentContours=n.readUInt16(),this.maxZones=n.readUInt16(),this.maxTwilightPoints=n.readUInt16(),this.maxStorage=n.readUInt16(),this.maxFunctionDefs=n.readUInt16(),this.maxInstructionDefs=n.readUInt16(),this.maxStackElements=n.readUInt16(),this.maxSizeOfInstructions=n.readUInt16(),this.maxComponentElements=n.readUInt16(),this.maxComponentDepth=n.readUInt16()},e}(),_h=function(r){function e(){return e.__super__.constructor.apply(this,arguments)}return tr(e,qn),e.prototype.tag="hmtx",e.prototype.parse=function(n){var a,o,s,c,h,f,p;for(n.pos=this.offset,this.metrics=[],a=0,f=this.file.hhea.numberOfMetrics;0<=f?a<f:a>f;a=0<=f?++a:--a)this.metrics.push({advance:n.readUInt16(),lsb:n.readInt16()});for(s=this.file.maxp.numGlyphs-this.file.hhea.numberOfMetrics,this.leftSideBearings=function(){var w,L;for(L=[],a=w=0;0<=s?w<s:w>s;a=0<=s?++w:--w)L.push(n.readInt16());return L}(),this.widths=(function(){var w,L,x,m;for(m=[],w=0,L=(x=this.metrics).length;w<L;w++)c=x[w],m.push(c.advance);return m}).call(this),o=this.widths[this.widths.length-1],p=[],a=h=0;0<=s?h<s:h>s;a=0<=s?++h:--h)p.push(this.widths.push(o));return p},e.prototype.forGlyph=function(n){return n in this.metrics?this.metrics[n]:{advance:this.metrics[this.metrics.length-1].advance,lsb:this.leftSideBearings[n-this.metrics.length]}},e}(),tc=[].slice,Ph=function(r){function e(){return e.__super__.constructor.apply(this,arguments)}return tr(e,qn),e.prototype.tag="glyf",e.prototype.parse=function(){return this.cache={}},e.prototype.glyphFor=function(n){var a,o,s,c,h,f,p,w,L,x;return n in this.cache?this.cache[n]:(c=this.file.loca,a=this.file.contents,o=c.indexOf(n),(s=c.lengthOf(n))===0?this.cache[n]=null:(a.pos=this.offset+o,h=(f=new Ir(a.read(s))).readShort(),w=f.readShort(),x=f.readShort(),p=f.readShort(),L=f.readShort(),this.cache[n]=h===-1?new Ih(f,w,x,p,L):new Ch(f,h,w,x,p,L),this.cache[n]))},e.prototype.encode=function(n,a,o){var s,c,h,f,p;for(h=[],c=[],f=0,p=a.length;f<p;f++)s=n[a[f]],c.push(h.length),s&&(h=h.concat(s.encode(o)));return c.push(h.length),{table:h,offsets:c}},e}(),Ch=function(){function r(e,n,a,o,s,c){this.raw=e,this.numberOfContours=n,this.xMin=a,this.yMin=o,this.xMax=s,this.yMax=c,this.compound=!1}return r.prototype.encode=function(){return this.raw.data},r}(),Ih=function(){function r(e,n,a,o,s){var c,h;for(this.raw=e,this.xMin=n,this.yMin=a,this.xMax=o,this.yMax=s,this.compound=!0,this.glyphIDs=[],this.glyphOffsets=[],c=this.raw;h=c.readShort(),this.glyphOffsets.push(c.pos),this.glyphIDs.push(c.readUInt16()),32&h;)c.pos+=1&h?4:2,128&h?c.pos+=8:64&h?c.pos+=4:8&h&&(c.pos+=2)}return r.prototype.encode=function(){var e,n,a;for(n=new Ir(tc.call(this.raw.data)),e=0,a=this.glyphIDs.length;e<a;++e)n.pos=this.glyphOffsets[e];return n.data},r}(),kh=function(r){function e(){return e.__super__.constructor.apply(this,arguments)}return tr(e,qn),e.prototype.tag="loca",e.prototype.parse=function(n){var a,o;return n.pos=this.offset,a=this.file.head.indexToLocFormat,this.offsets=a===0?(function(){var s,c;for(c=[],o=0,s=this.length;o<s;o+=2)c.push(2*n.readUInt16());return c}).call(this):(function(){var s,c;for(c=[],o=0,s=this.length;o<s;o+=4)c.push(n.readUInt32());return c}).call(this)},e.prototype.indexOf=function(n){return this.offsets[n]},e.prototype.lengthOf=function(n){return this.offsets[n+1]-this.offsets[n]},e.prototype.encode=function(n,a){for(var o=new Uint32Array(this.offsets.length),s=0,c=0,h=0;h<o.length;++h)if(o[h]=s,c<a.length&&a[c]==h){++c,o[h]=s;var f=this.offsets[h],p=this.offsets[h+1]-f;p>0&&(s+=p)}for(var w=new Array(4*o.length),L=0;L<o.length;++L)w[4*L+3]=255&o[L],w[4*L+2]=(65280&o[L])>>8,w[4*L+1]=(16711680&o[L])>>16,w[4*L]=(**********&o[L])>>24;return w},e}(),Fh=function(){function r(e){this.font=e,this.subset={},this.unicodes={},this.next=33}return r.prototype.generateCmap=function(){var e,n,a,o,s;for(n in o=this.font.cmap.tables[0].codeMap,e={},s=this.subset)a=s[n],e[n]=o[a];return e},r.prototype.glyphsFor=function(e){var n,a,o,s,c,h,f;for(o={},c=0,h=e.length;c<h;c++)o[s=e[c]]=this.font.glyf.glyphFor(s);for(s in n=[],o)(a=o[s])!=null&&a.compound&&n.push.apply(n,a.glyphIDs);if(n.length>0)for(s in f=this.glyphsFor(n))a=f[s],o[s]=a;return o},r.prototype.encode=function(e,n){var a,o,s,c,h,f,p,w,L,x,m,D,k,O,_;for(o in a=Zl.encode(this.generateCmap(),"unicode"),c=this.glyphsFor(e),m={0:0},_=a.charMap)m[(f=_[o]).old]=f.new;for(D in x=a.maxGlyphID,c)D in m||(m[D]=x++);return w=function(j){var J,st;for(J in st={},j)st[j[J]]=J;return st}(m),L=Object.keys(w).sort(function(j,J){return j-J}),k=function(){var j,J,st;for(st=[],j=0,J=L.length;j<J;j++)h=L[j],st.push(w[h]);return st}(),s=this.font.glyf.encode(c,k,m),p=this.font.loca.encode(s.offsets,k),O={cmap:this.font.cmap.raw(),glyf:s.table,loca:p,hmtx:this.font.hmtx.raw(),hhea:this.font.hhea.raw(),maxp:this.font.maxp.raw(),post:this.font.post.raw(),name:this.font.name.raw(),head:this.font.head.encode(n)},this.font.os2.exists&&(O["OS/2"]=this.font.os2.raw()),this.font.directory.encode(O)},r}();Vt.API.PDFObject=function(){var r;function e(){}return r=function(n,a){return(Array(a+1).join("0")+n).slice(-a)},e.convert=function(n){var a,o,s,c;if(Array.isArray(n))return"["+function(){var h,f,p;for(p=[],h=0,f=n.length;h<f;h++)a=n[h],p.push(e.convert(a));return p}().join(" ")+"]";if(typeof n=="string")return"/"+n;if(n!=null&&n.isString)return"("+n+")";if(n instanceof Date)return"(D:"+r(n.getUTCFullYear(),4)+r(n.getUTCMonth(),2)+r(n.getUTCDate(),2)+r(n.getUTCHours(),2)+r(n.getUTCMinutes(),2)+r(n.getUTCSeconds(),2)+"Z)";if({}.toString.call(n)==="[object Object]"){for(o in s=["<<"],n)c=n[o],s.push("/"+o+" "+e.convert(c));return s.push(">>"),s.join(`
`)}return""+n},e}();export{Qh as $,cf as A,Wh as B,Uh as C,Mu as D,qh as E,bf as F,Bu as G,Uu as H,uf as I,Hu as J,If as K,ff as L,gf as M,yf as N,Cf as O,Vh as P,Nf as Q,Lf as R,Hh as S,Af as T,Sf as U,_f as V,xf as W,Pf as X,zh as Y,df as Z,pf as _,mf as a,gl as a0,gs as a1,mo as a2,de as a3,Vt as b,tf as c,Zh as d,rf as e,af as f,sf as g,Eu as h,hf as i,Ou as j,ju as k,Gu as l,ef as m,nf as n,Kh as o,Yh as p,$h as q,Gh as r,of as s,vf as t,ms as u,wf as v,zl as w,Xh as x,Jh as y,lf as z};
