import{ao as s,ap as r,iG as n}from"./index-a2fbd71b.js";const o=(e,t)=>s(`${r}/crs/ticket/queryByPnr`,{headers:{gid:t}}).post({pnrNo:e}).json(),c=(e,t,i)=>s(`${r}/crs/ticket?ticketNo=${e}${i?`&type=${i}`:""}`,{headers:{gid:t}}).get().json(),u=(e,t)=>s(`${r}/crs/ticket/queryTicketDetail`,{headers:{gid:t}}).post(e).json(),p=(e,t)=>s(`${r}/crs/ticket/queryTicketDetail`,{headers:{gid:t}},{ignoreError:!0}).post(e).json(),d=(e,t)=>s(`${r}/crs/ticket/queryTicketDigestsByCert`,{headers:{gid:t}}).post(e).json(),k=(e,t)=>s(`${r}/crs/ticket/getTicketDigestsByName`,{headers:{gid:t}}).post(e).json(),y=(e,t)=>s(`${r}/crs/ticket/invalid`,{headers:{gid:t}}).post(e).json(),h=e=>s(`${r}/crs/passenger/queryAllPassengersByTktNumber`).post(e).json(),A=(e,t,i)=>s(`${r}/crs/office/queryOfficeInformation`,{headers:{gid:t}},{ignoreError:i??!1}).post(e).json(),g=(e,t)=>s(`${r}/crs/etTicket/getTol`,{headers:{gid:t}}).post(e).json(),l=(e,t)=>s(`${r}/crs/ticket/queryTicketByDetr`,{headers:{gid:t}},{ignoreError:!0,originalValue:!0}).post(e).json(),T=(e,t)=>s(`${r}/crs/ticket/queryTicketByRtkt/${e}`,{headers:{gid:t}},{ignoreError:!0,originalValue:!0}).get().json(),f=(e,t)=>s(`${r}/crs/ticket/queryTicketByRtkt/${e}`,{headers:{gid:t}}).get().json(),$=(e,t)=>s(`${r}/crs/ticket/tssChangeTicketStatus`,{headers:{gid:t}}).post(e).json(),j=(e,t)=>s(`${r}/crs/ticket/tssChangeTicketStatusByPnr`,{headers:{gid:t}}).post(e).json(),R=(e,t)=>s(`${r}/crs/ticket/etrfChangeTicketStatus`,{headers:{gid:t}}).post(e).json(),q=(e,t)=>s(`${r}/crs/bopRefund/dptr`,{headers:{gid:t}}).post(e).json(),B=(e,t)=>s(`${r}/crs/cccf/queryCRSCreditCardInfo`,{headers:{gid:t}}).post(e).json(),m=(e,t)=>s(`${r}/crs/ticket/findRefundTicketAndFee`,{headers:{gid:t}},{originalValue:!0}).post(e).json(),C=(e,t)=>s(`${r}/apiRefundTicket/batchRefund`,{headers:{gid:t}}).post(e).json(),b=(e,t)=>s(`${r}/apiRefundTicket/batchAutoRefund`,{headers:{gid:t}}).post(e).json(),Q=(e,t)=>s(`${r}/apiRefundTicket/batchQueryTrfdZ`,{headers:{gid:t}}).post(e).json(),S=(e,t)=>s(`${r}/apiRefundTicket/deleteRefundForm`,{headers:{gid:t}}).post(e).json(),V=(e,t)=>s(`${r}/crs/ticket/queryRefundForm`,{headers:{gid:t}},{originalValue:!0}).post(e).json(),F=(e,t)=>s(`${r}/apiRefundTicket/modifyRefundForm`,{headers:{gid:t}},{originalValue:!0}).post(e).json(),P=(e,t)=>s(`${r}/apiRefundTicket/ticket/refund/supplementary`,{headers:{gid:t}},{originalValue:!0}).post(e).json(),D=(e,t)=>s(`${r}/crs/ticketAuth/airTicketDisplayAuth`,{headers:{gid:t}},{originalValue:!0}).post(e).json(),E=(e,t)=>s(`${r}/crs/ticketAuth/airTicketRemoveAuth`,{headers:{gid:t}},{originalValue:!0}).post(e).json(),N=(e,t)=>s(`${r}/crs/ticketAuth/airTicketAddAuth`,{headers:{gid:t}},{originalValue:!0}).post(e).json(),v=(e,t)=>s(`${r}/crs/ticket/pullControl`,{headers:{gid:t}}).post(e).json(),w=e=>s(`${n}/crs/passenger/queryAllPassengersByPnrNo`).post(e).json(),I=(e,t)=>s(`${r}/apiRefundTicket/batchFindRefundFeeZ`,{headers:{gid:t}}).post(e).json(),_=(e,t)=>s(`${r}/crs/passenger/queryTicketByInvalid`,{headers:{gid:t}}).post(e).json(),O=(e,t)=>s(`${r}/crs/etTicket/getCRSStockTQTD`,{headers:{gid:t}}).post(e).json(),z=(e,t)=>s(`${r}/crs/ticketControl/getTicketPooll`,{headers:{gid:t}}).post(e).json(),M=(e,t)=>s(`${r}/crs/ticket/queryTicketManagementOrganization/${e}`,{headers:{gid:t}},{ignoreError:!0,originalValue:!0}).get().json(),Z=e=>s(`${r}/crs/business/queryTicketWindowSwitch`,{headers:{gid:e}},{ignoreError:!0,originalValue:!0}).get().json();export{q as A,B,p as C,c as D,S as E,F,A as G,z as H,g as I,O as J,l as K,u as a,o as b,d as c,k as d,b as e,f,Q as g,V as h,h as i,_ as j,T as k,D as l,E as m,N as n,$ as o,m as p,Z as q,y as r,j as s,R as t,v as u,M as v,P as w,w as x,C as y,I as z};
