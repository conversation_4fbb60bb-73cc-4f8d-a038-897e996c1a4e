import"./index-a2fbd71b.js";const p=n=>{if(!n||n.length!==7)return n;const e=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],c=n.slice(5,7),s=n.slice(2,5),t=n.slice(0,2),o=e.findIndex(m=>m.toLocaleUpperCase()===s.toLocaleUpperCase())+1;if(o===0)throw new Error("传入时间不符合规则，月份获取失败");const r=o<10?`0${o}`:o;return`20${c}/${r}/${t}`},i=n=>{if(!n||n.length!==7)return n;const e=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],c=n.slice(5,7),s=n.slice(2,5),t=n.slice(0,2),o=e.findIndex(m=>m.toLocaleUpperCase()===s.toLocaleUpperCase())+1;if(o===0)throw new Error("传入时间不符合规则，月份获取失败");const r=o<10?`0${o}`:o;return`${c}/${r}/${t}`};export{p as a,i as g};
