import{L as D,_ as X,q as P,O as Z,v as G,m as ee,r as c,w,e6 as te,u as A,cY as K,x as k,y as W,z as I,a5 as ae,P as Y,D as H,A as m,C as $,a6 as le,T as se,e8 as oe,B as J,G as j,ai as re,M as ne,e7 as E,a3 as F,ac as U,el as ie,aF as V,W as ce,b0 as ue,o as ve,dN as fe,F as me,b9 as de,J as pe,dF as he,K as be}from"./index-a2fbd71b.js";const g=4,ye={vertical:{offset:"offsetHeight",scroll:"scrollTop",scrollSize:"scrollHeight",size:"height",key:"vertical",axis:"Y",client:"clientY",direction:"top"},horizontal:{offset:"offsetWidth",scroll:"scrollLeft",scrollSize:"scrollWidth",size:"width",key:"horizontal",axis:"X",client:"clientX",direction:"left"}},ge=({move:d,size:u,bar:r})=>({[r.size]:u,transform:`translate${r.axis}(${d}%)`}),Q=Symbol("scrollbarContextKey"),we=D({vertical:Boolean,size:String,move:Number,ratio:{type:Number,required:!0},always:Boolean}),Se="Thumb",ze=P({__name:"thumb",props:we,setup(d){const u=d,r=Z(Q),l=G("scrollbar");r||ee(Se,"can not inject scrollbar context");const i=c(),v=c(),o=c({}),f=c(!1);let a=!1,h=!1,b=oe?document.onselectstart:null;const t=w(()=>ye[u.vertical?"vertical":"horizontal"]),S=w(()=>ge({size:u.size,move:u.move,bar:t.value})),z=w(()=>i.value[t.value.offset]**2/r.wrapElement[t.value.scrollSize]/u.ratio/v.value[t.value.offset]),T=s=>{var e;if(s.stopPropagation(),s.ctrlKey||[1,2].includes(s.button))return;(e=window.getSelection())==null||e.removeAllRanges(),M(s);const n=s.currentTarget;n&&(o.value[t.value.axis]=n[t.value.offset]-(s[t.value.client]-n.getBoundingClientRect()[t.value.direction]))},R=s=>{if(!v.value||!i.value||!r.wrapElement)return;const e=Math.abs(s.target.getBoundingClientRect()[t.value.direction]-s[t.value.client]),n=v.value[t.value.offset]/2,p=(e-n)*100*z.value/i.value[t.value.offset];r.wrapElement[t.value.scroll]=p*r.wrapElement[t.value.scrollSize]/100},M=s=>{s.stopImmediatePropagation(),a=!0,document.addEventListener("mousemove",C),document.addEventListener("mouseup",y),b=document.onselectstart,document.onselectstart=()=>!1},C=s=>{if(!i.value||!v.value||a===!1)return;const e=o.value[t.value.axis];if(!e)return;const n=(i.value.getBoundingClientRect()[t.value.direction]-s[t.value.client])*-1,p=v.value[t.value.offset]-e,_=(n-p)*100*z.value/i.value[t.value.offset];r.wrapElement[t.value.scroll]=_*r.wrapElement[t.value.scrollSize]/100},y=()=>{a=!1,o.value[t.value.axis]=0,document.removeEventListener("mousemove",C),document.removeEventListener("mouseup",y),N(),h&&(f.value=!1)},O=()=>{h=!1,f.value=!!u.size},x=()=>{h=!0,f.value=a};te(()=>{N(),document.removeEventListener("mouseup",y)});const N=()=>{document.onselectstart!==b&&(document.onselectstart=b)};return A(K(r,"scrollbarElement"),"mousemove",O),A(K(r,"scrollbarElement"),"mouseleave",x),(s,e)=>(k(),W(se,{name:m(l).b("fade"),persisted:""},{default:I(()=>[ae(Y("div",{ref_key:"instance",ref:i,class:H([m(l).e("bar"),m(l).is(m(t).key)]),onMousedown:R},[Y("div",{ref_key:"thumb",ref:v,class:H(m(l).e("thumb")),style:$(m(S)),onMousedown:T},null,38)],34),[[le,s.always||f.value]])]),_:1},8,["name"]))}});var q=X(ze,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/scrollbar/src/thumb.vue"]]);const _e=D({always:{type:Boolean,default:!0},width:String,height:String,ratioX:{type:Number,default:1},ratioY:{type:Number,default:1}}),Ee=P({__name:"bar",props:_e,setup(d,{expose:u}){const r=d,l=c(0),i=c(0);return u({handleScroll:o=>{if(o){const f=o.offsetHeight-g,a=o.offsetWidth-g;i.value=o.scrollTop*100/f*r.ratioY,l.value=o.scrollLeft*100/a*r.ratioX}}}),(o,f)=>(k(),J(re,null,[j(q,{move:l.value,ratio:o.ratioX,size:o.width,always:o.always},null,8,["move","ratio","size","always"]),j(q,{move:i.value,ratio:o.ratioY,size:o.height,vertical:"",always:o.always},null,8,["move","ratio","size","always"])],64))}});var ke=X(Ee,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/scrollbar/src/bar.vue"]]);const He=D({height:{type:[String,Number],default:""},maxHeight:{type:[String,Number],default:""},native:{type:Boolean,default:!1},wrapStyle:{type:ne([String,Object,Array]),default:""},wrapClass:{type:[String,Array],default:""},viewClass:{type:[String,Array],default:""},viewStyle:{type:[String,Array,Object],default:""},noresize:Boolean,tag:{type:String,default:"div"},always:Boolean,minSize:{type:Number,default:20},id:String,role:String,ariaLabel:String,ariaOrientation:{type:String,values:["horizontal","vertical"]}}),Te={scroll:({scrollTop:d,scrollLeft:u})=>[d,u].every(E)},Ce="ElScrollbar",Ne=P({name:Ce}),Be=P({...Ne,props:He,emits:Te,setup(d,{expose:u,emit:r}){const l=d,i=G("scrollbar");let v,o;const f=c(),a=c(),h=c(),b=c("0"),t=c("0"),S=c(),z=c(1),T=c(1),R=w(()=>{const e={};return l.height&&(e.height=F(l.height)),l.maxHeight&&(e.maxHeight=F(l.maxHeight)),[l.wrapStyle,e]}),M=w(()=>[l.wrapClass,i.e("wrap"),{[i.em("wrap","hidden-default")]:!l.native}]),C=w(()=>[i.e("view"),l.viewClass]),y=()=>{var e;a.value&&((e=S.value)==null||e.handleScroll(a.value),r("scroll",{scrollTop:a.value.scrollTop,scrollLeft:a.value.scrollLeft}))};function O(e,n){he(e)?a.value.scrollTo(e):E(e)&&E(n)&&a.value.scrollTo(e,n)}const x=e=>{E(e)&&(a.value.scrollTop=e)},N=e=>{E(e)&&(a.value.scrollLeft=e)},s=()=>{if(!a.value)return;const e=a.value.offsetHeight-g,n=a.value.offsetWidth-g,p=e**2/a.value.scrollHeight,_=n**2/a.value.scrollWidth,B=Math.max(p,l.minSize),L=Math.max(_,l.minSize);z.value=p/(e-p)/(B/(e-B)),T.value=_/(n-_)/(L/(n-L)),t.value=B+g<e?`${B}px`:"",b.value=L+g<n?`${L}px`:""};return U(()=>l.noresize,e=>{e?(v==null||v(),o==null||o()):({stop:v}=ie(h,s),o=A("resize",s))},{immediate:!0}),U(()=>[l.maxHeight,l.height],()=>{l.native||V(()=>{var e;s(),a.value&&((e=S.value)==null||e.handleScroll(a.value))})}),ce(Q,ue({scrollbarElement:f,wrapElement:a})),ve(()=>{l.native||V(()=>{s()})}),fe(()=>s()),u({wrapRef:a,update:s,scrollTo:O,setScrollTop:x,setScrollLeft:N,handleScroll:y}),(e,n)=>(k(),J("div",{ref_key:"scrollbarRef",ref:f,class:H(m(i).b())},[Y("div",{ref_key:"wrapRef",ref:a,class:H(m(M)),style:$(m(R)),onScroll:y},[(k(),W(de(e.tag),{id:e.id,ref_key:"resizeRef",ref:h,class:H(m(C)),style:$(e.viewStyle),role:e.role,"aria-label":e.ariaLabel,"aria-orientation":e.ariaOrientation},{default:I(()=>[me(e.$slots,"default")]),_:3},8,["id","class","style","role","aria-label","aria-orientation"]))],38),e.native?pe("v-if",!0):(k(),W(ke,{key:0,ref_key:"barRef",ref:S,height:t.value,width:b.value,always:e.always,"ratio-x":T.value,"ratio-y":z.value},null,8,["height","width","always","ratio-x","ratio-y"]))],2))}});var Le=X(Be,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/scrollbar/src/scrollbar.vue"]]);const Re=be(Le);export{ye as B,Re as E};
