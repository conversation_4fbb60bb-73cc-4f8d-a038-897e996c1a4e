import{bS as a,bT as i,ao as c,dl as p}from"./index-a2fbd71b.js";import{d as g,s as l}from"./browser-6cfa1fde.js";const m=()=>a.get(`${i}/airportSelector/airports`),y=()=>a.get(`${i}/airportSelector/airportAll`),h=()=>a.get(`${i}/quickInstruction/searchTip`),f=()=>c(`${p}/commonConfiguration/hotCities`).get().json(),u=r=>{const s="=".repeat((4-r.length%4)%4),e=(r+s).replace(/-/g,"+").replace(/_/g,"/"),t=window.atob(e),n=new Uint8Array(t.length);for(let o=0;o<t.length;++o)n[o]=t.charCodeAt(o);return n},S=r=>{const s=u(r),e=g(s),t=l(e);return JSON.stringify(JSON.parse(t))};export{h as a,S as d,m as i,f as q,y as s};
