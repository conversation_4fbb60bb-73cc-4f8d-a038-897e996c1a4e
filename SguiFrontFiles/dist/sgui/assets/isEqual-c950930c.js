import{e as F,h as J,iA as x,il as Q,iB as D,iC as M,i as S,hd as $,ha as B,it as y,hb as X,a as G,gD as Y,gE as Z,iD as W,ik as b,hc as m}from"./index-a2fbd71b.js";function z(n,e){for(var f=-1,a=n==null?0:n.length;++f<a;)if(e(n[f],f,n))return!0;return!1}var c=1,h=2;function H(n,e,f,a,i,r){var s=f&c,g=n.length,u=e.length;if(g!=u&&!(s&&u>g))return!1;var v=r.get(n),T=r.get(e);if(v&&T)return v==e&&T==n;var A=-1,l=!0,O=f&h?new F:void 0;for(r.set(n,e),r.set(e,n);++A<g;){var t=n[A],d=e[A];if(a)var P=s?a(d,t,A,e,n,r):a(t,d,A,n,e,r);if(P!==void 0){if(P)continue;l=!1;break}if(O){if(!z(e,function(w,L){if(!J(O,L)&&(t===w||i(t,w,f,a,r)))return O.push(L)})){l=!1;break}}else if(!(t===d||i(t,d,f,a,r))){l=!1;break}}return r.delete(n),r.delete(e),l}function V(n){var e=-1,f=Array(n.size);return n.forEach(function(a,i){f[++e]=[i,a]}),f}function j(n){var e=-1,f=Array(n.size);return n.forEach(function(a){f[++e]=a}),f}var o=1,k=2,nn="[object Boolean]",en="[object Date]",rn="[object Error]",an="[object Map]",fn="[object Number]",sn="[object RegExp]",ln="[object Set]",un="[object String]",gn="[object Symbol]",vn="[object ArrayBuffer]",An="[object DataView]",C=x?x.prototype:void 0,R=C?C.valueOf:void 0;function tn(n,e,f,a,i,r,s){switch(f){case An:if(n.byteLength!=e.byteLength||n.byteOffset!=e.byteOffset)return!1;n=n.buffer,e=e.buffer;case vn:return!(n.byteLength!=e.byteLength||!r(new D(n),new D(e)));case nn:case en:case fn:return Q(+n,+e);case rn:return n.name==e.name&&n.message==e.message;case sn:case un:return n==e+"";case an:var g=V;case ln:var u=a&o;if(g||(g=j),n.size!=e.size&&!u)return!1;var v=s.get(n);if(v)return v==e;a|=k,s.set(n,e);var T=H(g(n),g(e),a,i,r,s);return s.delete(n),T;case gn:if(R)return R.call(n)==R.call(e)}return!1}var dn=1,Tn=Object.prototype,On=Tn.hasOwnProperty;function Pn(n,e,f,a,i,r){var s=f&dn,g=M(n),u=g.length,v=M(e),T=v.length;if(u!=T&&!s)return!1;for(var A=u;A--;){var l=g[A];if(!(s?l in e:On.call(e,l)))return!1}var O=r.get(n),t=r.get(e);if(O&&t)return O==e&&t==n;var d=!0;r.set(n,e),r.set(e,n);for(var P=s;++A<u;){l=g[A];var w=n[l],L=e[l];if(a)var I=s?a(L,w,l,e,n,r):a(w,L,l,n,e,r);if(!(I===void 0?w===L||i(w,L,f,a,r):I)){d=!1;break}P||(P=l=="constructor")}if(d&&!P){var _=n.constructor,p=e.constructor;_!=p&&"constructor"in n&&"constructor"in e&&!(typeof _=="function"&&_ instanceof _&&typeof p=="function"&&p instanceof p)&&(d=!1)}return r.delete(n),r.delete(e),d}var wn=1,N="[object Arguments]",U="[object Array]",E="[object Object]",Ln=Object.prototype,q=Ln.hasOwnProperty;function _n(n,e,f,a,i,r){var s=S(n),g=S(e),u=s?U:$(n),v=g?U:$(e);u=u==N?E:u,v=v==N?E:v;var T=u==E,A=v==E,l=u==v;if(l&&B(n)){if(!B(e))return!1;s=!0,T=!1}if(l&&!T)return r||(r=new y),s||X(n)?H(n,e,f,a,i,r):tn(n,e,u,f,a,i,r);if(!(f&wn)){var O=T&&q.call(n,"__wrapped__"),t=A&&q.call(e,"__wrapped__");if(O||t){var d=O?n.value():n,P=t?e.value():e;return r||(r=new y),i(d,P,f,a,r)}}return l?(r||(r=new y),Pn(n,e,f,a,i,r)):!1}function K(n,e,f,a,i){return n===e?!0:n==null||e==null||!G(n)&&!G(e)?n!==n&&e!==e:_n(n,e,f,a,K,i)}function pn(n,e){return n!=null&&e in Object(n)}function En(n,e,f){e=Y(e,n);for(var a=-1,i=e.length,r=!1;++a<i;){var s=Z(e[a]);if(!(r=n!=null&&f(n,s)))break;n=n[s]}return r||++a!=i?r:(i=n==null?0:n.length,!!i&&W(i)&&b(s,i)&&(S(n)||m(n)))}function Rn(n,e){return n!=null&&En(n,e,pn)}function Sn(n,e){return K(n,e)}export{K as b,Rn as h,Sn as i,j as s};
