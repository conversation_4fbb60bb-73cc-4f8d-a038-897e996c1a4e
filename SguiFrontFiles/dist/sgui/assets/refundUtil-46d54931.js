import{c3 as s}from"./index-a2fbd71b.js";const e=s.global.t,u=[{label:e("app.agentTicketRefund.pay.cash"),value:"CASH"},{label:e("app.agentTicketRefund.pay.tc"),value:"TC"},{label:e("app.agentTicketRefund.pay.check"),value:"CHECK"},{label:e("app.agentTicketRefund.pay.gr"),value:"GR"},{label:e("app.agentTicketRefund.pay.ef"),value:"EF"}],g=[{label:e("app.agentTicketRefund.passenger.adt"),value:"ADT"},{label:e("app.agentTicketRefund.passenger.chd"),value:"CHD"},{label:e("app.agentTicketRefund.passenger.chd"),value:"CNN"},{label:e("app.agentTicketRefund.passenger.yth"),value:"YTH"},{label:e("app.agentTicketRefund.passenger.inf"),value:"INF"}],T="TYN202",i="UP6225819300614137",r=(a,n)=>{const t=[];return a==null||a.split("-").forEach((p,l)=>{t[l]=n.filter(c=>p===c.tktTag)}),t},R=a=>`${[e("app.agentTicketRefund.one"),e("app.agentTicketRefund.two"),e("app.agentTicketRefund.three"),e("app.agentTicketRefund.four")][a]}`,d=a=>{var n,t;return((t=(n=u.filter(p=>p.value===a))==null?void 0:n[0])==null?void 0:t.label)??a},o=a=>{var n,t;return((t=(n=g.filter(p=>p.value===a))==null?void 0:n[0])==null?void 0:t.label)??a};export{T as D,i as a,o as b,R as f,r as g,d as h,u as p};
