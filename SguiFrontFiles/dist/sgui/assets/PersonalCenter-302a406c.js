import{bt as ie,b0 as ue,ab as le,r as O,a9 as pe,w as X,cI as je,cJ as $e,au as x,bI as me,bz as Se,bA as ge,bC as he,cK as qe,q as ce,ah as j,al as te,H as Ae,aH as Qe,aE as Ge,cL as ke,a4 as ve,x as h,B as T,P as t,a5 as L,a6 as Pe,G as n,z as u,J as I,ai as W,aj as se,D as P,Q as i,C as We,ak as Q,y as D,am as Ue,an as De,a8 as Re,bf as Be,cM as Ye,cN as ze,cO as Ke,cP as He,o as _e,cQ as we,ac as be,aG as re,at as Je,cR as Ze,aX as Le,bB as ea,bD as aa,R as ta,cS as Te,bH as Ee,aa as ye,bG as sa,bM as na,a$ as la,A as a,b3 as z,cT as oa,cU as Ne,cV as ia,aY as Ie,c4 as ra,cW as ua,ae as Fe,b1 as pa,aZ as ca,b9 as da}from"./index-a2fbd71b.js";import{U as fa}from"./passwordValid-45ecf3f8.js";import{g as fe}from"./encrypt-facb275d.js";import{u as ma}from"./usePersonalization-e583570d.js";import{g as ga}from"./config-6a4e2d9f.js";import{a as ne,E as q}from"./index-96be5bee.js";import{_ as ha}from"./Personalization.vue_vue_type_script_setup_true_lang-c9263e2a.js";import{_ as de}from"./_plugin-vue_export-helper-c27b6911.js";import{i as va,d as ba}from"./airline-cde76858.js";import{b as ya}from"./common-859eeea5.js";import{E as v}from"./index-a5188d18.js";import{E as A}from"./index-4cb0667b.js";import{P as Ve}from"./PrintNoSelect-2c6cab30.js";import{E as _a}from"./index-ac58700d.js";import{E as Ca}from"./index-e149ca58.js";import{E as $a}from"./index-61b49c38.js";import{E as ka,a as wa}from"./index-2484b7a8.js";import{a as xe,E as Oe}from"./index-8ff7f67e.js";import"./castArray-2ae6cabb.js";import"./browser-6cfa1fde.js";import"./ticketOperationApi-4e3e854b.js";import"./index-68054860.js";import"./index-fe3402a7.js";import"./isUndefined-aa0326a0.js";import"./index-350461d4.js";import"./strings-cc725bd5.js";import"./isEqual-c950930c.js";import"./index-4e9a4449.js";import"./flatten-b73d2287.js";import"./index-a42e5d8f.js";function Ta(){const s=document.querySelector("#msg-two-loading")?"#msg-two-loading":"#msg-loading";ie.service({target:"#aside-loading",spinner:!0,customClass:"customLoading"}),ie.service({target:"#tabs-loading",spinner:!0,customClass:"customLoading"}),ie.service({target:s,spinner:!0,customClass:"customLoading"}),ie.service({target:"#server-loading",spinner:!0,customClass:"customLoading"})}const K=ue({oldcertificate:"",certificate:"",recertificate:""}),Ea=()=>({securityTitle:()=>{const y=ue({btn:["简体中文","繁體中文","English"],select:x()==="en"?2:0,language:[0,1,2]});return{title:y,clickActive:o=>{y.select=o}}},securityMessage:()=>{const y=ue({securityInfo:!0,bgColor:"#EBF2FF"});return{showOrClose:y,iconClose:()=>{y.bgColor="#ffffff",y.securityInfo=!1}}}}),Na=()=>{const{t:s}=le(),p=O([]),y=[s("app.personal.oauthTokenRule1"),s("app.personal.oauthTokenRule2"),s("app.personal.oauthTokenRule3"),s("app.personal.oauthTokenRule4"),s("app.personal.oauthTokenRule5")],{validLength:E,validVaried:o,validContinuity:N,validKeyboard:d,validRepeat:$}=fa(),_=k=>{const l=[];return E(k)&&l.push(y[0]),o(k)&&l.push(y[1]),N(k)&&l.push(y[2]),d(k)&&l.push(y[3]),$(k)&&l.push(y[4]),l},r=(k,l,c)=>{const{certificate:f,oldcertificate:b}=K;p.value=_(K.certificate),p.value.length!==0?c(new Error("  ")):f===b&&c(s("app.personal.confirmPasswordOldTip")),c()};return{formInline:K,rules:{oldcertificate:[{required:!0,message:" ",trigger:"blur"}],certificate:[{required:!0,validator:r,trigger:"blur"},{required:!0,validator:r,trigger:"change"}],recertificate:[{required:!0,message:" ",trigger:"blur"},{required:!0,validator:(k,l,c)=>{const{certificate:f}=K,{recertificate:b}=K;f===""?c(new Error(" ")):f!==b&&c(s("app.personal.confirmPasswordTip")),c()},trigger:"change"}]},validList:p,validArray:y}},Ia=()=>{const{t:s}=le(),p=pe(),y=X(()=>p.state.user.userName),E=O(),o=async $=>{try{await me($),await Se()}finally{const _=ge();localStorage.clear(),he(_)}},N=async($,_)=>{try{await $e($,_);const r=await qe();if([s("app.tips.networkErrCode")].includes((r==null?void 0:r.code)??""))await N($,_);else{const V=ge();localStorage.clear(),he(V)}}finally{}};return{userName:y,formRef:E,onSubmit:()=>{E.value.validate(async $=>{if($){const{oldcertificate:_,certificate:r,recertificate:F}=K,V={oldcertificate:fe(_),certificate:fe(r),recertificate:fe(F)},k=await je(V),l=["SGUI-0142-10","SGUI-0143-11","ARCHETYPE-0142-15","ARCHETYPE-0143-16","SGUI-0150-19"],c=["SGUI-0141-19","ARCHETYPE-0141-14"],f=[s("app.tips.networkErrCode")];k.code==="200"?await o(s("app.tips.oauthTokenUpdatedSuccessfully")):l.includes(k.code)?await o(s("app.tips.notificationSendingFailure")):c.includes(k.code)?await o(s("app.tips.notificationConfigurationIncorrect")):f.includes(k.code)?await N(s("app.tips.networkErrCode"),s("app.tips.notificationNetworkErr")):$e(k.code,k.msg)}})}}},Fa=ce({name:"UpdatePassword",components:{ElForm:ne,ElFormItem:q,ElInput:j,ElButton:te,ElIcon:Ae,Close:Qe,Personalization:ha},setup(){const s=O(!1),{securityTitle:p,securityMessage:y}=Ea(),{title:E,clickActive:o}=p(),{showOrClose:N,iconClose:d}=y(),{formInline:$,rules:_,validArray:r,validList:F}=Na(),{userName:V,formRef:k,onSubmit:l}=Ia(),{personalizationRules:c}=ma(ga.personalCenter);return Ge(()=>{s.value=$.certificate.length===0&&F.value.length===0}),{showDefault:s,...ke(E),...ke(N),iconClose:d,clickActive:o,validArray:r,formInline:$,rules:_,validList:F,userName:V,formRef:k,onSubmit:l,getLocale:x,personalizationRules:c}}});const G=s=>(Ue("data-v-abfff5fe"),s=s(),De(),s),Va={key:0,class:"update-password"},Sa={key:0,class:"security-title"},Aa={key:1,class:"security-title"},Pa={key:2,class:"security-title"},Ua=["onClick"],Da={class:"security-content"},Ra={key:0},Ba=G(()=>t("span",null,"由于近期撞库攻击日益猖獗，敬请各位用户在本系统中",-1)),La=G(()=>t("span",{style:{color:"red"}},"不要使用与其他网站相同或相近的用户名口令",-1)),xa=G(()=>t("span",null,"。如因您的故意或疏忽过失，导致您在本系统的用户名口令泄露，对业务造成的影响由您自行承担，对本系统带来的安全危害，",-1)),Oa=G(()=>t("span",null,"系统所有权人中国民航信息网络股份有限公司将保留进一步追究法律责任的权利。——中国民航信息网络股份有限公司",-1)),Ma=[Ba,La,xa,Oa],Xa={key:1},ja=G(()=>t("span",null,"由於近期撞庫攻擊日益猖獗，敬請各位用戶在本系統中",-1)),qa=G(()=>t("span",{style:{color:"red"}},"不要使用與其他網站相同或相近的用戶名口令",-1)),Qa=G(()=>t("span",null,"。如因您的故意或疏忽過失，導致您在本系統的用戶名口令泄露，對業務造成的影響由您自行承擔，對",-1)),Ga=G(()=>t("span",null,"本系統帶來的安全危害，系統所有權人中國民航信息網絡股份有限公司將保留進壹步追究法律責任的權利。——中國民航信息網絡股份有限公司",-1)),Wa=[ja,qa,Qa,Ga],Ya={key:2},za={class:"message-ul"},Ka=G(()=>t("span",null,null,-1));function Ha(s,p,y,E,o,N){const d=ve("close"),$=Ae,_=j,r=q,F=te,V=ne,k=ve("Personalization");return h(),T("div",null,[s.personalizationRules?(h(),D(k,{key:1,"rule-info":s.personalizationRules,"user-name":s.userName},null,8,["rule-info","user-name"])):(h(),T("div",Va,[t("div",{class:"security-info",style:We({"background-color":s.bgColor})},[L(t("div",null,[n($,{class:"security-close",onClick:s.iconClose},{default:u(()=>[n(d)]),_:1},8,["onClick"]),t("p",null,[s.language[0]===s.select?(h(),T("span",Sa,"安全声明")):I("",!0),s.language[1]===s.select?(h(),T("span",Aa,"安全聲明")):I("",!0),s.language[2]===s.select?(h(),T("span",Pa,"Security Disclaimer")):I("",!0),(h(!0),T(W,null,se(s.btn,(l,c)=>(h(),T("span",{key:l,class:P(["title-btn",{"btn-active":c===s.select}]),onClick:f=>s.clickActive(c)},i(l),11,Ua))),128))]),t("div",Da,[s.language[0]===s.select?(h(),T("p",Ra,Ma)):I("",!0),s.language[1]===s.select?(h(),T("p",Xa,Wa)):I("",!0),s.language[2]===s.select?(h(),T("p",Ya," With the ever-increasing amount of Social Engineering Attack, system users should not reuse the same or similar password of this site on any other sites. In case of any leakage of the username and/or password, no matter intentionally of un-intentionally, you will be liable for the business impact caused. The system owner, Travelsky Limited Inc., will reserve the right to take necessary legal actions against the relevant system users. ——Travelsky Limited Inc ")):I("",!0)])],512),[[Pe,s.securityInfo]])],4),t("div",{class:P(["form",s.getLocale()==="en"?"en-form":"cn-form"])},[n(V,{ref:"formRef","hide-required-asterisk":!1,rules:s.rules,inline:!1,model:s.formInline,"label-width":"95px","label-position":"left"},{default:u(()=>[n(r,{label:s.$t("app.personal.oldPassword"),prop:"oldcertificate"},{default:u(()=>[n(_,{modelValue:s.formInline.oldcertificate,"onUpdate:modelValue":p[0]||(p[0]=l=>s.formInline.oldcertificate=l),type:"password",placeholder:s.$t("app.check.inputOldPwd"),autocomplete:"off","show-password":""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),n(r,{label:s.$t("app.personal.newPassword"),prop:"certificate",class:"form-item"},{default:u(()=>[n(_,{modelValue:s.formInline.certificate,"onUpdate:modelValue":p[1]||(p[1]=l=>s.formInline.certificate=l),type:"password","show-message":!1,placeholder:s.$t("app.check.inputNewPwd"),class:"form-item",autocomplete:"off","show-password":""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),n(r,{label:s.$t("app.personal.confirmPassword"),prop:"recertificate",class:"form-item confirm"},{default:u(()=>[n(_,{modelValue:s.formInline.recertificate,"onUpdate:modelValue":p[2]||(p[2]=l=>s.formInline.recertificate=l),type:"password",placeholder:s.$t("app.check.reEnterNewPwd"),class:"form-item",autocomplete:"off","show-password":""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),n(r,{class:"passWordButton"},{default:u(()=>[n(F,{type:"primary",class:"form-item btn-submit","data-gid":"081W0102",onClick:s.onSubmit},{default:u(()=>[Q(i(s.$t("app.button.confirmeTheChange")),1)]),_:1},8,["onClick"])]),_:1})]),_:1},8,["rules","model"])],2),t("div",{class:P(["valid",s.getLocale()==="en"?"en-valid":"cn-valid"])},[t("div",{class:P(["message",s.getLocale()==="en"?"en-message":"cn-message"])},[t("div",{class:P(["message-left",s.getLocale()==="en"?"en-message-left":"cn-message-left"])},null,2),t("div",za,[t("ul",null,[(h(!0),T(W,null,se(s.validArray,(l,c)=>(h(),T("li",{key:c,class:P({error:s.validList.includes(l),default:s.showDefault,"en-li":s.getLocale()==="en"})},[Ka,Q(" "+i(l),1)],2))),128))])])],2)],2)]))])}const Ja=de(Fa,[["render",Ha],["__scopeId","data-v-abfff5fe"]]),Za=()=>{const{t:s}=le(),p=pe(),y=Re(),E=O(),o=O();let N="";const d=X(()=>$("defaultSellingGuiIataNum","sellingGuiIataNum")),$=(e,g)=>{var w,M,oe;const C=((w=p.state.user)==null?void 0:w[e])??"";return C||(((oe=(((M=p.state.user)==null?void 0:M[g])??"").split(";"))==null?void 0:oe[0])??"")},_={0:s("app.personal.oauthToken"),1:s("app.personal.oauthTokenMessage"),4:s("app.personal.oauthTokenEmail"),5:s("app.personal.oauthTokenMessageEmail"),6:s("app.personal.oauthTokenWechat"),7:s("app.personal.oauthTokenWechatMessage"),8:s("app.personal.oauthTokenWechatEmail"),9:s("app.personal.oauthTokenWechatMessageEmail")},r=ue({roleNames:"",mgrAirline:"",office:"",userName:"",defaultRole:"",departmentName:"",locale:"",employeeId:"",employeeName:"",signature:"",certId:"",mobile:"",email:"",switchAirline:"",defaultOffice:"",defaultSystem:"",agentNo:"",agent:"",aviationAssociation:"",currency:"",airportCode:"",operator:!0,defaultRoleWithPid:!0,roleMap:{},flowLimit:"",overFlowed:"",unlockTimes:"",currentVersion:""}),F={switchAirline:[{required:!0,message:s("app.check.input2Word"),trigger:"blur"},{type:"string",pattern:Be,message:s("app.check.input2WordOrNumber"),trigger:"blur"}]},V={aviationAssociation:[{required:!0,message:s("app.check.inputIata"),trigger:"blur"}],agentNo:[{required:!0,message:s("app.check.enterOnly"),trigger:"blur"},{type:"string",pattern:Ye,message:s("app.check.agentRule"),trigger:"blur"}],defaultOffice:[{required:!0,message:s("app.check.officeRule"),trigger:"blur"},{type:"string",pattern:ze,message:s("app.check.officeRule"),trigger:"blur"}],currency:[{required:!0,message:s("app.check.upTo10SeperateBySemicolon"),trigger:"blur"},{type:"string",pattern:Ke,message:s("app.check.inputCorrectCurrency"),trigger:"blur"}],airportCode:[{required:!0,message:s("app.check.airportRule"),trigger:"blur"},{type:"string",pattern:He,message:s("app.check.airportRule"),trigger:"blur"}]},k=async e=>{if(r.userName=e.userName,r.defaultRole=e.defaultRole,x()==="en"){const g=await p.getters.userStrus,C=g==null?void 0:g.filter(w=>w.struName===e.departmentName);r.departmentName=C&&C.length>0?C[0].struEnName:e.departmentName}else r.departmentName=e.departmentName;r.locale=e.locale==="en"?s("app.personal.english"):s("app.personal.chinese"),r.employeeId=e.employeeId,r.employeeName=e.employeeName,r.signature=_[e.securityLevel],r.certId=e.certId||"-",r.mobile=e.mobile,r.email=e.email,r.switchAirline=e.switchAirline,r.defaultOffice=e.office,r.defaultSystem=e.defaultSystem,r.agent=e.agent,r.agentNo=e.agentNo,r.aviationAssociation=e.sellingGuiIataNum,r.currency=e.tssellingguicurrency,r.airportCode=e.tssellingguiairportcode,r.operator=e.operator,r.mgrAirline=e.mgrAirline,r.defaultRoleWithPid=e.defaultRoleWithPid,r.flowLimit=e.flowLimit?e.flowLimit:"-",r.overFlowed=e.overFlowed?s("app.personal.exceedFlowLimit"):s("app.personal.nonExceedFlowLimit"),r.unlockTimes=e.unlockTimes?e.unlockTimes:"-",r.expireTime="",r.epidNumber=""},l=()=>{const e=[re("div",{class:"info-tip-title text-[18px] text-gray-1"},s("app.domesticRepresentatives.unbindConfirm",{epidNumber:r.epidNumber})),re("div",{class:"info-tip-title text-[18px] mr-[10px] text-gray-1"},s("app.domesticRepresentatives.unbindConfirmTip"))];Je.confirm(re("div",{class:"unbind-title"},e),{icon:re("em",{class:"iconfont icon-info-circle-line text-brand-2 text-[32px]"}),customClass:"alert-message-common crs-btn-ui unbind-epid-message-box min-w-[500px]",confirmButtonText:s("app.domesticRepresentatives.unbind"),cancelButtonText:s("app.ticketStatus.cancelBtn"),closeOnClickModal:!1,showClose:!1}).then(async()=>{const g=y.query.authValue,C=await ya(g);await Ze(C,r.epidNumber??""),Le({type:"success",message:s("app.domesticRepresentatives.unbind")}),Se();const w=ge();ea(),he(w)})},c=async()=>{try{const g=(await aa()).data;if(g){const{expireTime:C,epidNumber:w}=g;r.expireTime=ta(C).format("YYYY-MM-DD"),r.epidNumber=w}}catch{}},f=async()=>{const e=await p.getters.user;if(await k(e),!(e!=null&&e.defaultOfficeInternational)&&(e!=null&&e.crsSystem)&&await c(),r.operator)try{await E.value.validate()}catch{}},b=async e=>{var Ce;const g=p.state.user.office,C=ye("currnetHasEtermInfoSessionession",""),w=JSON.parse(C.value?C.value:"{}");if(e===g)return;const M=((Ce=(e||"").split(";"))==null?void 0:Ce[0])??"",oe=p.state.user.defaultRole,Me=p.state.user.defaultUserGroup;if(M){const Xe={office:M,roleName:oe,userGroup:Me,currentSellingGuiIataNum:"",system:r.defaultSystem,etermId:(w==null?void 0:w.etermId)??"",etermPwd:(w==null?void 0:w.etermPwd)??"",etermServerAddress:(w==null?void 0:w.etermServerAddress)??""};await sa(Xe)}},S=async e=>{var w;const g=p.state.user.sellingGuiIataNum;if(e===g)return;const C=((w=(e||"").split(";"))==null?void 0:w[0])??"";await na({sellingGuiIataNum:C})},R=async()=>{if(N.trim()!==r.switchAirline.trim()){const e=await va(),g=new Date().getTime();await la("diLocalData",ba(e.data),g)}N=r.switchAirline.trim()},U=async()=>{const e={switchAirline:r.switchAirline,office:r.defaultOffice,agentNo:r.agentNo,sellingGuiIataNum:r.aviationAssociation,tssellingguicurrency:r.currency,tssellingguiairportcode:r.airportCode};await Promise.all([o.value.validate(),E.value.validate()]),await Te(e),await R(),await me(s("app.tips.success")),await b(r.defaultOffice),await S(r.aviationAssociation);const g=await Ee(),C=ye("needCallEtermInfoDialog","");g.data.manualInputEtermInfo&&(C.value="need"),p.dispatch("addUser",g.data)},Y=()=>{o.value.validate(async e=>{if(!e)return;await Te({switchAirline:r.switchAirline}),await R(),await me(s("app.tips.success"));const g=await Ee();p.dispatch("addUser",g.data)})},H=async()=>{!E.value&&!o.value||(r.defaultRoleWithPid?Y():await U())},J=X({get:()=>{var e;return(e=r.switchAirline)==null?void 0:e.toUpperCase()},set:e=>{r.switchAirline=e==null?void 0:e.toUpperCase()}}),Z=X({get:()=>{var e;return(e=r.agentNo)==null?void 0:e.toUpperCase()},set:e=>{r.agentNo=e==null?void 0:e.toUpperCase()}}),B=X({get:()=>{var e;return(e=r.currency)==null?void 0:e.toUpperCase()},set:e=>{r.currency=e==null?void 0:e.toUpperCase()}}),ee=X({get:()=>{var e;return(e=r.aviationAssociation)==null?void 0:e.toUpperCase()},set:e=>{r.aviationAssociation=e==null?void 0:e.toUpperCase()}}),ae=X({get:()=>{var e;return(e=r.airportCode)==null?void 0:e.toUpperCase()},set:e=>{r.airportCode=e==null?void 0:e.toUpperCase()}}),m=X({get:()=>{var e;return(e=r.defaultOffice)==null?void 0:e.toUpperCase()},set:e=>{r.defaultOffice=e==null?void 0:e.toUpperCase()}});return _e(()=>{r.currentVersion=`SGUI_V_${we.slice(0,we.lastIndexOf("."))}`,f().then()}),be(()=>p.getters.user,async(e,g)=>{const C=await g,w=await e;C.defaultRoleWithPid!==w.defaultRoleWithPid&&f()}),{form:r,rules:V,formRef:E,getCurrentUserInfo:f,onSave:H,switchAirline:J,agentNo:Z,currency:B,aviationAssociation:ee,airportCode:ae,defaultOffice:m,airlineFormRef:o,unbindEpidClick:l,airlineFormRules:F,defaultSellingGuiIataNum:d}},et=Za,at=s=>(Ue("data-v-7dcff83a"),s=s(),De(),s),tt={class:"base-info"},st={class:"base-info-title"},nt={class:"base-info-container"},lt={class:"base-info-value"},ot={class:"base-info-label"},it={class:"base-info-value"},rt={class:"base-info-label"},ut={class:"base-info-value"},pt={class:"base-info-value"},ct={class:"base-info-label"},dt={class:"base-info-value"},ft={class:"base-info-label"},mt={class:"base-info-value"},gt={class:"base-info-value"},ht={class:"base-info-value"},vt={class:"base-info-label"},bt={class:"base-info-value"},yt={class:"base-info-label"},_t={class:"base-info-value"},Ct={class:"base-info-container"},$t={class:"base-info-value"},kt={class:"base-info-label"},wt={class:"base-info-value"},Tt={class:"base-info-container"},Et={class:"base-info-value"},Nt={class:"base-info-label"},It={class:"base-info-value"},Ft={class:"base-info-container"},Vt={class:"base-info-label"},St={class:"base-info-value"},At={key:0,class:"base-info-container"},Pt={class:"base-info-label"},Ut={class:"base-info-value base-info-epid-value"},Dt={class:"base-info-label"},Rt={class:"base-info-value"},Bt={class:"base-info-container"},Lt={class:"base-info-label"},xt={class:"base-info-value"},Ot={key:0,class:"sell-setting"},Mt={class:"base-info-title"},Xt={class:"base-info-container"},jt={class:"base-info-label"},qt={class:"base-info-value"},Qt=at(()=>t("label",{class:"base-info-label"},"Office",-1)),Gt={class:"base-info-value"},Wt={class:"base-info-label base-info-labelw100"},Yt={class:"base-info-value showAgentTop"},zt={class:"base-info-label"},Kt={class:"base-info-value"},Ht={class:"base-info-label"},Jt={class:"base-info-value"},Zt={class:"save-btn"},es=ce({__name:"Information",setup(s){const{form:p,rules:y,formRef:E,onSave:o,switchAirline:N,agentNo:d,currency:$,aviationAssociation:_,airportCode:r,defaultOffice:F,airlineFormRef:V,airlineFormRules:k,unbindEpidClick:l,defaultSellingGuiIataNum:c}=et();return(f,b)=>(h(),T("div",tt,[t("div",null,[t("p",st,i(f.$t("app.personal.settingsProfile")),1),t("div",nt,[n(a(A),null,{default:u(()=>[n(a(v),{span:8},{default:u(()=>[t("label",{class:P(["base-info-label",a(x)()==="en"?"base-info-label-en":""])},i(f.$t("app.personal.userName")),3),t("span",lt,i(a(p).userName),1)]),_:1}),n(a(v),{span:8},{default:u(()=>[t("label",ot,i(f.$t("app.personal.organization")),1),t("span",it,i(a(p).departmentName),1)]),_:1}),n(a(v),{span:8},{default:u(()=>[t("label",rt,i(f.$t("app.personal.language")),1),t("span",ut,i(a(p).locale),1)]),_:1})]),_:1}),n(a(A),null,{default:u(()=>[n(a(v),{span:8},{default:u(()=>[t("label",{class:P(["base-info-label",a(x)()==="en"?"base-info-label-en":""])},i(f.$t("app.personal.employeeNumber")),3),t("span",pt,i(a(p).employeeId),1)]),_:1}),n(a(v),{span:8},{default:u(()=>[t("label",ct,i(f.$t("app.personal.name")),1),t("span",dt,i(a(p).employeeName),1)]),_:1}),a(p).agent?(h(),D(a(v),{key:0,span:8},{default:u(()=>[t("label",ft,i(f.$t("app.personal.agent")),1),t("span",mt,i(a(p).agent),1)]),_:1})):(h(),D(a(v),{key:1,span:8}))]),_:1}),n(a(A),null,{default:u(()=>[n(a(v),{span:8},{default:u(()=>[t("label",{class:P(["base-info-label",a(x)()==="en"?"base-info-label-en":""])},i(f.$t("app.personal.settlementNo")),3),t("span",gt,i(a(c)),1)]),_:1}),n(a(v),{span:8},{default:u(()=>[t("label",{class:P(["base-info-label",a(x)()==="en"?"base-info-label-en":""])},i(f.$t("app.personal.flowLimit")),3),t("span",ht,i(a(p).flowLimit),1)]),_:1}),n(a(v),{span:8},{default:u(()=>[t("label",vt,i(f.$t("app.personal.overFlowed")),1),t("span",bt,i(a(p).overFlowed),1)]),_:1})]),_:1}),n(a(A),null,{default:u(()=>[n(a(v),{span:8},{default:u(()=>[t("label",yt,i(f.$t("app.personal.unlockTimes")),1),t("span",_t,i(a(p).unlockTimes),1)]),_:1})]),_:1})]),t("div",Ct,[n(a(A),null,{default:u(()=>[n(a(v),{span:8},{default:u(()=>[t("label",{class:P(["base-info-label",a(x)()==="en"?"base-info-label-en":""])},i(f.$t("app.personal.certificationType")),3),t("span",$t,i(a(p).signature),1)]),_:1}),n(a(v),{span:8},{default:u(()=>[t("label",kt,i(f.$t("app.personal.certificateID")),1),t("span",wt,i(a(p).certId),1)]),_:1}),n(a(v),{span:8})]),_:1})]),t("div",Tt,[n(a(A),null,{default:u(()=>[n(a(v),{span:8},{default:u(()=>[t("label",{class:P(["base-info-label",a(x)()==="en"?"base-info-label-en":""])},i(f.$t("app.personal.cellphone")),3),t("span",Et,i(a(p).mobile),1)]),_:1}),n(a(v),{span:8},{default:u(()=>[t("label",Nt,i(f.$t("app.personal.email")),1),t("span",It,i(a(p).email),1)]),_:1}),n(a(v),{span:8})]),_:1})]),t("div",Ft,[n(a(A),null,{default:u(()=>[a(p).operator?(h(),D(a(ne),{key:1,ref_key:"airlineFormRef",ref:V,model:a(p),rules:a(k)},{default:u(()=>[n(a(q),{label:f.$t("app.personal.airline"),prop:"switchAirline",class:"airline"},{default:u(()=>[n(a(j),{modelValue:a(N),"onUpdate:modelValue":b[0]||(b[0]=S=>z(N)?N.value=S:null),class:"hasVal",placeholder:f.$t("app.check.input2Word")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1},8,["model","rules"])):(h(),D(a(v),{key:0,span:8},{default:u(()=>[t("label",Vt,i(f.$t("app.personal.airline")),1),t("span",St,i(a(p).switchAirline),1)]),_:1})),n(a(v),{span:8}),n(a(v),{span:8})]),_:1})]),a(p).epidNumber?(h(),T("div",At,[n(a(A),null,{default:u(()=>[n(a(v),{span:8},{default:u(()=>[t("label",{class:P(["font-bold text-[16px] text-gray-1",a(x)()==="en"?"base-info-label-en":""])},i(f.$t("app.domesticRepresentatives.connectionChannel")),3)]),_:1}),n(a(v),{span:8})]),_:1}),n(a(A),null,{default:u(()=>[n(a(v),{class:"flex items-center"},{default:u(()=>[t("label",Pt,i(f.$t("app.domesticRepresentatives.ChannelNumber")),1),t("span",Ut,i(a(p).epidNumber),1),n(a(te),{type:"primary",size:"small",onClick:a(l)},{default:u(()=>[Q(i(f.$t("app.domesticRepresentatives.unbind")),1)]),_:1},8,["onClick"])]),_:1}),n(a(v),{span:16})]),_:1}),n(a(A),null,{default:u(()=>[n(a(v),{span:8},{default:u(()=>[t("label",Dt,i(f.$t("app.domesticRepresentatives.expiryDate")),1),t("span",Rt,i(a(p).expireTime),1)]),_:1}),n(a(v),{span:16})]),_:1})])):I("",!0),t("div",Bt,[n(a(A),null,{default:u(()=>[n(a(v),{span:8},{default:u(()=>[t("label",{class:P(["base-info-label",a(x)()==="en"?"base-info-label-en":""])},i(f.$t("app.personal.versionInfo")),3)]),_:1}),n(a(v),{span:8})]),_:1}),n(a(A),null,{default:u(()=>[n(a(v),{span:8},{default:u(()=>[t("label",Lt,i(f.$t("app.personal.currentVersion")),1),t("span",xt,i(a(p).currentVersion),1)]),_:1}),n(a(v),{span:16})]),_:1})])]),a(p).defaultRoleWithPid?I("",!0):(h(),T("div",Ot,[t("p",Mt,i(f.$t("app.personal.salesAccountConfiguration")),1),t("div",Xt,[a(p).operator?(h(),D(a(ne),{key:1,ref_key:"formRef",ref:E,"label-width":"74px","label-position":"left",model:a(p),rules:a(y)},{default:u(()=>[n(a(A),null,{default:u(()=>[n(a(v),{span:8},{default:u(()=>[n(a(q),{label:f.$t("app.personal.iata"),class:"aviationNum",prop:"aviationAssociation"},{default:u(()=>[n(a(j),{modelValue:a(_),"onUpdate:modelValue":b[1]||(b[1]=S=>z(_)?_.value=S:null),class:"hasVal",placeholder:f.$t("app.check.enterOnly")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1}),n(a(v),{span:8},{default:u(()=>[n(a(q),{label:"Office",prop:"defaultOffice"},{default:u(()=>[n(a(j),{modelValue:a(F),"onUpdate:modelValue":b[2]||(b[2]=S=>z(F)?F.value=S:null),placeholder:f.$t("app.check.upTo5SeperateBySemicolon")},null,8,["modelValue","placeholder"])]),_:1})]),_:1}),n(a(v),{span:8},{default:u(()=>[n(a(q),{label:f.$t("app.personal.extraServiceAgent"),class:"agent",prop:"agentNo",style:{"align-items":"flex-start"},"label-width":"125px"},{default:u(()=>[n(a(j),{modelValue:a(d),"onUpdate:modelValue":b[3]||(b[3]=S=>z(d)?d.value=S:null),class:"hasVal",placeholder:f.$t("app.check.enterOnly")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1})]),_:1}),n(a(A),null,{default:u(()=>[n(a(v),{span:8},{default:u(()=>[n(a(q),{label:f.$t("app.personal.currency"),prop:"currency"},{default:u(()=>[n(a(j),{modelValue:a($),"onUpdate:modelValue":b[4]||(b[4]=S=>z($)?$.value=S:null),placeholder:f.$t("app.check.upTo10SeperateBySemicolon")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1}),n(a(v),{span:8},{default:u(()=>[n(a(q),{label:f.$t("app.personal.airport"),prop:"airportCode"},{default:u(()=>[n(a(j),{modelValue:a(r),"onUpdate:modelValue":b[5]||(b[5]=S=>z(r)?r.value=S:null),placeholder:f.$t("app.check.upTo5SeperateBySemicolon")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1}),n(a(v),{span:8})]),_:1})]),_:1},8,["model","rules"])):(h(),T(W,{key:0},[n(a(A),null,{default:u(()=>[n(a(v),{span:8},{default:u(()=>[t("label",jt,i(f.$t("app.personal.iata")),1),t("span",qt,i(a(p).aviationAssociation),1)]),_:1}),n(a(v),{span:8},{default:u(()=>[Qt,t("span",Gt,i(a(p).defaultOffice),1)]),_:1}),n(a(v),{span:8},{default:u(()=>[t("label",Wt,i(f.$t("app.personal.extraServiceAgent")),1),t("span",Yt,i(a(p).agentNo),1)]),_:1})]),_:1}),n(a(A),null,{default:u(()=>[n(a(v),{span:8},{default:u(()=>[t("label",zt,i(f.$t("app.personal.currency")),1),t("span",Kt,i(a(p).currency),1)]),_:1}),n(a(v),{span:8},{default:u(()=>[t("label",Ht,i(f.$t("app.personal.airport")),1),t("span",Jt,i(a(p).airportCode),1)]),_:1}),n(a(v),{span:8})]),_:1})],64))])])),t("div",Zt,[a(p).operator?(h(),D(a(te),{key:0,type:"primary","data-gid":"081W0101",onClick:a(o)},{default:u(()=>[Q(i(f.$t("app.button.save")),1)]),_:1},8,["onClick"])):I("",!0)])]))}});const as=de(es,[["__scopeId","data-v-7dcff83a"]]),ts=s=>{const{t:p}=le(),y=Re(),E=O(!1),o=O(),N=pe(),d=O({ct:"",ctct:"",ctce:"",airlineSettings:!1,airlinesCTCT:[{airline:"",ctct:""}],unshared:!0,nonstop:!1,autoSelectCabinClass:!1,useQtb:!1,manualQuery:!1,domesticPrinterno:"",internationalPrinterno:"",backfieldEnName:!0,checkTrip:{airlineNameCheckSwitch:!1,cityName:!1,airportName:!1,passengerInformation:!1,priceIncludingTax:!1,cabinSpace:!1,flightTime:!1},defaultRemark:!1,remarkList:[""],defaultNatAndIssCountry:!1,autoOccupy:!1,passengerInfo:{passengerNationality:"",visaIssueCountry:""},place:"BOTTOM",followPnr:!1}),$=X(()=>{var e;return((e=N.state.user)==null?void 0:e.defaultOfficeInternational)??!1}),_=(e,g,C)=>{g||C(new Error(p("app.pnrManagement.validate.required"))),d.value.airlinesCTCT.filter(M=>M.airline.toLocaleUpperCase()===(g==null?void 0:g.toLocaleUpperCase())).length>1?C(new Error(p("app.pnrManagement.validate.contactor"))):C()},r=(e,g,C)=>{g?C():C(new Error(p("app.pnrManagement.validate.required")))},F={ct:[{pattern:oa,message:p("app.pnrManagement.validate.formatErr"),trigger:"blur"}],ctct:[{pattern:Ne,message:p("app.pnrManagement.validate.formatErr"),trigger:"blur"}],airlinesCtct:[{pattern:Ne,message:p("app.pnrManagement.validate.formatErr"),trigger:"blur"},{validator:r,trigger:"blur"}],ctce:[{required:!1,pattern:ia,message:p("app.pnrManagement.validate.enterValidEmail"),trigger:["blur","change"]}],airline:[{pattern:Be,trigger:["blur","change"],message:p("app.pnrManagement.validate.characterCode")},{validator:_,trigger:["blur","change"]}],"passengerInfo.visaIssueCountry":[{pattern:/^[a-zA-Z]{2,3}$/,trigger:["change","blur"],message:p("app.intlPassengerForm.enterCharacterCode")}],"passengerInfo.passengerNationality":[{pattern:/^[a-zA-Z]{2,3}$/,trigger:["change","blur"],message:p("app.intlPassengerForm.enterCharacterCode")}]},V=()=>{d.value.airlinesCTCT.push({airline:"",ctct:""})},k=e=>{d.value.airlinesCTCT.splice(e,1)},l=()=>{d.value.remarkList.push("")},c=e=>{d.value.remarkList.splice(e,1)},f=async()=>{o.value.validate(async e=>{if(e)try{d.value.checkTrip.priceIncludingTax=d.value.checkTrip.passengerInformation,E.value=!0;const g={autoOccupy:d.value.autoOccupy,ct:d.value.ct,ctct:d.value.ctct,ctce:d.value.ctce,unshared:d.value.unshared,nonstop:d.value.nonstop,autoSelectCabinClass:d.value.autoSelectCabinClass,useQtb:d.value.useQtb,manualQuery:d.value.manualQuery,backfieldEnName:d.value.backfieldEnName,checkTrip:{airlineNameCheckSwitch:d.value.checkTrip.airlineNameCheckSwitch,cityName:d.value.checkTrip.cityName,airportName:d.value.checkTrip.airportName,passengerInformation:d.value.checkTrip.passengerInformation,priceIncludingTax:d.value.checkTrip.priceIncludingTax,cabinSpace:d.value.checkTrip.cabinSpace,flightTime:d.value.checkTrip.flightTime},airlinesCTCT:d.value.airlineSettings?d.value.airlinesCTCT:[],internationalPrinterno:d.value.internationalPrinterno,domesticPrinterno:d.value.domesticPrinterno,remarkList:d.value.defaultRemark?d.value.remarkList:[""],passengerInfo:{visaIssueCountry:d.value.defaultNatAndIssCountry?d.value.passengerInfo.visaIssueCountry:"",passengerNationality:d.value.defaultNatAndIssCountry?d.value.passengerInfo.passengerNationality:""},place:d.value.place,followPnr:d.value.followPnr},C=Ie("081W0103"),{data:w}=await ua(g,C);w.value&&(N.dispatch("updateUserPreferences",g),Le({message:p("app.tips.success"),type:"success"}))}finally{E.value=!1}})},b=e=>{(e.keyCode===13||e.keyCode===100)&&f()};be(()=>s.activeTab,e=>{e!==p("app.personal.preferences")?window.removeEventListener("keydown",b,!1):window.addEventListener("keydown",b)}),be(()=>y.fullPath,e=>{e!=="/personalCenter"?window.removeEventListener("keydown",b,!1):s.activeTab===p("app.personal.preferences")&&window.addEventListener("keydown",b)},{immediate:!0,deep:!0});const S=e=>{d.value.ct=(e==null?void 0:e.ct)??"",d.value.ctct=(e==null?void 0:e.ctct)??"",d.value.ctce=(e==null?void 0:e.ctce)??"",d.value.airlinesCTCT=(e==null?void 0:e.airlinesCTCT)??[{airline:"",ctct:""}],d.value.airlineSettings=!!(e!=null&&e.airlinesCTCT)},R=e=>{d.value.unshared=(e==null?void 0:e.unshared)??!0,d.value.nonstop=(e==null?void 0:e.nonstop)??!1,d.value.autoSelectCabinClass=(e==null?void 0:e.autoSelectCabinClass)??!1,d.value.autoOccupy=(e==null?void 0:e.autoOccupy)??!1},U=e=>{d.value.useQtb=(e==null?void 0:e.useQtb)??!1,d.value.manualQuery=(e==null?void 0:e.manualQuery)??!1},Y=e=>{d.value.backfieldEnName=(e==null?void 0:e.backfieldEnName)??!0},H=e=>{d.value.followPnr=(e==null?void 0:e.followPnr)??!1},J=e=>{d.value.domesticPrinterno=(e==null?void 0:e.domesticPrinterno)??"",d.value.internationalPrinterno=(e==null?void 0:e.internationalPrinterno)??""},Z=e=>{if(e!=null&&e.checkTrip){const g=e.checkTrip.priceIncludingTax||e.checkTrip.passengerInformation;e.checkTrip.priceIncludingTax=g,e.checkTrip.passengerInformation=g}d.value.checkTrip=(e==null?void 0:e.checkTrip)??{cityName:!1,airportName:!1,cabinSpace:!1,flightTime:!1,passengerInformation:!1,priceIncludingTax:!1,airlineNameCheckSwitch:!1}},B=e=>{d.value.defaultRemark=!!((e==null?void 0:e.remarkList)??[]).length,d.value.remarkList=((e==null?void 0:e.remarkList)??[]).length?(e==null?void 0:e.remarkList)??[""]:[""]},ee=e=>{var g,C,w,M;d.value.defaultNatAndIssCountry=!!((g=e==null?void 0:e.passengerInfo)!=null&&g.visaIssueCountry||(C=e==null?void 0:e.passengerInfo)!=null&&C.passengerNationality),d.value.passengerInfo.visaIssueCountry=((w=e==null?void 0:e.passengerInfo)==null?void 0:w.visaIssueCountry)??"",d.value.passengerInfo.passengerNationality=((M=e==null?void 0:e.passengerInfo)==null?void 0:M.passengerNationality)??""},ae=e=>{d.value.place=(e==null?void 0:e.place)??"BOTTOM"};function m(e){S(e),R(e),U(e),Y(e),J(e),Z(e),B(e),ee(e),ae(e),H(e)}return _e(async()=>{try{E.value=!0;const e=Ie("081W0104"),{data:g}=await ra(e);m(g.value)}finally{E.value=!1}}),{FORM_RULES:F,formRef:o,formParams:d,isIntlCrs:$,onSave:f,loading:E,addAirline:V,delAirline:k,keyDown:b,deleteRemark:c,addReamrk:l}},ss=ts,ns={class:"preference-conf mb-5"},ls={class:"mb-2.5 text-gray-1 text-base font-bold"},os={class:"my-[5px]"},is={class:"text-gray-2 text-xs font-normal leading-tight"},rs=["onClick"],us={class:"flex-col justify-start items-start gap-2.5 flex mb-7"},ps={class:"self-stretch justify-between items-start inline-flex"},cs={class:"text-gray-1 text-base font-bold leading-normal"},ds={class:"justify-start items-center gap-2 inline-flex"},fs={class:"self-stretch justify-start items-center gap-1.5 flex"},ms={class:"text-gray-3 text-xs font-normal leading-tight"},gs={class:"self-stretch justify-start items-center gap-2 inline-flex"},hs={class:"text-gray-3 text-xs font-normal leading-tight"},vs={class:"self-stretch justify-start items-center gap-2 inline-flex"},bs={class:"text-gray-3 text-xs font-normal leading-tight"},ys={class:"self-stretch justify-start items-center gap-2 inline-flex"},_s={class:"text-gray-3 text-xs font-normal leading-tight"},Cs={class:"h-[54px] flex-col justify-start items-start gap-2.5 flex mb-7"},$s={class:"self-stretch justify-between items-start inline-flex"},ks={class:"text-gray-1 text-base font-bold leading-normal"},ws={class:"justify-start items-center gap-2 inline-flex"},Ts={class:"self-stretch justify-start items-center gap-1.5 flex"},Es={class:"text-gray-3 text-xs font-normal leading-tight"},Ns={class:"h-[114px] flex-col justify-start items-start gap-2.5 inline-flex"},Is={class:"self-stretch justify-between items-start inline-flex"},Fs={class:"text-gray-1 text-base font-bold leading-normal"},Vs={class:"justify-start items-center gap-2 inline-flex"},Ss={class:"w-70 self-stretch justify-start items-center gap-1.5 flex"},As={class:"text-gray-3 text-xs font-normal leading-tight"},Ps={class:"self-stretch h-5 justify-start items-center gap-1.5 inline-flex"},Us={class:"w-70 self-stretch justify-start items-center gap-1.5 flex"},Ds={class:"text-gray-3 text-xs font-normal leading-tight"},Rs={class:"flex-col justify-start items-start gap-2.5 flex mb-3"},Bs={class:"self-stretch justify-between items-start inline-flex"},Ls={class:"text-gray-1 text-base font-bold leading-normal"},xs={class:"justify-start items-center gap-2 inline-flex"},Os={class:"w-70 self-stretch justify-start items-center gap-1.5 flex"},Ms={class:"text-gray-3 text-xs font-normal leading-tight"},Xs={class:"text-gray-1 text-base font-bold leading-normal mb-2.5"},js={class:"text-gray-3 text-xs font-normal leading-tight mb-2.5"},qs={class:"mr-2.5"},Qs={class:"mr-[6px]"},Gs={class:"mr-[6px]"},Ws={class:"flex-col justify-start items-start gap-2.5 flex mb-3"},Ys={class:"self-stretch justify-between items-start inline-flex"},zs={class:"text-gray-1 text-base font-bold leading-normal"},Ks={class:"justify-start items-center gap-2 inline-flex"},Hs={class:"w-70 self-stretch justify-start items-center gap-1.5 flex"},Js={class:"text-gray-3 text-xs font-normal leading-tight"},Zs={class:"flex-col justify-start items-start gap-2.5 inline-flex mb-3"},en={class:"self-stretch justify-between items-start inline-flex"},an={class:"text-gray-1 text-base font-bold leading-normal"},tn={class:"flex items-center"},sn={class:"text-gray-3 text-xs font-normal leading-tight mr-[20px]"},nn={class:"text-gray-3 text-xs font-normal leading-tight flex"},ln={class:"mr-1 py-2"},on={class:"bg-gray-7 px-1 py-2 rounded-sm flex-col justify-center items-start inline-flex gap-2"},rn={key:0},un={class:"flex-col inline-flex gap-2"},pn={class:"text-gray-1 text-base font-bold leading-normal mb-2.5"},cn={class:"w-70 mb-[5px] self-stretch justify-start items-center gap-1.5 flex"},dn={class:"text-gray-3 text-xs font-normal leading-tight"},fn=["onClick"],mn={key:2},gn={class:"text-gray-1 text-base font-bold leading-normal mb-2.5"},hn={class:"w-[100%] h-8 justify-end items-center gap-3.5 inline-flex"},vn=ce({__name:"Preferences",props:{activeTab:{}},setup(s){const p=s,{FORM_RULES:y,formRef:E,formParams:o,isIntlCrs:N,onSave:d,loading:$,addAirline:_,delAirline:r,keyDown:F,deleteRemark:V,addReamrk:k}=ss(p);return(l,c)=>{const f=j,b=q,S=_a,R=Ca,U=$a,Y=ka,H=wa,J=ne,Z=te,B=Fe("trimUpper"),ee=Fe("permission"),ae=ca;return L((h(),T("div",ns,[t("div",ls,i(l.$t("app.personal.setSefaultCTAndCTCT")),1),n(J,{ref_key:"formRef",ref:E,"require-asterisk-position":"right",model:a(o),inline:!0,rules:a(y),onKeyup:pa(a(F),["enter"])},{default:u(()=>[t("div",null,[n(b,{label:"CT",prop:"ct"},{default:u(()=>[L(n(f,{modelValue:a(o).ct,"onUpdate:modelValue":c[0]||(c[0]=m=>a(o).ct=m)},null,8,["modelValue"]),[[B]])]),_:1}),n(b,{label:"CTCT",prop:"ctct"},{default:u(()=>[L(n(f,{modelValue:a(o).ctct,"onUpdate:modelValue":c[1]||(c[1]=m=>a(o).ctct=m)},null,8,["modelValue"]),[[B]])]),_:1}),n(b,{label:"CTCE",prop:"ctce"},{default:u(()=>[L(n(f,{modelValue:a(o).ctce,"onUpdate:modelValue":c[2]||(c[2]=m=>a(o).ctce=m)},null,8,["modelValue"]),[[B]])]),_:1}),n(S,{size:"large",type:"info"},{default:u(()=>[Q(i(l.$t("app.personal.defaultValue")),1)]),_:1})]),t("div",os,[n(b,{prop:"airlineSettings"},{default:u(()=>[n(R,{modelValue:a(o).airlineSettings,"onUpdate:modelValue":c[3]||(c[3]=m=>a(o).airlineSettings=m),type:"default"},{default:u(()=>[t("span",is,i(l.$t("app.personal.airlineSettings")),1)]),_:1},8,["modelValue"])]),_:1})]),a(o).airlineSettings?(h(!0),T(W,{key:0},se(a(o).airlinesCTCT,(m,e)=>(h(),T("div",{key:e,class:"mb-[14px]"},[n(b,{label:l.$t("app.personal.airlineCompany"),prop:"airlinesCTCT."+e+".airline",class:"w-[100px]",rules:a(y).airline},{default:u(()=>[L(n(f,{modelValue:m.airline,"onUpdate:modelValue":g=>m.airline=g},null,8,["modelValue","onUpdate:modelValue"]),[[B]])]),_:2},1032,["label","prop","rules"]),n(b,{label:"CTCT",prop:"airlinesCTCT."+e+".ctct",rules:a(y).airlinesCtct},{default:u(()=>[L(n(f,{modelValue:m.ctct,"onUpdate:modelValue":g=>m.ctct=g},null,8,["modelValue","onUpdate:modelValue"]),[[B]])]),_:2},1032,["prop","rules"]),e===0&&a(o).airlinesCTCT.length!==1||e>0?(h(),T("em",{key:0,class:"iconfont icon-minus-square text-[20px] relative text-undefined text-brand-2 mr-[5px] cursor-pointer",onClick:g=>a(r)(e)},null,8,rs)):I("",!0),e===a(o).airlinesCTCT.length-1?(h(),T("em",{key:1,class:"iconfont icon-plus-square text-[20px] relative text-undefined text-brand-2 cursor-pointer",onClick:c[4]||(c[4]=(...g)=>a(_)&&a(_)(...g))})):I("",!0)]))),128)):I("",!0),t("div",us,[t("div",ps,[t("div",cs,i(l.$t("app.personal.flightQueryConditions")),1)]),t("div",ds,[t("div",fs,[t("div",ms,i(l.$t("app.personal.defaultNonShared")),1),n(U,{modelValue:a(o).unshared,"onUpdate:modelValue":c[5]||(c[5]=m=>a(o).unshared=m),"inline-prompt":"","active-text":l.$t("app.avSearch.openYes"),"inactive-text":l.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])])]),t("div",gs,[t("div",hs,i(l.$t("app.personal.defaultDirectFlightOnly")),1),n(U,{modelValue:a(o).nonstop,"onUpdate:modelValue":c[6]||(c[6]=m=>a(o).nonstop=m),"inline-prompt":"","active-text":l.$t("app.avSearch.openYes"),"inactive-text":l.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])]),t("div",vs,[t("div",bs,i(l.$t("app.personal.autoSelectCabinClass")),1),n(U,{modelValue:a(o).autoSelectCabinClass,"onUpdate:modelValue":c[7]||(c[7]=m=>a(o).autoSelectCabinClass=m),"inline-prompt":"","active-text":l.$t("app.avSearch.openYes"),"inactive-text":l.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])]),t("div",ys,[t("div",_s,i(l.$t("app.personal.autoPlaceholder")),1),n(U,{modelValue:a(o).autoOccupy,"onUpdate:modelValue":c[8]||(c[8]=m=>a(o).autoOccupy=m),"inline-prompt":"","active-text":l.$t("app.avSearch.openYes"),"inactive-text":l.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])])]),t("div",Cs,[t("div",$s,[t("div",ks,i(l.$t("app.personal.passengerInfo")),1)]),t("div",ws,[t("div",Ts,[t("div",Es,i(l.$t("app.personal.defaultPassengerDocuments")),1),n(U,{modelValue:a(o).defaultNatAndIssCountry,"onUpdate:modelValue":c[9]||(c[9]=m=>a(o).defaultNatAndIssCountry=m),"inline-prompt":"","active-text":l.$t("app.avSearch.openYes"),"inactive-text":l.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])]),a(o).defaultNatAndIssCountry?(h(),T(W,{key:0},[n(b,{label:l.$t("app.personal.passengerNationality"),prop:"passengerInfo.passengerNationality"},{default:u(()=>[L(n(f,{modelValue:a(o).passengerInfo.passengerNationality,"onUpdate:modelValue":c[10]||(c[10]=m=>a(o).passengerInfo.passengerNationality=m)},null,8,["modelValue"]),[[B]])]),_:1},8,["label"]),n(b,{label:l.$t("app.personal.visaIssueCountry"),prop:"passengerInfo.visaIssueCountry"},{default:u(()=>[L(n(f,{modelValue:a(o).passengerInfo.visaIssueCountry,"onUpdate:modelValue":c[11]||(c[11]=m=>a(o).passengerInfo.visaIssueCountry=m)},null,8,["modelValue"]),[[B]])]),_:1},8,["label"])],64)):I("",!0)])]),t("div",Ns,[t("div",Is,[t("div",Fs,i(l.$t("app.personal.fareQueryConditions")),1)]),t("div",Vs,[t("div",Ss,[t("div",As,i(l.$t("app.personal.defaultUseQTB")),1),n(U,{modelValue:a(o).useQtb,"onUpdate:modelValue":c[12]||(c[12]=m=>a(o).useQtb=m),"inline-prompt":"","active-text":l.$t("app.avSearch.openYes"),"inactive-text":l.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])])]),t("div",Ps,[t("div",Us,[t("div",Ds,i(l.$t("app.personal.manualRateQuery")),1),n(U,{modelValue:a(o).manualQuery,"onUpdate:modelValue":c[13]||(c[13]=m=>a(o).manualQuery=m),"inline-prompt":"","active-text":l.$t("app.avSearch.openYes"),"inactive-text":l.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])])])]),t("div",Rs,[t("div",Bs,[t("div",Ls,i(l.$t("app.personal.followPnr")),1)]),t("div",xs,[t("div",Os,[t("div",Ms,i(l.$t("app.personal.followPnrTip")),1),n(U,{modelValue:a(o).followPnr,"onUpdate:modelValue":c[14]||(c[14]=m=>a(o).followPnr=m),"inline-prompt":"","active-text":l.$t("app.avSearch.openYes"),"inactive-text":l.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])])])]),t("div",Xs,i(l.$t("app.personal.ticketMachine")),1),t("div",js,[t("span",qs,i(l.$t("app.personal.defaultTicketMachine")),1),t("span",Qs,i(l.$t("app.personal.domestic")),1),n(Ve,{modelValue:a(o).domesticPrinterno,"onUpdate:modelValue":c[15]||(c[15]=m=>a(o).domesticPrinterno=m),"need-distinguish":!0,"select-class":"w-[100px] mr-2.5"},null,8,["modelValue"]),t("span",Gs,i(l.$t("app.personal.international")),1),n(Ve,{modelValue:a(o).internationalPrinterno,"onUpdate:modelValue":c[16]||(c[16]=m=>a(o).internationalPrinterno=m),"is-inter":!0,"need-distinguish":!0,"select-class":"w-[100px]"},null,8,["modelValue"])]),t("div",Ws,[t("div",Ys,[t("div",zs,i(l.$t("app.personal.ticketRefund")),1)]),t("div",Ks,[t("div",Hs,[t("div",Js,i(l.$t("app.personal.manualRefundName")),1),n(U,{modelValue:a(o).backfieldEnName,"onUpdate:modelValue":c[17]||(c[17]=m=>a(o).backfieldEnName=m),"inline-prompt":"","active-text":l.$t("app.avSearch.openYes"),"inactive-text":l.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])])])]),t("div",Zs,[t("div",en,[t("div",an,i(l.$t("app.personal.checkTrip")),1)]),t("div",tn,[t("div",sn,i(l.$t("app.personal.verifyItinerary")),1),n(R,{modelValue:a(o).checkTrip.airlineNameCheckSwitch,"onUpdate:modelValue":c[18]||(c[18]=m=>a(o).checkTrip.airlineNameCheckSwitch=m),label:l.$t("app.personal.airlineName")},null,8,["modelValue","label"]),n(R,{modelValue:a(o).checkTrip.cityName,"onUpdate:modelValue":c[19]||(c[19]=m=>a(o).checkTrip.cityName=m),label:l.$t("app.personal.cityName"),class:"ml-[-10px]"},null,8,["modelValue","label"]),n(R,{modelValue:a(o).checkTrip.airportName,"onUpdate:modelValue":c[20]||(c[20]=m=>a(o).checkTrip.airportName=m),label:l.$t("app.personal.airportName"),class:"ml-[-10px]"},null,8,["modelValue","label"]),n(R,{modelValue:a(o).checkTrip.passengerInformation,"onUpdate:modelValue":c[21]||(c[21]=m=>a(o).checkTrip.passengerInformation=m),label:l.$t("app.personal.pasgInfoAndPriceTax"),class:"ml-[-10px]"},null,8,["modelValue","label"]),n(R,{modelValue:a(o).checkTrip.cabinSpace,"onUpdate:modelValue":c[22]||(c[22]=m=>a(o).checkTrip.cabinSpace=m),label:l.$t("app.personal.cabinCode"),class:"ml-[-10px]"},null,8,["modelValue","label"]),n(R,{modelValue:a(o).checkTrip.flightTime,"onUpdate:modelValue":c[23]||(c[23]=m=>a(o).checkTrip.flightTime=m),label:l.$t("app.personal.flightTime"),class:"ml-[-10px]"},null,8,["modelValue","label"])]),t("div",nn,[t("div",ln,i(l.$t("app.personal.example"))+" :",1),t("div",on,[t("div",null,[a(o).checkTrip.airlineNameCheckSwitch?(h(),T("span",rn,i(l.$t("app.personal.AirChina")),1)):I("",!0),t("span",null," CA1519 "+i(a(o).checkTrip.cabinSpace?l.$t("app.personal.cabinY"):" ")+" "+i(l.$t("app.personal.date"))+" 2024-10-23("+i(l.$t("app.pnrManagement.flight.Days3"))+")   "+i(a(o).checkTrip.cityName?l.$t("app.personal.BeiJing"):" ")+" PEK"+i(a(o).checkTrip.airportName?l.$t("app.personal.CapitalAirport"):" ")+"T3 ",1),Q(" — "+i(a(o).checkTrip.cityName?l.$t("app.personal.Shanghai"):" ")+"SHA"+i(a(o).checkTrip.airportName?l.$t("app.personal.HongqiaoAirport"):" ")+"T2  "+i(l.$t("app.personal.takeOff"))+"0930  "+i(l.$t("app.personal.reach"))+"1155 "+i(a(o).checkTrip.flightTime?l.$t("app.personal.flightTimeExample"):""),1)]),L(t("div",un,[t("div",null,i(a(o).checkTrip.passengerInformation?`${l.$t("app.personal.passenger")+"1 张三 110106XXXXXXXXXXXXX 150113XXXXX"} ${l.$t("app.personal.taxPrice")+"3000.00"}`:""),1),t("div",null,i(a(o).checkTrip.passengerInformation?`${l.$t("app.personal.passenger")+"2 李四 110106XXXXXXXXXXXXX 150113XXXXX"} ${l.$t("app.personal.taxPrice")+"3000.00"}`:""),1)],512),[[Pe,a(o).checkTrip.passengerInformation]])])])]),t("div",pn,i(l.$t("app.personal.remark")),1),n(b,null,{default:u(()=>[t("div",cn,[t("div",dn,i(l.$t("app.personal.defaultRemark")),1),n(U,{modelValue:a(o).defaultRemark,"onUpdate:modelValue":c[24]||(c[24]=m=>a(o).defaultRemark=m),"inline-prompt":"","active-text":l.$t("app.avSearch.openYes"),"inactive-text":l.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])])]),_:1}),a(o).defaultRemark?(h(!0),T(W,{key:1},se(a(o).remarkList,(m,e)=>(h(),T("div",{key:e,class:"mb-2.5 flex items-center"},[n(b,{class:"w-[600px]",label:"RMK"},{default:u(()=>[L(n(f,{modelValue:a(o).remarkList[e],"onUpdate:modelValue":g=>a(o).remarkList[e]=g},null,8,["modelValue","onUpdate:modelValue"]),[[B]])]),_:2},1024),e===0&&a(o).remarkList.length!==1||e>0?(h(),T("em",{key:0,class:"iconfont icon-minus-square text-[20px] relative text-undefined text-brand-2 mr-[5px] cursor-pointer",onClick:g=>a(V)(e)},null,8,fn)):I("",!0),e===a(o).remarkList.length-1&&e<2?(h(),T("em",{key:1,class:"iconfont icon-plus-square text-[20px] relative text-undefined text-brand-2 cursor-pointer",onClick:c[25]||(c[25]=(...g)=>a(k)&&a(k)(...g))})):I("",!0)]))),128)):I("",!0),a(N)?I("",!0):L((h(),T("div",mn,[t("div",gn,i(l.$t("app.personal.frequentlyAskedQuestionsEntrance")),1),n(H,{modelValue:a(o).place,"onUpdate:modelValue":c[26]||(c[26]=m=>a(o).place=m),class:"frequently-asked-questions"},{default:u(()=>[n(Y,{label:"BOTTOM"},{default:u(()=>[Q(i(l.$t("app.personal.suspendedDisplay")),1)]),_:1}),n(Y,{label:"TOP"},{default:u(()=>[Q(i(l.$t("app.personal.topFixedDisplay")),1)]),_:1})]),_:1},8,["modelValue"])])),[[ee,"frequently-asked-questions-show"]])]),_:1},8,["model","rules","onKeyup"]),t("div",hn,[n(Z,{size:"default",type:"primary","data-gid":"081W0103",onClick:a(d)},{default:u(()=>[Q(i(l.$t("app.personal.save")),1)]),_:1},8,["onClick"])])])),[[ae,a($)]])}}});const bn=de(vn,[["__scopeId","data-v-0dc50ccb"]]),yn=ce({name:"PersonalCenter",components:{Information:as,UpdatePassword:Ja,Preferences:bn,ElTabs:xe,ElTabPane:Oe},setup(){const{t:s}=le(),p=pe(),{user:y}=p.state,E=ye("isMini",!1),o=O(!1),N=async()=>(o.value=y.longTimeNoChangePwd,y&&(y.lastLoginTime===null||o.value||y.twoPwdMismatch)),d=O(!1),$=[{component:"",label:s("app.personal.accountMapping"),auth:"setting-personCenter-accountMapping"},{component:"",label:s("app.personal.OnTheFlight"),auth:"setting-personCenter-followFlight"},{component:"Preferences",label:s("app.personal.preferences"),auth:"setting-personCenter-preferences"}],_=O([]),r=O(""),F=async()=>{var l;const k=await p.getters.roleResource;_.value.push({component:"Information",label:s("app.personal.basicInformation"),auth:"setting-personalCenter-baseInfo"}),_.value.push({component:"UpdatePassword",label:s("app.personal.updatePwd"),auth:"setting-personCenter-updatePassword"}),$.forEach(c=>{k.includes(c.auth)&&_.value.push(c)}),r.value=((l=_.value[0])==null?void 0:l.label)??""},V=()=>x()==="en"?r.value===s("app.personal.preferences")?"tabs_short_en":"tabs_en":"";return _e(async()=>{const l=(await p.getters.user).twoPwdMismatch;await N()||l?(d.value=!0,E.value||Ta()):d.value=!1,F()}),{onlyUpdatePassword:d,activeTab:r,centerComponents:_,getTabENClass:V,longTimeNoChangePwd:o}}});const _n={class:"user-center"};function Cn(s,p,y,E,o,N){const d=ve("UpdatePassword"),$=Oe,_=xe;return h(),T("div",_n,[s.onlyUpdatePassword?(h(),D(_,{key:0},{default:u(()=>[n($,{label:s.$t("app.personal.updatePwd")},{default:u(()=>[n(d)]),_:1},8,["label"]),s.longTimeNoChangePwd?(h(),D($,{key:0,disabled:"",label:s.$t("app.personal.longTimeChange")},null,8,["label"])):(h(),D($,{key:1,disabled:"",label:s.$t("app.personal.updateFirst")},null,8,["label"]))]),_:1})):I("",!0),!s.onlyUpdatePassword&&s.centerComponents.length>0?(h(),D(_,{key:1,modelValue:s.activeTab,"onUpdate:modelValue":p[0]||(p[0]=r=>s.activeTab=r),class:P(s.getTabENClass())},{default:u(()=>[(h(!0),T(W,null,se(s.centerComponents,r=>(h(),D($,{key:r.component,label:r.label,name:r.label},{default:u(()=>[r.component.length>0?(h(),D(da(r.component),{key:0,"active-tab":s.activeTab},null,8,["active-tab"])):I("",!0)]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue","class"])):I("",!0)])}const Jn=de(yn,[["render",Cn],["__scopeId","data-v-b6f9bf98"]]);export{Jn as default};
