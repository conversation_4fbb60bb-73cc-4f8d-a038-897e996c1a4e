import{hO as de,hP as be,dW as ce,hQ as ve,gD as me,eb as J,em as z,dM as U,e7 as K,hr as G,O as I,w as h,ec as N,eI as fe,ef as R,dJ as Q,ac as j,ek as Y,aF as Z,r as D,U as E,dF as ke,ed as A,ej as W,eg as pe,hs as H,q as L,Y as X,v as T,x as C,y as _,z as ee,P as q,D as x,A as t,a5 as $,B as y,b3 as F,E as V,hR as w,F as M,ai as he,ak as le,Q as ae,J as P,b9 as ne,_ as O,C as ge,L as xe,M as Ce,W as ye,cL as Se,K as Le,Z as te}from"./index-a2fbd71b.js";import{h as Be,i as Ee}from"./isEqual-c950930c.js";import{f as Ie}from"./flatten-b73d2287.js";function $e(e){return de(be(e,void 0,Ie),e+"")}function Fe(e,u,i){for(var l=-1,k=u.length,b={};++l<k;){var c=u[l],o=ce(e,c);i(o,c)&&ve(b,me(c,e),o)}return b}function Ve(e,u){return Fe(e,u,function(i,l){return Be(e,l)})}var we=$e(function(e,u){return e==null?{}:Ve(e,u)});const ze=we,oe={modelValue:{type:[Number,String,Boolean],default:void 0},label:{type:[String,Boolean,Number,Object],default:void 0},indeterminate:Boolean,disabled:Boolean,checked:Boolean,name:{type:String,default:void 0},trueLabel:{type:[String,Number],default:void 0},falseLabel:{type:[String,Number],default:void 0},id:{type:String,default:void 0},controls:{type:String,default:void 0},border:Boolean,size:J,tabindex:[String,Number],validateEvent:{type:Boolean,default:!0}},se={[z]:e=>U(e)||K(e)||G(e),change:e=>U(e)||K(e)||G(e)},B=Symbol("checkboxGroupContextKey"),Ge=({model:e,isChecked:u})=>{const i=I(B,void 0),l=h(()=>{var b,c;const o=(b=i==null?void 0:i.max)==null?void 0:b.value,v=(c=i==null?void 0:i.min)==null?void 0:c.value;return!N(o)&&e.value.length>=o&&!u.value||!N(v)&&e.value.length<=v&&u.value});return{isDisabled:fe(h(()=>(i==null?void 0:i.disabled.value)||l.value)),isLimitDisabled:l}},Ne=(e,{model:u,isLimitExceeded:i,hasOwnLabel:l,isDisabled:k,isLabeledByFormItem:b})=>{const c=I(B,void 0),{formItem:o}=R(),{emit:v}=Q();function a(n){var d,f;return n===e.trueLabel||n===!0?(d=e.trueLabel)!=null?d:!0:(f=e.falseLabel)!=null?f:!1}function m(n,d){v("change",a(n),d)}function p(n){if(i.value)return;const d=n.target;v("change",a(d.checked),n)}async function S(n){i.value||!l.value&&!k.value&&b.value&&(n.composedPath().some(r=>r.tagName==="LABEL")||(u.value=a([!1,e.falseLabel].includes(u.value)),await Z(),m(u.value,n)))}const s=h(()=>(c==null?void 0:c.validateEvent)||e.validateEvent);return j(()=>e.modelValue,()=>{s.value&&(o==null||o.validate("change").catch(n=>Y()))}),{handleChange:p,onClickRoot:S}},De=e=>{const u=D(!1),{emit:i}=Q(),l=I(B,void 0),k=h(()=>N(l)===!1),b=D(!1);return{model:h({get(){var o,v;return k.value?(o=l==null?void 0:l.modelValue)==null?void 0:o.value:(v=e.modelValue)!=null?v:u.value},set(o){var v,a;k.value&&E(o)?(b.value=((v=l==null?void 0:l.max)==null?void 0:v.value)!==void 0&&o.length>(l==null?void 0:l.max.value),b.value===!1&&((a=l==null?void 0:l.changeEvent)==null||a.call(l,o))):(i(z,o),u.value=o)}}),isGroup:k,isLimitExceeded:b}},Pe=(e,u,{model:i})=>{const l=I(B,void 0),k=D(!1),b=h(()=>{const a=i.value;return G(a)?a:E(a)?ke(e.label)?a.map(A).some(m=>Ee(m,e.label)):a.map(A).includes(e.label):a!=null?a===e.trueLabel:!!a}),c=W(h(()=>{var a;return(a=l==null?void 0:l.size)==null?void 0:a.value}),{prop:!0}),o=W(h(()=>{var a;return(a=l==null?void 0:l.size)==null?void 0:a.value})),v=h(()=>!!u.default||!pe(e.label));return{checkboxButtonSize:c,isChecked:b,isFocused:k,checkboxSize:o,hasOwnLabel:v}},Re=(e,{model:u})=>{function i(){E(u.value)&&!u.value.includes(e.label)?u.value.push(e.label):u.value=e.trueLabel||!0}e.checked&&i()},ue=(e,u)=>{const{formItem:i}=R(),{model:l,isGroup:k,isLimitExceeded:b}=De(e),{isFocused:c,isChecked:o,checkboxButtonSize:v,checkboxSize:a,hasOwnLabel:m}=Pe(e,u,{model:l}),{isDisabled:p}=Ge({model:l,isChecked:o}),{inputId:S,isLabeledByFormItem:s}=H(e,{formItemContext:i,disableIdGeneration:m,disableIdManagement:k}),{handleChange:n,onClickRoot:d}=Ne(e,{model:l,isLimitExceeded:b,hasOwnLabel:m,isDisabled:p,isLabeledByFormItem:s});return Re(e,{model:l}),{inputId:S,isLabeledByFormItem:s,isChecked:o,isDisabled:p,isFocused:c,checkboxButtonSize:v,checkboxSize:a,hasOwnLabel:m,model:l,handleChange:n,onClickRoot:d}},Te=["id","indeterminate","name","tabindex","disabled","true-value","false-value"],Me=["id","indeterminate","disabled","value","name","tabindex"],Oe=L({name:"ElCheckbox"}),Ue=L({...Oe,props:oe,emits:se,setup(e){const u=e,i=X(),{inputId:l,isLabeledByFormItem:k,isChecked:b,isDisabled:c,isFocused:o,checkboxSize:v,hasOwnLabel:a,model:m,handleChange:p,onClickRoot:S}=ue(u,i),s=T("checkbox"),n=h(()=>[s.b(),s.m(v.value),s.is("disabled",c.value),s.is("bordered",u.border),s.is("checked",b.value)]),d=h(()=>[s.e("input"),s.is("disabled",c.value),s.is("checked",b.value),s.is("indeterminate",u.indeterminate),s.is("focus",o.value)]);return(f,r)=>(C(),_(ne(!t(a)&&t(k)?"span":"label"),{class:x(t(n)),"aria-controls":f.indeterminate?f.controls:null,onClick:t(S)},{default:ee(()=>[q("span",{class:x(t(d))},[f.trueLabel||f.falseLabel?$((C(),y("input",{key:0,id:t(l),"onUpdate:modelValue":r[0]||(r[0]=g=>F(m)?m.value=g:null),class:x(t(s).e("original")),type:"checkbox",indeterminate:f.indeterminate,name:f.name,tabindex:f.tabindex,disabled:t(c),"true-value":f.trueLabel,"false-value":f.falseLabel,onChange:r[1]||(r[1]=(...g)=>t(p)&&t(p)(...g)),onFocus:r[2]||(r[2]=g=>o.value=!0),onBlur:r[3]||(r[3]=g=>o.value=!1),onClick:r[4]||(r[4]=V(()=>{},["stop"]))},null,42,Te)),[[w,t(m)]]):$((C(),y("input",{key:1,id:t(l),"onUpdate:modelValue":r[5]||(r[5]=g=>F(m)?m.value=g:null),class:x(t(s).e("original")),type:"checkbox",indeterminate:f.indeterminate,disabled:t(c),value:f.label,name:f.name,tabindex:f.tabindex,onChange:r[6]||(r[6]=(...g)=>t(p)&&t(p)(...g)),onFocus:r[7]||(r[7]=g=>o.value=!0),onBlur:r[8]||(r[8]=g=>o.value=!1),onClick:r[9]||(r[9]=V(()=>{},["stop"]))},null,42,Me)),[[w,t(m)]]),q("span",{class:x(t(s).e("inner"))},null,2)],2),t(a)?(C(),y("span",{key:0,class:x(t(s).e("label"))},[M(f.$slots,"default"),f.$slots.default?P("v-if",!0):(C(),y(he,{key:0},[le(ae(f.label),1)],64))],2)):P("v-if",!0)]),_:3},8,["class","aria-controls","onClick"]))}});var Ke=O(Ue,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/checkbox/src/checkbox.vue"]]);const Ae=["name","tabindex","disabled","true-value","false-value"],We=["name","tabindex","disabled","value"],qe=L({name:"ElCheckboxButton"}),Je=L({...qe,props:oe,emits:se,setup(e){const u=e,i=X(),{isFocused:l,isChecked:k,isDisabled:b,checkboxButtonSize:c,model:o,handleChange:v}=ue(u,i),a=I(B,void 0),m=T("checkbox"),p=h(()=>{var s,n,d,f;const r=(n=(s=a==null?void 0:a.fill)==null?void 0:s.value)!=null?n:"";return{backgroundColor:r,borderColor:r,color:(f=(d=a==null?void 0:a.textColor)==null?void 0:d.value)!=null?f:"",boxShadow:r?`-1px 0 0 0 ${r}`:void 0}}),S=h(()=>[m.b("button"),m.bm("button",c.value),m.is("disabled",b.value),m.is("checked",k.value),m.is("focus",l.value)]);return(s,n)=>(C(),y("label",{class:x(t(S))},[s.trueLabel||s.falseLabel?$((C(),y("input",{key:0,"onUpdate:modelValue":n[0]||(n[0]=d=>F(o)?o.value=d:null),class:x(t(m).be("button","original")),type:"checkbox",name:s.name,tabindex:s.tabindex,disabled:t(b),"true-value":s.trueLabel,"false-value":s.falseLabel,onChange:n[1]||(n[1]=(...d)=>t(v)&&t(v)(...d)),onFocus:n[2]||(n[2]=d=>l.value=!0),onBlur:n[3]||(n[3]=d=>l.value=!1),onClick:n[4]||(n[4]=V(()=>{},["stop"]))},null,42,Ae)),[[w,t(o)]]):$((C(),y("input",{key:1,"onUpdate:modelValue":n[5]||(n[5]=d=>F(o)?o.value=d:null),class:x(t(m).be("button","original")),type:"checkbox",name:s.name,tabindex:s.tabindex,disabled:t(b),value:s.label,onChange:n[6]||(n[6]=(...d)=>t(v)&&t(v)(...d)),onFocus:n[7]||(n[7]=d=>l.value=!0),onBlur:n[8]||(n[8]=d=>l.value=!1),onClick:n[9]||(n[9]=V(()=>{},["stop"]))},null,42,We)),[[w,t(o)]]),s.$slots.default||s.label?(C(),y("span",{key:2,class:x(t(m).be("button","inner")),style:ge(t(k)?t(p):void 0)},[M(s.$slots,"default",{},()=>[le(ae(s.label),1)])],6)):P("v-if",!0)],2))}});var ie=O(Je,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/checkbox/src/checkbox-button.vue"]]);const Qe=xe({modelValue:{type:Ce(Array),default:()=>[]},disabled:Boolean,min:Number,max:Number,size:J,label:String,fill:String,textColor:String,tag:{type:String,default:"div"},validateEvent:{type:Boolean,default:!0}}),je={[z]:e=>E(e),change:e=>E(e)},Ye=L({name:"ElCheckboxGroup"}),Ze=L({...Ye,props:Qe,emits:je,setup(e,{emit:u}){const i=e,l=T("checkbox"),{formItem:k}=R(),{inputId:b,isLabeledByFormItem:c}=H(i,{formItemContext:k}),o=async a=>{u(z,a),await Z(),u("change",a)},v=h({get(){return i.modelValue},set(a){o(a)}});return ye(B,{...ze(Se(i),["size","min","max","disabled","validateEvent","fill","textColor"]),modelValue:v,changeEvent:o}),j(()=>i.modelValue,()=>{i.validateEvent&&(k==null||k.validate("change").catch(a=>Y()))}),(a,m)=>{var p;return C(),_(ne(a.tag),{id:t(b),class:x(t(l).b("group")),role:"group","aria-label":t(c)?void 0:a.label||"checkbox-group","aria-labelledby":t(c)?(p=t(k))==null?void 0:p.labelId:void 0},{default:ee(()=>[M(a.$slots,"default")]),_:3},8,["id","class","aria-label","aria-labelledby"])}}});var re=O(Ze,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/checkbox/src/checkbox-group.vue"]]);const el=Le(Ke,{CheckboxButton:ie,CheckboxGroup:re});te(ie);const ll=te(re);export{el as E,ll as a,$e as f};
