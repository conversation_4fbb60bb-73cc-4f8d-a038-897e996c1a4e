import{dJ as At,w as h,e2 as bl,e3 as yl,e4 as Ve,M as ne,L as Ee,e5 as Sl,q as ze,v as be,r as B,b0 as qe,A as C,ac as Se,e6 as Vl,aG as Ce,E as le,e7 as <PERSON>,o as Ft,e8 as wl,dN as Il,b9 as ke,dM as Ot,aF as ae,dE as Ol,_ as dt,x as R,B as K,Q as ie,D as E,C as ce,P as X,e9 as re,dO as Cl,ea as Tl,eb as Ml,O as Pt,F as We,ec as El,G as se,X as Ct,dF as je,ed as zl,V as Ll,ee as Rl,dL as nt,N as Nl,ef as kl,eg as Tt,eh as Bl,ei as Dl,U as Ke,ej as $l,d1 as Al,ek as Fl,el as Pl,em as Ht,en as Wt,H as Hl,eo as Wl,cL as Mt,W as Kl,a4 as Pe,ae as Et,a5 as He,z as te,J as he,ai as Ne,aj as tt,y as ge,b1 as me,a6 as Ul,cZ as Gl,ep as ql}from"./index-a2fbd71b.js";import{u as jl,a as Ql,E as Zl}from"./index-fe3402a7.js";import{E as Xl}from"./index-ac58700d.js";import{d as Kt,r as Ut}from"./index-7a3e9562.js";import{B as Yl}from"./index-350461d4.js";import{e as Jl}from"./strings-cc725bd5.js";import{i as zt}from"./isEqual-c950930c.js";import{C as xl}from"./index-4e9a4449.js";var Lt=Number.isNaN||function(t){return typeof t=="number"&&t!==t};function _l(e,t){return!!(e===t||Lt(e)&&Lt(t))}function en(e,t){if(e.length!==t.length)return!1;for(var o=0;o<e.length;o++)if(!_l(e[o],t[o]))return!1;return!0}function tn(e,t){t===void 0&&(t=en);var o=null;function s(){for(var i=[],u=0;u<arguments.length;u++)i[u]=arguments[u];if(o&&o.lastThis===this&&t(i,o.lastArgs))return o.lastResult;var r=e.apply(this,i);return o={lastResult:r,lastArgs:i,lastThis:this},r}return s.clear=function(){o=null},s}const ln=()=>{const t=At().proxy.$props;return h(()=>{const o=(s,i,u)=>({});return t.perfMode?bl(o):tn(o)})},nn=50,Rt="itemRendered",Nt="scroll",Gt="forward",qt="backward",Be="auto",jt="smart",Qt="start",Qe="center",Zt="end",Me="horizontal",ct="vertical",on="ltr",Ue="rtl",ot="negative",Xt="positive-ascending",Yt="positive-descending",an={[Me]:"left",[ct]:"top"},sn=20,rn={[Me]:"deltaX",[ct]:"deltaY"},un=({atEndEdge:e,atStartEdge:t,layout:o},s)=>{let i,u=0;const r=f=>f<0&&t.value||f>0&&e.value;return{hasReachedEdge:r,onWheel:f=>{Kt(i);const b=f[rn[o.value]];r(u)&&r(u+b)||(u+=b,yl()||f.preventDefault(),i=Ut(()=>{s(u),u=0}))}}},at=Ve({type:ne([Number,Function]),required:!0}),st=Ve({type:Number}),it=Ve({type:Number,default:2}),dn=Ve({type:String,values:["ltr","rtl"],default:"ltr"}),rt=Ve({type:Number,default:0}),Ze=Ve({type:Number,required:!0}),Jt=Ve({type:String,values:["horizontal","vertical"],default:ct}),xt=Ee({className:{type:String,default:""},containerElement:{type:ne([String,Object]),default:"div"},data:{type:ne(Array),default:()=>Sl([])},direction:dn,height:{type:[String,Number],required:!0},innerElement:{type:[String,Object],default:"div"},style:{type:ne([Object,String,Array])},useIsScrolling:{type:Boolean,default:!1},width:{type:[Number,String],required:!1},perfMode:{type:Boolean,default:!0},scrollbarAlwaysOn:{type:Boolean,default:!1}}),cn=Ee({cache:it,estimatedItemSize:st,layout:Jt,initScrollOffset:rt,total:Ze,itemSize:at,...xt}),ut={type:Number,default:6},_t={type:Number,default:0},el={type:Number,default:2};Ee({columnCache:it,columnWidth:at,estimatedColumnWidth:st,estimatedRowHeight:st,initScrollLeft:rt,initScrollTop:rt,itemKey:{type:ne(Function),default:({columnIndex:e,rowIndex:t})=>`${t}:${e}`},rowCache:it,rowHeight:at,totalColumn:Ze,totalRow:Ze,hScrollbarSize:ut,vScrollbarSize:ut,scrollbarStartGap:_t,scrollbarEndGap:el,role:String,...xt});const fn=Ee({alwaysOn:Boolean,class:String,layout:Jt,total:Ze,ratio:{type:Number,required:!0},clientSize:{type:Number,required:!0},scrollFrom:{type:Number,required:!0},scrollbarSize:ut,startGap:_t,endGap:el,visible:Boolean}),lt=(e,t)=>e<t?Gt:qt,De=e=>e===on||e===Ue||e===Me;let Oe=null;function kt(e=!1){if(Oe===null||e){const t=document.createElement("div"),o=t.style;o.width="50px",o.height="50px",o.overflow="scroll",o.direction="rtl";const s=document.createElement("div"),i=s.style;return i.width="100px",i.height="100px",t.appendChild(s),document.body.appendChild(t),t.scrollLeft>0?Oe=Yt:(t.scrollLeft=1,t.scrollLeft===0?Oe=ot:Oe=Xt),document.body.removeChild(t),Oe}return Oe}function pn({move:e,size:t,bar:o},s){const i={},u=`translate${o.axis}(${e}px)`;return i[o.size]=t,i.transform=u,i.msTransform=u,i.webkitTransform=u,s==="horizontal"?i.height="100%":i.width="100%",i}const mn=ze({name:"ElVirtualScrollBar",props:fn,emits:["scroll","start-move","stop-move"],setup(e,{emit:t}){const o=h(()=>e.startGap+e.endGap),s=be("virtual-scrollbar"),i=be("scrollbar"),u=B(),r=B();let p=null,f=null;const b=qe({isDragging:!1,traveled:0}),c=h(()=>Yl[e.layout]),n=h(()=>e.clientSize-C(o)),a=h(()=>({position:"absolute",width:`${Me===e.layout?n.value:e.scrollbarSize}px`,height:`${Me===e.layout?e.scrollbarSize:n.value}px`,[an[e.layout]]:"2px",right:"2px",bottom:"2px",borderRadius:"4px"})),V=h(()=>{const v=e.ratio,D=e.clientSize;if(v>=100)return Number.POSITIVE_INFINITY;if(v>=50)return v*D/100;const U=D/3;return Math.floor(Math.min(Math.max(v*D,sn),U))}),N=h(()=>{if(!Number.isFinite(V.value))return{display:"none"};const v=`${V.value}px`;return pn({bar:c.value,size:v,move:b.traveled},e.layout)}),T=h(()=>Math.floor(e.clientSize-V.value-C(o))),j=()=>{window.addEventListener("mousemove",w),window.addEventListener("mouseup",Q);const v=C(r);v&&(f=document.onselectstart,document.onselectstart=()=>!1,v.addEventListener("touchmove",w),v.addEventListener("touchend",Q))},F=()=>{window.removeEventListener("mousemove",w),window.removeEventListener("mouseup",Q),document.onselectstart=f,f=null;const v=C(r);v&&(v.removeEventListener("touchmove",w),v.removeEventListener("touchend",Q))},J=v=>{v.stopImmediatePropagation(),!(v.ctrlKey||[1,2].includes(v.button))&&(b.isDragging=!0,b[c.value.axis]=v.currentTarget[c.value.offset]-(v[c.value.client]-v.currentTarget.getBoundingClientRect()[c.value.direction]),t("start-move"),j())},Q=()=>{b.isDragging=!1,b[c.value.axis]=0,t("stop-move"),F()},w=v=>{const{isDragging:D}=b;if(!D||!r.value||!u.value)return;const U=b[c.value.axis];if(!U)return;Kt(p);const oe=(u.value.getBoundingClientRect()[c.value.direction]-v[c.value.client])*-1,Z=r.value[c.value.offset]-U,y=oe-Z;p=Ut(()=>{b.traveled=Math.max(e.startGap,Math.min(y,T.value)),t("scroll",y,T.value)})},L=v=>{const D=Math.abs(v.target.getBoundingClientRect()[c.value.direction]-v[c.value.client]),U=r.value[c.value.offset]/2,oe=D-U;b.traveled=Math.max(0,Math.min(oe,T.value)),t("scroll",oe,T.value)};return Se(()=>e.scrollFrom,v=>{b.isDragging||(b.traveled=Math.ceil(v*T.value))}),Vl(()=>{F()}),()=>Ce("div",{role:"presentation",ref:u,class:[s.b(),e.class,(e.alwaysOn||b.isDragging)&&"always-on"],style:a.value,onMousedown:le(L,["stop","prevent"]),onTouchstartPrevent:J},Ce("div",{ref:r,class:i.e("thumb"),style:N.value,onMousedown:J},[]))}}),tl=({name:e,getOffset:t,getItemSize:o,getItemOffset:s,getEstimatedTotalSize:i,getStartIndexForOffset:u,getStopIndexForStartIndex:r,initCache:p,clearCache:f,validateProps:b})=>ze({name:e??"ElVirtualList",props:cn,emits:[Rt,Nt],setup(c,{emit:n,expose:a}){b(c);const V=At(),N=be("vl"),T=B(p(c,V)),j=ln(),F=B(),J=B(),Q=B(),w=B({isScrolling:!1,scrollDir:"forward",scrollOffset:Fe(c.initScrollOffset)?c.initScrollOffset:0,updateRequested:!1,isScrollbarDragging:!1,scrollbarAlwaysOn:c.scrollbarAlwaysOn}),L=h(()=>{const{total:m,cache:I}=c,{isScrolling:M,scrollDir:P,scrollOffset:z}=C(w);if(m===0)return[0,0,0,0];const H=u(c,z,C(T)),W=r(c,H,z,C(T)),ve=!M||P===qt?Math.max(1,I):1,pe=!M||P===Gt?Math.max(1,I):1;return[Math.max(0,H-ve),Math.max(0,Math.min(m-1,W+pe)),H,W]}),v=h(()=>i(c,C(T))),D=h(()=>De(c.layout)),U=h(()=>[{position:"relative",[`overflow-${D.value?"x":"y"}`]:"scroll",WebkitOverflowScrolling:"touch",willChange:"transform"},{direction:c.direction,height:Fe(c.height)?`${c.height}px`:c.height,width:Fe(c.width)?`${c.width}px`:c.width},c.style]),oe=h(()=>{const m=C(v),I=C(D);return{height:I?"100%":`${m}px`,pointerEvents:C(w).isScrolling?"none":void 0,width:I?`${m}px`:"100%"}}),Z=h(()=>D.value?c.width:c.height),{onWheel:y}=un({atStartEdge:h(()=>w.value.scrollOffset<=0),atEndEdge:h(()=>w.value.scrollOffset>=v.value),layout:h(()=>c.layout)},m=>{var I,M;(M=(I=Q.value).onMouseUp)==null||M.call(I),A(Math.min(w.value.scrollOffset+m,v.value-Z.value))}),S=()=>{const{total:m}=c;if(m>0){const[z,H,W,ve]=C(L);n(Rt,z,H,W,ve)}const{scrollDir:I,scrollOffset:M,updateRequested:P}=C(w);n(Nt,I,M,P)},k=m=>{const{clientHeight:I,scrollHeight:M,scrollTop:P}=m.currentTarget,z=C(w);if(z.scrollOffset===P)return;const H=Math.max(0,Math.min(P,M-I));w.value={...z,isScrolling:!0,scrollDir:lt(z.scrollOffset,H),scrollOffset:H,updateRequested:!1},ae(ue)},G=m=>{const{clientWidth:I,scrollLeft:M,scrollWidth:P}=m.currentTarget,z=C(w);if(z.scrollOffset===M)return;const{direction:H}=c;let W=M;if(H===Ue)switch(kt()){case ot:{W=-M;break}case Yt:{W=P-I-M;break}}W=Math.max(0,Math.min(W,P-I)),w.value={...z,isScrolling:!0,scrollDir:lt(z.scrollOffset,W),scrollOffset:W,updateRequested:!1},ae(ue)},q=m=>{C(D)?G(m):k(m),S()},$=(m,I)=>{const M=(v.value-Z.value)/I*m;A(Math.min(v.value-Z.value,M))},A=m=>{m=Math.max(m,0),m!==C(w).scrollOffset&&(w.value={...C(w),scrollOffset:m,scrollDir:lt(C(w).scrollOffset,m),updateRequested:!0},ae(ue))},x=(m,I=Be)=>{const{scrollOffset:M}=C(w);m=Math.max(0,Math.min(m,c.total-1)),A(t(c,m,I,M,C(T)))},fe=m=>{const{direction:I,itemSize:M,layout:P}=c,z=j.value(f&&M,f&&P,f&&I);let H;if(Ol(z,String(m)))H=z[m];else{const W=s(c,m,C(T)),ve=o(c,m,C(T)),pe=C(D),we=I===Ue,Le=pe?W:0;z[m]=H={position:"absolute",left:we?void 0:`${Le}px`,right:we?`${Le}px`:void 0,top:pe?0:`${W}px`,height:pe?"100%":`${ve}px`,width:pe?`${ve}px`:"100%"}}return H},ue=()=>{w.value.isScrolling=!1,ae(()=>{j.value(-1,null,null)})},Y=()=>{const m=F.value;m&&(m.scrollTop=0)};Ft(()=>{if(!wl)return;const{initScrollOffset:m}=c,I=C(F);Fe(m)&&I&&(C(D)?I.scrollLeft=m:I.scrollTop=m),S()}),Il(()=>{const{direction:m,layout:I}=c,{scrollOffset:M,updateRequested:P}=C(w),z=C(F);if(P&&z)if(I===Me)if(m===Ue)switch(kt()){case ot:{z.scrollLeft=-M;break}case Xt:{z.scrollLeft=M;break}default:{const{clientWidth:H,scrollWidth:W}=z;z.scrollLeft=W-H-M;break}}else z.scrollLeft=M;else z.scrollTop=M});const _={ns:N,clientSize:Z,estimatedTotalSize:v,windowStyle:U,windowRef:F,innerRef:J,innerStyle:oe,itemsToRender:L,scrollbarRef:Q,states:w,getItemStyle:fe,onScroll:q,onScrollbarScroll:$,onWheel:y,scrollTo:A,scrollToItem:x,resetScrollTop:Y};return a({windowRef:F,innerRef:J,getItemStyleCache:j,scrollTo:A,scrollToItem:x,resetScrollTop:Y,states:w}),_},render(c){var n;const{$slots:a,className:V,clientSize:N,containerElement:T,data:j,getItemStyle:F,innerElement:J,itemsToRender:Q,innerStyle:w,layout:L,total:v,onScroll:D,onScrollbarScroll:U,onWheel:oe,states:Z,useIsScrolling:y,windowStyle:S,ns:k}=c,[G,q]=Q,$=ke(T),A=ke(J),x=[];if(v>0)for(let _=G;_<=q;_++)x.push((n=a.default)==null?void 0:n.call(a,{data:j,key:_,index:_,isScrolling:y?Z.isScrolling:void 0,style:F(_)}));const fe=[Ce(A,{style:w,ref:"innerRef"},Ot(A)?x:{default:()=>x})],ue=Ce(mn,{ref:"scrollbarRef",clientSize:N,layout:L,onScroll:U,ratio:N*100/this.estimatedTotalSize,scrollFrom:Z.scrollOffset/(this.estimatedTotalSize-N),total:v}),Y=Ce($,{class:[k.e("window"),V],style:S,onScroll:D,onWheel:oe,ref:"windowRef",key:0},Ot($)?[fe]:{default:()=>[fe]});return Ce("div",{key:0,class:[k.e("wrapper"),Z.scrollbarAlwaysOn?"always-on":""]},[Y,ue])}}),vn=tl({name:"ElFixedSizeList",getItemOffset:({itemSize:e},t)=>t*e,getItemSize:({itemSize:e})=>e,getEstimatedTotalSize:({total:e,itemSize:t})=>t*e,getOffset:({height:e,total:t,itemSize:o,layout:s,width:i},u,r,p)=>{const f=De(s)?i:e,b=Math.max(0,t*o-f),c=Math.min(b,u*o),n=Math.max(0,(u+1)*o-f);switch(r===jt&&(p>=n-f&&p<=c+f?r=Be:r=Qe),r){case Qt:return c;case Zt:return n;case Qe:{const a=Math.round(n+(c-n)/2);return a<Math.ceil(f/2)?0:a>b+Math.floor(f/2)?b:a}case Be:default:return p>=n&&p<=c?p:p<n?n:c}},getStartIndexForOffset:({total:e,itemSize:t},o)=>Math.max(0,Math.min(e-1,Math.floor(o/t))),getStopIndexForStartIndex:({height:e,total:t,itemSize:o,layout:s,width:i},u,r)=>{const p=u*o,f=De(s)?i:e,b=Math.ceil((f+r-p)/o);return Math.max(0,Math.min(t-1,u+b-1))},initCache(){},clearCache:!0,validateProps(){}}),Te=(e,t,o)=>{const{itemSize:s}=e,{items:i,lastVisitedIndex:u}=o;if(t>u){let r=0;if(u>=0){const p=i[u];r=p.offset+p.size}for(let p=u+1;p<=t;p++){const f=s(p);i[p]={offset:r,size:f},r+=f}o.lastVisitedIndex=t}return i[t]},hn=(e,t,o)=>{const{items:s,lastVisitedIndex:i}=t;return(i>0?s[i].offset:0)>=o?ll(e,t,0,i,o):gn(e,t,Math.max(0,i),o)},ll=(e,t,o,s,i)=>{for(;o<=s;){const u=o+Math.floor((s-o)/2),r=Te(e,u,t).offset;if(r===i)return u;r<i?o=u+1:r>i&&(s=u-1)}return Math.max(0,o-1)},gn=(e,t,o,s)=>{const{total:i}=e;let u=1;for(;o<i&&Te(e,o,t).offset<s;)o+=u,u*=2;return ll(e,t,Math.floor(o/2),Math.min(o,i-1),s)},Bt=({total:e},{items:t,estimatedItemSize:o,lastVisitedIndex:s})=>{let i=0;if(s>=e&&(s=e-1),s>=0){const p=t[s];i=p.offset+p.size}const r=(e-s-1)*o;return i+r},bn=tl({name:"ElDynamicSizeList",getItemOffset:(e,t,o)=>Te(e,t,o).offset,getItemSize:(e,t,{items:o})=>o[t].size,getEstimatedTotalSize:Bt,getOffset:(e,t,o,s,i)=>{const{height:u,layout:r,width:p}=e,f=De(r)?p:u,b=Te(e,t,i),c=Bt(e,i),n=Math.max(0,Math.min(c-f,b.offset)),a=Math.max(0,b.offset-f+b.size);switch(o===jt&&(s>=a-f&&s<=n+f?o=Be:o=Qe),o){case Qt:return n;case Zt:return a;case Qe:return Math.round(a+(n-a)/2);case Be:default:return s>=a&&s<=n?s:s<a?a:n}},getStartIndexForOffset:(e,t,o)=>hn(e,o,t),getStopIndexForStartIndex:(e,t,o,s)=>{const{height:i,total:u,layout:r,width:p}=e,f=De(r)?p:i,b=Te(e,t,s),c=o+f;let n=b.offset+b.size,a=t;for(;a<u-1&&n<c;)a++,n+=Te(e,a,s).size;return a},initCache({estimatedItemSize:e=nn},t){const o={items:{},estimatedItemSize:e,lastVisitedIndex:-1};return o.clearCacheAfterIndex=(s,i=!0)=>{var u,r;o.lastVisitedIndex=Math.min(o.lastVisitedIndex,s-1),(u=t.exposed)==null||u.getItemStyleCache(-1),i&&((r=t.proxy)==null||r.$forceUpdate())},o},clearCache:!1,validateProps:({itemSize:e})=>{}}),yn=ze({props:{item:{type:Object,required:!0},style:Object,height:Number},setup(){return{ns:be("select")}}});function Sn(e,t,o,s,i,u){return e.item.isTitle?(R(),K("div",{key:0,class:E(e.ns.be("group","title")),style:ce([e.style,{lineHeight:`${e.height}px`}])},ie(e.item.label),7)):(R(),K("div",{key:1,class:E(e.ns.be("group","split")),style:ce(e.style)},[X("span",{class:E(e.ns.be("group","split-dash")),style:ce({top:`${e.height/2}px`})},null,6)],6))}var Vn=dt(yn,[["render",Sn],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/select-v2/src/group-item.vue"]]);function wn(e,{emit:t}){return{hoverItem:()=>{e.disabled||t("hover",e.index)},selectOptionClick:()=>{e.disabled||t("select",e.item,e.index)}}}const nl={label:"label",value:"value",disabled:"disabled",options:"options"};function Xe(e){const t=h(()=>({...nl,...e.props}));return{aliasProps:t,getLabel:r=>re(r,t.value.label),getValue:r=>re(r,t.value.value),getDisabled:r=>re(r,t.value.disabled),getOptions:r=>re(r,t.value.options)}}const In=Ee({allowCreate:Boolean,autocomplete:{type:ne(String),default:"none"},automaticDropdown:Boolean,clearable:Boolean,clearIcon:{type:Cl,default:Tl},effect:{type:ne(String),default:"light"},collapseTags:Boolean,collapseTagsTooltip:{type:Boolean,default:!1},maxCollapseTags:{type:Number,default:1},defaultFirstOption:Boolean,disabled:Boolean,estimatedOptionHeight:{type:Number,default:void 0},filterable:Boolean,filterMethod:Function,height:{type:Number,default:170},itemHeight:{type:Number,default:34},id:String,loading:Boolean,loadingText:String,label:String,modelValue:{type:ne([Array,String,Number,Boolean,Object])},multiple:Boolean,multipleLimit:{type:Number,default:0},name:String,noDataText:String,noMatchText:String,remoteMethod:Function,reserveKeyword:{type:Boolean,default:!0},options:{type:ne(Array),required:!0},placeholder:{type:String},teleported:jl.teleported,persistent:{type:Boolean,default:!0},popperClass:{type:String,default:""},popperOptions:{type:ne(Object),default:()=>({})},remote:Boolean,size:Ml,props:{type:ne(Object),default:()=>nl},valueKey:{type:String,default:"value"},scrollbarAlwaysOn:{type:Boolean,default:!1},validateEvent:{type:Boolean,default:!0},placement:{type:ne(String),values:Ql,default:"bottom-start"}}),On=Ee({data:Array,disabled:Boolean,hovering:Boolean,item:{type:ne(Object),required:!0},index:Number,style:Object,selected:Boolean,created:Boolean}),ft=Symbol("ElSelectV2Injection"),Cn=ze({props:On,emits:["select","hover"],setup(e,{emit:t}){const o=Pt(ft),s=be("select"),{hoverItem:i,selectOptionClick:u}=wn(e,{emit:t}),{getLabel:r}=Xe(o.props);return{ns:s,hoverItem:i,selectOptionClick:u,getLabel:r}}}),Tn=["aria-selected"];function Mn(e,t,o,s,i,u){return R(),K("li",{"aria-selected":e.selected,style:ce(e.style),class:E([e.ns.be("dropdown","option-item"),e.ns.is("selected",e.selected),e.ns.is("disabled",e.disabled),e.ns.is("created",e.created),{hover:e.hovering}]),onMouseenter:t[0]||(t[0]=(...r)=>e.hoverItem&&e.hoverItem(...r)),onClick:t[1]||(t[1]=le((...r)=>e.selectOptionClick&&e.selectOptionClick(...r),["stop"]))},[We(e.$slots,"default",{item:e.item,index:e.index,disabled:e.disabled},()=>[X("span",null,ie(e.getLabel(e.item)),1)])],46,Tn)}var En=dt(Cn,[["render",Mn],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/select-v2/src/option-item.vue"]]),zn=ze({name:"ElSelectDropdown",props:{data:{type:Array,required:!0},hoveringIndex:Number,width:Number},setup(e,{slots:t,expose:o}){const s=Pt(ft),i=be("select"),{getLabel:u,getValue:r,getDisabled:p}=Xe(s.props),f=B([]),b=B(),c=h(()=>e.data.length);Se(()=>c.value,()=>{var y,S;(S=(y=s.popper.value).updatePopper)==null||S.call(y)});const n=h(()=>El(s.props.estimatedOptionHeight)),a=h(()=>n.value?{itemSize:s.props.itemHeight}:{estimatedSize:s.props.estimatedOptionHeight,itemSize:y=>f.value[y]}),V=(y=[],S)=>{const{props:{valueKey:k}}=s;return je(S)?y&&y.some(G=>zl(re(G,k))===re(S,k)):y.includes(S)},N=(y,S)=>{if(je(S)){const{valueKey:k}=s.props;return re(y,k)===re(S,k)}else return y===S},T=(y,S)=>s.props.multiple?V(y,r(S)):N(y,r(S)),j=(y,S)=>{const{disabled:k,multiple:G,multipleLimit:q}=s.props;return k||!S&&(G?q>0&&y.length>=q:!1)},F=y=>e.hoveringIndex===y;o({listRef:b,isSized:n,isItemDisabled:j,isItemHovering:F,isItemSelected:T,scrollToItem:y=>{const S=b.value;S&&S.scrollToItem(y)},resetScrollTop:()=>{const y=b.value;y&&y.resetScrollTop()}});const w=y=>{const{index:S,data:k,style:G}=y,q=C(n),{itemSize:$,estimatedSize:A}=C(a),{modelValue:x}=s.props,{onSelect:fe,onHover:ue}=s,Y=k[S];if(Y.type==="Group")return se(Vn,{item:Y,style:G,height:q?$:A},null);const _=T(x,Y),m=j(x,_),I=F(S);return se(En,Ct(y,{selected:_,disabled:p(Y)||m,created:!!Y.created,hovering:I,item:Y,onSelect:fe,onHover:ue}),{default:M=>{var P;return((P=t.default)==null?void 0:P.call(t,M))||se("span",null,[u(Y)])}})},{onKeyboardNavigate:L,onKeyboardSelect:v}=s,D=()=>{L("forward")},U=()=>{L("backward")},oe=()=>{s.expanded=!1},Z=y=>{const{code:S}=y,{tab:k,esc:G,down:q,up:$,enter:A}=Ll;switch(S!==k&&(y.preventDefault(),y.stopPropagation()),S){case k:case G:{oe();break}case q:{D();break}case $:{U();break}case A:{v();break}}};return()=>{var y;const{data:S,width:k}=e,{height:G,multiple:q,scrollbarAlwaysOn:$}=s.props;if(S.length===0)return se("div",{class:i.b("dropdown"),style:{width:`${k}px`}},[(y=t.empty)==null?void 0:y.call(t)]);const A=C(n)?vn:bn;return se("div",{class:[i.b("dropdown"),i.is("multiple",q)]},[se(A,Ct({ref:b},C(a),{className:i.be("dropdown","list"),scrollbarAlwaysOn:$,data:S,height:G,width:k,total:S.length,onKeydown:Z}),{default:x=>se(w,x,null)})])}}});function Ln(e,t){const{aliasProps:o,getLabel:s,getValue:i}=Xe(e),u=B(0),r=B(null),p=h(()=>e.allowCreate&&e.filterable);function f(V){const N=T=>i(T)===V;return e.options&&e.options.some(N)||t.createdOptions.some(N)}function b(V){p.value&&(e.multiple&&V.created?u.value++:r.value=V)}function c(V){if(p.value)if(V&&V.length>0&&!f(V)){const N={[o.value.value]:V,[o.value.label]:V,created:!0,[o.value.disabled]:!1};t.createdOptions.length>=u.value?t.createdOptions[u.value]=N:t.createdOptions.push(N)}else if(e.multiple)t.createdOptions.length=u.value;else{const N=r.value;t.createdOptions.length=0,N&&N.created&&t.createdOptions.push(N)}}function n(V){if(!p.value||!V||!V.created||V.created&&e.reserveKeyword&&t.inputValue===s(V))return;const N=t.createdOptions.findIndex(T=>i(T)===i(V));~N&&(t.createdOptions.splice(N,1),u.value--)}function a(){p.value&&(t.createdOptions.length=0,u.value=0)}return{createNewOption:c,removeNewOption:n,selectNewOption:b,clearAllNewOption:a}}function Rn(e){const t=B(!1);return{handleCompositionStart:()=>{t.value=!0},handleCompositionUpdate:u=>{const r=u.target.value,p=r[r.length-1]||"";t.value=!Rl(p)},handleCompositionEnd:u=>{t.value&&(t.value=!1,nt(e)&&e(u))}}}const Dt="",$t=11,Nn={larget:51,default:42,small:33},kn=(e,t)=>{const{t:o}=Nl(),s=be("select-v2"),i=be("input"),{form:u,formItem:r}=kl(),{getLabel:p,getValue:f,getDisabled:b,getOptions:c}=Xe(e),n=qe({inputValue:Dt,displayInputValue:Dt,calculatedWidth:0,cachedPlaceholder:"",cachedOptions:[],createdOptions:[],createdLabel:"",createdSelected:!1,currentPlaceholder:"",hoveringIndex:-1,comboBoxHovering:!1,isOnComposition:!1,isSilentBlur:!1,isComposing:!1,inputLength:20,selectWidth:200,initialInputHeight:0,previousQuery:null,previousValue:void 0,query:"",selectedLabel:"",softFocus:!1,tagInMultiLine:!1}),a=B(-1),V=B(-1),N=B(null),T=B(null),j=B(null),F=B(null),J=B(null),Q=B(null),w=B(null),L=B(!1),v=h(()=>e.disabled||(u==null?void 0:u.disabled)),D=h(()=>{const l=$.value.length*34;return l>e.height?e.height:l}),U=h(()=>!Tt(e.modelValue)),oe=h(()=>{const l=e.multiple?Array.isArray(e.modelValue)&&e.modelValue.length>0:U.value;return e.clearable&&!v.value&&n.comboBoxHovering&&l}),Z=h(()=>e.remote&&e.filterable?"":Bl),y=h(()=>Z.value&&s.is("reverse",L.value)),S=h(()=>(r==null?void 0:r.validateState)||""),k=h(()=>Dl[S.value]),G=h(()=>e.remote?300:0),q=h(()=>{const l=$.value;return e.loading?e.loadingText||o("el.select.loading"):e.remote&&n.inputValue===""&&l.length===0?!1:e.filterable&&n.inputValue&&l.length>0?e.noMatchText||o("el.select.noMatch"):l.length===0?e.noDataText||o("el.select.noData"):null}),$=h(()=>{const l=d=>{const g=n.inputValue,O=new RegExp(Jl(g),"i");return g?O.test(p(d)||""):!0};return e.loading?[]:[...e.options,...n.createdOptions].reduce((d,g)=>{const O=c(g);if(Ke(O)){const ee=O.filter(l);ee.length>0&&d.push({label:p(g),isTitle:!0,type:"Group"},...ee,{type:"Group"})}else(e.remote||l(g))&&d.push(g);return d},[])}),A=h(()=>{const l=new Map;return $.value.forEach((d,g)=>{l.set(Ie(f(d)),{option:d,index:g})}),l}),x=h(()=>$.value.every(l=>b(l))),fe=$l(),ue=h(()=>fe.value==="small"?"small":"default"),Y=h(()=>{const l=Q.value,d=ue.value||"default",g=l?Number.parseInt(getComputedStyle(l).paddingLeft):0,O=l?Number.parseInt(getComputedStyle(l).paddingRight):0;return n.selectWidth-O-g-Nn[d]}),_=()=>{var l;V.value=((l=J.value)==null?void 0:l.offsetWidth)||200},m=h(()=>({width:`${n.calculatedWidth===0?$t:Math.ceil(n.calculatedWidth)+$t}px`})),I=h(()=>Ke(e.modelValue)?e.modelValue.length===0&&!n.displayInputValue:e.filterable?n.displayInputValue.length===0:!0),M=h(()=>{const l=e.placeholder||o("el.select.placeholder");return e.multiple||Tt(e.modelValue)?l:n.selectedLabel}),P=h(()=>{var l,d;return(d=(l=F.value)==null?void 0:l.popperRef)==null?void 0:d.contentRef}),z=h(()=>{if(e.multiple){const l=e.modelValue.length;if(e.modelValue.length>0&&A.value.has(e.modelValue[l-1])){const{index:d}=A.value.get(e.modelValue[l-1]);return d}}else if(e.modelValue&&A.value.has(e.modelValue)){const{index:l}=A.value.get(e.modelValue);return l}return-1}),H=h({get(){return L.value&&q.value!==!1},set(l){L.value=l}}),W=h(()=>n.cachedOptions.slice(0,e.maxCollapseTags)),ve=h(()=>n.cachedOptions.slice(e.maxCollapseTags)),{createNewOption:pe,removeNewOption:we,selectNewOption:Le,clearAllNewOption:Ye}=Ln(e,n),{handleCompositionStart:ol,handleCompositionUpdate:al,handleCompositionEnd:sl}=Rn(l=>It(l)),pt=()=>{var l,d,g;(d=(l=T.value)==null?void 0:l.focus)==null||d.call(l),(g=F.value)==null||g.updatePopper()},Je=()=>{if(!e.automaticDropdown&&!v.value)return n.isComposing&&(n.softFocus=!0),ae(()=>{var l,d;L.value=!L.value,(d=(l=T.value)==null?void 0:l.focus)==null||d.call(l)})},mt=()=>(e.filterable&&n.inputValue!==n.selectedLabel&&(n.query=n.selectedLabel),ht(n.inputValue),ae(()=>{pe(n.inputValue)})),vt=Al(mt,G.value),ht=l=>{n.previousQuery!==l&&(n.previousQuery=l,e.filterable&&nt(e.filterMethod)?e.filterMethod(l):e.filterable&&e.remote&&nt(e.remoteMethod)&&e.remoteMethod(l))},il=l=>{zt(e.modelValue,l)||t(Wt,l)},Re=l=>{t(Ht,l),il(l),n.previousValue=String(l)},gt=(l=[],d)=>{if(!je(d))return l.indexOf(d);const g=e.valueKey;let O=-1;return l.some((ee,de)=>re(ee,g)===re(d,g)?(O=de,!0):!1),O},Ie=l=>je(l)?re(l,e.valueKey):l,xe=()=>ae(()=>{var l,d;if(!T.value)return;const g=Q.value;J.value.height=g.offsetHeight,L.value&&q.value!==!1&&((d=(l=F.value)==null?void 0:l.updatePopper)==null||d.call(l))}),bt=()=>{var l,d;if(rl(),_(),(d=(l=F.value)==null?void 0:l.updatePopper)==null||d.call(l),e.multiple)return xe()},rl=()=>{const l=Q.value;l&&(n.selectWidth=l.getBoundingClientRect().width)},yt=(l,d,g=!0)=>{var O,ee;if(e.multiple){let de=e.modelValue.slice();const Ae=gt(de,f(l));Ae>-1?(de=[...de.slice(0,Ae),...de.slice(Ae+1)],n.cachedOptions.splice(Ae,1),we(l)):(e.multipleLimit<=0||de.length<e.multipleLimit)&&(de=[...de,f(l)],n.cachedOptions.push(l),Le(l),ye(d)),Re(de),l.created&&(n.query="",ht(""),n.inputLength=20),e.filterable&&!e.reserveKeyword&&((ee=(O=T.value).focus)==null||ee.call(O),$e("")),e.filterable&&(n.calculatedWidth=w.value.getBoundingClientRect().width),xe(),vl()}else a.value=d,n.selectedLabel=p(l),Re(f(l)),L.value=!1,n.isComposing=!1,n.isSilentBlur=g,Le(l),l.created||Ye(),ye(d)},ul=(l,d)=>{let g=e.modelValue.slice();const O=gt(g,f(d));if(O>-1&&!v.value)return g=[...e.modelValue.slice(0,O),...e.modelValue.slice(O+1)],n.cachedOptions.splice(O,1),Re(g),t("remove-tag",f(d)),n.softFocus=!0,we(d),ae(pt);l.stopPropagation()},dl=l=>{const d=n.isComposing;n.isComposing=!0,n.softFocus?n.softFocus=!1:d||t("focus",l)},St=l=>(n.softFocus=!1,ae(()=>{var d,g;(g=(d=T.value)==null?void 0:d.blur)==null||g.call(d),w.value&&(n.calculatedWidth=w.value.getBoundingClientRect().width),n.isSilentBlur?n.isSilentBlur=!1:n.isComposing&&t("blur",l),n.isComposing=!1})),cl=()=>{n.displayInputValue.length>0?$e(""):L.value=!1},fl=l=>{if(n.displayInputValue.length===0){l.preventDefault();const d=e.modelValue.slice();d.pop(),we(n.cachedOptions.pop()),Re(d)}},pl=()=>{let l;return Ke(e.modelValue)?l=[]:l=void 0,n.softFocus=!0,e.multiple?n.cachedOptions=[]:n.selectedLabel="",L.value=!1,Re(l),t("clear"),Ye(),ae(pt)},$e=l=>{n.displayInputValue=l,n.inputValue=l},Vt=(l,d=void 0)=>{const g=$.value;if(!["forward","backward"].includes(l)||v.value||g.length<=0||x.value)return;if(!L.value)return Je();d===void 0&&(d=n.hoveringIndex);let O=-1;l==="forward"?(O=d+1,O>=g.length&&(O=0)):l==="backward"&&(O=d-1,(O<0||O>=g.length)&&(O=g.length-1));const ee=g[O];if(b(ee)||ee.type==="Group")return Vt(l,O);ye(O),_e(O)},ml=()=>{if(L.value)~n.hoveringIndex&&$.value[n.hoveringIndex]&&yt($.value[n.hoveringIndex],n.hoveringIndex,!1);else return Je()},ye=l=>{n.hoveringIndex=l},wt=()=>{n.hoveringIndex=-1},vl=()=>{var l;const d=T.value;d&&((l=d.focus)==null||l.call(d))},It=l=>{const d=l.target.value;if($e(d),n.displayInputValue.length>0&&!L.value&&(L.value=!0),n.calculatedWidth=w.value.getBoundingClientRect().width,e.multiple&&xe(),e.remote)vt();else return mt()},hl=()=>(L.value=!1,St()),gl=()=>(n.inputValue=n.displayInputValue,ae(()=>{~z.value&&(ye(z.value),_e(n.hoveringIndex))})),_e=l=>{j.value.scrollToItem(l)},et=()=>{if(wt(),e.multiple)if(e.modelValue.length>0){let l=!1;n.cachedOptions.length=0,n.previousValue=e.modelValue.toString();for(const d of e.modelValue){const g=Ie(d);if(A.value.has(g)){const{index:O,option:ee}=A.value.get(g);n.cachedOptions.push(ee),l||ye(O),l=!0}}}else n.cachedOptions=[],n.previousValue=void 0;else if(U.value){n.previousValue=e.modelValue;const l=$.value,d=l.findIndex(g=>Ie(f(g))===Ie(e.modelValue));~d?(n.selectedLabel=p(l[d]),ye(d)):n.selectedLabel=Ie(e.modelValue)}else n.selectedLabel="",n.previousValue=void 0;Ye(),_()};return Se(L,l=>{var d,g;t("visible-change",l),l?(g=(d=F.value).update)==null||g.call(d):(n.displayInputValue="",n.previousQuery=null,pe(""))}),Se(()=>e.modelValue,(l,d)=>{var g;(!l||l.toString()!==n.previousValue)&&et(),!zt(l,d)&&e.validateEvent&&((g=r==null?void 0:r.validate)==null||g.call(r,"change").catch(O=>Fl()))},{deep:!0}),Se(()=>e.options,()=>{const l=T.value;(!l||l&&document.activeElement!==l)&&et()},{deep:!0}),Se($,()=>j.value&&ae(j.value.resetScrollTop)),Se(()=>H.value,l=>{l||wt()}),Ft(()=>{et()}),Pl(J,bt),{collapseTagSize:ue,currentPlaceholder:M,expanded:L,emptyText:q,popupHeight:D,debounce:G,filteredOptions:$,iconComponent:Z,iconReverse:y,inputWrapperStyle:m,popperSize:V,dropdownMenuVisible:H,hasModelValue:U,shouldShowPlaceholder:I,selectDisabled:v,selectSize:fe,showClearBtn:oe,states:n,tagMaxWidth:Y,nsSelectV2:s,nsInput:i,calculatorRef:w,controlRef:N,inputRef:T,menuRef:j,popper:F,selectRef:J,selectionRef:Q,popperRef:P,validateState:S,validateIcon:k,showTagList:W,collapseTagList:ve,debouncedOnInputChange:vt,deleteTag:ul,getLabel:p,getValue:f,getDisabled:b,getValueKey:Ie,handleBlur:St,handleClear:pl,handleClickOutside:hl,handleDel:fl,handleEsc:cl,handleFocus:dl,handleMenuEnter:gl,handleResize:bt,toggleMenu:Je,scrollTo:_e,onInput:It,onKeyboardNavigate:Vt,onKeyboardSelect:ml,onSelect:yt,onHover:ye,onUpdateInputValue:$e,handleCompositionStart:ol,handleCompositionEnd:sl,handleCompositionUpdate:al}},Bn=ze({name:"ElSelectV2",components:{ElSelectMenu:zn,ElTag:Xl,ElTooltip:Zl,ElIcon:Hl},directives:{ClickOutside:xl,ModelText:Wl},props:In,emits:[Ht,Wt,"remove-tag","clear","visible-change","focus","blur"],setup(e,{emit:t}){const o=h(()=>{const{modelValue:i,multiple:u}=e,r=u?[]:void 0;return Ke(i)?u?i:r:u?r:i}),s=kn(qe({...Mt(e),modelValue:o}),t);return Kl(ft,{props:qe({...Mt(e),height:s.popupHeight,modelValue:o}),popper:s.popper,onSelect:s.onSelect,onHover:s.onHover,onKeyboardNavigate:s.onKeyboardNavigate,onKeyboardSelect:s.onKeyboardSelect}),{...s,modelValue:o}}}),Dn={key:0},$n=["id","autocomplete","aria-expanded","aria-labelledby","disabled","readonly","name","unselectable"],An=["textContent"],Fn=["id","aria-labelledby","aria-expanded","autocomplete","disabled","name","readonly","unselectable"],Pn=["textContent"];function Hn(e,t,o,s,i,u){const r=Pe("el-tag"),p=Pe("el-tooltip"),f=Pe("el-icon"),b=Pe("el-select-menu"),c=Et("model-text"),n=Et("click-outside");return He((R(),K("div",{ref:"selectRef",class:E([e.nsSelectV2.b(),e.nsSelectV2.m(e.selectSize)]),onClick:t[24]||(t[24]=le((...a)=>e.toggleMenu&&e.toggleMenu(...a),["stop"])),onMouseenter:t[25]||(t[25]=a=>e.states.comboBoxHovering=!0),onMouseleave:t[26]||(t[26]=a=>e.states.comboBoxHovering=!1)},[se(p,{ref:"popper",visible:e.dropdownMenuVisible,teleported:e.teleported,"popper-class":[e.nsSelectV2.e("popper"),e.popperClass],"gpu-acceleration":!1,"stop-popper-mouse-event":!1,"popper-options":e.popperOptions,"fallback-placements":["bottom-start","top-start","right","left"],effect:e.effect,placement:e.placement,pure:"",transition:`${e.nsSelectV2.namespace.value}-zoom-in-top`,trigger:"click",persistent:e.persistent,onBeforeShow:e.handleMenuEnter,onHide:t[23]||(t[23]=a=>e.states.inputValue=e.states.displayInputValue)},{default:te(()=>[X("div",{ref:"selectionRef",class:E([e.nsSelectV2.e("wrapper"),e.nsSelectV2.is("focused",e.states.isComposing||e.expanded),e.nsSelectV2.is("hovering",e.states.comboBoxHovering),e.nsSelectV2.is("filterable",e.filterable),e.nsSelectV2.is("disabled",e.selectDisabled)])},[e.$slots.prefix?(R(),K("div",Dn,[We(e.$slots,"prefix")])):he("v-if",!0),e.multiple?(R(),K("div",{key:1,class:E(e.nsSelectV2.e("selection"))},[e.collapseTags&&e.modelValue.length>0?(R(),K(Ne,{key:0},[(R(!0),K(Ne,null,tt(e.showTagList,a=>(R(),K("div",{key:e.getValueKey(e.getValue(a)),class:E(e.nsSelectV2.e("selected-item"))},[se(r,{closable:!e.selectDisabled&&!e.getDisabled(a),size:e.collapseTagSize,type:"info","disable-transitions":"",onClose:V=>e.deleteTag(V,a)},{default:te(()=>[X("span",{class:E(e.nsSelectV2.e("tags-text")),style:ce({maxWidth:`${e.tagMaxWidth}px`})},ie(e.getLabel(a)),7)]),_:2},1032,["closable","size","onClose"])],2))),128)),X("div",{class:E(e.nsSelectV2.e("selected-item"))},[e.modelValue.length>e.maxCollapseTags?(R(),ge(r,{key:0,closable:!1,size:e.collapseTagSize,type:"info","disable-transitions":""},{default:te(()=>[e.collapseTagsTooltip?(R(),ge(p,{key:0,disabled:e.dropdownMenuVisible,"fallback-placements":["bottom","top","right","left"],effect:e.effect,placement:"bottom",teleported:!1},{default:te(()=>[X("span",{class:E(e.nsSelectV2.e("tags-text")),style:ce({maxWidth:`${e.tagMaxWidth}px`})}," + "+ie(e.modelValue.length-e.maxCollapseTags),7)]),content:te(()=>[X("div",{class:E(e.nsSelectV2.e("selection"))},[(R(!0),K(Ne,null,tt(e.collapseTagList,a=>(R(),K("div",{key:e.getValueKey(e.getValue(a)),class:E(e.nsSelectV2.e("selected-item"))},[se(r,{closable:!e.selectDisabled&&!e.getDisabled(a),size:e.collapseTagSize,class:"in-tooltip",type:"info","disable-transitions":"",onClose:V=>e.deleteTag(V,a)},{default:te(()=>[X("span",{class:E(e.nsSelectV2.e("tags-text")),style:ce({maxWidth:`${e.tagMaxWidth}px`})},ie(e.getLabel(a)),7)]),_:2},1032,["closable","size","onClose"])],2))),128))],2)]),_:1},8,["disabled","effect"])):(R(),K("span",{key:1,class:E(e.nsSelectV2.e("tags-text")),style:ce({maxWidth:`${e.tagMaxWidth}px`})}," + "+ie(e.modelValue.length-e.maxCollapseTags),7))]),_:1},8,["size"])):he("v-if",!0)],2)],64)):(R(!0),K(Ne,{key:1},tt(e.states.cachedOptions,a=>(R(),K("div",{key:e.getValueKey(e.getValue(a)),class:E(e.nsSelectV2.e("selected-item"))},[se(r,{closable:!e.selectDisabled&&!e.getDisabled(a),size:e.collapseTagSize,type:"info","disable-transitions":"",onClose:V=>e.deleteTag(V,a)},{default:te(()=>[X("span",{class:E(e.nsSelectV2.e("tags-text")),style:ce({maxWidth:`${e.tagMaxWidth}px`})},ie(e.getLabel(a)),7)]),_:2},1032,["closable","size","onClose"])],2))),128)),X("div",{class:E([e.nsSelectV2.e("selected-item"),e.nsSelectV2.e("input-wrapper")]),style:ce(e.inputWrapperStyle)},[He(X("input",{id:e.id,ref:"inputRef",autocomplete:e.autocomplete,"aria-autocomplete":"list","aria-haspopup":"listbox",autocapitalize:"off","aria-expanded":e.expanded,"aria-labelledby":e.label,class:E([e.nsSelectV2.is(e.selectSize),e.nsSelectV2.e("combobox-input")]),disabled:e.disabled,role:"combobox",readonly:!e.filterable,spellcheck:"false",type:"text",name:e.name,unselectable:e.expanded?"on":void 0,"onUpdate:modelValue":t[0]||(t[0]=(...a)=>e.onUpdateInputValue&&e.onUpdateInputValue(...a)),onFocus:t[1]||(t[1]=(...a)=>e.handleFocus&&e.handleFocus(...a)),onBlur:t[2]||(t[2]=(...a)=>e.handleBlur&&e.handleBlur(...a)),onInput:t[3]||(t[3]=(...a)=>e.onInput&&e.onInput(...a)),onCompositionstart:t[4]||(t[4]=(...a)=>e.handleCompositionStart&&e.handleCompositionStart(...a)),onCompositionupdate:t[5]||(t[5]=(...a)=>e.handleCompositionUpdate&&e.handleCompositionUpdate(...a)),onCompositionend:t[6]||(t[6]=(...a)=>e.handleCompositionEnd&&e.handleCompositionEnd(...a)),onKeydown:[t[7]||(t[7]=me(le(a=>e.onKeyboardNavigate("backward"),["stop","prevent"]),["up"])),t[8]||(t[8]=me(le(a=>e.onKeyboardNavigate("forward"),["stop","prevent"]),["down"])),t[9]||(t[9]=me(le((...a)=>e.onKeyboardSelect&&e.onKeyboardSelect(...a),["stop","prevent"]),["enter"])),t[10]||(t[10]=me(le((...a)=>e.handleEsc&&e.handleEsc(...a),["stop","prevent"]),["esc"])),t[11]||(t[11]=me(le((...a)=>e.handleDel&&e.handleDel(...a),["stop"]),["delete"]))]},null,42,$n),[[c,e.states.displayInputValue]]),e.filterable?(R(),K("span",{key:0,ref:"calculatorRef","aria-hidden":"true",class:E(e.nsSelectV2.e("input-calculator")),textContent:ie(e.states.displayInputValue)},null,10,An)):he("v-if",!0)],6)],2)):(R(),K(Ne,{key:2},[X("div",{class:E([e.nsSelectV2.e("selected-item"),e.nsSelectV2.e("input-wrapper")])},[He(X("input",{id:e.id,ref:"inputRef","aria-autocomplete":"list","aria-haspopup":"listbox","aria-labelledby":e.label,"aria-expanded":e.expanded,autocapitalize:"off",autocomplete:e.autocomplete,class:E(e.nsSelectV2.e("combobox-input")),disabled:e.disabled,name:e.name,role:"combobox",readonly:!e.filterable,spellcheck:"false",type:"text",unselectable:e.expanded?"on":void 0,onCompositionstart:t[12]||(t[12]=(...a)=>e.handleCompositionStart&&e.handleCompositionStart(...a)),onCompositionupdate:t[13]||(t[13]=(...a)=>e.handleCompositionUpdate&&e.handleCompositionUpdate(...a)),onCompositionend:t[14]||(t[14]=(...a)=>e.handleCompositionEnd&&e.handleCompositionEnd(...a)),onFocus:t[15]||(t[15]=(...a)=>e.handleFocus&&e.handleFocus(...a)),onBlur:t[16]||(t[16]=(...a)=>e.handleBlur&&e.handleBlur(...a)),onInput:t[17]||(t[17]=(...a)=>e.onInput&&e.onInput(...a)),onKeydown:[t[18]||(t[18]=me(le(a=>e.onKeyboardNavigate("backward"),["stop","prevent"]),["up"])),t[19]||(t[19]=me(le(a=>e.onKeyboardNavigate("forward"),["stop","prevent"]),["down"])),t[20]||(t[20]=me(le((...a)=>e.onKeyboardSelect&&e.onKeyboardSelect(...a),["stop","prevent"]),["enter"])),t[21]||(t[21]=me(le((...a)=>e.handleEsc&&e.handleEsc(...a),["stop","prevent"]),["esc"]))],"onUpdate:modelValue":t[22]||(t[22]=(...a)=>e.onUpdateInputValue&&e.onUpdateInputValue(...a))},null,42,Fn),[[c,e.states.displayInputValue]])],2),e.filterable?(R(),K("span",{key:0,ref:"calculatorRef","aria-hidden":"true",class:E([e.nsSelectV2.e("selected-item"),e.nsSelectV2.e("input-calculator")]),textContent:ie(e.states.displayInputValue)},null,10,Pn)):he("v-if",!0)],64)),e.shouldShowPlaceholder?(R(),K("span",{key:3,class:E([e.nsSelectV2.e("placeholder"),e.nsSelectV2.is("transparent",e.multiple?e.modelValue.length===0:!e.hasModelValue)])},ie(e.currentPlaceholder),3)):he("v-if",!0),X("span",{class:E(e.nsSelectV2.e("suffix"))},[e.iconComponent?He((R(),ge(f,{key:0,class:E([e.nsSelectV2.e("caret"),e.nsInput.e("icon"),e.iconReverse])},{default:te(()=>[(R(),ge(ke(e.iconComponent)))]),_:1},8,["class"])),[[Ul,!e.showClearBtn]]):he("v-if",!0),e.showClearBtn&&e.clearIcon?(R(),ge(f,{key:1,class:E([e.nsSelectV2.e("caret"),e.nsInput.e("icon")]),onClick:le(e.handleClear,["prevent","stop"])},{default:te(()=>[(R(),ge(ke(e.clearIcon)))]),_:1},8,["class","onClick"])):he("v-if",!0),e.validateState&&e.validateIcon?(R(),ge(f,{key:2,class:E([e.nsInput.e("icon"),e.nsInput.e("validateIcon")])},{default:te(()=>[(R(),ge(ke(e.validateIcon)))]),_:1},8,["class"])):he("v-if",!0)],2)],2)]),content:te(()=>[se(b,{ref:"menuRef",data:e.filteredOptions,width:e.popperSize,"hovering-index":e.states.hoveringIndex,"scrollbar-always-on":e.scrollbarAlwaysOn},{default:te(a=>[We(e.$slots,"default",Gl(ql(a)))]),empty:te(()=>[We(e.$slots,"empty",{},()=>[X("p",{class:E(e.nsSelectV2.e("empty"))},ie(e.emptyText?e.emptyText:""),3)])]),_:3},8,["data","width","hovering-index","scrollbar-always-on"])]),_:3},8,["visible","teleported","popper-class","popper-options","effect","placement","transition","persistent","onBeforeShow"])],34)),[[n,e.handleClickOutside,e.popperRef]])}var Ge=dt(Bn,[["render",Hn],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/select-v2/src/select.vue"]]);Ge.install=e=>{e.component(Ge.name,Ge)};const Wn=Ge,Yn=Wn;export{Yn as E};
