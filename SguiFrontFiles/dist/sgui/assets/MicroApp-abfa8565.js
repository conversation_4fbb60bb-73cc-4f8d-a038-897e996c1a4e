import{ca as ve,cb as Z,cc as qr,cd as Kr,ce as Ze,cf as Yr,cg as Xr,ch as Ca,ci as zr,cj as fn,ck as dn,cl as Qr,cm as La,cn as Aa,co as <PERSON>,cp as <PERSON>,cq as Ia,cr as Ga,cs as Da,ct as ka,cu as Et,cv as Na,cw as ja,cx as Ha,cy as $a,cz as Fa,cA as Wa,cB as ee,cC as Ba,cD as Zr,cE as Me,cF as Va,cG as Ua,q as qa,cH as Ka,a8 as Ya,ab as Xa,c2 as za,s as Dn,r as Qa,ac as Ja,o as Za,aF as At,ar as ei,a5 as ti,x as Rt,B as It,ai as ni,aj as ri,D as oi,aZ as ai,at as ii}from"./index-a2fbd71b.js";import{_ as ui}from"./_plugin-vue_export-helper-c27b6911.js";function kn(e,t,n,r,o,a,u){try{var i=e[a](u),c=i.value}catch(s){n(s);return}i.done?t(c):Promise.resolve(c).then(r,o)}function j(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var a=e.apply(t,n);function u(c){kn(a,r,o,u,i,"next",c)}function i(c){kn(a,r,o,u,i,"throw",c)}u(void 0)})}}function ci(){}var si=ci;const pt=ve(si);function li(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,a;for(a=0;a<r.length;a++)o=r[a],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}function fi(e,t){if(e==null)return{};var n=li(e,t),r,o;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)r=a[o],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function Vt(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function di(e){if(Array.isArray(e))return Vt(e)}function pi(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function eo(e,t){if(e){if(typeof e=="string")return Vt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Vt(e,t)}}function hi(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function be(e){return di(e)||pi(e)||eo(e)||hi()}function Nn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,r)}return n}function k(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Nn(Object(n),!0).forEach(function(r){Z(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Nn(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}var to={exports:{}},no={exports:{}};(function(e){function t(n){"@babel/helpers - typeof";return e.exports=t=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(r){return typeof r}:function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},e.exports.__esModule=!0,e.exports.default=e.exports,t(n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports})(no);var vi=no.exports;(function(e){var t=vi.default;function n(){e.exports=n=function(){return o},e.exports.__esModule=!0,e.exports.default=e.exports;var r,o={},a=Object.prototype,u=a.hasOwnProperty,i=Object.defineProperty||function(S,g,E){S[g]=E.value},c=typeof Symbol=="function"?Symbol:{},s=c.iterator||"@@iterator",l=c.asyncIterator||"@@asyncIterator",h=c.toStringTag||"@@toStringTag";function p(S,g,E){return Object.defineProperty(S,g,{value:E,enumerable:!0,configurable:!0,writable:!0}),S[g]}try{p({},"")}catch{p=function(E,_,C){return E[_]=C}}function m(S,g,E,_){var C=g&&g.prototype instanceof L?g:L,x=Object.create(C.prototype),N=new se(_||[]);return i(x,"_invoke",{value:ce(S,E,N)}),x}function d(S,g,E){try{return{type:"normal",arg:S.call(g,E)}}catch(_){return{type:"throw",arg:_}}}o.wrap=m;var y="suspendedStart",f="suspendedYield",v="executing",b="completed",T={};function L(){}function M(){}function w(){}var R={};p(R,s,function(){return this});var I=Object.getPrototypeOf,A=I&&I(I(oe([])));A&&A!==a&&u.call(A,s)&&(R=A);var H=w.prototype=L.prototype=Object.create(R);function Y(S){["next","throw","return"].forEach(function(g){p(S,g,function(E){return this._invoke(g,E)})})}function V(S,g){function E(C,x,N,F){var W=d(S[C],S,x);if(W.type!=="throw"){var J=W.arg,K=J.value;return K&&t(K)=="object"&&u.call(K,"__await")?g.resolve(K.__await).then(function(ae){E("next",ae,N,F)},function(ae){E("throw",ae,N,F)}):g.resolve(K).then(function(ae){J.value=ae,N(J)},function(ae){return E("throw",ae,N,F)})}F(W.arg)}var _;i(this,"_invoke",{value:function(x,N){function F(){return new g(function(W,J){E(x,N,W,J)})}return _=_?_.then(F,F):F()}})}function ce(S,g,E){var _=y;return function(C,x){if(_===v)throw new Error("Generator is already running");if(_===b){if(C==="throw")throw x;return{value:r,done:!0}}for(E.method=C,E.arg=x;;){var N=E.delegate;if(N){var F=q(N,E);if(F){if(F===T)continue;return F}}if(E.method==="next")E.sent=E._sent=E.arg;else if(E.method==="throw"){if(_===y)throw _=b,E.arg;E.dispatchException(E.arg)}else E.method==="return"&&E.abrupt("return",E.arg);_=v;var W=d(S,g,E);if(W.type==="normal"){if(_=E.done?b:f,W.arg===T)continue;return{value:W.arg,done:E.done}}W.type==="throw"&&(_=b,E.method="throw",E.arg=W.arg)}}}function q(S,g){var E=g.method,_=S.iterator[E];if(_===r)return g.delegate=null,E==="throw"&&S.iterator.return&&(g.method="return",g.arg=r,q(S,g),g.method==="throw")||E!=="return"&&(g.method="throw",g.arg=new TypeError("The iterator does not provide a '"+E+"' method")),T;var C=d(_,S.iterator,g.arg);if(C.type==="throw")return g.method="throw",g.arg=C.arg,g.delegate=null,T;var x=C.arg;return x?x.done?(g[S.resultName]=x.value,g.next=S.nextLoc,g.method!=="return"&&(g.method="next",g.arg=r),g.delegate=null,T):x:(g.method="throw",g.arg=new TypeError("iterator result is not an object"),g.delegate=null,T)}function me(S){var g={tryLoc:S[0]};1 in S&&(g.catchLoc=S[1]),2 in S&&(g.finallyLoc=S[2],g.afterLoc=S[3]),this.tryEntries.push(g)}function Q(S){var g=S.completion||{};g.type="normal",delete g.arg,S.completion=g}function se(S){this.tryEntries=[{tryLoc:"root"}],S.forEach(me,this),this.reset(!0)}function oe(S){if(S||S===""){var g=S[s];if(g)return g.call(S);if(typeof S.next=="function")return S;if(!isNaN(S.length)){var E=-1,_=function C(){for(;++E<S.length;)if(u.call(S,E))return C.value=S[E],C.done=!1,C;return C.value=r,C.done=!0,C};return _.next=_}}throw new TypeError(t(S)+" is not iterable")}return M.prototype=w,i(H,"constructor",{value:w,configurable:!0}),i(w,"constructor",{value:M,configurable:!0}),M.displayName=p(w,h,"GeneratorFunction"),o.isGeneratorFunction=function(S){var g=typeof S=="function"&&S.constructor;return!!g&&(g===M||(g.displayName||g.name)==="GeneratorFunction")},o.mark=function(S){return Object.setPrototypeOf?Object.setPrototypeOf(S,w):(S.__proto__=w,p(S,h,"GeneratorFunction")),S.prototype=Object.create(H),S},o.awrap=function(S){return{__await:S}},Y(V.prototype),p(V.prototype,l,function(){return this}),o.AsyncIterator=V,o.async=function(S,g,E,_,C){C===void 0&&(C=Promise);var x=new V(m(S,g,E,_),C);return o.isGeneratorFunction(g)?x:x.next().then(function(N){return N.done?N.value:x.next()})},Y(H),p(H,h,"Generator"),p(H,s,function(){return this}),p(H,"toString",function(){return"[object Generator]"}),o.keys=function(S){var g=Object(S),E=[];for(var _ in g)E.push(_);return E.reverse(),function C(){for(;E.length;){var x=E.pop();if(x in g)return C.value=x,C.done=!1,C}return C.done=!0,C}},o.values=oe,se.prototype={constructor:se,reset:function(g){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(Q),!g)for(var E in this)E.charAt(0)==="t"&&u.call(this,E)&&!isNaN(+E.slice(1))&&(this[E]=r)},stop:function(){this.done=!0;var g=this.tryEntries[0].completion;if(g.type==="throw")throw g.arg;return this.rval},dispatchException:function(g){if(this.done)throw g;var E=this;function _(J,K){return N.type="throw",N.arg=g,E.next=J,K&&(E.method="next",E.arg=r),!!K}for(var C=this.tryEntries.length-1;C>=0;--C){var x=this.tryEntries[C],N=x.completion;if(x.tryLoc==="root")return _("end");if(x.tryLoc<=this.prev){var F=u.call(x,"catchLoc"),W=u.call(x,"finallyLoc");if(F&&W){if(this.prev<x.catchLoc)return _(x.catchLoc,!0);if(this.prev<x.finallyLoc)return _(x.finallyLoc)}else if(F){if(this.prev<x.catchLoc)return _(x.catchLoc,!0)}else{if(!W)throw new Error("try statement without catch or finally");if(this.prev<x.finallyLoc)return _(x.finallyLoc)}}}},abrupt:function(g,E){for(var _=this.tryEntries.length-1;_>=0;--_){var C=this.tryEntries[_];if(C.tryLoc<=this.prev&&u.call(C,"finallyLoc")&&this.prev<C.finallyLoc){var x=C;break}}x&&(g==="break"||g==="continue")&&x.tryLoc<=E&&E<=x.finallyLoc&&(x=null);var N=x?x.completion:{};return N.type=g,N.arg=E,x?(this.method="next",this.next=x.finallyLoc,T):this.complete(N)},complete:function(g,E){if(g.type==="throw")throw g.arg;return g.type==="break"||g.type==="continue"?this.next=g.arg:g.type==="return"?(this.rval=this.arg=g.arg,this.method="return",this.next="end"):g.type==="normal"&&E&&(this.next=E),T},finish:function(g){for(var E=this.tryEntries.length-1;E>=0;--E){var _=this.tryEntries[E];if(_.finallyLoc===g)return this.complete(_.completion,_.afterLoc),Q(_),T}},catch:function(g){for(var E=this.tryEntries.length-1;E>=0;--E){var _=this.tryEntries[E];if(_.tryLoc===g){var C=_.completion;if(C.type==="throw"){var x=C.arg;Q(_)}return x}}throw new Error("illegal catch attempt")},delegateYield:function(g,E,_){return this.delegate={iterator:oe(g),resultName:E,nextLoc:_},this.method==="next"&&(this.arg=r),T}},o}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports})(to);var mi=to.exports,st=mi(),gi=st;try{regeneratorRuntime=st}catch{typeof globalThis=="object"?globalThis.regeneratorRuntime=st:Function("r","regeneratorRuntime = r")(st)}const O=ve(gi);var yi=Object.freeze({__proto__:null,get start(){return Po},get ensureJQuerySupport(){return ho},get setBootstrapMaxTime(){return _i},get setMountMaxTime(){return Oi},get setUnmountMaxTime(){return xi},get setUnloadMaxTime(){return Mi},get registerApplication(){return Ii},get unregisterApplication(){return bo},get getMountedApps(){return mo},get getAppStatus(){return yo},get unloadApplication(){return Eo},get checkActivityFunctions(){return wo},get getAppNames(){return go},get pathToActiveWhen(){return So},get navigateToUrl(){return hn},get triggerAppChange(){return Gi},get addErrorHandler(){return wi},get removeErrorHandler(){return bi},get mountRootParcel(){return co},get NOT_LOADED(){return pe},get LOADING_SOURCE_CODE(){return St},get NOT_BOOTSTRAPPED(){return Ge},get BOOTSTRAPPING(){return ro},get NOT_MOUNTED(){return he},get MOUNTING(){return Ei},get UPDATING(){return oo},get LOAD_ERROR(){return De},get MOUNTED(){return te},get UNLOADING(){return Ut},get UNMOUNTING(){return ao},get SKIP_BECAUSE_BROKEN(){return U}});function ue(e){return(ue=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}function rt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var jn=(typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{}).CustomEvent,ge=function(){try{var e=new jn("cat",{detail:{foo:"bar"}});return e.type==="cat"&&e.detail.foo==="bar"}catch{}return!1}()?jn:typeof document<"u"&&typeof document.createEvent=="function"?function(e,t){var n=document.createEvent("CustomEvent");return t?n.initCustomEvent(e,t.bubbles,t.cancelable,t.detail):n.initCustomEvent(e,!1,!1,void 0),n}:function(e,t){var n=document.createEventObject();return n.type=e,t?(n.bubbles=!!t.bubbles,n.cancelable=!!t.cancelable,n.detail=t.detail):(n.bubbles=!1,n.cancelable=!1,n.detail=void 0),n},Ye=[];function Ee(e,t,n){var r=Ie(e,t,n);Ye.length?Ye.forEach(function(o){return o(r)}):setTimeout(function(){throw r})}function wi(e){if(typeof e!="function")throw Error(G(28,!1));Ye.push(e)}function bi(e){if(typeof e!="function")throw Error(G(29,!1));var t=!1;return Ye=Ye.filter(function(n){var r=n===e;return t=t||r,!r}),t}function G(e,t){for(var n=arguments.length,r=new Array(n>2?n-2:0),o=2;o<n;o++)r[o-2]=arguments[o];return"single-spa minified message #".concat(e,": ").concat(t?t+" ":"","See https://single-spa.js.org/error/?code=").concat(e).concat(r.length?"&arg=".concat(r.join("&arg=")):"")}function Ie(e,t,n){var r,o="".concat(Pt(t)," '").concat($(t),"' died in status ").concat(t.status,": ");if(e instanceof Error){try{e.message=o+e.message}catch{}r=e}else{console.warn(G(30,!1,t.status,$(t)));try{r=Error(o+JSON.stringify(e))}catch{r=e}}return r.appOrParcelName=$(t),t.status=n,r}var pe="NOT_LOADED",St="LOADING_SOURCE_CODE",Ge="NOT_BOOTSTRAPPED",ro="BOOTSTRAPPING",he="NOT_MOUNTED",Ei="MOUNTING",te="MOUNTED",oo="UPDATING",ao="UNMOUNTING",Ut="UNLOADING",De="LOAD_ERROR",U="SKIP_BECAUSE_BROKEN";function Si(e){return e.status===te}function qt(e){try{return e.activeWhen(window.location)}catch(t){return Ee(t,e,U),!1}}function $(e){return e.name}function io(e){return!!e.unmountThisParcel}function Pt(e){return io(e)?"parcel":"application"}function et(){for(var e=arguments.length-1;e>0;e--)for(var t in arguments[e])t!=="__proto__"&&(arguments[e-1][t]=arguments[e][t]);return arguments[0]}function Tt(e,t){for(var n=0;n<e.length;n++)if(t(e[n]))return e[n];return null}function xe(e){return e&&(typeof e=="function"||(t=e,Array.isArray(t)&&!Tt(t,function(n){return typeof n!="function"})));var t}function ye(e,t){var n=e[t]||[];(n=Array.isArray(n)?n:[n]).length===0&&(n=[function(){return Promise.resolve()}]);var r=Pt(e),o=$(e);return function(a){return n.reduce(function(u,i,c){return u.then(function(){var s=i(a);return uo(s)?s:Promise.reject(G(15,!1,r,o,t,c))})},Promise.resolve())}}function uo(e){return e&&typeof e.then=="function"&&typeof e.catch=="function"}function pn(e,t){return Promise.resolve().then(function(){return e.status!==Ge?e:(e.status=ro,e.bootstrap?tt(e,"bootstrap").then(n).catch(function(r){if(t)throw Ie(r,e,U);return Ee(r,e,U),e}):Promise.resolve().then(n))});function n(){return e.status=he,e}}function _t(e,t){return Promise.resolve().then(function(){if(e.status!==te)return e;e.status=ao;var n=Object.keys(e.parcels).map(function(o){return e.parcels[o].unmountThisParcel()});return Promise.all(n).then(r,function(o){return r().then(function(){var a=Error(o.message);if(t)throw Ie(a,e,U);Ee(a,e,U)})}).then(function(){return e});function r(){return tt(e,"unmount").then(function(){e.status=he}).catch(function(o){if(t)throw Ie(o,e,U);Ee(o,e,U)})}})}var Hn=!1,$n=!1;function Kt(e,t){return Promise.resolve().then(function(){return e.status!==he?e:(Hn||(window.dispatchEvent(new ge("single-spa:before-first-mount")),Hn=!0),tt(e,"mount").then(function(){return e.status=te,$n||(window.dispatchEvent(new ge("single-spa:first-mount")),$n=!0),e}).catch(function(n){return e.status=te,_t(e,!0).then(r,r);function r(){if(t)throw Ie(n,e,U);return Ee(n,e,U),e}}))})}var Pi=0,Ti={parcels:{}};function co(){return so.apply(Ti,arguments)}function so(e,t){var n=this;if(!e||ue(e)!=="object"&&typeof e!="function")throw Error(G(2,!1));if(e.name&&typeof e.name!="string")throw Error(G(3,!1,ue(e.name)));if(ue(t)!=="object")throw Error(G(4,!1,name,ue(t)));if(!t.domElement)throw Error(G(5,!1,name));var r,o=Pi++,a=typeof e=="function",u=a?e:function(){return Promise.resolve(e)},i={id:o,parcels:{},status:a?St:Ge,customProps:t,parentName:$(n),unmountThisParcel:function(){return p.then(function(){if(i.status!==te)throw Error(G(6,!1,name,i.status));return _t(i,!0)}).then(function(d){return i.parentName&&delete n.parcels[i.id],d}).then(function(d){return s(d),d}).catch(function(d){throw i.status=U,l(d),d})}};n.parcels[o]=i;var c=u();if(!c||typeof c.then!="function")throw Error(G(7,!1));var s,l,h=(c=c.then(function(d){if(!d)throw Error(G(8,!1));var y=d.name||"parcel-".concat(o);if(Object.prototype.hasOwnProperty.call(d,"bootstrap")&&!xe(d.bootstrap))throw Error(G(9,!1,y));if(!xe(d.mount))throw Error(G(10,!1,y));if(!xe(d.unmount))throw Error(G(11,!1,y));if(d.update&&!xe(d.update))throw Error(G(12,!1,y));var f=ye(d,"bootstrap"),v=ye(d,"mount"),b=ye(d,"unmount");i.status=Ge,i.name=y,i.bootstrap=f,i.mount=v,i.unmount=b,i.timeouts=fo(d.timeouts),d.update&&(i.update=ye(d,"update"),r.update=function(T){return i.customProps=T,Te(function(L){return Promise.resolve().then(function(){if(L.status!==te)throw Error(G(32,!1,$(L)));return L.status=oo,tt(L,"update").then(function(){return L.status=te,L}).catch(function(M){throw Ie(M,L,U)})})}(i))})})).then(function(){return pn(i,!0)}),p=h.then(function(){return Kt(i,!0)}),m=new Promise(function(d,y){s=d,l=y});return r={mount:function(){return Te(Promise.resolve().then(function(){if(i.status!==he)throw Error(G(13,!1,name,i.status));return n.parcels[o]=i,Kt(i)}))},unmount:function(){return Te(i.unmountThisParcel())},getStatus:function(){return i.status},loadPromise:Te(c),bootstrapPromise:Te(h),mountPromise:Te(p),unmountPromise:Te(m)}}function Te(e){return e.then(function(){return null})}function lo(e){var t=$(e),n=typeof e.customProps=="function"?e.customProps(t,window.location):e.customProps;(ue(n)!=="object"||n===null||Array.isArray(n))&&(n={},console.warn(G(40,!1),t,n));var r=et({},n,{name:t,mountParcel:so.bind(e),singleSpa:yi});return io(e)&&(r.unmountSelf=e.unmountThisParcel),r}var ke={bootstrap:{millis:4e3,dieOnTimeout:!1,warningMillis:1e3},mount:{millis:3e3,dieOnTimeout:!1,warningMillis:1e3},unmount:{millis:3e3,dieOnTimeout:!1,warningMillis:1e3},unload:{millis:3e3,dieOnTimeout:!1,warningMillis:1e3},update:{millis:3e3,dieOnTimeout:!1,warningMillis:1e3}};function _i(e,t,n){if(typeof e!="number"||e<=0)throw Error(G(16,!1));ke.bootstrap={millis:e,dieOnTimeout:t,warningMillis:n||1e3}}function Oi(e,t,n){if(typeof e!="number"||e<=0)throw Error(G(17,!1));ke.mount={millis:e,dieOnTimeout:t,warningMillis:n||1e3}}function xi(e,t,n){if(typeof e!="number"||e<=0)throw Error(G(18,!1));ke.unmount={millis:e,dieOnTimeout:t,warningMillis:n||1e3}}function Mi(e,t,n){if(typeof e!="number"||e<=0)throw Error(G(19,!1));ke.unload={millis:e,dieOnTimeout:t,warningMillis:n||1e3}}function tt(e,t){var n=e.timeouts[t],r=n.warningMillis,o=Pt(e);return new Promise(function(a,u){var i=!1,c=!1;e[t](lo(e)).then(function(h){i=!0,a(h)}).catch(function(h){i=!0,u(h)}),setTimeout(function(){return l(1)},r),setTimeout(function(){return l(!0)},n.millis);var s=G(31,!1,t,o,$(e),n.millis);function l(h){if(!i){if(h===!0)c=!0,n.dieOnTimeout?u(Error(s)):console.error(s);else if(!c){var p=h,m=p*r;console.warn(s),m+r<n.millis&&setTimeout(function(){return l(p+1)},r)}}}})}function fo(e){var t={};for(var n in ke)t[n]=et({},ke[n],e&&e[n]||{});return t}function Yt(e){return Promise.resolve().then(function(){return e.loadPromise?e.loadPromise:e.status!==pe&&e.status!==De?e:(e.status=St,e.loadPromise=Promise.resolve().then(function(){var r=e.loadApp(lo(e));if(!uo(r))throw n=!0,Error(G(33,!1,$(e)));return r.then(function(o){var a;e.loadErrorTime=null,ue(t=o)!=="object"&&(a=34),Object.prototype.hasOwnProperty.call(t,"bootstrap")&&!xe(t.bootstrap)&&(a=35),xe(t.mount)||(a=36),xe(t.unmount)||(a=37);var u=Pt(t);if(a){var i;try{i=JSON.stringify(t)}catch{}return console.error(G(a,!1,u,$(e),i),t),Ee(void 0,e,U),e}return t.devtools&&t.devtools.overlays&&(e.devtools.overlays=et({},e.devtools.overlays,t.devtools.overlays)),e.status=Ge,e.bootstrap=ye(t,"bootstrap"),e.mount=ye(t,"mount"),e.unmount=ye(t,"unmount"),e.unload=ye(t,"unload"),e.timeouts=fo(t.timeouts),delete e.loadPromise,e})}).catch(function(r){var o;return delete e.loadPromise,n?o=U:(o=De,e.loadErrorTime=new Date().getTime()),Ee(r,e,o),e}));var t,n})}var po,je=typeof window<"u",Ue={hashchange:[],popstate:[]},ht=["hashchange","popstate"];function hn(e){var t;if(typeof e=="string")t=e;else if(this&&this.href)t=this.href;else{if(!(e&&e.currentTarget&&e.currentTarget.href&&e.preventDefault))throw Error(G(14,!1));t=e.currentTarget.href,e.preventDefault()}var n=Vn(window.location.href),r=Vn(t);t.indexOf("#")===0?window.location.hash=r.hash:n.host!==r.host&&r.host?window.location.href=t:r.pathname===n.pathname&&r.search===n.search?window.location.hash=r.hash:window.history.pushState(null,null,t)}function Fn(e){var t=this;if(e){var n=e[0].type;ht.indexOf(n)>=0&&Ue[n].forEach(function(r){try{r.apply(t,e)}catch(o){setTimeout(function(){throw o})}})}}function Wn(){Se([],arguments)}function Bn(e,t){return function(){var n=window.location.href,r=e.apply(this,arguments),o=window.location.href;return po&&n===o||(To()?window.dispatchEvent(Ci(window.history.state,t)):Se([])),r}}function Ci(e,t){var n;try{n=new PopStateEvent("popstate",{state:e})}catch{(n=document.createEvent("PopStateEvent")).initPopStateEvent("popstate",!1,!1,e)}return n.singleSpa=!0,n.singleSpaTrigger=t,n}if(je){window.addEventListener("hashchange",Wn),window.addEventListener("popstate",Wn);var Li=window.addEventListener,Ai=window.removeEventListener;window.addEventListener=function(e,t){if(!(typeof t=="function"&&ht.indexOf(e)>=0)||Tt(Ue[e],function(n){return n===t}))return Li.apply(this,arguments);Ue[e].push(t)},window.removeEventListener=function(e,t){if(!(typeof t=="function"&&ht.indexOf(e)>=0))return Ai.apply(this,arguments);Ue[e]=Ue[e].filter(function(n){return n!==t})},window.history.pushState=Bn(window.history.pushState,"pushState"),window.history.replaceState=Bn(window.history.replaceState,"replaceState"),window.singleSpaNavigate?console.warn(G(41,!1)):window.singleSpaNavigate=hn}function Vn(e){var t=document.createElement("a");return t.href=e,t}var Un=!1;function ho(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:window.jQuery;if(e||window.$&&window.$.fn&&window.$.fn.jquery&&(e=window.$),e&&!Un){var t=e.fn.on,n=e.fn.off;e.fn.on=function(r,o){return qn.call(this,t,window.addEventListener,r,o,arguments)},e.fn.off=function(r,o){return qn.call(this,n,window.removeEventListener,r,o,arguments)},Un=!0}}function qn(e,t,n,r,o){return typeof n!="string"?e.apply(this,o):(n.split(/\s+/).forEach(function(a){ht.indexOf(a)>=0&&(t(a,r),n=n.replace(a,""))}),n.trim()===""?this:e.apply(this,o))}var Ne={};function Xt(e){return Promise.resolve().then(function(){var t=Ne[$(e)];if(!t)return e;if(e.status===pe)return Kn(e,t),e;if(e.status===Ut)return t.promise.then(function(){return e});if(e.status!==he&&e.status!==De)return e;var n=e.status===De?Promise.resolve():tt(e,"unload");return e.status=Ut,n.then(function(){return Kn(e,t),e}).catch(function(r){return function(o,a,u){delete Ne[$(o)],delete o.bootstrap,delete o.mount,delete o.unmount,delete o.unload,Ee(u,o,U),a.reject(u)}(e,t,r),e})})}function Kn(e,t){delete Ne[$(e)],delete e.bootstrap,delete e.mount,delete e.unmount,delete e.unload,e.status=pe,t.resolve()}function Yn(e,t,n,r){Ne[$(e)]={app:e,resolve:n,reject:r},Object.defineProperty(Ne[$(e)],"promise",{get:t})}function vo(e){return Ne[e]}var ne=[];function Ri(){var e=[],t=[],n=[],r=[],o=new Date().getTime();return ne.forEach(function(a){var u=a.status!==U&&qt(a);switch(a.status){case De:u&&o-a.loadErrorTime>=200&&n.push(a);break;case pe:case St:u&&n.push(a);break;case Ge:case he:!u&&vo($(a))?e.push(a):u&&r.push(a);break;case te:u||t.push(a)}}),{appsToUnload:e,appsToUnmount:t,appsToLoad:n,appsToMount:r}}function mo(){return ne.filter(Si).map($)}function go(){return ne.map($)}function yo(e){var t=Tt(ne,function(n){return $(n)===e});return t?t.status:null}function Ii(e,t,n,r){var o=function(a,u,i,c){var s,l={name:null,loadApp:null,activeWhen:null,customProps:null};return ue(a)==="object"?(function(h){if(Array.isArray(h)||h===null)throw Error(G(39,!1));var p=["name","app","activeWhen","customProps"],m=Object.keys(h).reduce(function(y,f){return p.indexOf(f)>=0?y:y.concat(f)},[]);if(m.length!==0)throw Error(G(38,!1,p.join(", "),m.join(", ")));if(typeof h.name!="string"||h.name.length===0||ue(h.app)!=="object"&&typeof h.app!="function")throw Error(G(20,!1));var d=function(y){return typeof y=="string"||typeof y=="function"};if(!(d(h.activeWhen)||Array.isArray(h.activeWhen)&&h.activeWhen.every(d)))throw Error(G(24,!1));if(!zn(h.customProps))throw Error(G(22,!1))}(a),l.name=a.name,l.loadApp=a.app,l.activeWhen=a.activeWhen,l.customProps=a.customProps):(function(h,p,m,d){if(typeof h!="string"||h.length===0)throw Error(G(20,!1));if(!p)throw Error(G(23,!1));if(typeof m!="function")throw Error(G(24,!1));if(!zn(d))throw Error(G(22,!1))}(a,u,i,c),l.name=a,l.loadApp=u,l.activeWhen=i,l.customProps=c),l.loadApp=typeof(s=l.loadApp)!="function"?function(){return Promise.resolve(s)}:s,l.customProps=function(h){return h||{}}(l.customProps),l.activeWhen=function(h){var p=Array.isArray(h)?h:[h];return p=p.map(function(m){return typeof m=="function"?m:So(m)}),function(m){return p.some(function(d){return d(m)})}}(l.activeWhen),l}(e,t,n,r);if(go().indexOf(o.name)!==-1)throw Error(G(21,!1,o.name));ne.push(et({loadErrorTime:null,status:pe,parcels:{},devtools:{overlays:{options:{},selectors:[]}}},o)),je&&(ho(),Se())}function wo(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:window.location;return ne.filter(function(t){return t.activeWhen(e)}).map($)}function bo(e){if(ne.filter(function(t){return $(t)===e}).length===0)throw Error(G(25,!1,e));return Eo(e).then(function(){var t=ne.map($).indexOf(e);ne.splice(t,1)})}function Eo(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{waitForUnmount:!1};if(typeof e!="string")throw Error(G(26,!1));var n=Tt(ne,function(u){return $(u)===e});if(!n)throw Error(G(27,!1,e));var r,o=vo($(n));if(t&&t.waitForUnmount){if(o)return o.promise;var a=new Promise(function(u,i){Yn(n,function(){return a},u,i)});return a}return o?(r=o.promise,Xn(n,o.resolve,o.reject)):r=new Promise(function(u,i){Yn(n,function(){return r},u,i),Xn(n,u,i)}),r}function Xn(e,t,n){_t(e).then(Xt).then(function(){t(),setTimeout(function(){Se()})}).catch(n)}function zn(e){return!e||typeof e=="function"||ue(e)==="object"&&e!==null&&!Array.isArray(e)}function So(e,t){var n=function(r,o){var a=0,u=!1,i="^";r[0]!=="/"&&(r="/"+r);for(var c=0;c<r.length;c++){var s=r[c];(!u&&s===":"||u&&s==="/")&&l(c)}return l(r.length),new RegExp(i,"i");function l(h){var p=r.slice(a,h).replace(/[|\\{}()[\]^$+*?.]/g,"\\$&");if(i+=u?"[^/]+/?":p,h===r.length)if(u)o&&(i+="$");else{var m=o?"":".*";i=i.charAt(i.length-1)==="/"?"".concat(i).concat(m,"$"):"".concat(i,"(/").concat(m,")?(#.*)?$")}u=!u,a=h}}(e,t);return function(r){var o=r.origin;o||(o="".concat(r.protocol,"//").concat(r.host));var a=r.href.replace(o,"").replace(r.search,"").split("?")[0];return n.test(a)}}var Gt=!1,ot=[],Qn=je&&window.location.href;function Gi(){return Se()}function Se(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0;if(Gt)return new Promise(function(v,b){ot.push({resolve:v,reject:b,eventArguments:t})});var n,r=Ri(),o=r.appsToUnload,a=r.appsToUnmount,u=r.appsToLoad,i=r.appsToMount,c=!1,s=Qn,l=Qn=window.location.href;return To()?(Gt=!0,n=o.concat(u,a,i),m()):(n=u,p());function h(){c=!0}function p(){return Promise.resolve().then(function(){var v=u.map(Yt);return Promise.all(v).then(y).then(function(){return[]}).catch(function(b){throw y(),b})})}function m(){return Promise.resolve().then(function(){if(window.dispatchEvent(new ge(n.length===0?"single-spa:before-no-app-change":"single-spa:before-app-change",f(!0))),window.dispatchEvent(new ge("single-spa:before-routing-event",f(!0,{cancelNavigation:h}))),c)return window.dispatchEvent(new ge("single-spa:before-mount-routing-event",f(!0))),d(),void hn(s);var v=o.map(Xt),b=a.map(_t).map(function(w){return w.then(Xt)}).concat(v),T=Promise.all(b);T.then(function(){window.dispatchEvent(new ge("single-spa:before-mount-routing-event",f(!0)))});var L=u.map(function(w){return Yt(w).then(function(R){return Jn(R,T)})}),M=i.filter(function(w){return u.indexOf(w)<0}).map(function(w){return Jn(w,T)});return T.catch(function(w){throw y(),w}).then(function(){return y(),Promise.all(L.concat(M)).catch(function(w){throw e.forEach(function(R){return R.reject(w)}),w}).then(d)})})}function d(){var v=mo();e.forEach(function(L){return L.resolve(v)});try{var b=n.length===0?"single-spa:no-app-change":"single-spa:app-change";window.dispatchEvent(new ge(b,f())),window.dispatchEvent(new ge("single-spa:routing-event",f()))}catch(L){setTimeout(function(){throw L})}if(Gt=!1,ot.length>0){var T=ot;ot=[],Se(T)}return v}function y(){e.forEach(function(v){Fn(v.eventArguments)}),Fn(t)}function f(){var v,b=arguments.length>0&&arguments[0]!==void 0&&arguments[0],T=arguments.length>1?arguments[1]:void 0,L={},M=(rt(v={},te,[]),rt(v,he,[]),rt(v,pe,[]),rt(v,U,[]),v);b?(u.concat(i).forEach(function(I,A){R(I,te)}),o.forEach(function(I){R(I,pe)}),a.forEach(function(I){R(I,he)})):n.forEach(function(I){R(I)});var w={detail:{newAppStatuses:L,appsByNewStatus:M,totalAppChanges:n.length,originalEvent:t==null?void 0:t[0],oldUrl:s,newUrl:l,navigationIsCanceled:c}};return T&&et(w.detail,T),w;function R(I,A){var H=$(I);A=A||yo(H),L[H]=A,(M[A]=M[A]||[]).push(H)}}}function Jn(e,t){return qt(e)?pn(e).then(function(n){return t.then(function(){return qt(n)?Kt(n):n})}):t.then(function(){return e})}var vn=!1;function Po(e){var t;vn=!0,e&&e.urlRerouteOnly&&(t=e.urlRerouteOnly,po=t),je&&Se()}function To(){return vn}je&&setTimeout(function(){vn||console.warn(G(1,!1))},5e3);var Di={getRawAppData:function(){return[].concat(ne)},reroute:Se,NOT_LOADED:pe,toLoadPromise:Yt,toBootstrapPromise:pn,unregisterApplication:bo};je&&window.__SINGLE_SPA_DEVTOOLS__&&(window.__SINGLE_SPA_DEVTOOLS__.exposedMethods=Di);var Zn=qr,ki=Kr,Ni=Ze,er=Zn?Zn.isConcatSpreadable:void 0;function ji(e){return Ni(e)||ki(e)||!!(er&&e&&e[er])}var Hi=ji,$i=Yr,Fi=Hi;function _o(e,t,n,r,o){var a=-1,u=e.length;for(n||(n=Fi),o||(o=[]);++a<u;){var i=e[a];t>0&&n(i)?t>1?_o(i,t-1,n,r,o):$i(o,i):r||(o[o.length]=i)}return o}var Wi=_o,Bi=Yr,Vi=Wi,Ui=Xr,qi=Ze;function Ki(){var e=arguments.length;if(!e)return[];for(var t=Array(e-1),n=arguments[0],r=e;r--;)t[r-1]=arguments[r];return Bi(qi(n)?Ui(n):[n],Vi(t,1))}var Yi=Ki;const Oo=ve(Yi);var Xi=Ca,zi=zr;function Qi(e,t,n){(n!==void 0&&!zi(e[t],n)||n===void 0&&!(t in e))&&Xi(e,t,n)}var xo=Qi;function Ji(e){return function(t,n,r){for(var o=-1,a=Object(t),u=r(t),i=u.length;i--;){var c=u[e?i:++o];if(n(a[c],c,a)===!1)break}return t}}var Zi=Ji,eu=Zi,tu=eu(),Mo=tu,nu=fn,ru=dn;function ou(e){return ru(e)&&nu(e)}var Co=ou,au=Qr,iu=La,uu=dn,cu="[object Object]",su=Function.prototype,lu=Object.prototype,Lo=su.toString,fu=lu.hasOwnProperty,du=Lo.call(Object);function pu(e){if(!uu(e)||au(e)!=cu)return!1;var t=iu(e);if(t===null)return!0;var n=fu.call(t,"constructor")&&t.constructor;return typeof n=="function"&&n instanceof n&&Lo.call(n)==du}var hu=pu;function vu(e,t){if(!(t==="constructor"&&typeof e[t]=="function")&&t!="__proto__")return e[t]}var Ao=vu,mu=Aa,gu=Jr;function yu(e){return mu(e,gu(e))}var wu=yu,tr=xo,bu=Ra,Eu=Ia,Su=Xr,Pu=Ga,nr=Kr,rr=Ze,Tu=Co,_u=Da,Ou=ka,xu=Et,Mu=hu,Cu=Na,or=Ao,Lu=wu;function Au(e,t,n,r,o,a,u){var i=or(e,n),c=or(t,n),s=u.get(c);if(s){tr(e,n,s);return}var l=a?a(i,c,n+"",e,t,u):void 0,h=l===void 0;if(h){var p=rr(c),m=!p&&_u(c),d=!p&&!m&&Cu(c);l=c,p||m||d?rr(i)?l=i:Tu(i)?l=Su(i):m?(h=!1,l=bu(c,!0)):d?(h=!1,l=Eu(c,!0)):l=[]:Mu(c)||nr(c)?(l=i,nr(i)?l=Lu(i):(!xu(i)||Ou(i))&&(l=Pu(c))):h=!1}h&&(u.set(c,l),o(l,c,r,a,u),u.delete(c)),tr(e,n,l)}var Ru=Au,Iu=ja,Gu=xo,Du=Mo,ku=Ru,Nu=Et,ju=Jr,Hu=Ao;function Ro(e,t,n,r,o){e!==t&&Du(t,function(a,u){if(o||(o=new Iu),Nu(a))ku(e,t,u,n,Ro,r,o);else{var i=r?r(Hu(e,u),a,u+"",e,t,o):void 0;i===void 0&&(i=a),Gu(e,u,i)}},ju)}var $u=Ro;function Fu(e){return e}var mn=Fu;function Wu(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}var Bu=Wu,Vu=Bu,ar=Math.max;function Uu(e,t,n){return t=ar(t===void 0?e.length-1:t,0),function(){for(var r=arguments,o=-1,a=ar(r.length-t,0),u=Array(a);++o<a;)u[o]=r[t+o];o=-1;for(var i=Array(t+1);++o<t;)i[o]=r[o];return i[t]=n(u),Vu(e,this,i)}}var qu=Uu;function Ku(e){return function(){return e}}var Yu=Ku,Xu=Yu,ir=Ha,zu=mn,Qu=ir?function(e,t){return ir(e,"toString",{configurable:!0,enumerable:!1,value:Xu(t),writable:!0})}:zu,Ju=Qu,Zu=800,ec=16,tc=Date.now;function nc(e){var t=0,n=0;return function(){var r=tc(),o=ec-(r-n);if(n=r,o>0){if(++t>=Zu)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}var rc=nc,oc=Ju,ac=rc,ic=ac(oc),uc=ic,cc=mn,sc=qu,lc=uc;function fc(e,t){return lc(sc(e,t,cc),e+"")}var Io=fc,dc=zr,pc=fn,hc=$a,vc=Et;function mc(e,t,n){if(!vc(n))return!1;var r=typeof t;return(r=="number"?pc(n)&&hc(t,n.length):r=="string"&&t in n)?dc(n[t],e):!1}var gc=mc,yc=Io,wc=gc;function bc(e){return yc(function(t,n){var r=-1,o=n.length,a=o>1?n[o-1]:void 0,u=o>2?n[2]:void 0;for(a=e.length>3&&typeof a=="function"?(o--,a):void 0,u&&wc(n[0],n[1],u)&&(a=o<3?void 0:a,o=1),t=Object(t);++r<o;){var i=n[r];i&&e(t,i,r,a)}return t})}var Ec=bc,Sc=$u,Pc=Ec,Tc=Pc(function(e,t,n,r){Sc(e,t,n,r)}),_c=Tc;const Go=ve(_c);var Oc=Mo,xc=Fa;function Mc(e,t){return e&&Oc(e,t,xc)}var Cc=Mc,Lc=fn;function Ac(e,t){return function(n,r){if(n==null)return n;if(!Lc(n))return e(n,r);for(var o=n.length,a=t?o:-1,u=Object(n);(t?a--:++a<o)&&r(u[a],a,u)!==!1;);return n}}var Rc=Ac,Ic=Cc,Gc=Rc,Dc=Gc(Ic),kc=Dc,Nc=mn;function jc(e){return typeof e=="function"?e:Nc}var Hc=jc,$c=Wa,Fc=kc,Wc=Hc,Bc=Ze;function Vc(e,t){var n=Bc(e)?$c:Fc;return n(e,Wc(t))}var Uc=Vc;const qc=ve(Uc);function Kc(e){if(Array.isArray(e))return e}function Yc(e,t){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var r,o,a,u,i=[],c=!0,s=!1;try{if(a=(n=n.call(e)).next,t===0){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=a.call(n)).done)&&(i.push(r.value),i.length!==t);c=!0);}catch(l){s=!0,o=l}finally{try{if(!c&&n.return!=null&&(u=n.return(),Object(u)!==u))return}finally{if(s)throw o}}return i}}function Xc(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function gn(e,t){return Kc(e)||Yc(e,t)||eo(e,t)||Xc()}var zc=typeof navigator<"u"&&navigator.userAgent.indexOf("Trident")!==-1;function Do(e,t){if(!e.hasOwnProperty(t)||!isNaN(t)&&t<e.length)return!0;if(zc)try{return e[t]&&typeof window<"u"&&e[t].parent===window}catch{return!0}else return!1}var lt,ft,zt;function Qc(e){var t=0,n,r=!1;for(var o in e)if(!Do(e,o)){for(var a=0;a<window.frames.length&&!r;a++){var u=window.frames[a];if(u===e[o]){r=!0;break}}if(!r&&(t===0&&o!==lt||t===1&&o!==ft))return o;t++,n=o}if(n!==zt)return n}function Jc(e){lt=ft=void 0;for(var t in e)Do(e,t)||(lt?ft||(ft=t):lt=t,zt=t);return zt}function yn(e){var t=e.indexOf(">")+1,n=e.lastIndexOf("<");return e.substring(t,n)}function Qt(e){if(ee(e)==="object")return"/";try{var t=new URL(e,location.href),n=t.origin,r=t.pathname,o=r.split("/");return o.pop(),"".concat(n).concat(o.join("/"),"/")}catch(a){return console.warn(a),""}}function Zc(){var e=document.createElement("script");return"noModule"in e}var es=window.requestIdleCallback||function(t){var n=Date.now();return setTimeout(function(){t({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-n))}})},1)};function ts(e,t){if(!t||!e.headers)return e.text();var n=e.headers.get("Content-Type");if(!n)return e.text();var r="utf-8",o=n.split(";");if(o.length===2){var a=o[1].split("="),u=gn(a,2),i=u[1],c=i&&i.trim();c&&(r=c)}return r.toUpperCase()==="UTF-8"?e.text():e.blob().then(function(s){return new Promise(function(l,h){var p=new window.FileReader;p.onload=function(){l(p.result)},p.onerror=h,p.readAsText(s,r)})})}var Dt={};function ns(e,t){var n=e;if(!Dt[n]){var r="(function(){".concat(t,"})");Dt[n]=(0,eval)(r)}var o=Dt[n];o.call(window)}function ur(e){var t=new DOMParser,n='<script src="'.concat(e,'"><\/script>'),r=t.parseFromString(n,"text/html");return r.scripts[0].src}var rs=/(<script[\s\S]*?>)[\s\S]*?<\/script>/gi,os=/<(script)\s+((?!type=('|")text\/ng\x2Dtemplate\3)[\s\S])*?>[\s\S]*?<\/\1>/i,cr=/.*\ssrc=('|")?([^>'"\s]+)/,as=/.*\stype=('|")?([^>'"\s]+)/,is=/.*\sentry\s*.*/,us=/.*\sasync\s*.*/,cs=/.*\scrossorigin=('|")?use-credentials\1/,ss=/.*\snomodule\s*.*/,ls=/.*\stype=('|")?module('|")?\s*.*/,fs=/<(link)\s+[\s\S]*?>/ig,ds=/\srel=('|")?(preload|prefetch)\1/,sr=/.*\shref=('|")?([^>'"\s]+)/,ps=/.*\sas=('|")?font\1.*/,hs=/<style[^>]*>[\s\S]*?<\/style>/gi,vs=/\s+rel=('|")?stylesheet\1.*/,ms=/.*\shref=('|")?([^>'"\s]+)/,gs=/<!--([\s\S]*?)-->/g,ys=/<link(\s+|\s+[\s\S]+\s+)ignore(\s*|\s+[\s\S]*|=[\s\S]*)>/i,ws=/<style(\s+|\s+[\s\S]+\s+)ignore(\s*|\s+[\s\S]*|=[\s\S]*)>/i,bs=/<script(\s+|\s+[\s\S]+\s+)ignore(\s*|\s+[\s\S]*|=[\s\S]*)>/i;function lr(e){return e.startsWith("http://")||e.startsWith("https://")}function fr(e,t){return new URL(e,t).toString()}function Es(e){var t=["text/javascript","module","application/javascript","text/ecmascript","application/ecmascript"];return!e||t.indexOf(e)!==-1}var vt=function(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return"<!-- ".concat(n?"prefetch/preload":""," link ").concat(t," replaced by import-html-entry -->")},ko=function(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;return"<!-- ".concat(r?"cors":""," ").concat(n?"async":""," script ").concat(t," replaced by import-html-entry -->")},Ss="<!-- inline scripts replaced by import-html-entry -->",at=function(t){return"<!-- ignore asset ".concat(t||"file"," replaced by import-html-entry -->")},dr=function(t,n){return"<!-- ".concat(n?"nomodule":"module"," script ").concat(t," ignored by import-html-entry -->")};function Ps(e,t,n){var r=[],o=[],a=null,u=Zc(),i=e.replace(gs,"").replace(fs,function(s){var l=!!s.match(vs);if(l){var h=s.match(ms),p=s.match(ys);if(h){var m=h&&h[2],d=m;return m&&!lr(m)&&(d=fr(m,t)),p?at(d):(d=ur(d),o.push(d),vt(d))}}var y=s.match(ds)&&s.match(sr)&&!s.match(ps);if(y){var f=s.match(sr),v=gn(f,3),b=v[2];return vt(b,!0)}return s}).replace(hs,function(s){return ws.test(s)?at("style file"):s}).replace(rs,function(s,l){var h=l.match(bs),p=u&&!!l.match(ss)||!u&&!!l.match(ls),m=l.match(as),d=m&&m[2];if(!Es(d))return s;if(os.test(s)&&l.match(cr)){var y=l.match(is),f=l.match(cr),v=f&&f[2];if(a&&y)throw new SyntaxError("You should not set multiply entry script!");if(v&&(lr(v)||(v=fr(v,t)),v=ur(v)),a=a||y&&v,h)return at(v||"js file");if(p)return dr(v||"js file",u);if(v){var b=!!l.match(us),T=!!l.match(cs);return r.push(b||T?{async:b,src:v,crossOrigin:T}:v),ko(v,b,T)}return s}else{if(h)return at("js file");if(p)return dr("js file",u);var L=yn(s),M=L.split(/[\r\n]+/).every(function(w){return!w.trim()||w.trim().startsWith("//")});return M||r.push(s),Ss}});r=r.filter(function(s){return!!s});var c={template:i,scripts:r,styles:o,entry:a||r[r.length-1]};return typeof n=="function"&&(c=n(c)),c}function pr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,r)}return n}function No(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?pr(Object(n),!0).forEach(function(r){Z(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):pr(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}var hr={},vr={},mr={};if(!window.fetch)throw new Error('[import-html-entry] Here is no "fetch" on the window env, you need to polyfill it');var Ce=window.fetch.bind(window);function Jt(e){return e}function jo(e,t){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},r=n.fetch,o=r===void 0?Ce:r,a=e;return wn(t,o).then(function(u){return a=t.reduce(function(i,c,s){return i=i.replace(vt(c),Ot(c)?"".concat(c):"<style>/* ".concat(c," */").concat(u[s],"</style>")),i},a),a})}var Ot=function(t){return t.startsWith("<")};function Ts(e,t){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},r=n.proxy,o=n.strictGlobal,a=n.scopedGlobalVariables,u=a===void 0?[]:a,i=Ot(e)?"":"//# sourceURL=".concat(e,`
`),c=u.length?"const {".concat(u.join(","),"}=this;"):"",s=(0,eval)("window");return s.proxy=r,o?c?";(function(){with(this){".concat(c).concat(t,`
`).concat(i,"}}).bind(window.proxy)();"):";(function(window, self, globalThis){with(window){;".concat(t,`
`).concat(i,"}}).bind(window.proxy)(window.proxy, window.proxy, window.proxy);"):";(function(window, self, globalThis){;".concat(t,`
`).concat(i,"}).bind(window.proxy)(window.proxy, window.proxy, window.proxy);")}function wn(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Ce;return Promise.all(e.map(function(n){return Ot(n)?yn(n):hr[n]||(hr[n]=t(n).then(function(r){return r.text()}))}))}function bn(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Ce,n=function(o,a){return vr[o]||(vr[o]=t(o,a).then(function(u){if(u.status>=400)throw new Error("".concat(o," load failed with status ").concat(u.status));return u.text()}))};return Promise.all(e.map(function(r){if(typeof r=="string")return Ot(r)?yn(r):n(r);var o=r.src,a=r.async,u=r.crossOrigin,i=u?{credentials:"include"}:{};return a?{src:o,async:!0,content:new Promise(function(c,s){return es(function(){return n(o,i).then(c,s)})})}:n(o,i)}))}function gr(e,t){setTimeout(function(){throw console.error(t),e})}function mt(e,t){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:window,r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},o=r.fetch,a=o===void 0?Ce:o,u=r.strictGlobal,i=u===void 0?!1:u,c=r.success,s=r.error,l=s===void 0?function(){}:s,h=r.beforeExec,p=h===void 0?function(){}:h,m=r.afterExec,d=m===void 0?function(){}:m,y=r.scopedGlobalVariables,f=y===void 0?[]:y;return bn(t,a).then(function(v){var b=function(w,R){var I=p(R,w)||R,A=Ts(w,I,{proxy:n,strictGlobal:i,scopedGlobalVariables:f});ns(w,A),d(R,w)};function T(M,w,R){if(M===e){Jc(i?n:window);try{b(M,w);var I=n[Qc(i?n:window)]||{};R(I)}catch(A){throw console.error("[import-html-entry]: error occurs while executing entry script ".concat(M)),A}}else if(typeof w=="string")try{M!=null&&M.src?b(M.src,w):b(M,w)}catch(A){gr(A,"[import-html-entry]: error occurs while executing normal script ".concat(M))}else w.async&&(w==null||w.content.then(function(A){return b(w.src,A)}).catch(function(A){gr(A,"[import-html-entry]: error occurs while executing async script ".concat(w.src))}))}function L(M,w){if(M<t.length){var R=t[M],I=v[M];T(R,I,w),!e&&M===t.length-1?w():L(M+1,w)}}return new Promise(function(M){return L(0,c||M)})}).catch(function(v){throw l(),v})}function _s(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=Ce,r=!1,o=Qt,a=Jt,u=t.postProcessTemplate;return typeof t=="function"?n=t:(t.fetch&&(typeof t.fetch=="function"?n=t.fetch:(n=t.fetch.fn||Ce,r=!!t.fetch.autoDecodeResponse)),o=t.getPublicPath||t.getDomain||Qt,a=t.getTemplate||Jt),mr[e]||(mr[e]=n(e).then(function(i){return ts(i,r)}).then(function(i){var c=o(e),s=Ps(a(i),c,u),l=s.template,h=s.scripts,p=s.entry,m=s.styles;return jo(l,m,{fetch:n}).then(function(d){return{template:d,assetPublicPath:c,getExternalScripts:function(){return bn(h,n)},getExternalStyleSheets:function(){return wn(m,n)},execScripts:function(f,v){var b=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return h.length?mt(p,h,f,No({fetch:n,strictGlobal:v},b)):Promise.resolve()}}})}))}function Os(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.fetch,r=n===void 0?Ce:n,o=t.getTemplate,a=o===void 0?Jt:o,u=t.postProcessTemplate,i=t.getPublicPath||t.getDomain||Qt;if(!e)throw new SyntaxError("entry should not be empty!");if(typeof e=="string")return _s(e,{fetch:r,getPublicPath:i,getTemplate:a,postProcessTemplate:u});if(Array.isArray(e.scripts)||Array.isArray(e.styles)){var c=e.scripts,s=c===void 0?[]:c,l=e.styles,h=l===void 0?[]:l,p=e.html,m=p===void 0?"":p,d=function(v){return h.reduceRight(function(b,T){return"".concat(vt(T)).concat(b)},v)},y=function(v){return s.reduce(function(b,T){return"".concat(b).concat(ko(T))},v)};return jo(a(y(d(m))),h,{fetch:r}).then(function(f){return{template:f,assetPublicPath:i(e),getExternalScripts:function(){return bn(s,r)},getExternalStyleSheets:function(){return wn(h,r)},execScripts:function(b,T){var L=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return s.length?mt(s[s.length-1],s,b,No({fetch:r,strictGlobal:T},L)):Promise.resolve()}}})}else throw new SyntaxError("entry scripts or styles should be array!")}function xs(e){return{beforeLoad:function(){return j(O.mark(function n(){return O.wrap(function(o){for(;;)switch(o.prev=o.next){case 0:e.__POWERED_BY_QIANKUN__=!0;case 1:case"end":return o.stop()}},n)}))()},beforeMount:function(){return j(O.mark(function n(){return O.wrap(function(o){for(;;)switch(o.prev=o.next){case 0:e.__POWERED_BY_QIANKUN__=!0;case 1:case"end":return o.stop()}},n)}))()},beforeUnmount:function(){return j(O.mark(function n(){return O.wrap(function(o){for(;;)switch(o.prev=o.next){case 0:delete e.__POWERED_BY_QIANKUN__;case 1:case"end":return o.stop()}},n)}))()}}}var yr=window.__INJECTED_PUBLIC_PATH_BY_QIANKUN__;function Ms(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"/",n=!1;return{beforeLoad:function(){return j(O.mark(function o(){return O.wrap(function(u){for(;;)switch(u.prev=u.next){case 0:e.__INJECTED_PUBLIC_PATH_BY_QIANKUN__=t;case 1:case"end":return u.stop()}},o)}))()},beforeMount:function(){return j(O.mark(function o(){return O.wrap(function(u){for(;;)switch(u.prev=u.next){case 0:n&&(e.__INJECTED_PUBLIC_PATH_BY_QIANKUN__=t);case 1:case"end":return u.stop()}},o)}))()},beforeUnmount:function(){return j(O.mark(function o(){return O.wrap(function(u){for(;;)switch(u.prev=u.next){case 0:yr===void 0?delete e.__INJECTED_PUBLIC_PATH_BY_QIANKUN__:e.__INJECTED_PUBLIC_PATH_BY_QIANKUN__=yr,n=!0;case 2:case"end":return u.stop()}},o)}))()}}}function Cs(e,t){return Go({},xs(e),Ms(e,t),function(n,r){return Oo(n??[],r??[])})}function wr(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Ba(r.key),r)}}function He(e,t,n){return t&&wr(e.prototype,t),n&&wr(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function $e(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Xe(e,t){return Xe=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,o){return r.__proto__=o,r},Xe(e,t)}function Ls(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Xe(e,t)}function ze(e){return ze=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},ze(e)}function Ho(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function As(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Rs(e,t){if(t&&(ee(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return As(e)}function Is(e){var t=Ho();return function(){var r=ze(e),o;if(t){var a=ze(this).constructor;o=Reflect.construct(r,arguments,a)}else o=r.apply(this,arguments);return Rs(this,o)}}function Gs(e){try{return Function.toString.call(e).indexOf("[native code]")!==-1}catch{return typeof e=="function"}}function dt(e,t,n){return Ho()?dt=Reflect.construct.bind():dt=function(o,a,u){var i=[null];i.push.apply(i,a);var c=Function.bind.apply(o,i),s=new c;return u&&Xe(s,u.prototype),s},dt.apply(null,arguments)}function Zt(e){var t=typeof Map=="function"?new Map:void 0;return Zt=function(r){if(r===null||!Gs(r))return r;if(typeof r!="function")throw new TypeError("Super expression must either be null or a function");if(typeof t<"u"){if(t.has(r))return t.get(r);t.set(r,o)}function o(){return dt(r,arguments,ze(this).constructor)}return o.prototype=Object.create(r.prototype,{constructor:{value:o,enumerable:!1,writable:!0,configurable:!0}}),Xe(o,r)},Zt(e)}var Qe=function(e){Ls(n,e);var t=Is(n);function n(r){return $e(this,n),t.call(this,"[qiankun]: ".concat(r))}return He(n)}(Zt(Error)),re;(function(e){e.Proxy="Proxy",e.Snapshot="Snapshot",e.LegacyProxy="LegacyProxy"})(re||(re={}));var Ds=/\s/;function ks(e){for(var t=e.length;t--&&Ds.test(e.charAt(t)););return t}var Ns=ks,js=Ns,Hs=/^\s+/;function $s(e){return e&&e.slice(0,js(e)+1).replace(Hs,"")}var Fs=$s,Ws=Qr,Bs=dn,Vs="[object Symbol]";function Us(e){return typeof e=="symbol"||Bs(e)&&Ws(e)==Vs}var $o=Us,qs=Fs,br=Et,Ks=$o,Er=0/0,Ys=/^[-+]0x[0-9a-f]+$/i,Xs=/^0b[01]+$/i,zs=/^0o[0-7]+$/i,Qs=parseInt;function Js(e){if(typeof e=="number")return e;if(Ks(e))return Er;if(br(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=br(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=qs(e);var n=Xs.test(e);return n||zs.test(e)?Qs(e.slice(2),n?2:8):Ys.test(e)?Er:+e}var Zs=Js,el=Zs,Sr=1/0,tl=17976931348623157e292;function nl(e){if(!e)return e===0?e:0;if(e=el(e),e===Sr||e===-Sr){var t=e<0?-1:1;return t*tl}return e===e?e:0}var rl=nl,ol=rl;function al(e){var t=ol(e),n=t%1;return t===t?n?t-n:t:0}var il=al,ul=il,cl="Expected a function";function sl(e,t){var n;if(typeof t!="function")throw new TypeError(cl);return e=ul(e),function(){return--e>0&&(n=t.apply(this,arguments)),e<=1&&(t=void 0),n}}var ll=sl,fl=ll;function dl(e){return fl(2,e)}var pl=dl;const hl=ve(pl);function vl(e,t,n,r){var o=-1,a=e==null?0:e.length;for(r&&a&&(n=e[++o]);++o<a;)n=t(n,e[o],o,e);return n}var ml=vl;function gl(e){return function(t){return e==null?void 0:e[t]}}var yl=gl,wl=yl,bl={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},El=wl(bl),Sl=El;function Pl(e,t){for(var n=-1,r=e==null?0:e.length,o=Array(r);++n<r;)o[n]=t(e[n],n,e);return o}var Fo=Pl,Pr=qr,Tl=Fo,_l=Ze,Ol=$o,xl=1/0,Tr=Pr?Pr.prototype:void 0,_r=Tr?Tr.toString:void 0;function Wo(e){if(typeof e=="string")return e;if(_l(e))return Tl(e,Wo)+"";if(Ol(e))return _r?_r.call(e):"";var t=e+"";return t=="0"&&1/e==-xl?"-0":t}var Ml=Wo,Cl=Ml;function Ll(e){return e==null?"":Cl(e)}var Bo=Ll,Al=Sl,Rl=Bo,Il=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,Gl="\\u0300-\\u036f",Dl="\\ufe20-\\ufe2f",kl="\\u20d0-\\u20ff",Nl=Gl+Dl+kl,jl="["+Nl+"]",Hl=RegExp(jl,"g");function $l(e){return e=Rl(e),e&&e.replace(Il,Al).replace(Hl,"")}var Fl=$l,Wl=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g;function Bl(e){return e.match(Wl)||[]}var Vl=Bl,Ul=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/;function ql(e){return Ul.test(e)}var Kl=ql,Vo="\\ud800-\\udfff",Yl="\\u0300-\\u036f",Xl="\\ufe20-\\ufe2f",zl="\\u20d0-\\u20ff",Ql=Yl+Xl+zl,Uo="\\u2700-\\u27bf",qo="a-z\\xdf-\\xf6\\xf8-\\xff",Jl="\\xac\\xb1\\xd7\\xf7",Zl="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",ef="\\u2000-\\u206f",tf=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",Ko="A-Z\\xc0-\\xd6\\xd8-\\xde",nf="\\ufe0e\\ufe0f",Yo=Jl+Zl+ef+tf,Xo="['’]",Or="["+Yo+"]",rf="["+Ql+"]",zo="\\d+",of="["+Uo+"]",Qo="["+qo+"]",Jo="[^"+Vo+Yo+zo+Uo+qo+Ko+"]",af="\\ud83c[\\udffb-\\udfff]",uf="(?:"+rf+"|"+af+")",cf="[^"+Vo+"]",Zo="(?:\\ud83c[\\udde6-\\uddff]){2}",ea="[\\ud800-\\udbff][\\udc00-\\udfff]",Le="["+Ko+"]",sf="\\u200d",xr="(?:"+Qo+"|"+Jo+")",lf="(?:"+Le+"|"+Jo+")",Mr="(?:"+Xo+"(?:d|ll|m|re|s|t|ve))?",Cr="(?:"+Xo+"(?:D|LL|M|RE|S|T|VE))?",ta=uf+"?",na="["+nf+"]?",ff="(?:"+sf+"(?:"+[cf,Zo,ea].join("|")+")"+na+ta+")*",df="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",pf="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",hf=na+ta+ff,vf="(?:"+[of,Zo,ea].join("|")+")"+hf,mf=RegExp([Le+"?"+Qo+"+"+Mr+"(?="+[Or,Le,"$"].join("|")+")",lf+"+"+Cr+"(?="+[Or,Le+xr,"$"].join("|")+")",Le+"?"+xr+"+"+Mr,Le+"+"+Cr,pf,df,zo,vf].join("|"),"g");function gf(e){return e.match(mf)||[]}var yf=gf,wf=Vl,bf=Kl,Ef=Bo,Sf=yf;function Pf(e,t,n){return e=Ef(e),t=n?void 0:t,t===void 0?bf(e)?Sf(e):wf(e):e.match(t)||[]}var Tf=Pf,_f=ml,Of=Fl,xf=Tf,Mf="['’]",Cf=RegExp(Mf,"g");function Lf(e){return function(t){return _f(xf(Of(t).replace(Cf,"")),e,"")}}var Af=Lf,Rf=Af,If=Rf(function(e,t,n){return e+(n?"_":"")+t.toLowerCase()}),Gf=If;const Df=ve(Gf);var ra=Zr,kf="Expected a function";function En(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new TypeError(kf);var n=function(){var r=arguments,o=t?t.apply(this,r):r[0],a=n.cache;if(a.has(o))return a.get(o);var u=e.apply(this,r);return n.cache=a.set(o,u)||a,u};return n.cache=new(En.Cache||ra),n}En.Cache=ra;var Nf=En;const jf=ve(Nf);var Hf="2.10.15";function Ae(e){return Array.isArray(e)?e:[e]}var $f=typeof window.__zone_symbol__setTimeout=="function"?window.__zone_symbol__setTimeout:function(e){return Promise.resolve().then(e)},kt=!1;function Ff(e){kt||(kt=!0,$f(function(){e(),kt=!1}))}var Nt=new WeakMap;function Wf(e){var t=e.prototype&&e.prototype.constructor===e&&Object.getOwnPropertyNames(e.prototype).length>1;if(t)return!0;if(Nt.has(e))return Nt.get(e);var n=t;if(!n){var r=e.toString(),o=/^function\b\s[A-Z].*/,a=/^class\b/;n=o.test(r)||a.test(r)}return Nt.set(e,n),n}var Lr=new WeakMap;function oa(e){if(Lr.has(e))return!0;var t=typeof e=="function"&&e instanceof Function;return t&&Lr.set(e,t),t}var Ar=new WeakMap;function Bf(e,t){if(!e||!t)return!1;var n=Ar.get(e)||{};if(n[t])return n[t];var r=Object.getOwnPropertyDescriptor(e,t),o=!!(r&&r.configurable===!1&&(r.writable===!1||r.get&&!r.set));return n[t]=o,Ar.set(e,n),o}var jt=new WeakMap;function aa(e){if(jt.has(e))return jt.get(e);var t=e.name.indexOf("bound ")===0&&!e.hasOwnProperty("prototype");return jt.set(e,t),t}var Vf=jf(function(){try{return new Function("const { a } = { a: 1 }")(),!0}catch{return!1}}),qe="qiankun-head";function Uf(e,t){return function(n){var r;return n.indexOf("<head>")!==-1?r=n.replace("<head>","<".concat(qe,">")).replace("</head>","</".concat(qe,">")):r="<".concat(qe,"></").concat(qe,">").concat(n),'<div id="'.concat(ia(e),'" data-name="').concat(e,'" data-version="').concat(Hf,'" data-sandbox-cfg=').concat(JSON.stringify(t),">").concat(r,"</div>")}}function ia(e){return"__qiankun_microapp_wrapper_for_".concat(Df(e),"__")}var z=new Function("return this")(),Rr=new Function("return document")(),qf=hl(function(){return z.hasOwnProperty("__app_instance_name_map__")||Object.defineProperty(z,"__app_instance_name_map__",{enumerable:!1,configurable:!0,writable:!0,value:{}}),z.__app_instance_name_map__}),Kf=function(t){var n=qf();return t in n?(n[t]++,"".concat(t,"_").concat(n[t])):(z.__app_instance_name_map__[t]=0,t)};function Ht(e){var t=e??{},n=t.bootstrap,r=t.mount,o=t.unmount;return Me(n)&&Me(r)&&Me(o)}var ua=He(function e(){var t=this;$e(this,e),this.promise=void 0,this.resolve=void 0,this.reject=void 0,this.promise=new Promise(function(n,r){t.resolve=n,t.reject=r})});function Yf(e){return ee(e)!=="object"||e.strictStyleIsolation?!1:!!e.experimentalStyleIsolation}function Xf(e,t){if(t.body.contains(e)){for(var n="",r,o,a=e;a!==t.documentElement;){for(r=0,o=a;o;)o.nodeType===1&&o.nodeName===a.nodeName&&(r+=1),o=o.previousSibling;n="*[name()='".concat(a.nodeName,"'][").concat(r,"]/").concat(n),a=a.parentNode}return n="/*[name()='".concat(t.documentElement.nodeName,"']/").concat(n),n=n.replace(/\/$/,""),n}}function ca(e){return typeof e=="string"?document.querySelector(e):e}function zf(e){if(e){var t=ca(e);if(t)return Xf(t,document)}}var Sn=null;function en(){return Sn}function Qf(e){Sn=e}function Jf(){Sn=null}var Ir=new WeakMap;function sa(e,t){if(oa(t)&&!aa(t)&&!Wf(t)){var n=Ir.get(t);if(n)return n;var r=Function.prototype.bind.call(t,e);if(Object.getOwnPropertyNames(t).forEach(function(i){r.hasOwnProperty(i)||Object.defineProperty(r,i,Object.getOwnPropertyDescriptor(t,i))}),t.hasOwnProperty("prototype")&&!r.hasOwnProperty("prototype")&&Object.defineProperty(r,"prototype",{value:t.prototype,enumerable:!1,writable:!0}),typeof t.toString=="function"){var o=t.hasOwnProperty("toString")&&!r.hasOwnProperty("toString"),a=r.toString===Function.prototype.toString;if(o||a){var u=Object.getOwnPropertyDescriptor(o?t:Function.prototype,"toString");Object.defineProperty(r,"toString",k(k({},u),u!=null&&u.get?null:{value:function(){return t.toString()}}))}}return Ir.set(t,r),r}return t}function Zf(e,t){var n=Object.getOwnPropertyDescriptor(e,t);return n?n.configurable:!0}var ed=function(){function e(t){var n=this,r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:window;$e(this,e),this.addedPropsMapInSandbox=new Map,this.modifiedPropsOriginalValueMapInSandbox=new Map,this.currentUpdatedPropsValueMap=new Map,this.name=void 0,this.proxy=void 0,this.globalContext=void 0,this.type=void 0,this.sandboxRunning=!0,this.latestSetProp=null,this.name=t,this.globalContext=r,this.type=re.LegacyProxy;var o=this.addedPropsMapInSandbox,a=this.modifiedPropsOriginalValueMapInSandbox,u=this.currentUpdatedPropsValueMap,i=r,c=Object.create(null),s=function(p,m,d){var y=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0;return n.sandboxRunning&&(i.hasOwnProperty(p)?a.has(p)||a.set(p,d):o.set(p,m),u.set(p,m),y&&(i[p]=m),n.latestSetProp=p),!0},l=new Proxy(c,{set:function(p,m,d){var y=i[m];return s(m,d,y,!0)},get:function(p,m){if(m==="top"||m==="parent"||m==="window"||m==="self")return l;var d=i[m];return sa(i,d)},has:function(p,m){return m in i},getOwnPropertyDescriptor:function(p,m){var d=Object.getOwnPropertyDescriptor(i,m);return d&&!d.configurable&&(d.configurable=!0),d},defineProperty:function(p,m,d){var y=i[m],f=Reflect.defineProperty(i,m,d),v=i[m];return s(m,v,y,!1),f}});this.proxy=l}return He(e,[{key:"setWindowProp",value:function(n,r,o){r===void 0&&o?delete this.globalContext[n]:Zf(this.globalContext,n)&&ee(n)!=="symbol"&&(Object.defineProperty(this.globalContext,n,{writable:!0,configurable:!0}),this.globalContext[n]=r)}},{key:"active",value:function(){var n=this;this.sandboxRunning||this.currentUpdatedPropsValueMap.forEach(function(r,o){return n.setWindowProp(o,r)}),this.sandboxRunning=!0}},{key:"inactive",value:function(){var n=this;this.modifiedPropsOriginalValueMapInSandbox.forEach(function(r,o){return n.setWindowProp(o,r)}),this.addedPropsMapInSandbox.forEach(function(r,o){return n.setWindowProp(o,void 0,!0)}),this.sandboxRunning=!1}},{key:"patchDocument",value:function(){}}]),e}(),Ke;(function(e){e[e.STYLE=1]="STYLE",e[e.MEDIA=4]="MEDIA",e[e.SUPPORTS=12]="SUPPORTS",e[e.IMPORT=3]="IMPORT",e[e.FONT_FACE=5]="FONT_FACE",e[e.PAGE=6]="PAGE",e[e.KEYFRAMES=7]="KEYFRAMES",e[e.KEYFRAME=8]="KEYFRAME"})(Ke||(Ke={}));var it=function(t){return[].slice.call(t,0)},td=HTMLBodyElement.prototype.appendChild,la=function(){function e(){$e(this,e),this.sheet=void 0,this.swapNode=void 0;var t=document.createElement("style");td.call(document.body,t),this.swapNode=t,this.sheet=t.sheet,this.sheet.disabled=!0}return He(e,[{key:"process",value:function(n){var r=this,o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";if(!(e.ModifiedTag in n)){if(n.textContent!==""){var a,u=document.createTextNode(n.textContent||"");this.swapNode.appendChild(u);var i=this.swapNode.sheet,c=it((a=i==null?void 0:i.cssRules)!==null&&a!==void 0?a:[]),s=this.rewrite(c,o);n.textContent=s,this.swapNode.removeChild(u),n[e.ModifiedTag]=!0;return}var l=new MutationObserver(function(h){for(var p=0;p<h.length;p+=1){var m=h[p];if(e.ModifiedTag in n)return;if(m.type==="childList"){var d,y=n.sheet,f=it((d=y==null?void 0:y.cssRules)!==null&&d!==void 0?d:[]),v=r.rewrite(f,o);n.textContent=v,n[e.ModifiedTag]=!0}}});l.observe(n,{childList:!0})}}},{key:"rewrite",value:function(n){var r=this,o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",a="";return n.forEach(function(u){switch(u.type){case Ke.STYLE:a+=r.ruleStyle(u,o);break;case Ke.MEDIA:a+=r.ruleMedia(u,o);break;case Ke.SUPPORTS:a+=r.ruleSupport(u,o);break;default:typeof u.cssText=="string"&&(a+="".concat(u.cssText));break}}),a}},{key:"ruleStyle",value:function(n,r){var o=/((?:[^\w\-.#]|^)(body|html|:root))/gm,a=/(html[^\w{[]+)/gm,u=n.selectorText.trim(),i="";if(typeof n.cssText=="string"&&(i=n.cssText),u==="html"||u==="body"||u===":root")return i.replace(o,r);if(a.test(n.selectorText)){var c=/(html[^\w{]+)(\+|~)/gm;c.test(n.selectorText)||(i=i.replace(a,""))}return i=i.replace(/^[\s\S]+{/,function(s){return s.replace(/(^|,\n?)([^,]+)/g,function(l,h,p){return o.test(l)?l.replace(o,function(m){var d=[",","("];return m&&d.includes(m[0])?"".concat(m[0]).concat(r):r}):"".concat(h).concat(r," ").concat(p.replace(/^ */,""))})}),i}},{key:"ruleMedia",value:function(n,r){var o=this.rewrite(it(n.cssRules),r);return"@media ".concat(n.conditionText||n.media.mediaText," {").concat(o,"}")}},{key:"ruleSupport",value:function(n,r){var o=this.rewrite(it(n.cssRules),r);return"@supports ".concat(n.conditionText||n.cssText.split("{")[0]," {").concat(o,"}")}}]),e}();la.ModifiedTag="Symbol(style-modified-qiankun)";var $t,tn="data-qiankun",nn=function(t,n,r){$t||($t=new la),n.tagName==="LINK"&&console.warn("Feature: sandbox.experimentalStyleIsolation is not support for link element yet.");var o=t;if(o){var a=(o.tagName||"").toLowerCase();if(a&&n.tagName==="STYLE"){var u="".concat(a,"[").concat(tn,'="').concat(r,'"]');$t.process(n,u)}}},nd="__lodash_hash_undefined__";function rd(e){return this.__data__.set(e,nd),this}var od=rd;function ad(e){return this.__data__.has(e)}var id=ad,ud=Zr,cd=od,sd=id;function gt(e){var t=-1,n=e==null?0:e.length;for(this.__data__=new ud;++t<n;)this.add(e[t])}gt.prototype.add=gt.prototype.push=cd;gt.prototype.has=sd;var ld=gt;function fd(e,t,n,r){for(var o=e.length,a=n+(r?1:-1);r?a--:++a<o;)if(t(e[a],a,e))return a;return-1}var dd=fd;function pd(e){return e!==e}var hd=pd;function vd(e,t,n){for(var r=n-1,o=e.length;++r<o;)if(e[r]===t)return r;return-1}var md=vd,gd=dd,yd=hd,wd=md;function bd(e,t,n){return t===t?wd(e,t,n):gd(e,yd,n)}var Ed=bd,Sd=Ed;function Pd(e,t){var n=e==null?0:e.length;return!!n&&Sd(e,t,0)>-1}var Td=Pd;function _d(e,t,n){for(var r=-1,o=e==null?0:e.length;++r<o;)if(n(t,e[r]))return!0;return!1}var Od=_d;function xd(e,t){return e.has(t)}var Md=xd,Cd=ld,Ld=Td,Ad=Od,Rd=Fo,Id=Va,Gd=Md,Dd=200;function kd(e,t,n,r){var o=-1,a=Ld,u=!0,i=e.length,c=[],s=t.length;if(!i)return c;n&&(t=Rd(t,Id(n))),r?(a=Ad,u=!1):t.length>=Dd&&(a=Gd,u=!1,t=new Cd(t));e:for(;++o<i;){var l=e[o],h=n==null?l:n(l);if(l=r||l!==0?l:0,u&&h===h){for(var p=s;p--;)if(t[p]===h)continue e;c.push(l)}else a(t,h,r)||c.push(l)}return c}var Nd=kd,jd=Nd,Hd=Io,$d=Co,Fd=Hd(function(e,t){return $d(e)?jd(e,t):[]}),Wd=Fd;const fa=ve(Wd);var Bd=window.Proxy?["Array","ArrayBuffer","Boolean","constructor","DataView","Date","decodeURI","decodeURIComponent","encodeURI","encodeURIComponent","Error","escape","eval","EvalError","Float32Array","Float64Array","Function","hasOwnProperty","Infinity","Int16Array","Int32Array","Int8Array","isFinite","isNaN","isPrototypeOf","JSON","Map","Math","NaN","Number","Object","parseFloat","parseInt","Promise","propertyIsEnumerable","Proxy","RangeError","ReferenceError","Reflect","RegExp","Set","String","Symbol","SyntaxError","toLocaleString","toString","TypeError","Uint16Array","Uint32Array","Uint8Array","Uint8ClampedArray","undefined","unescape","URIError","valueOf","WeakMap","WeakSet"].filter(function(e){return e in window}):[],Vd=["AbortController","AbortSignal","addEventListener","alert","AnalyserNode","Animation","AnimationEffectReadOnly","AnimationEffectTiming","AnimationEffectTimingReadOnly","AnimationEvent","AnimationPlaybackEvent","AnimationTimeline","applicationCache","ApplicationCache","ApplicationCacheErrorEvent","atob","Attr","Audio","AudioBuffer","AudioBufferSourceNode","AudioContext","AudioDestinationNode","AudioListener","AudioNode","AudioParam","AudioProcessingEvent","AudioScheduledSourceNode","AudioWorkletGlobalScope","AudioWorkletNode","AudioWorkletProcessor","BarProp","BaseAudioContext","BatteryManager","BeforeUnloadEvent","BiquadFilterNode","Blob","BlobEvent","blur","BroadcastChannel","btoa","BudgetService","ByteLengthQueuingStrategy","Cache","caches","CacheStorage","cancelAnimationFrame","cancelIdleCallback","CanvasCaptureMediaStreamTrack","CanvasGradient","CanvasPattern","CanvasRenderingContext2D","ChannelMergerNode","ChannelSplitterNode","CharacterData","clearInterval","clearTimeout","clientInformation","ClipboardEvent","ClipboardItem","close","closed","CloseEvent","Comment","CompositionEvent","CompressionStream","confirm","console","ConstantSourceNode","ConvolverNode","CountQueuingStrategy","createImageBitmap","Credential","CredentialsContainer","crypto","Crypto","CryptoKey","CSS","CSSConditionRule","CSSFontFaceRule","CSSGroupingRule","CSSImportRule","CSSKeyframeRule","CSSKeyframesRule","CSSMatrixComponent","CSSMediaRule","CSSNamespaceRule","CSSPageRule","CSSPerspective","CSSRotate","CSSRule","CSSRuleList","CSSScale","CSSSkew","CSSSkewX","CSSSkewY","CSSStyleDeclaration","CSSStyleRule","CSSStyleSheet","CSSSupportsRule","CSSTransformValue","CSSTranslate","CustomElementRegistry","customElements","CustomEvent","DataTransfer","DataTransferItem","DataTransferItemList","DecompressionStream","defaultstatus","defaultStatus","DelayNode","DeviceMotionEvent","DeviceOrientationEvent","devicePixelRatio","dispatchEvent","document","Document","DocumentFragment","DocumentType","DOMError","DOMException","DOMImplementation","DOMMatrix","DOMMatrixReadOnly","DOMParser","DOMPoint","DOMPointReadOnly","DOMQuad","DOMRect","DOMRectList","DOMRectReadOnly","DOMStringList","DOMStringMap","DOMTokenList","DragEvent","DynamicsCompressorNode","Element","ErrorEvent","event","Event","EventSource","EventTarget","external","fetch","File","FileList","FileReader","find","focus","FocusEvent","FontFace","FontFaceSetLoadEvent","FormData","FormDataEvent","frameElement","frames","GainNode","Gamepad","GamepadButton","GamepadEvent","getComputedStyle","getSelection","HashChangeEvent","Headers","history","History","HTMLAllCollection","HTMLAnchorElement","HTMLAreaElement","HTMLAudioElement","HTMLBaseElement","HTMLBodyElement","HTMLBRElement","HTMLButtonElement","HTMLCanvasElement","HTMLCollection","HTMLContentElement","HTMLDataElement","HTMLDataListElement","HTMLDetailsElement","HTMLDialogElement","HTMLDirectoryElement","HTMLDivElement","HTMLDListElement","HTMLDocument","HTMLElement","HTMLEmbedElement","HTMLFieldSetElement","HTMLFontElement","HTMLFormControlsCollection","HTMLFormElement","HTMLFrameElement","HTMLFrameSetElement","HTMLHeadElement","HTMLHeadingElement","HTMLHRElement","HTMLHtmlElement","HTMLIFrameElement","HTMLImageElement","HTMLInputElement","HTMLLabelElement","HTMLLegendElement","HTMLLIElement","HTMLLinkElement","HTMLMapElement","HTMLMarqueeElement","HTMLMediaElement","HTMLMenuElement","HTMLMetaElement","HTMLMeterElement","HTMLModElement","HTMLObjectElement","HTMLOListElement","HTMLOptGroupElement","HTMLOptionElement","HTMLOptionsCollection","HTMLOutputElement","HTMLParagraphElement","HTMLParamElement","HTMLPictureElement","HTMLPreElement","HTMLProgressElement","HTMLQuoteElement","HTMLScriptElement","HTMLSelectElement","HTMLShadowElement","HTMLSlotElement","HTMLSourceElement","HTMLSpanElement","HTMLStyleElement","HTMLTableCaptionElement","HTMLTableCellElement","HTMLTableColElement","HTMLTableElement","HTMLTableRowElement","HTMLTableSectionElement","HTMLTemplateElement","HTMLTextAreaElement","HTMLTimeElement","HTMLTitleElement","HTMLTrackElement","HTMLUListElement","HTMLUnknownElement","HTMLVideoElement","IDBCursor","IDBCursorWithValue","IDBDatabase","IDBFactory","IDBIndex","IDBKeyRange","IDBObjectStore","IDBOpenDBRequest","IDBRequest","IDBTransaction","IDBVersionChangeEvent","IdleDeadline","IIRFilterNode","Image","ImageBitmap","ImageBitmapRenderingContext","ImageCapture","ImageData","indexedDB","innerHeight","innerWidth","InputEvent","IntersectionObserver","IntersectionObserverEntry","Intl","isSecureContext","KeyboardEvent","KeyframeEffect","KeyframeEffectReadOnly","length","localStorage","location","Location","locationbar","matchMedia","MediaDeviceInfo","MediaDevices","MediaElementAudioSourceNode","MediaEncryptedEvent","MediaError","MediaKeyMessageEvent","MediaKeySession","MediaKeyStatusMap","MediaKeySystemAccess","MediaList","MediaMetadata","MediaQueryList","MediaQueryListEvent","MediaRecorder","MediaSettingsRange","MediaSource","MediaStream","MediaStreamAudioDestinationNode","MediaStreamAudioSourceNode","MediaStreamConstraints","MediaStreamEvent","MediaStreamTrack","MediaStreamTrackEvent","menubar","MessageChannel","MessageEvent","MessagePort","MIDIAccess","MIDIConnectionEvent","MIDIInput","MIDIInputMap","MIDIMessageEvent","MIDIOutput","MIDIOutputMap","MIDIPort","MimeType","MimeTypeArray","MouseEvent","moveBy","moveTo","MutationEvent","MutationObserver","MutationRecord","name","NamedNodeMap","NavigationPreloadManager","navigator","Navigator","NavigatorUAData","NetworkInformation","Node","NodeFilter","NodeIterator","NodeList","Notification","OfflineAudioCompletionEvent","OfflineAudioContext","offscreenBuffering","OffscreenCanvas","OffscreenCanvasRenderingContext2D","onabort","onafterprint","onanimationend","onanimationiteration","onanimationstart","onappinstalled","onauxclick","onbeforeinstallprompt","onbeforeprint","onbeforeunload","onblur","oncancel","oncanplay","oncanplaythrough","onchange","onclick","onclose","oncontextmenu","oncuechange","ondblclick","ondevicemotion","ondeviceorientation","ondeviceorientationabsolute","ondrag","ondragend","ondragenter","ondragleave","ondragover","ondragstart","ondrop","ondurationchange","onemptied","onended","onerror","onfocus","ongotpointercapture","onhashchange","oninput","oninvalid","onkeydown","onkeypress","onkeyup","onlanguagechange","onload","onloadeddata","onloadedmetadata","onloadstart","onlostpointercapture","onmessage","onmessageerror","onmousedown","onmouseenter","onmouseleave","onmousemove","onmouseout","onmouseover","onmouseup","onmousewheel","onoffline","ononline","onpagehide","onpageshow","onpause","onplay","onplaying","onpointercancel","onpointerdown","onpointerenter","onpointerleave","onpointermove","onpointerout","onpointerover","onpointerup","onpopstate","onprogress","onratechange","onrejectionhandled","onreset","onresize","onscroll","onsearch","onseeked","onseeking","onselect","onstalled","onstorage","onsubmit","onsuspend","ontimeupdate","ontoggle","ontransitionend","onunhandledrejection","onunload","onvolumechange","onwaiting","onwheel","open","openDatabase","opener","Option","origin","OscillatorNode","outerHeight","outerWidth","OverconstrainedError","PageTransitionEvent","pageXOffset","pageYOffset","PannerNode","parent","Path2D","PaymentAddress","PaymentRequest","PaymentRequestUpdateEvent","PaymentResponse","performance","Performance","PerformanceEntry","PerformanceLongTaskTiming","PerformanceMark","PerformanceMeasure","PerformanceNavigation","PerformanceNavigationTiming","PerformanceObserver","PerformanceObserverEntryList","PerformancePaintTiming","PerformanceResourceTiming","PerformanceTiming","PeriodicWave","Permissions","PermissionStatus","personalbar","PhotoCapabilities","Plugin","PluginArray","PointerEvent","PopStateEvent","postMessage","Presentation","PresentationAvailability","PresentationConnection","PresentationConnectionAvailableEvent","PresentationConnectionCloseEvent","PresentationConnectionList","PresentationReceiver","PresentationRequest","print","ProcessingInstruction","ProgressEvent","PromiseRejectionEvent","prompt","PushManager","PushSubscription","PushSubscriptionOptions","queueMicrotask","RadioNodeList","Range","ReadableByteStreamController","ReadableStream","ReadableStreamBYOBReader","ReadableStreamBYOBRequest","ReadableStreamDefaultController","ReadableStreamDefaultReader","registerProcessor","RemotePlayback","removeEventListener","reportError","Request","requestAnimationFrame","requestIdleCallback","resizeBy","ResizeObserver","ResizeObserverEntry","resizeTo","Response","RTCCertificate","RTCDataChannel","RTCDataChannelEvent","RTCDtlsTransport","RTCIceCandidate","RTCIceGatherer","RTCIceTransport","RTCPeerConnection","RTCPeerConnectionIceEvent","RTCRtpContributingSource","RTCRtpReceiver","RTCRtpSender","RTCSctpTransport","RTCSessionDescription","RTCStatsReport","RTCTrackEvent","screen","Screen","screenLeft","ScreenOrientation","screenTop","screenX","screenY","ScriptProcessorNode","scroll","scrollbars","scrollBy","scrollTo","scrollX","scrollY","SecurityPolicyViolationEvent","Selection","self","ServiceWorker","ServiceWorkerContainer","ServiceWorkerRegistration","sessionStorage","setInterval","setTimeout","ShadowRoot","SharedWorker","SourceBuffer","SourceBufferList","speechSynthesis","SpeechSynthesisEvent","SpeechSynthesisUtterance","StaticRange","status","statusbar","StereoPannerNode","stop","Storage","StorageEvent","StorageManager","structuredClone","styleMedia","StyleSheet","StyleSheetList","SubmitEvent","SubtleCrypto","SVGAElement","SVGAngle","SVGAnimatedAngle","SVGAnimatedBoolean","SVGAnimatedEnumeration","SVGAnimatedInteger","SVGAnimatedLength","SVGAnimatedLengthList","SVGAnimatedNumber","SVGAnimatedNumberList","SVGAnimatedPreserveAspectRatio","SVGAnimatedRect","SVGAnimatedString","SVGAnimatedTransformList","SVGAnimateElement","SVGAnimateMotionElement","SVGAnimateTransformElement","SVGAnimationElement","SVGCircleElement","SVGClipPathElement","SVGComponentTransferFunctionElement","SVGDefsElement","SVGDescElement","SVGDiscardElement","SVGElement","SVGEllipseElement","SVGFEBlendElement","SVGFEColorMatrixElement","SVGFEComponentTransferElement","SVGFECompositeElement","SVGFEConvolveMatrixElement","SVGFEDiffuseLightingElement","SVGFEDisplacementMapElement","SVGFEDistantLightElement","SVGFEDropShadowElement","SVGFEFloodElement","SVGFEFuncAElement","SVGFEFuncBElement","SVGFEFuncGElement","SVGFEFuncRElement","SVGFEGaussianBlurElement","SVGFEImageElement","SVGFEMergeElement","SVGFEMergeNodeElement","SVGFEMorphologyElement","SVGFEOffsetElement","SVGFEPointLightElement","SVGFESpecularLightingElement","SVGFESpotLightElement","SVGFETileElement","SVGFETurbulenceElement","SVGFilterElement","SVGForeignObjectElement","SVGGElement","SVGGeometryElement","SVGGradientElement","SVGGraphicsElement","SVGImageElement","SVGLength","SVGLengthList","SVGLinearGradientElement","SVGLineElement","SVGMarkerElement","SVGMaskElement","SVGMatrix","SVGMetadataElement","SVGMPathElement","SVGNumber","SVGNumberList","SVGPathElement","SVGPatternElement","SVGPoint","SVGPointList","SVGPolygonElement","SVGPolylineElement","SVGPreserveAspectRatio","SVGRadialGradientElement","SVGRect","SVGRectElement","SVGScriptElement","SVGSetElement","SVGStopElement","SVGStringList","SVGStyleElement","SVGSVGElement","SVGSwitchElement","SVGSymbolElement","SVGTextContentElement","SVGTextElement","SVGTextPathElement","SVGTextPositioningElement","SVGTitleElement","SVGTransform","SVGTransformList","SVGTSpanElement","SVGUnitTypes","SVGUseElement","SVGViewElement","TaskAttributionTiming","Text","TextDecoder","TextDecoderStream","TextEncoder","TextEncoderStream","TextEvent","TextMetrics","TextTrack","TextTrackCue","TextTrackCueList","TextTrackList","TimeRanges","ToggleEvent","toolbar","top","Touch","TouchEvent","TouchList","TrackEvent","TransformStream","TransformStreamDefaultController","TransitionEvent","TreeWalker","UIEvent","URL","URLSearchParams","ValidityState","visualViewport","VisualViewport","VTTCue","WaveShaperNode","WebAssembly","WebGL2RenderingContext","WebGLActiveInfo","WebGLBuffer","WebGLContextEvent","WebGLFramebuffer","WebGLProgram","WebGLQuery","WebGLRenderbuffer","WebGLRenderingContext","WebGLSampler","WebGLShader","WebGLShaderPrecisionFormat","WebGLSync","WebGLTexture","WebGLTransformFeedback","WebGLUniformLocation","WebGLVertexArrayObject","WebSocket","WheelEvent","window","Window","Worker","WritableStream","WritableStreamDefaultController","WritableStreamDefaultWriter","XMLDocument","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload","XMLSerializer","XPathEvaluator","XPathExpression","XPathResult","XSLTProcessor"];function Ud(e){return e.filter(function(n){return n in this?!1:this[n]=!0},Object.create(null))}var qd=Vd.concat([]).reduce(function(e,t){return k(k({},e),{},Z({},t,!0))},Object.create(null));function Kd(e){return e in qd}var Yd=Object.defineProperty,Xd=window.__QIANKUN_DEVELOPMENT__?["__REACT_ERROR_OVERLAY_GLOBAL_HOOK__","event"]:[],Gr=["System","__cjsWrapper"].concat(Xd),rn=!1,da=["document","top","parent","eval"],pa=["window","self","globalThis","hasOwnProperty"].concat([]),xt=Array.from(new Set(fa.apply(void 0,[Bd.concat(pa).concat("requestAnimationFrame")].concat(da)))),zd=xt.reduce(function(e,t){return k(k({},e),{},Z({},t,!0))},{}),Qd=fa.apply(void 0,[xt].concat(be(da.concat(pa)))).reduce(function(e,t){return k(k({},e),{},Z({},t,!0))},Object.create(null)),Dr=new Map([["fetch",!0],["mockDomAPIInBlackList",!1]]);function Jd(e,t){var n=new Map,r={};return Object.getOwnPropertyNames(e).filter(function(o){var a=Object.getOwnPropertyDescriptor(e,o);return!(a!=null&&a.configurable)}).forEach(function(o){var a=Object.getOwnPropertyDescriptor(e,o);if(a){var u=Object.prototype.hasOwnProperty.call(a,"get");(o==="top"||o==="parent"||o==="self"||o==="window"||o==="document"&&t||rn)&&(a.configurable=!0,u||(a.writable=!0)),u&&n.set(o,!0),Yd(r,o,Object.freeze(a))}}),{fakeWindow:r,propertiesWithGetter:n}}var Ft=0,Zd=function(){function e(t){var n=this,r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:window,o=arguments.length>2?arguments[2]:void 0;$e(this,e),this.updatedValueSet=new Set,this.document=document,this.name=void 0,this.type=void 0,this.proxy=void 0,this.sandboxRunning=!0,this.latestSetProp=null,this.globalWhitelistPrevDescriptor={},this.globalContext=void 0,this.name=t,this.globalContext=r,this.type=re.Proxy;var a=this.updatedValueSet,u=o||{},i=u.speedy,c=Jd(r,!!i),s=c.fakeWindow,l=c.propertiesWithGetter,h=new Map,p=new Proxy(s,{set:function(y,f,v){if(n.sandboxRunning){if(n.registerRunningApp(t,p),typeof f=="string"&&Gr.indexOf(f)!==-1)n.globalWhitelistPrevDescriptor[f]=Object.getOwnPropertyDescriptor(r,f),r[f]=v;else if(!y.hasOwnProperty(f)&&r.hasOwnProperty(f)){var b=Object.getOwnPropertyDescriptor(r,f),T=b.writable,L=b.configurable,M=b.enumerable,w=b.set;(T||w)&&Object.defineProperty(y,f,{configurable:L,enumerable:M,writable:!0,value:v})}else y[f]=v;return a.add(f),n.latestSetProp=f,!0}return!0},get:function(y,f){if(n.registerRunningApp(t,p),f===Symbol.unscopables)return Qd;if(f==="window"||f==="self"||f==="globalThis"||rn)return p;if(f==="top"||f==="parent"||rn)return r===r.parent?p:r[f];if(f==="hasOwnProperty")return m;if(f==="document")return n.document;if(f==="eval")return eval;if(f==="string"&&Gr.indexOf(f)!==-1)return r[f];var v=l.has(f)?r:f in y?y:r,b=v[f];if(Bf(v,f)||!Kd(f)&&!Dr.has(f))return b;var T=Dr.get(f)?z:r;return sa(T,b)},has:function(y,f){return f in zd||f in y||f in r},getOwnPropertyDescriptor:function(y,f){if(y.hasOwnProperty(f)){var v=Object.getOwnPropertyDescriptor(y,f);return h.set(f,"target"),v}if(r.hasOwnProperty(f)){var b=Object.getOwnPropertyDescriptor(r,f);return h.set(f,"globalContext"),b&&!b.configurable&&(b.configurable=!0),b}},ownKeys:function(y){return Ud(Reflect.ownKeys(r).concat(Reflect.ownKeys(y)))},defineProperty:function(y,f,v){var b=h.get(f);switch(b){case"globalContext":return Reflect.defineProperty(r,f,v);default:return Reflect.defineProperty(y,f,v)}},deleteProperty:function(y,f){return n.registerRunningApp(t,p),y.hasOwnProperty(f)&&(delete y[f],a.delete(f)),!0},getPrototypeOf:function(){return Reflect.getPrototypeOf(r)}});this.proxy=p,Ft++;function m(d){return this!==p&&this!==null&&ee(this)==="object"?Object.prototype.hasOwnProperty.call(this,d):s.hasOwnProperty(d)||r.hasOwnProperty(d)}}return He(e,[{key:"active",value:function(){this.sandboxRunning||Ft++,this.sandboxRunning=!0}},{key:"inactive",value:function(){var n=this;--Ft===0&&Object.keys(this.globalWhitelistPrevDescriptor).forEach(function(r){var o=n.globalWhitelistPrevDescriptor[r];o?Object.defineProperty(n.globalContext,r,o):delete n.globalContext[r]}),this.sandboxRunning=!1}},{key:"patchDocument",value:function(n){this.document=n}},{key:"registerRunningApp",value:function(n,r){if(this.sandboxRunning){var o=en();(!o||o.name!==n)&&Qf({name:n,window:r}),Ff(Jf)}}}]),e}(),Pn="SCRIPT",yt="LINK",Tn="STYLE",ha=Symbol("target"),va=Symbol("refNodeNo"),Oe=Symbol("qiankun-overwritten"),Je=function(t){return t.querySelector(qe)};function ep(e){return!e.type||["text/javascript","module","application/javascript","text/ecmascript","application/ecmascript"].indexOf(e.type)!==-1}function _n(e){return(e==null?void 0:e.toUpperCase())===yt||(e==null?void 0:e.toUpperCase())===Tn||(e==null?void 0:e.toUpperCase())===Pn}function ma(e){var t,n;return!e.textContent&&(((t=e.sheet)===null||t===void 0?void 0:t.cssRules.length)||((n=Ta(e))===null||n===void 0?void 0:n.length))}var on=new Map;function we(e,t,n){var r=on.get(e)||{bootstrappingPatchCount:0,mountingPatchCount:0};switch(t){case"increase":r["".concat(n,"PatchCount")]+=1;break;case"decrease":r["".concat(n,"PatchCount")]>0&&(r["".concat(n,"PatchCount")]-=1);break}on.set(e,r)}function ga(){return Array.from(on.entries()).every(function(e){var t=gn(e,2),n=t[1],r=n.bootstrappingPatchCount,o=n.mountingPatchCount;return r===0&&o===0})}function ya(e,t){return Object.defineProperties(e,{srcElement:{get:t},target:{get:t}}),e}function wa(e){var t=new CustomEvent("load"),n=ya(t,function(){return e});Me(e.onload)?e.onload(n):e.dispatchEvent(n)}function ba(e){var t=new CustomEvent("error"),n=ya(t,function(){return e});Me(e.onerror)?e.onerror(n):e.dispatchEvent(n)}function tp(e,t){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:fetch,r=document.createElement("style"),o=e.href;return r.dataset.qiankunHref=o,n(o).then(function(a){return a.text()}).then(function(a){r.appendChild(document.createTextNode(a)),t(r),wa(e)}).catch(function(){return ba(e)}),r}var kr=function(t,n,r){Object.defineProperty(t,n,{configurable:!0,enumerable:!1,writable:!0,value:r})},Ea=new WeakMap,an=new WeakMap,Sa=new WeakMap;function Pa(e){e.forEach(function(t){t instanceof HTMLStyleElement&&ma(t)&&t.sheet&&Ea.set(t,t.sheet.cssRules)})}function Ta(e){return Ea.get(e)}function Wt(e){function t(n){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null,o=n,a=e.rawDOMAppendOrInsertBefore,u=e.isInvokedByMicroApp,i=e.containerConfigGetter,c=e.target,s=c===void 0?"body":c;if(!_n(o.tagName)||!u(o))return a.call(this,o,r);if(o.tagName){var l=i(o),h=l.appName,p=l.appWrapperGetter,m=l.proxy,d=l.strictGlobal,y=l.speedySandbox,f=l.dynamicStyleSheetElements,v=l.scopedCSS,b=l.excludeAssetFilter;switch(o.tagName){case yt:case Tn:{var T=n,L=T,M=L.href;if(b&&M&&b(M))return a.call(this,o,r);kr(T,ha,s);var w=p();if(v){var R,I=((R=o.tagName)===null||R===void 0?void 0:R.toUpperCase())===yt&&o.rel==="stylesheet"&&o.href;if(I){var A,H=typeof Re.fetch=="function"?Re.fetch:(A=Re.fetch)===null||A===void 0?void 0:A.fn;T=tp(o,function(F){return nn(w,F,h)},H),Sa.set(o,T)}else nn(w,T,h)}var Y=s==="head"?Je(w):w,V=Y.contains(r)?r:null,ce;V&&(ce=Array.from(Y.childNodes).indexOf(V));var q=a.call(Y,T,V);return typeof ce=="number"&&ce!==-1&&kr(T,va,ce),f.push(T),q}case Pn:{var me=o,Q=me.src,se=me.text;if(b&&Q&&b(Q)||!ep(o))return a.call(this,o,r);var oe=p(),S=s==="head"?Je(oe):oe,g=Re.fetch,E=S.contains(r)?r:null,_=y?xt:[];if(Q){var C=!1;mt(null,[Q],m,{fetch:g,strictGlobal:d,scopedGlobalVariables:_,beforeExec:function(){var W=function(){var K=Object.getOwnPropertyDescriptor(document,"currentScript");return!K||K.configurable};W()&&(Object.defineProperty(document,"currentScript",{get:function(){return o},configurable:!0}),C=!0)},success:function(){wa(o),C&&delete document.currentScript,o=null},error:function(){ba(o),C&&delete document.currentScript,o=null}});var x=document.createComment("dynamic script ".concat(Q," replaced by qiankun"));return an.set(o,x),a.call(S,x,E)}mt(null,["<script>".concat(se,"<\/script>")],m,{strictGlobal:d,scopedGlobalVariables:_});var N=document.createComment("dynamic inline script replaced by qiankun");return an.set(o,N),a.call(S,N,E)}}}return a.call(this,o,r)}return t[Oe]=!0,t}function Nr(e,t,n,r){function o(a){var u=a.tagName;if(!_n(u)||!r(a))return e.call(this,a);try{var i,c=t(a),s=c.appWrapperGetter,l=c.dynamicStyleSheetElements;switch(u){case Tn:case yt:{i=Sa.get(a)||a;var h=l.indexOf(i);h!==-1&&l.splice(h,1);break}case Pn:{i=an.get(a)||a;break}default:i=a}var p=s(),m=n==="head"?Je(p):p;if(m.contains(i))return e.call(i.parentNode,i)}catch(d){console.warn(d)}return e.call(this,a)}return o[Oe]=!0,o}function _a(e,t){var n=HTMLHeadElement.prototype.appendChild,r=HTMLBodyElement.prototype.appendChild,o=HTMLHeadElement.prototype.insertBefore;n[Oe]!==!0&&r[Oe]!==!0&&o[Oe]!==!0&&(HTMLHeadElement.prototype.appendChild=Wt({rawDOMAppendOrInsertBefore:n,containerConfigGetter:t,isInvokedByMicroApp:e,target:"head"}),HTMLBodyElement.prototype.appendChild=Wt({rawDOMAppendOrInsertBefore:r,containerConfigGetter:t,isInvokedByMicroApp:e,target:"body"}),HTMLHeadElement.prototype.insertBefore=Wt({rawDOMAppendOrInsertBefore:o,containerConfigGetter:t,isInvokedByMicroApp:e,target:"head"}));var a=HTMLHeadElement.prototype.removeChild,u=HTMLBodyElement.prototype.removeChild;return a[Oe]!==!0&&u[Oe]!==!0&&(HTMLHeadElement.prototype.removeChild=Nr(a,t,"head",e),HTMLBodyElement.prototype.removeChild=Nr(u,t,"body",e)),function(){HTMLHeadElement.prototype.appendChild=n,HTMLHeadElement.prototype.removeChild=a,HTMLBodyElement.prototype.appendChild=r,HTMLBodyElement.prototype.removeChild=u,HTMLHeadElement.prototype.insertBefore=o}}function Oa(e,t){e.forEach(function(n){var r=t(n);if(r&&n instanceof HTMLStyleElement&&ma(n)){var o=Ta(n);if(o)for(var a=0;a<o.length;a++){var u=o[a],i=n.sheet;i.insertRule(u.cssText,i.cssRules.length)}}})}function wt(e,t,n){var r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,o=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1,a=arguments.length>5?arguments[5]:void 0,u=n.proxy,i=[],c=_a(function(){return wo(window.location).some(function(s){return s===e})},function(){return{appName:e,appWrapperGetter:t,proxy:u,strictGlobal:!1,speedySandbox:!1,scopedCSS:o,dynamicStyleSheetElements:i,excludeAssetFilter:a}});return r||we(e,"increase","bootstrapping"),r&&we(e,"increase","mounting"),function(){return r||we(e,"decrease","bootstrapping"),r&&we(e,"decrease","mounting"),ga()&&c(),Pa(i),function(){Oa(i,function(h){var p=t();return p.contains(h)?!1:(document.head.appendChild.call(p,h),!0)}),r&&(i=[])}}}Object.defineProperty(z,"__proxyAttachContainerConfigMap__",{enumerable:!1,writable:!0});Object.defineProperty(z,"__currentLockingSandbox__",{enumerable:!1,writable:!0,configurable:!0});var np=HTMLHeadElement.prototype.appendChild,rp=HTMLHeadElement.prototype.insertBefore;z.__proxyAttachContainerConfigMap__=z.__proxyAttachContainerConfigMap__||new WeakMap;var bt=z.__proxyAttachContainerConfigMap__,un=new WeakMap,jr=new WeakMap,de=new WeakMap;function op(e){var t=e.sandbox,n=e.speedy,r=function(f,v){var b=bt.get(v);b&&un.set(f,b)};if(n){var o={},a=new Proxy(document,{set:function(f,v,b){switch(v){case"createElement":{o.createElement=b;break}case"querySelector":{o.querySelector=b;break}default:f[v]=b;break}return!0},get:function(f,v,b){switch(v){case"createElement":{var T=o.createElement||f.createElement;return function(){z.__currentLockingSandbox__||(z.__currentLockingSandbox__=t.name);for(var R=arguments.length,I=new Array(R),A=0;A<R;A++)I[A]=arguments[A];var H=T.call.apply(T,[f].concat(I));return z.__currentLockingSandbox__===t.name&&(r(H,t.proxy),delete z.__currentLockingSandbox__),H}}case"querySelector":{var L=o.querySelector||f.querySelector;return function(){for(var R=arguments.length,I=new Array(R),A=0;A<R;A++)I[A]=arguments[A];var H=I[0];switch(H){case"head":{var Y=bt.get(t.proxy);if(Y){var V=Je(Y.appWrapperGetter());return V.appendChild=HTMLHeadElement.prototype.appendChild,V.insertBefore=HTMLHeadElement.prototype.insertBefore,V.removeChild=HTMLHeadElement.prototype.removeChild,V}break}}return L.call.apply(L,[f].concat(I))}}}var M=f[v];return oa(M)&&!aa(M)?function(){for(var R=arguments.length,I=new Array(R),A=0;A<R;A++)I[A]=arguments[A];return M.call.apply(M,[f].concat(be(I.map(function(H){return H===b?f:H}))))}:M}});t.patchDocument(a);var u=MutationObserver.prototype.observe;if(!de.has(u)){var i=function(f,v){var b=f instanceof Document?Rr:f;return u.call(this,b,v)};MutationObserver.prototype.observe=i,de.set(u,i)}var c=Node.prototype.compareDocumentPosition;de.has(c)||(Node.prototype.compareDocumentPosition=function(f){var v=f instanceof Document?Rr:f;return c.call(this,v)},de.set(c,Node.prototype.compareDocumentPosition));var s=Object.getOwnPropertyDescriptor(Node.prototype,"parentNode");if(s&&!de.has(s)){var l=s.get,h=s.configurable;if(l&&h){var p=k(k({},s),{},{get:function(){var f=l.call(this);if(f instanceof Document){var v,b=(v=en())===null||v===void 0?void 0:v.window;if(b)return b.document}return f}});Object.defineProperty(Node.prototype,"parentNode",p),de.set(s,p)}}return function(){MutationObserver.prototype.observe=u,de.delete(u),Node.prototype.compareDocumentPosition=c,de.delete(c),s&&(Object.defineProperty(Node.prototype,"parentNode",s),de.delete(s))}}var m=jr.get(document.createElement);if(!m){var d=document.createElement;Document.prototype.createElement=function(f,v){var b=d.call(this,f,v);if(_n(f)){var T=en()||{},L=T.window;L&&r(b,L)}return b},document.hasOwnProperty("createElement")&&(document.createElement=Document.prototype.createElement),jr.set(Document.prototype.createElement,d)}return function(){m&&(Document.prototype.createElement=m,document.createElement=m)}}function xa(e,t,n){var r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,o=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1,a=arguments.length>5?arguments[5]:void 0,u=arguments.length>6&&arguments[6]!==void 0?arguments[6]:!1,i=n.proxy,c=bt.get(i);c||(c={appName:e,proxy:i,appWrapperGetter:t,dynamicStyleSheetElements:[],strictGlobal:!0,speedySandbox:u,excludeAssetFilter:a,scopedCSS:o},bt.set(i,c));var s=c,l=s.dynamicStyleSheetElements,h=_a(function(m){return un.has(m)},function(m){return un.get(m)}),p=op({sandbox:n,speedy:u});return r||we(e,"increase","bootstrapping"),r&&we(e,"increase","mounting"),function(){return r||we(e,"decrease","bootstrapping"),r&&we(e,"decrease","mounting"),ga()&&(h(),p()),Pa(l),function(){Oa(l,function(y){var f=t();if(!f.contains(y)){var v=y[ha]==="head"?Je(f):f,b=y[va];if(typeof b=="number"&&b!==-1){var T=v.childNodes[b]||null;return rp.call(v,y,T),!0}else return np.call(v,y),!0}return!1})}}}function ap(){var e=function(o){return pt},t=[],n=[];return window.g_history&&Me(window.g_history.listen)&&(e=window.g_history.listen.bind(window.g_history),window.g_history.listen=function(r){t.push(r);var o=e(r);return n.push(o),function(){o(),n.splice(n.indexOf(o),1),t.splice(t.indexOf(r),1)}}),function(){var o=pt;return t.length&&(o=function(){t.forEach(function(u){return window.g_history.listen(u)})}),n.forEach(function(a){return a()}),window.g_history&&Me(window.g_history.listen)&&(window.g_history.listen=e),o}}var Hr=window.setInterval,$r=window.clearInterval;function ip(e){var t=[];return e.clearInterval=function(n){return t=t.filter(function(r){return r!==n}),$r.call(window,n)},e.setInterval=function(n,r){for(var o=arguments.length,a=new Array(o>2?o-2:0),u=2;u<o;u++)a[u-2]=arguments[u];var i=Hr.apply(void 0,[n,r].concat(a));return t=[].concat(be(t),[i]),i},function(){return t.forEach(function(r){return e.clearInterval(r)}),e.setInterval=Hr,e.clearInterval=$r,pt}}var Fr=window.addEventListener,Wr=window.removeEventListener;function up(e){var t=new Map;return e.addEventListener=function(n,r,o){var a=t.get(n)||[];return t.set(n,[].concat(be(a),[r])),Fr.call(window,n,r,o)},e.removeEventListener=function(n,r,o){var a=t.get(n);return a&&a.length&&a.indexOf(r)!==-1&&a.splice(a.indexOf(r),1),Wr.call(window,n,r,o)},function(){return t.forEach(function(r,o){return be(r).forEach(function(a){return e.removeEventListener(o,a)})}),e.addEventListener=Fr,e.removeEventListener=Wr,pt}}function cp(e,t,n,r,o,a){var u,i,c=[function(){return ip(n.proxy)},function(){return up(n.proxy)},function(){return ap()}],s=(u={},Z(u,re.LegacyProxy,[].concat(c,[function(){return wt(e,t,n,!0,r,o)}])),Z(u,re.Proxy,[].concat(c,[function(){return xa(e,t,n,!0,r,o,a)}])),Z(u,re.Snapshot,[].concat(c,[function(){return wt(e,t,n,!0,r,o)}])),u);return(i=s[n.type])===null||i===void 0?void 0:i.map(function(l){return l()})}function sp(e,t,n,r,o,a){var u,i,c=(u={},Z(u,re.LegacyProxy,[function(){return wt(e,t,n,!1,r,o)}]),Z(u,re.Proxy,[function(){return xa(e,t,n,!1,r,o,a)}]),Z(u,re.Snapshot,[function(){return wt(e,t,n,!1,r,o)}]),u);return(i=c[n.type])===null||i===void 0?void 0:i.map(function(s){return s()})}function Br(e,t){for(var n in e)(e.hasOwnProperty(n)||n==="clearInterval")&&t(n)}var lp=function(){function e(t){$e(this,e),this.proxy=void 0,this.name=void 0,this.type=void 0,this.sandboxRunning=!0,this.windowSnapshot=void 0,this.modifyPropsMap={},this.name=t,this.proxy=window,this.type=re.Snapshot}return He(e,[{key:"active",value:function(){var n=this;this.windowSnapshot={},Br(window,function(r){n.windowSnapshot[r]=window[r]}),Object.keys(this.modifyPropsMap).forEach(function(r){window[r]=n.modifyPropsMap[r]}),this.sandboxRunning=!0}},{key:"inactive",value:function(){var n=this;this.modifyPropsMap={},Br(window,function(r){window[r]!==n.windowSnapshot[r]&&(n.modifyPropsMap[r]=window[r],window[r]=n.windowSnapshot[r])}),this.sandboxRunning=!1}},{key:"patchDocument",value:function(){}}]),e}();function fp(e,t,n,r,o,a,u){var i;window.Proxy?i=r?new ed(e,a):new Zd(e,a,{speedy:!!u}):i=new lp(e);var c=sp(e,t,i,n,o,u),s=[],l=[];return{instance:i,mount:function(){return j(O.mark(function p(){var m,d;return O.wrap(function(f){for(;;)switch(f.prev=f.next){case 0:i.active(),m=l.slice(0,c.length),d=l.slice(c.length),m.length&&m.forEach(function(v){return v()}),s=cp(e,t,i,n,o,u),d.length&&d.forEach(function(v){return v()}),l=[];case 7:case"end":return f.stop()}},p)}))()},unmount:function(){return j(O.mark(function p(){return O.wrap(function(d){for(;;)switch(d.prev=d.next){case 0:l=[].concat(be(c),be(s)).map(function(y){return y()}),i.inactive();case 2:case"end":return d.stop()}},p)}))()}}}var dp=["singular","sandbox","excludeAssetFilter","globalContext"];function cn(e,t){if(!e)throw t?new Qe(t):new Qe("element not existed!")}function Ve(e,t){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:window;return e.length?e.reduce(function(r,o){return r.then(function(){return o(t,n)})},Promise.resolve()):Promise.resolve()}function ut(e,t){return sn.apply(this,arguments)}function sn(){return sn=j(O.mark(function e(t,n){return O.wrap(function(o){for(;;)switch(o.prev=o.next){case 0:return o.abrupt("return",typeof t=="function"?t(n):!!t);case 1:case"end":return o.stop()}},e)})),sn.apply(this,arguments)}var Ma=!!document.head.attachShadow||!!document.head.createShadowRoot;function Vr(e,t,n,r){var o=document.createElement("div");o.innerHTML=e;var a=o.firstChild;if(t)if(!Ma)console.warn("[qiankun]: As current browser not support shadow dom, your strictStyleIsolation configuration will be ignored!");else{var u=a.innerHTML;a.innerHTML="";var i;a.attachShadow?i=a.attachShadow({mode:"open"}):i=a.createShadowRoot(),i.innerHTML=u}if(n){var c=a.getAttribute(tn);c||a.setAttribute(tn,r);var s=a.querySelectorAll("style")||[];qc(s,function(l){nn(a,l,r)})}return a}function Ur(e,t,n,r,o){return function(){if(t){if(n)throw new Qe("strictStyleIsolation can not be used with legacy render!");if(r)throw new Qe("experimentalStyleIsolation can not be used with legacy render!");var a=document.getElementById(ia(e));return cn(a,"Wrapper element for ".concat(e," is not existed!")),a}var u=o();return cn(u,"Wrapper element for ".concat(e," is not existed!")),n&&Ma?u.shadowRoot:u}}var pp=HTMLElement.prototype.appendChild,hp=HTMLElement.prototype.removeChild;function vp(e,t,n){var r=function(a,u){var i=a.element,c=a.loading,s=a.container;if(n)return n({loading:c,appContent:i?t:""});var l=ca(s);if(u!=="unmounted"){var h=function(){switch(u){case"loading":case"mounting":return"Target container with ".concat(s," not existed while ").concat(e," ").concat(u,"!");case"mounted":return"Target container with ".concat(s," not existed after ").concat(e," ").concat(u,"!");default:return"Target container with ".concat(s," not existed while ").concat(e," rendering!")}}();cn(l,h)}if(l&&!l.contains(i)){for(;l.firstChild;)hp.call(l,l.firstChild);i&&pp.call(l,i)}};return r}function mp(e,t,n,r){if(Ht(e))return e;if(r){var o=n[r];if(Ht(o))return o}var a=n[t];if(Ht(a))return a;throw new Qe("You need to export lifecycle functions in ".concat(t," entry"))}var _e;function gp(e){return ln.apply(this,arguments)}function ln(){return ln=j(O.mark(function e(t){var n,r,o,a,u,i,c,s,l,h,p,m,d,y,f,v,b,T,L,M,w,R,I,A,H,Y,V,ce,q,me,Q,se,oe,S,g,E,_,C,x,N,F,W,J,K,ae,On,Fe,xn,Mn,Cn,Mt,nt,Ln,An,Rn,Ct,In,We=arguments;return O.wrap(function(X){for(;;)switch(X.prev=X.next){case 0:return o=We.length>1&&We[1]!==void 0?We[1]:{},a=We.length>2?We[2]:void 0,u=t.entry,i=t.name,c=Kf(i),s=o.singular,l=s===void 0?!1:s,h=o.sandbox,p=h===void 0?!0:h,m=o.excludeAssetFilter,d=o.globalContext,y=d===void 0?window:d,f=fi(o,dp),X.next=9,Os(u,f);case 9:return v=X.sent,b=v.template,T=v.execScripts,L=v.assetPublicPath,M=v.getExternalScripts,X.next=16,M();case 16:return X.next=18,ut(l,t);case 18:if(!X.sent){X.next=21;break}return X.next=21,_e&&_e.promise;case 21:return w=Uf(c,p)(b),R=ee(p)==="object"&&!!p.strictStyleIsolation,I=Yf(p),A=Vr(w,R,I,c),H="container"in t?t.container:void 0,Y="render"in t?t.render:void 0,V=vp(c,w,Y),V({element:A,loading:!0,container:H},"loading"),ce=Ur(c,!!Y,R,I,function(){return A}),q=y,me=function(){return Promise.resolve()},Q=function(){return Promise.resolve()},se=ee(p)==="object"&&!!p.loose,oe=ee(p)==="object"?p.speedy!==!1:!0,p&&(S=fp(c,ce,I,se,m,q,oe),q=S.instance.proxy,me=S.mount,Q=S.unmount),g=Go({},Cs(q,L),a,function(Pe,ie){return Oo(Pe??[],ie??[])}),E=g.beforeUnmount,_=E===void 0?[]:E,C=g.afterUnmount,x=C===void 0?[]:C,N=g.afterMount,F=N===void 0?[]:N,W=g.beforeMount,J=W===void 0?[]:W,K=g.beforeLoad,ae=K===void 0?[]:K,X.next=40,Ve(Ae(ae),t,q);case 40:return X.next=42,T(q,p&&!se,{scopedGlobalVariables:oe?xt:[]});case 42:return On=X.sent,Fe=mp(On,i,q,(n=S)===null||n===void 0||(r=n.instance)===null||r===void 0?void 0:r.latestSetProp),xn=Fe.bootstrap,Mn=Fe.mount,Cn=Fe.unmount,Mt=Fe.update,nt=Ua(c),Ln=nt.onGlobalStateChange,An=nt.setGlobalState,Rn=nt.offGlobalStateChange,Ct=function(ie){return A=ie},In=function(){var ie=arguments.length>0&&arguments[0]!==void 0?arguments[0]:H,le,Lt,Gn={name:c,bootstrap:xn,mount:[j(O.mark(function D(){return O.wrap(function(P){for(;;)switch(P.prev=P.next){case 0:case 1:case"end":return P.stop()}},D)})),j(O.mark(function D(){return O.wrap(function(P){for(;;)switch(P.prev=P.next){case 0:return P.next=2,ut(l,t);case 2:if(P.t0=P.sent,!P.t0){P.next=5;break}P.t0=_e;case 5:if(!P.t0){P.next=7;break}return P.abrupt("return",_e.promise);case 7:return P.abrupt("return",void 0);case 8:case"end":return P.stop()}},D)})),j(O.mark(function D(){return O.wrap(function(P){for(;;)switch(P.prev=P.next){case 0:le=A,Lt=Ur(c,!!Y,R,I,function(){return le});case 2:case"end":return P.stop()}},D)})),j(O.mark(function D(){var B;return O.wrap(function(Be){for(;;)switch(Be.prev=Be.next){case 0:B=ie!==H,(B||!le)&&(le=Vr(w,R,I,c),Ct(le)),V({element:le,loading:!0,container:ie},"mounting");case 3:case"end":return Be.stop()}},D)})),me,j(O.mark(function D(){return O.wrap(function(P){for(;;)switch(P.prev=P.next){case 0:return P.abrupt("return",Ve(Ae(J),t,q));case 1:case"end":return P.stop()}},D)})),function(){var D=j(O.mark(function B(P){return O.wrap(function(fe){for(;;)switch(fe.prev=fe.next){case 0:return fe.abrupt("return",Mn(k(k({},P),{},{container:Lt(),setGlobalState:An,onGlobalStateChange:Ln})));case 1:case"end":return fe.stop()}},B)}));return function(B){return D.apply(this,arguments)}}(),j(O.mark(function D(){return O.wrap(function(P){for(;;)switch(P.prev=P.next){case 0:return P.abrupt("return",V({element:le,loading:!1,container:ie},"mounted"));case 1:case"end":return P.stop()}},D)})),j(O.mark(function D(){return O.wrap(function(P){for(;;)switch(P.prev=P.next){case 0:return P.abrupt("return",Ve(Ae(F),t,q));case 1:case"end":return P.stop()}},D)})),j(O.mark(function D(){return O.wrap(function(P){for(;;)switch(P.prev=P.next){case 0:return P.next=2,ut(l,t);case 2:if(!P.sent){P.next=4;break}_e=new ua;case 4:case"end":return P.stop()}},D)})),j(O.mark(function D(){return O.wrap(function(P){for(;;)switch(P.prev=P.next){case 0:case 1:case"end":return P.stop()}},D)}))],unmount:[j(O.mark(function D(){return O.wrap(function(P){for(;;)switch(P.prev=P.next){case 0:return P.abrupt("return",Ve(Ae(_),t,q));case 1:case"end":return P.stop()}},D)})),function(){var D=j(O.mark(function B(P){return O.wrap(function(fe){for(;;)switch(fe.prev=fe.next){case 0:return fe.abrupt("return",Cn(k(k({},P),{},{container:Lt()})));case 1:case"end":return fe.stop()}},B)}));return function(B){return D.apply(this,arguments)}}(),Q,j(O.mark(function D(){return O.wrap(function(P){for(;;)switch(P.prev=P.next){case 0:return P.abrupt("return",Ve(Ae(x),t,q));case 1:case"end":return P.stop()}},D)})),j(O.mark(function D(){return O.wrap(function(P){for(;;)switch(P.prev=P.next){case 0:V({element:null,loading:!1,container:ie},"unmounted"),Rn(c),le=null,Ct(le);case 4:case"end":return P.stop()}},D)})),j(O.mark(function D(){return O.wrap(function(P){for(;;)switch(P.prev=P.next){case 0:return P.next=2,ut(l,t);case 2:if(P.t0=P.sent,!P.t0){P.next=5;break}P.t0=_e;case 5:if(!P.t0){P.next=7;break}_e.resolve();case 7:case"end":return P.stop()}},D)}))]};return typeof Mt=="function"&&(Gn.update=Mt),Gn},X.abrupt("return",In);case 48:case"end":return X.stop()}},e)})),ln.apply(this,arguments)}var Re={},yp=!0;new ua;var wp=function(t){var n=t.sandbox,r=n===void 0?!0:n,o=t.singular;if(r){if(!window.Proxy)return console.warn("[qiankun] Missing window.Proxy, proxySandbox will degenerate into snapshotSandbox"),o===!1&&console.warn("[qiankun] Setting singular as false may cause unexpected behavior while your browser not support window.Proxy"),k(k({},t),{},{sandbox:ee(r)==="object"?k(k({},r),{},{loose:!0}):{loose:!0}});if(!Vf()&&(r===!0||ee(r)==="object"&&r.speedy!==!1))return console.warn("[qiankun] Speedy mode will turn off as const destruct assignment not supported in current browser!"),k(k({},t),{},{sandbox:ee(r)==="object"?k(k({},r),{},{speedy:!1}):{speedy:!1}})}return t},ct=new Map,Bt=new Map;function bp(e,t,n){var r=e.props,o=e.name,a="container"in e?e.container:void 0,u=zf(a),i="".concat(o,"-").concat(u),c,s=function(y){var f=y;if(a&&u){var v=Bt.get(i);if(v!=null&&v.length){var b=[j(O.mark(function T(){var L,M;return O.wrap(function(R){for(;;)switch(R.prev=R.next){case 0:return L=v.slice(0,v.indexOf(c)),M=L.filter(function(I){return I.getStatus()!=="LOAD_ERROR"&&I.getStatus()!=="SKIP_BECAUSE_BROKEN"}),R.next=4,Promise.all(M.map(function(I){return I.unmountPromise}));case 4:case"end":return R.stop()}},T)}))].concat(be(Ae(f.mount)));f=k(k({},y),{},{mount:b})}}return k(k({},f),{},{bootstrap:function(){return Promise.resolve()}})},l=function(){var d=j(O.mark(function y(){var f,v,b,T,L;return O.wrap(function(w){for(;;)switch(w.prev=w.next){case 0:if(f=wp(t??k(k({},Re),{},{singular:!1})),v=f.$$cacheLifecycleByAppName,!a){w.next=21;break}if(!v){w.next=12;break}if(b=ct.get(o),!b){w.next=12;break}return w.t0=s,w.next=9,b;case 9:return w.t1=w.sent,w.t2=(0,w.t1)(a),w.abrupt("return",(0,w.t0)(w.t2));case 12:if(!u){w.next=21;break}if(T=ct.get(i),!T){w.next=21;break}return w.t3=s,w.next=18,T;case 18:return w.t4=w.sent,w.t5=(0,w.t4)(a),w.abrupt("return",(0,w.t3)(w.t5));case 21:return L=gp(e,f,n),a&&(v?ct.set(o,L):u&&ct.set(i,L)),w.next=25,L;case 25:return w.t6=w.sent,w.abrupt("return",(0,w.t6)(a));case 27:case"end":return w.stop()}},y)}));return function(){return d.apply(this,arguments)}}();if((t==null?void 0:t.autoStart)!==!1){var h;Po({urlRerouteOnly:(h=Re.urlRerouteOnly)!==null&&h!==void 0?h:yp})}if(c=co(l,k({domElement:document.createElement("div")},r)),a&&u){var p=Bt.get(i)||[];p.push(c),Bt.set(i,p);var m=function(){var y=p.indexOf(c);p.splice(y,1),c=null};c.unmountPromise.then(m).catch(m)}return c}const Ep={class:"sgui-container"},Sp=["id"],Pp={name:"MicroApp"},Tp=qa({...Pp,setup(e){const t=Ka({}),n=Ya(),{t:r}=Xa(),o=za(),a=Dn([]),u=Qa(""),i="frame",c=Dn(!1),s=async h=>{for(let p=0;p<=2;p++)try{await At(),await h.mountPromise;return}catch(m){if(p===2){console.error(m);const d=`${r("app.microApp.mountError")}${(m==null?void 0:m.message)??""} ${r("app.microApp.mountRefresh")}`;await ii.alert(d,r("app.microApp.tips"),{confirmButtonText:r("app.microApp.confirmButtonText"),type:"error",showCancelButton:!1,showClose:!1,draggable:!0}),window.location.reload()}}finally{c.value=!1}},l=async h=>{if(!a.value.map(v=>v.activeRule).some(v=>h.startsWith(v)))return;const d=a.value.find(v=>h.startsWith(v.activeRule.toString()));if(!d||(u.value=d.name,t[d.name]))return;await At(),c.value=!0;const f=bp({...d,container:`#${i}-${d.name}`});t[d.name]=f,await s(f),c.value=!1};return Ja(()=>n.path,l),Za(async()=>{a.value=await o.getApps(),await At(),!window.qiankunStarted&&(window.qiankunStarted=!0,l(n.path))}),ei(()=>{window.qiankunStarted=!1,Object.keys(t).forEach(h=>{t[h].unmount(),delete t[h]})}),(h,p)=>{const m=ai;return ti((Rt(),It("div",Ep,[(Rt(!0),It(ni,null,ri(a.value,d=>(Rt(),It("div",{id:`${i}-${d.name}`,key:d.name,class:oi(["micro-container",{show:u.value===d.name}])},null,10,Sp))),128))])),[[m,c.value]])}}});const Mp=ui(Tp,[["__scopeId","data-v-0a1ae654"]]);export{Mp as default};
