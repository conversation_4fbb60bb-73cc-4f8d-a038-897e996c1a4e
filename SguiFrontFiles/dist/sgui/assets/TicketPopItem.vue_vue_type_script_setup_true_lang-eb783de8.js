import{_ as _t}from"./theme-light_empty-0081a108.js";import{fe as vt,aS as at,aV as le,ao as M,hu as L,gY as ve,gX as w,R as ee,gS as bt,q as ce,aB as mt,aC as kt,w as ae,r as B,o as he,x as n,y as te,z as E,G as U,P as t,Q as r,B as l,ai as P,aj as V,ab as lt,b0 as $t,b4 as Dt,ar as Rt,b5 as rt,eP as Tt,a5 as dt,A as h,D as de,F as ct,ak as be,E as It,J as O,ah as Ct,al as At,aZ as pt,am as ut,an as ft,s as ye,aR as ke,bs as $e,au as gt,aH as wt,H as Nt,bq as St,aF as Ft,hv as me,bh as Et,c0 as qt,hw as ot,aY as ue}from"./index-a2fbd71b.js";import{_ as xe}from"./_plugin-vue_export-helper-c27b6911.js";import{E as Bt,a as Ht}from"./index-ff93035e.js";import{E as De}from"./index-350461d4.js";import{E as Pt}from"./index-fe3402a7.js";import{E as Qt,a as Mt}from"./index-96be5bee.js";import{E as Lt}from"./index-d88b3135.js";import{k as nt,K as Yt}from"./ticketOperationApi-4e3e854b.js";import{a as Ut}from"./time-04300430.js";import{a9 as Ot}from"./regular-crs-531dcd3f.js";import{a as Vt}from"./config-6a4e2d9f.js";var jt=(e=>(e.SK_QUERY="SKQuery",e.NFD_QUERY="queryNetPrice",e.FD_QUERY="netFare",e.FSD_QUERY="InternationalPublishedRatesQuery",e))(jt||{}),Wt=(e=>(e.AD="adult",e.CH="child",e.IN="baby",e))(Wt||{}),yt=(e=>(e.SEARCH="SEARCH",e.TAB_TO_SEARCH="TAB_TO_SEARCH",e))(yt||{});const Kt=vt("fastQuery",{state:()=>({historyAirline:{domesticHistory:[],internationalHistory:[]},queryHistory:new Map,targetInfo:{},showManualRefund:!1,rtktDetailInfoWindows:{active:"",list:[]},isOpened:!1}),getters:{getHistoryAirline(e){return e.historyAirline},getRtktDetailInfoWindows:e=>e.rtktDetailInfoWindows},actions:{setHistoryAirLine(e){this.historyAirline=e,localStorage.setItem("agentHistoryAirline",JSON.stringify(e))},setIsOpened(e){this.isOpened=e},setQueryHistory(e,o,u){var d,c;const a=at(o),y=le(a);if((d=this.queryHistory.get(e))!=null&&d.activeTagKey){u||(this.queryHistory.get(e).activeTagKey=y);const x=((c=this.queryHistory.get(e))==null?void 0:c.list.findIndex(p=>le(p)===y))??-1;if(x>-1){this.queryHistory.get(e).list.splice(x,1,a);return}this.queryHistory.get(e).list.push(a);return}const g={activeTagKey:y,list:[a]};this.queryHistory.set(e,g)},setQueryHistoryBySk(e,o){var g,d;const u=at(o.queryForm),a=le(u);if((g=this.queryHistory.get(e))!=null&&g.activeTagKey){if(this.queryHistory.get(e).activeTagKey=a,o.queryType===yt.TAB_TO_SEARCH)return;const c=((d=this.queryHistory.get(e))==null?void 0:d.list.findIndex(x=>le(x)===a))??-1;c>-1&&this.queryHistory.get(e).list.splice(c,1),this.queryHistory.get(e).list.unshift(u);return}const y={activeTagKey:a,list:[u]};this.queryHistory.set(e,y)},delQueryHistory(e,o){var a,y,g;const u=((a=this.queryHistory.get(e))==null?void 0:a.list.findIndex(d=>le(d)===le(o)))??-1;if(u>-1){if(((y=this.queryHistory.get(e))==null?void 0:y.activeTagKey)===le(o)&&this.queryHistory.get(e).list.length>1){const d=u+1<(((g=this.queryHistory.get(e))==null?void 0:g.list.length)??0)?u+1:0;this.queryHistory.get(e).activeTagKey=le(this.queryHistory.get(e).list[d])}this.queryHistory.get(e).list.splice(u,1),this.queryHistory.get(e).list.length||(this.queryHistory.get(e).activeTagKey="")}},setFastQueryTargetInfo(e){this.targetInfo={queryFlag:!0,targetInfo:e}},closeFastQuery(){this.targetInfo={queryFlag:!1,targetInfo:{}},this.setIsOpened(!1)},setShowManualRefund(e){this.showManualRefund=e},setActiveRtktDetailInfoWindows(e){this.rtktDetailInfoWindows.active=e},setRtktDetailInfoWindowsList(e){const o=e.id;this.setActiveRtktDetailInfoWindows(o),this.rtktDetailInfoWindows.list.push({...e,id:o,isShow:!0})},delRtktDetailInfoWindowsList(e){this.rtktDetailInfoWindows.list=this.rtktDetailInfoWindows.list.filter(o=>o.id!==e)}}}),Gt=Kt,gn=(e,o)=>M(`${L}/apiAvSearch/computeInterAirPrice`,{headers:{gid:o}}).post(e).json(),yn=(e,o)=>M(`${L}/fare/domestic`,{headers:{gid:o}}).post(e).json(),hn=(e,o)=>M(`${L}/fare/queryInterReprice`,{headers:{gid:o}}).post(e).json(),xn=(e,o)=>M(`${L}/fare/queryDomesticReprice`,{headers:{gid:o}}).post(e).json(),_n=(e,o)=>M(`${L}/fare/route/query`,{headers:{gid:o}}).post(e).json(),vn=(e,o)=>M(`${L}/apiAvSearch/fareRuleBaggage`,{headers:{gid:o}}).post(e).json(),bn=(e,o)=>M(`${L}/apiAvSearch/domesticFreeBaggage`,{headers:{gid:o}}).post(e).json(),mn=(e,o)=>M(`${L}/apiAvSearch/historyAndNewPriceCompute`,{headers:{gid:o}}).post(e).json(),kn=(e,o)=>M(`${L}/fare/domestic`,{headers:{gid:o}}).post(e).json(),$n=(e,o)=>M(`${L}/apiAvSearch/fareIntoAndEtdz`,{headers:{gid:o}}).post(e).json(),zt=(e,o)=>M(`${L}/apiAvSearch/queryRuleForInterAirPrice`,{headers:{gid:o}}).post(e).json(),Dn=(e,o)=>M(`${L}/fare/queryRTKTDetail`,{headers:{gid:o}},{originalValue:!0,ignoreError:!0}).post(e).json(),Rn=(e,o)=>M(`${L}/fare/cancelCreditCardAuth`,{headers:{gid:o}}).post(e).json(),Tn=(e,o)=>M(`${L}/cpay/crsCdsPay`,{headers:{gid:o}}).post(e).json(),In=(e,o)=>M(`${L}/dpay/crsBopDPay`,{headers:{gid:o}}).post(e).json(),Cn=(e,o)=>M(`${L}/fare/queryTicketsDetail`,{headers:{gid:o}},{ignoreError:!0}).post(e).json(),Jt=[{code:"<",offset:"-1"},{code:">",offset:"+1"},{code:"\\",offset:"+2"},{code:"+",offset:"+2"}],it=(e,o)=>!e||!o||!bt.test(o)?"":`${e}T${o.slice(0,2)}:${o.slice(2)}`,Xt=e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase(),Zt=(e,o,u,a)=>{if(Ot.test(e)){const m=ee(new Date).format("YYYY").slice(0,2),[k,R,D]=e.match(/(\d{2})([A-Za-z]{3})(\d{2})/).slice(1),N=`${m}${D}`,v=ee(`${N}-${Xt(R)}-${k}`,"YYYY-MMM-DD");if(!v.isValid())throw new Error;return v.format("YYYY-MM-DD")}let y=u,g=a;(y??"")===""&&(y=ee(new Date).format("YYYY-MM-DD"),g=ee(new Date).format("HHmm"));const d=y.substring(0,4);let c,x;const p=e?Ut(e,d):`${d}-${y.substring(5)}`;return g!==""&&o!==""?(c=ee(`${d}-${y.substring(5)}:${g}`),x=ee(`${p}:${o}`)):(c=ee(`${d}-${y.substring(5)}`),x=ee(`${p}`)),x.isBefore(c)?x.add(1,"year").format("YYYY-MM-DD"):x.format("YYYY-MM-DD")},es=e=>({[w.FLIGHT_STATUS]:{value:"",required:!0},[w.COMPANY_CODE]:{value:"",required:!0},[w.FLIGHT_NUMBER]:{value:"",required:e},[w.CABIN_CODE]:{value:"",required:!0},[w.DEPARTURE_DATE]:{value:"",required:e},[w.DEPARTURE_AIRPORT]:{value:"",required:!0},[w.DATE_TIME_RANGE]:{value:"",required:e},[w.ARRIVAL_AIRPORT]:{value:"",required:!0},[w.STOP_QUANTITY]:{value:"",required:!0},[w.STOP_FLAG]:{value:"",required:!0,unorderedCode:[w.GLOBAL_INDICATOR]},[w.AIR_EQUIPMENT_TYPE]:{value:""},[w.OC_AIRLINE]:{value:""},[w.OPEN_AND_CLOSE_CABINS]:{value:""}}),ts=()=>({[w.GLOBAL_INDICATOR]:{value:""}}),ss=e=>{const o=e[0]!=="O";let u=e.replaceAll(/ */gi,"");const a=ts(),y=es(o);try{if(Object.keys(y).forEach(d=>{var p;const c=ve.get(d),x=y[d];if(c){const m=c.exec(u);if(!m||m.index){if(x!=null&&x.required)throw new Error;return}x.value=m[0],u=u.slice(m[0].length),(p=x.unorderedCode)!=null&&p.length&&x.unorderedCode.forEach(k=>{const R=a[k],D=new RegExp(ve.get(k),"g"),N=u.match(D);if(N!=null&&N.length){if(N.length>1)throw new Error;R.value=N[0],u=u.replace(D,"")}})}}),u)throw new Error;return{valid:!0,flightInfoForm:{...y,...a}}}catch{return{valid:!1,flightInfoForm:{...y,...a}}}},as=(e,o,u,a)=>{var R;if(!e&&!o)return{departureDateTime:"",arrivalDateTime:""};const y=o,g=y.slice(0,4),d=Zt(e,g,u,a),c=((R=Jt.find(D=>y.includes(D.code)))==null?void 0:R.offset)??0,x=ee(d).add(Number(c),"day").format("YYYY-MM-DD"),p=c?y.slice(5):y.slice(4),m=it(d,g||"0000"),k=it(x,p||"0000");return{departureDateTime:m,arrivalDateTime:k}},rs=e=>e.slice(2),os=e=>{if(!e)return{openCabins:[],closeCabins:[]};let o=e,u=[],a=[];const y=ve.get(w.OPEN_CABINS),g=y.exec(o);if(y&&g){const x=g[0];if(u=x.replace("#D","").split("")??[],g.index)return{openCabins:u,closeCabins:[]};o=o.slice(x.length)}return ve.get(w.CLOSE_CABINS).exec(o)&&(a=o.replace("#C","").split("")??[]),{openCabins:u,closeCabins:a}},ns=(e,o,u)=>{const a=e[w.FLIGHT_STATUS].value==="O",{departureDateTime:y,arrivalDateTime:g}=as(e[w.DEPARTURE_DATE].value,e[w.DATE_TIME_RANGE].value,o,u),{openCabins:d,closeCabins:c}=os(e[w.OPEN_AND_CLOSE_CABINS].value),x=rs(e[w.OC_AIRLINE].value),p=e[w.GLOBAL_INDICATOR].value.slice(2);return{openFlag:a,globalIndicator:p,flightStatus:e[w.FLIGHT_STATUS].value,companyCode:e[w.COMPANY_CODE].value,flightNumber:e[w.FLIGHT_NUMBER].value,cabinCode:e[w.CABIN_CODE].value,departureAirport:e[w.DEPARTURE_AIRPORT].value,departureDateTime:y,arrivalDateTime:g,arrivalAirport:e[w.ARRIVAL_AIRPORT].value,stopQuantity:e[w.STOP_QUANTITY].value,stopFlag:e[w.STOP_FLAG].value,airEquipmentType:e[w.AIR_EQUIPMENT_TYPE].value,operatingAirline:x,openCabins:d,closeCabins:c,flightType:""}},An=(e,o,u)=>{const a=[];return e.some(d=>{const{valid:c,flightInfoForm:x}=ss(d);return a.push(x),!c})?[]:a.map(d=>[ns(d,o??"",u??"")])??[]},is=e=>{const o=[],u=[];return e.forEach(a=>{Vt.some(y=>y.value.toUpperCase()===a.toUpperCase())?u.push({passengerType:a==="CNN"?"CHD":a,selectedNumber:1}):o.push({passengerType:a,selectedNumber:1})}),{selectedPassengers:u,groupPassengers:o}},wn=e=>{switch(e){case"RR":case"HK":return"S";case"HL":case"HN":return"L";default:return"U"}},Nn=e=>e?` #O${e}`:"",ls=(e,o)=>M(`${L}/apiAvSearch/queryRules/translate`,{headers:{gid:o}}).post(e).json(),Sn=(e,o)=>M(`${L}/apiAvSearch/queryRules`,{headers:{gid:o}}).post(e).json(),ds={class:"item-name"},cs={class:"item-state"},ps={class:"item-container"},us={class:"item"},fs={class:"item-text"},gs={class:"item-name"},ys={class:"item-state"},hs={class:"item-container"},xs={class:"item"},_s={class:"item-text"},vs={class:"item-name"},bs={class:"item-state"},ms={class:"item-container"},ks={class:"item"},$s={class:"item-text"},Ds={class:"item-name"},Rs={class:"item-state"},Ts={class:"item-container"},Is={class:"item"},Cs={class:"item-text"},As=ce({__name:"CollapseInfo",props:{rule:{}},setup(e){const o=mt(),{orderInfo:u,activeTag:a}=kt(o),y=ae(()=>o.getFareType(a.value)),g=ae(()=>u.value.get(a.value).type==="2"),d=ae(()=>u.value.get(a.value).international),c=B("unfold"),x=B("unfold"),p=B(["change","refund"]),m=k=>{(k??[]).includes("change")?x.value="retract":x.value="unfold",(k??[]).includes("refund")?c.value="retract":c.value="unfold"};return he(()=>{!d.value&&g.value&&y.value==="normalFare"&&(c.value="retract",x.value="retract")}),(k,R)=>{const D=Bt,N=Ht;return!d.value&&g.value&&y.value==="normalFare"?(n(),te(N,{key:0,modelValue:p.value,"onUpdate:modelValue":R[0]||(R[0]=v=>p.value=v),onChange:m},{default:E(()=>[U(D,{name:"change"},{title:E(()=>[t("div",ds,[t("span",null,r(k.$t("app.fareQuery.freightate.revalidationRule")),1),t("span",cs,r(k.$t(`app.fareQuery.freightate.${x.value??"unfold"}`)),1)])]),default:E(()=>[t("div",ps,[t("div",us,[(n(!0),l(P,null,V(k.rule.ruleInfos.change,(v,I)=>(n(),l("div",{key:I},[t("div",fs,r(v.replace(/<br>/g,`\r
`)),1)]))),128))])])]),_:1}),U(D,{name:"refund"},{title:E(()=>[t("div",gs,[t("span",null,r(k.$t("app.fareQuery.freightate.refund")),1),t("span",ys,r(k.$t(`app.fareQuery.freightate.${c.value??"unfold"}`)),1)])]),default:E(()=>[t("div",hs,[t("div",xs,[(n(!0),l(P,null,V(k.rule.ruleInfos.refund,(v,I)=>(n(),l("div",{key:I},[t("span",_s,r(v.replace(/<br>/g,`\r
`)),1)]))),128))])])]),_:1})]),_:1},8,["modelValue"])):(n(),te(N,{key:1,modelValue:p.value,"onUpdate:modelValue":R[1]||(R[1]=v=>p.value=v),onChange:m},{default:E(()=>[U(D,{name:"change"},{title:E(()=>[t("div",vs,[t("span",null,r(k.$t("app.fareQuery.freightate.revalidationRule")),1),t("span",bs,r(k.$t(`app.fareQuery.freightate.${x.value??"unfold"}`)),1)])]),default:E(()=>[t("div",ms,[t("div",ks,[(n(!0),l(P,null,V(k.rule.ruleInfos.change,(v,I)=>(n(),l("div",{key:I},[t("div",$s,r(v.replace(/<br>/g,`\r
`)),1)]))),128))])])]),_:1}),U(D,{name:"refund"},{title:E(()=>[t("div",Ds,[t("span",null,r(k.$t("app.fareQuery.freightate.refund")),1),t("span",Rs,r(k.$t(`app.fareQuery.freightate.${c.value??"unfold"}`)),1)])]),default:E(()=>[t("div",Ts,[t("div",Is,[(n(!0),l(P,null,V(k.rule.ruleInfos.refund,(v,I)=>(n(),l("div",{key:I},[t("span",Cs,r(v.replace(/<br>/g,`\r
`)),1)]))),128))])])]),_:1})]),_:1},8,["modelValue"]))}}});const ws=xe(As,[["__scopeId","data-v-68b3b8dc"]]),Ns=e=>{var G;const{t:o}=lt(),u=B([]),a=B(!1),y=B([]),g=B([]),d=(G=navigator==null?void 0:navigator.userAgent)==null?void 0:G.toLowerCase(),c=ae(()=>d==null?void 0:d.includes("electron/")),x=ae(()=>e.isCnLang),p=ae(()=>e.issueDate??""),m=B(),k=B(),R=B({serialNumber:"16"}),D=B(!1),N={amount:"-",currency:"-",changeType:"-",allowRefund:"-",permission:"-"},v=ae(()=>e.rulesRes.translatedRuleInfo??""),I=$t({proChange:[],maxStay:"",minStay:""}),T={serialNumber:[{pattern:Dt,message:o("app.fareQuery.freightate.inputSplitNum")}]},H=_=>{if(!_)return"";if(_.includes(" ")){const[C,Q]=_.split(" "),i=/^(\d{1,2}):(\d{1,2})(?::(\d{1,2}))?$/,s=Q.match(i);if(s){const f=s[1].padStart(2,"0"),$=s[2].padStart(2,"0"),A=s[3]?s[3].padStart(2,"0"):"00";return`${C} ${f}:${$}:${A}`}else return _}else return`${_} 00:00:00`},j=_=>{u.value=_.map(C=>({...C,contents:C.contents.map(Q=>({strList:Q.split("<br>")}))}))},z=()=>{var _;a.value=!1,(_=m.value)==null||_.validate(C=>{if(C)if(R.value.serialNumber){const Q=R.value.serialNumber.split("/"),s=[...new Set(Q)].map(Number),f=[];s.forEach($=>{f.push(...e.rulesRes.fareRuleInfos.filter(A=>$===Number(A.number)))}),j(f)}else j(e.rulesRes.fareRuleInfos)})},q=()=>{var Q,i,s,f;a.value=!0,R.value.serialNumber="";const _=ae(()=>e.rulesRes.fareRuleInfos.map($=>({number:$.number,title:$.title}))),C=Math.ceil(((Q=_.value)==null?void 0:Q.length)/2);y.value=(i=_.value)==null?void 0:i.slice(0,C),g.value=(f=_.value)==null?void 0:f.slice(C,(s=_.value)==null?void 0:s.length)},W=_=>{R.value.serialNumber=_,z()},J=_=>{I.maxStay=_.maxStay??"-",I.minStay=_.minStay??"-",(_.change??[N]).forEach(C=>{I.proChange.push({...C,name:o("app.fareQuery.freightate.revalidationRule")})}),(_.refund??[N]).forEach(C=>{I.proChange.push({...C,name:o("app.fareQuery.freightate.refund")})}),(_.noShow??[N]).forEach(C=>{I.proChange.push({...C,name:C.allowRefund?o("app.fareQuery.freightate.missedReFundFlight"):o("app.fareQuery.freightate.missedFlight")})})},re=()=>e.detailInfo.segInfos.filter(_=>`${_.departureCode}${_.arrivalCode}`==`${e.rulesRes.departureAirport}${e.rulesRes.arrivalAirport}`).map(_=>{var C,Q;return{ref1:((C=_.ruleIds)==null?void 0:C[0])??"",ref2:((Q=_.ruleIds)==null?void 0:Q[1])??"",fareReference:_.fareBasis,departureDate:ee(_.departureDateTime).format("YYYY-MM-DD hh:mm:ss"),filingAirline:_.marketingAirline,departureAirport:_.departureCode,arrivalAirport:_.arrivalCode}}),oe=_=>{switch(_){case"1":return"per one way";case"2":return"per round way";case"3":return"per ticket";case"4":return"per coupon";case"5":return"per direction";default:return"-"}},b=_=>{switch(_){case"H":return"(取高)";case"L":return"(取低)";case"A":return"(叠加)";default:return""}},K=async()=>{var A,S,F,X,Z,ne,ie,se,fe,ge;const _=Date.now(),C=re();I.proChange=[],I.maxStay="",I.minStay="";const Q=(S=(A=C??[])==null?void 0:A[0].ref1)==null?void 0:S.slice(4,-1).split("::"),i=(X=(F=C??[])==null?void 0:F[0].ref2)==null?void 0:X.split("::"),s={};Q.forEach((pe,_e)=>{s[pe]=i[_e]});const f={originCode:(s==null?void 0:s.Origin)??"",destinationCode:(s==null?void 0:s.Destination)??"",travelDate:((ne=(Z=C??[])==null?void 0:Z[0])==null?void 0:ne.departureDate)??"",ticketingDate:H(p.value),carrier:(s==null?void 0:s.Carrier)??"",fareReference:(s==null?void 0:s.FBC)??"",passType:(s==null?void 0:s.PassengerType)??"",ruleNumber:(s==null?void 0:s.Rule)??"",fareType:(s==null?void 0:s.FareType)??"",ftnt1:(s==null?void 0:s.Footnote1)??"",ftnt2:(s==null?void 0:s.Footnote2)??"",owrt:(s==null?void 0:s.OWRT)??"",ref1:rt.encode(((se=(ie=C??[])==null?void 0:ie[0])==null?void 0:se.ref1)??""),ref2:rt.encode(((ge=(fe=C??[])==null?void 0:fe[0])==null?void 0:ge.ref2)??""),routing:(s==null?void 0:s.Routing)??"",ruleTariff:(s==null?void 0:s.RuleTariff)??"",source:(s==null?void 0:s.FareSource)??"",originAddonTrf:(s==null?void 0:s.OriginAddonTariff)??"",originAddonRouting:(s==null?void 0:s.OriginAddonRouting)??"",destinationAddonTrf:(s==null?void 0:s.DestinationAddonTariff)??"",destinationAddonRouting:(s==null?void 0:s.DestinationAddonRouting)??"",fbrBaseFareBasis:(s==null?void 0:s.FbrBaseFareBasis)??"",fbrBaseRule:(s==null?void 0:s.FbrBaseRule)??"",fbrBaseTariff:(s==null?void 0:s.FbrBaseTariff)??"",originAddonFtnt1:(s==null?void 0:s.OriginAddonFootnote1)??"",originAddonFtnt2:(s==null?void 0:s.OriginAddonFootnote2)??"",destinationAddonFtnt1:(s==null?void 0:s.DestinationAddonFootnote1)??"",destinationAddonFtnt2:(s==null?void 0:s.DestinationAddonFootnote2)??"",fbrR3C25TblNo:(s==null?void 0:s.R3c025TblNo)??"",accountCode:(s==null?void 0:s.AccountCode)??""};let $=null;try{D.value=!0;const{data:pe}=await ls(f,"********");$=Date.now(),pe.value&&J(pe.value)}finally{D.value=!1,Tt(_,$,"国际运价计算查询票规-中文化")}};return he(async()=>{await K(),z()}),Rt(()=>{u.value=[]}),{interRuleContent:u,priceRules:I,queryRuleInfo:z,translateRuleInfo:K,QUIT_OR_UPDATE_RULE:T,quitOrUpdateRuleForm:R,quitOrUpdateRuleRef:m,loading:D,scrollbar:k,getHightLow:b,getChargePer:oe,queryList:q,isList:a,leftColumn:y,rightColumn:g,queryRuleByNumber:W,isChineseShow:x,isCnRulesText:v,isClient:c}},Ss=Ns,Fs=e=>(ut("data-v-a6488a8b"),e=e(),ft(),e),Es={class:"u-content"},qs={class:"u-cat"},Bs={class:"u-cat-tit"},Hs={class:"u-cat-cont"},Ps={key:0,class:"text-gray-2 text-xs"},Qs={key:1,class:"loading-text"},Ms={class:"w-[100%] flex flex-col crs-new-ui-init-cls"},Ls={class:"pasg-name-tag"},Ys=Fs(()=>t("span",{class:"iconfont icon-info-circle-line"},null,-1)),Us={key:0},Os={key:1,class:"loading-text"},Vs={key:0},js=["onClick"],Ws=["onClick"],Ks={key:1,class:"loading-text"},Gs=ce({__name:"InterRuleInfo",props:{rulesRes:{},detailInfo:{},ruleFullHeight:{type:Boolean},isCnLang:{type:Boolean},openDialog:{type:Boolean},fsn:{type:Boolean},issueDate:{}},setup(e){const o=e,{interRuleContent:u,queryRuleInfo:a,quitOrUpdateRuleRef:y,quitOrUpdateRuleForm:g,QUIT_OR_UPDATE_RULE:d,loading:c,scrollbar:x,queryList:p,isList:m,queryRuleByNumber:k,leftColumn:R,rightColumn:D,isCnRulesText:N,isChineseShow:v,isClient:I}=Ss(o);return(T,H)=>{const j=De,z=Pt,q=Ct,W=Qt,J=At,re=Mt,oe=pt;return dt((n(),l("div",{class:de(["rule-table flex w-full mt-1",T.ruleFullHeight?"h-[668px]":h(I)?"":"h-[244px]"])},[t("div",Es,[U(j,{class:de([T.ruleFullHeight?"h-[668px]":"h-[233px]"])},{default:E(()=>[t("div",qs,[t("div",Bs,[t("span",{class:de([T.fsn?"w-[345px]":"w-[481px]"])},null,2)]),t("div",Hs,[h(N)?(n(),l("div",Ps,[t("pre",null,r(h(N)),1)])):(n(),l("div",Qs,r(T.$t("app.fareQuery.freightate.noDataAvailable")),1))])])]),_:1},8,["class"])]),t("div",Ms,[U(re,{ref_key:"quitOrUpdateRuleRef",ref:y,class:"relative",inline:"",model:h(g),rules:h(d),onSubmit:H[1]||(H[1]=It(()=>{},["prevent"]))},{default:E(()=>[ct(T.$slots,"default",{},void 0,!0),U(W,{label:T.$t("app.fareQuery.freightate.ruleNumber"),class:"rule-number-item",prop:"serialNumber"},{default:E(()=>[t("div",Ls,[U(z,{placement:"top"},{content:E(()=>[t("p",null,r(T.$t("app.fareQuery.freightate.inputSplitNumTip")),1)]),default:E(()=>[Ys]),_:1})]),U(q,{modelValue:h(g).serialNumber,"onUpdate:modelValue":H[0]||(H[0]=b=>h(g).serialNumber=b)},null,8,["modelValue"])]),_:1},8,["label"]),U(J,{type:"primary",size:"small",onClick:h(a)},{default:E(()=>[be(r(T.$t("app.fareQuery.freightate.search")),1)]),_:1},8,["onClick"]),U(J,{type:"primary",link:"",size:"small",class:"ml-1",onClick:h(p)},{default:E(()=>[be(r(T.$t("app.fareQuery.freightate.list")),1)]),_:1},8,["onClick"])]),_:3},8,["model","rules"]),U(j,{ref_key:"scrollbar",ref:x,height:T.ruleFullHeight?"610":"",class:"mt-2.5 text-xs text-gray-2 grow"},{default:E(()=>[h(m)?O("",!0):(n(!0),l(P,{key:0},V(h(u),(b,K)=>(n(),l("div",{key:K},[t("pre",null,r(`${b.number}.${b.title}`),1),b.contents.length?(n(),l("div",Us,[(n(!0),l(P,null,V(b.contents,(G,_)=>(n(),l("div",{key:_},[(n(!0),l(P,null,V(G.strList,C=>(n(),l("pre",{key:C,class:"whitespace-pre-wrap"},r(C),1))),128))]))),128))])):(n(),l("div",Os,r(T.$t("app.fareQuery.freightate.noDataAvailable")),1))]))),128)),h(m)&&!h(v)?(n(),l(P,{key:1},[h(R).length>0||h(D).length>0?(n(),l("div",Vs,[(n(!0),l(P,null,V(h(R),(b,K)=>(n(),l("div",{key:K},[h(R).length>0?(n(),l("div",{key:0,class:"cursor-pointer w-[50%]",style:{float:"left"},onClick:G=>h(k)(`${b==null?void 0:b.number}`)},[t("span",null,r((b==null?void 0:b.number)<10?`0${b==null?void 0:b.number}`:`${b==null?void 0:b.number}`)+" "+r(b==null?void 0:b.title),1)],8,js)):O("",!0)]))),128)),(n(!0),l(P,null,V(h(D),(b,K)=>(n(),l("div",{key:K},[h(D).length>0?(n(),l("div",{key:0,class:"cursor-pointer w-[50%]",style:{float:"left"},onClick:G=>h(k)(`${b==null?void 0:b.number}`)},[t("span",null,r((b==null?void 0:b.number)<10?`0${b==null?void 0:b.number}`:`${b==null?void 0:b.number}`)+" "+r(b==null?void 0:b.title),1)],8,Ws)):O("",!0)]))),128))])):(n(),l("div",Ks,r(T.$t("app.fareQuery.freightate.noDataAvailable")),1))],64)):O("",!0)]),_:1},8,["height"])])],2)),[[oe,h(c)]])}}});const ht=xe(Gs,[["__scopeId","data-v-a6488a8b"]]),zs={key:0,class:"w-[1012px]"},Js={key:0,class:"title flex gap-1"},Xs={key:0,class:"bg-gray-7 text-gray-3 text-[12px] font-[400] py-[4px] px-[5px] rounded-[2px]"},Zs={key:1},ea={key:2},ta={class:"content"},sa={key:1,class:"empty-info"},aa=ce({__name:"TicketRuleDialog",props:{detailInfo:{},isCnLang:{type:Boolean},issueDate:{},fsnTicketNos:{}},emits:["update:modelValue"],setup(e,{emit:o}){const u=o,a=()=>{u("update:modelValue",!1)},y=ye([]),g=d=>{const c=y.value.find(x=>x.airportCode===d.toUpperCase());return c?c.airportEnName:d};return he(async()=>{const d=await ke("searchLocalData");y.value=d?JSON.parse(d==null?void 0:d.localData):[]}),(d,c)=>{const x=De,p=Lt;return n(),te(p,{title:d.$t("app.fareQuery.freightate.tktRule"),class:"tkt-rules-dialog","align-center":!0,modal:"","close-on-click-modal":!1,width:"1040",onClose:a},{default:E(()=>[U(x,{ref:"scrollbar",height:"688"},{default:E(()=>{var m,k,R;return[((k=(m=d.detailInfo.ticketRegulation)==null?void 0:m.flightRules[0])==null?void 0:k.rules.length)!==0?(n(),l("div",zs,[(n(!0),l(P,null,V(((R=d.detailInfo.ticketRegulation)==null?void 0:R.flightRules)??[],(D,N)=>(n(),l("div",{key:N,class:"rule-item overflow-y-hidden"},[(n(!0),l(P,null,V(D.rules,(v,I)=>{var T,H;return n(),l("div",{key:I,class:"min-w-full"},[D.rules.length>1?(n(),l("div",Js,[(T=d.fsnTicketNos)!=null&&T[I]?(n(),l("span",Xs,r(h($e)((H=d.fsnTicketNos)==null?void 0:H[I])),1)):O("",!0),h(gt)()==="en"?(n(),l("span",Zs,r(`${g(v.departureAirport)}(${v.departureAirport}) - ${g(v.arrivalAirport)}(${v.arrivalAirport})`),1)):(n(),l("span",ea,r(`${v.departureAirportCityCh}(${v.departureAirport}) - ${v.arrivalAirportCityCh}(${v.arrivalAirport})`),1))])):O("",!0),t("div",ta,[U(ht,{"is-cn-lang":d.isCnLang,"rules-res":v,"detail-info":d.detailInfo,"rule-full-height":"","issue-date":d.issueDate},{default:E(()=>[ct(d.$slots,"default")]),_:2},1032,["is-cn-lang","rules-res","detail-info","issue-date"])])])}),128))]))),128))])):(n(),l("div",sa,[t("div",null,r(d.$t("app.fareQuery.freightate.noTktData")),1)]))]}),_:3},512)]),_:3},8,["title"])}}});const ra={key:0,class:"close-box"},oa={class:"flex gap-1 items-center"},na={key:0,class:"title-text"},ia={key:1,class:"title-text"},la={key:0,class:"title flex gap-1"},da={key:0,class:"bg-gray-7 text-gray-3 text-[12px] font-[400] py-[4px] px-[5px] rounded-[2px]"},ca={key:1},pa={key:2},ua={class:"content"},fa=ce({__name:"TicketRule",props:{international:{type:Boolean},ruleInfoData:{},detailInfo:{},needCloseIcon:{type:Boolean},fullHeight:{type:Boolean},fsn:{type:Boolean},issueDate:{},fsnTicketNos:{},isShowWindow:{type:Boolean,default:!1}},emits:["closePanel"],setup(e,{emit:o}){const u=B(!1),a=B(!1),y=o,g=p=>{y("closePanel",p)},d=()=>{u.value=!0},c=ye([]),x=p=>{const m=c.value.find(k=>k.airportCode===p.toUpperCase());return m?m.airportEnName:p};return he(async()=>{const p=await ke("searchLocalData");c.value=p?JSON.parse(p==null?void 0:p.localData):[]}),(p,m)=>{var R;const k=Nt;return n(),l("div",null,[t("div",{class:de(["rule-info",[p.fullHeight||p.fsn?"":" border-t border-gray-7 border-solid",p.fsn?"":"mt-2.5 mr-2.5"]])},[p.needCloseIcon?(n(),l("div",ra,[t("div",oa,[p.detailInfo.componentCode==="tktRule"?(n(),l("span",na,r(p.$t(`app.fareQuery.freightate.${p.detailInfo.componentCode}`)),1)):O("",!0),p.detailInfo.componentCode==="ticketRegulation"?(n(),l("span",ia,r(p.$t(`app.fare.singleFare.${p.detailInfo.componentCode}`)),1)):O("",!0),(p.detailInfo.componentCode==="tktRule"||p.detailInfo.componentCode==="ticketRegulation")&&p.international&&!p.isShowWindow?(n(),l("span",{key:2,class:"text-brand-2 text-xs font-normal leading-tight cursor-pointer",onClick:d},r(p.$t("app.fareQuery.freightate.fullView")),1)):O("",!0)]),p.fsn?O("",!0):(n(),te(k,{key:0,class:"cursor-pointer",size:"16px",onClick:m[0]||(m[0]=D=>g(p.detailInfo))},{default:E(()=>[U(h(wt))]),_:1}))])):O("",!0),(n(!0),l(P,null,V(((R=p.ruleInfoData)==null?void 0:R.flightRules)??[],(D,N)=>(n(),l("div",{key:N,class:"rule-item overflow-y-hidden"},[(n(!0),l(P,null,V(D.rules,(v,I)=>{var T,H;return n(),l("div",{key:I,class:"min-w-full"},[D.rules.length>1?(n(),l("div",la,[(T=p.fsnTicketNos)!=null&&T[I]?(n(),l("span",da,r(h($e)((H=p.fsnTicketNos)==null?void 0:H[I])),1)):O("",!0),h(gt)()==="en"?(n(),l("span",ca,r(`${x(v.departureAirport)}(${v.departureAirport}) - ${x(v.arrivalAirport)}(${v.arrivalAirport})`),1)):(n(),l("span",pa,r(`${v.departureAirportCityCh}(${v.departureAirport}) - ${v.arrivalAirportCityCh}(${v.arrivalAirport})`),1))])):O("",!0),t("div",ua,[p.international?(n(),te(ht,{key:0,"is-cn-lang":a.value,"onUpdate:isCnLang":m[1]||(m[1]=j=>a.value=j),"rules-res":v,"detail-info":p.detailInfo,"rule-full-height":p.fullHeight,fsn:p.fsn,"issue-date":p.issueDate},null,8,["is-cn-lang","rules-res","detail-info","rule-full-height","fsn","issue-date"])):(n(),te(ws,{key:1,rule:v},null,8,["rule"]))])])}),128))]))),128))],2),U(aa,{modelValue:u.value,"onUpdate:modelValue":m[2]||(m[2]=D=>u.value=D),"is-cn-lang":a.value,"onUpdate:isCnLang":m[3]||(m[3]=D=>a.value=D),"detail-info":p.detailInfo,"issue-date":p.issueDate,"fsn-ticket-nos":p.fsnTicketNos},null,8,["modelValue","is-cn-lang","detail-info","issue-date","fsn-ticket-nos"])])}}});const ga=xe(fa,[["__scopeId","data-v-11155a84"]]),Re=e=>(ut("data-v-fb40940e"),e=e(),ft(),e),ya={class:"w-full h-full overflow-auto"},ha={class:"grid grid-cols-[22%_28%_28%_22%] auto-rows-[minmax(32px,auto)]"},xa={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},_a={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},va={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},ba={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-gray-2"},ma={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},ka={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},$a={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},Da={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Ra={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Ta={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},Ia=Re(()=>t("div",{class:"text-gray-2 text-[12px] font-bold leading-[20px]"},"OFFICE：",-1)),Ca={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Aa={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},wa={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Na={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Sa={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-gray-2"},Fa={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Ea={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},qa={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},Ba={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Ha={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Pa={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},Qa={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Ma={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},La=St('<div class="flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2" data-v-fb40940e><div class="text-gray-2 text-[12px] font-bold leading-[20px]" data-v-fb40940e></div><div class="text-gray-2 text-[12px] font-normal leading-[20px]" data-v-fb40940e></div></div><div class="flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-gray-2" data-v-fb40940e><div class="text-gray-2 text-[12px] font-bold leading-[20px]" data-v-fb40940e></div><div class="text-gray-2 text-[12px] font-normal leading-[20px]" data-v-fb40940e></div></div>',2),Ya={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},Ua={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Oa={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Va={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},ja={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Wa={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Ka={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-gray-2"},Ga={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},za={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Ja={class:"col-span-4 flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-gray-2"},Xa={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Za={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},er={class:"grid grid-cols-[28%_22%_28%_22%] auto-rows-[minmax(32px,auto)]"},tr={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},sr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},ar={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},rr={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},or={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},nr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},ir={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},lr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},dr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},cr={class:"flex justify-start items-center flex-wrap row-span-2 bg-gray-0 border border-b-0 border-gray-2"},pr={class:"flex justify-start items-center h-[32px] px-[6px] py-[4px]"},ur={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},fr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},gr={class:"flex justify-start items-center h-[32px] px-[6px] py-[4px]"},yr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},hr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},xr={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},_r={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},vr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},br={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},mr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},kr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},$r={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},Dr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Rr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Tr={class:"grid grid-cols-[22%_28%_28%_22%] auto-rows-[minmax(32px,auto)]"},Ir={class:"col-span-1 flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},Cr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Ar={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},wr={class:"col-span-3 flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-gray-2"},Nr={class:"text-gray-2 text-[12px] font-bold leading-[20px] break-keep"},Sr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Fr={class:"grid grid-cols-[22%_28%_28%_22%] auto-rows-[minmax(32px,auto)]"},Er={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},qr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Br={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Hr={class:"col-span-3 flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-gray-2"},Pr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Qr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Mr={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},Lr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Yr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Ur={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},Or={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Vr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},jr={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},Wr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Kr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Gr={class:"flex justify-start items-center p-1.5 py-2.5 bg-gray-0 border border-b-0 border-gray-2"},zr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Jr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Xr={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},Zr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},eo={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},to={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},so={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},ao={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},ro={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-gray-2 border-r-0 border-b-0"},oo={class:"text-gray-2 text-[12px] font-bold leading-[20px] whitespace-pre-wrap"},no={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},io={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-gray-2"},lo={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},co={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},po={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-gray-2 border-r-0"},uo=Re(()=>t("div",{class:"text-gray-2 text-[12px] font-bold leading-[20px]"},"FP：",-1)),fo={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},go={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-gray-2 border-r-0"},yo=Re(()=>t("div",{class:"text-gray-2 text-[12px] font-bold leading-[20px]"},"TC：",-1)),ho={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},xo={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-gray-2"},_o={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},vo={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},bo={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-t-0 border-gray-2"},mo={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},ko={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},$o={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-t-0 border-gray-2"},Do={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Ro={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},To=ce({__name:"RTKTTable",props:{rtktDetailedInfo:{}},setup(e){const o=e,u=ae(()=>{var y,g;const a=((g=(y=o.rtktDetailedInfo)==null?void 0:y.price)==null?void 0:g.taxes)||[];if(a.length){const d=a.filter(c=>c.newOldRefundTax==="R");return d.length?d.map(c=>`${c.taxCode}:${c.currencyCode} ${c.taxAmount}`).join(" / "):"-"}return"-"});return(a,y)=>{var g,d,c,x,p,m,k,R,D,N,v,I,T,H,j,z,q,W,J,re,oe,b,K,G,_,C,Q,i,s,f,$,A,S,F,X,Z,ne,ie,se,fe,ge,pe,_e,Te,Ie,Ce,Ae,we,Ne,Se,Fe,Ee,qe,Be,He,Pe,Qe,Me,Le,Ye,Ue,Oe,Ve,je,We,Ke,Ge,ze,Je,Xe,Ze,et,tt,st;return n(),l("div",ya,[t("div",ha,[t("div",xa,[t("div",_a,r(a.$t("app.queryRtkt.issueAirline")),1),t("div",va,r(((d=(g=a.rtktDetailedInfo)==null?void 0:g.ticket)==null?void 0:d.issueAirline)||"-"),1)]),t("div",ba,[t("div",ma,r(a.$t("app.queryRtkt.issueMethod")),1),t("div",ka,r(((x=(c=a.rtktDetailedInfo)==null?void 0:c.ticket)==null?void 0:x.issueType)||"-"),1)]),t("div",$a,[t("div",Da,r(a.$t("app.queryRtkt.asa")),1),t("div",Ra,r(((m=(p=a.rtktDetailedInfo)==null?void 0:p.ticket)==null?void 0:m.iataCode)||"-"),1)]),t("div",Ta,[Ia,t("div",Ca,r(((R=(k=a.rtktDetailedInfo)==null?void 0:k.ticket)==null?void 0:R.office)||"-"),1)]),t("div",Aa,[t("div",wa,r(a.$t("app.queryRtkt.code")),1),t("div",Na,r(((N=(D=a.rtktDetailedInfo)==null?void 0:D.ticket)==null?void 0:N.code)||"-"),1)]),t("div",Sa,[t("div",Fa,r(a.$t("app.queryRtkt.ticketState")),1),t("div",Ea,r((I=(v=a.rtktDetailedInfo)==null?void 0:v.ticket)!=null&&I.ticketState?a.$t(`app.queryRtkt.${(H=(T=a.rtktDetailedInfo)==null?void 0:T.ticket)==null?void 0:H.ticketState}`):"-"),1)]),t("div",qa,[t("div",Ba,r(a.$t("app.queryRtkt.account")),1),t("div",Ha,r(((z=(j=a.rtktDetailedInfo)==null?void 0:j.ticket)==null?void 0:z.accountNumber)||"-"),1)]),t("div",Pa,[t("div",Qa,r(a.$t("app.queryRtkt.issueDate")),1),t("div",Ma,r(((W=(q=a.rtktDetailedInfo)==null?void 0:q.ticket)==null?void 0:W.issueDate)||"-"),1)]),La,t("div",Ya,[t("div",Ua,r(a.$t("app.queryRtkt.ticketNumber")),1),t("div",Oa,r(((re=(J=a.rtktDetailedInfo)==null?void 0:J.ticket)==null?void 0:re.ticketNumber)||"-"),1)]),t("div",Va,[t("div",ja,r(a.$t("app.queryRtkt.deviceNumber")),1),t("div",Wa,r((b=(oe=a.rtktDetailedInfo)==null?void 0:oe.ticket)!=null&&b.printNumber?`DEV-${(G=(K=a.rtktDetailedInfo)==null?void 0:K.ticket)==null?void 0:G.printNumber}`:"-"),1)]),t("div",Ka,[t("div",Ga,r(a.$t("app.queryRtkt.refundDeviceNumber")),1),t("div",za,r((C=(_=a.rtktDetailedInfo)==null?void 0:_.ticket)!=null&&C.refundPrintNumber?`DEV-${(i=(Q=a.rtktDetailedInfo)==null?void 0:Q.ticket)==null?void 0:i.refundPrintNumber}`:"-"),1)]),t("div",Ja,[t("div",Xa,r(a.$t("app.queryRtkt.eiItem")),1),t("div",Za,r(((f=(s=a.rtktDetailedInfo)==null?void 0:s.ticket)==null?void 0:f.ei)||"-"),1)])]),t("div",er,[(n(!0),l(P,null,V((A=($=a.rtktDetailedInfo)==null?void 0:$.passenger)==null?void 0:A.segments,(Y,xt)=>(n(),l(P,{key:xt},[t("div",tr,[t("div",sr,r(a.$t("app.queryRtkt.originAndDestination")),1),t("div",ar,r(Y.departureCityName)+"("+r(Y.departureCity)+")-"+r(Y.arrivalCityName)+"("+r(Y.arrivalCity)+") ",1)]),t("div",rr,[t("div",or,r(a.$t("app.queryRtkt.flightNumber")),1),t("div",nr,r(Y.flightNo),1)]),t("div",ir,[t("div",lr,r(a.$t("app.queryRtkt.departureDate")),1),t("div",dr,r(Y.departureDateTime),1)]),t("div",cr,[t("div",pr,[t("div",ur,r(a.$t("app.queryRtkt.effectiveDate")),1),t("div",fr,r(Y.notValidBefore),1)]),t("div",gr,[t("div",yr,r(a.$t("app.queryRtkt.expiryDate")),1),t("div",hr,r(Y.notValidAfter),1)])]),t("div",xr,[t("div",_r,r(a.$t("app.queryRtkt.cabin")),1),t("div",vr,r(Y==null?void 0:Y.cabin),1)]),t("div",br,[t("div",mr,r(a.$t("app.queryRtkt.fareBasis")),1),t("div",kr,r(Y==null?void 0:Y.fareBasis),1)]),t("div",$r,[t("div",Dr,r(a.$t("app.queryRtkt.baggage")),1),t("div",Rr,r(Y==null?void 0:Y.baggage),1)])],64))),128))]),t("div",Tr,[t("div",Ir,[t("div",Cr,r(a.$t("app.queryRtkt.fareFlag")),1),t("div",Ar,r((F=(S=a.rtktDetailedInfo)==null?void 0:S.price)!=null&&F.autoFareType?a.$t("app.queryRtkt.auto"):a.$t("app.queryRtkt.manual")),1)]),t("div",wr,[t("div",Nr,r(a.$t("app.queryRtkt.fcItem")),1),t("div",Sr,r(((Z=(X=a.rtktDetailedInfo)==null?void 0:X.price)==null?void 0:Z.fc)||"-"),1)])]),t("div",Fr,[t("div",Er,[t("div",qr,r(a.$t("app.queryRtkt.totalTax")),1),t("div",Br,r(((ie=(ne=a.rtktDetailedInfo)==null?void 0:ne.price)==null?void 0:ie.taxAmount)||"-"),1)]),t("div",Hr,[t("div",Pr,r(a.$t("app.queryRtkt.taxDetails")),1),t("div",Qr,r(((fe=(se=a.rtktDetailedInfo)==null?void 0:se.price)==null?void 0:fe.taxDetail)||"-"),1)]),t("div",Mr,[t("div",Lr,r(a.$t("app.queryRtkt.ticketAmountFR")),1),t("div",Yr,r(((pe=(ge=a.rtktDetailedInfo)==null?void 0:ge.price)==null?void 0:pe.ticketAmountFOrR)||"-"),1)]),t("div",Ur,[t("div",Or,r(a.$t("app.queryRtkt.ticketAmountE")),1),t("div",Vr,r(((Te=(_e=a.rtktDetailedInfo)==null?void 0:_e.price)==null?void 0:Te.ticketAmount)||"-"),1)]),t("div",jr,[t("div",Wr,r(a.$t("app.queryRtkt.commissionFare")),1),t("div",Kr,r(((Ce=(Ie=a.rtktDetailedInfo)==null?void 0:Ie.ticket)==null?void 0:Ce.internationalIndicator)==="D"?(we=(Ae=a.rtktDetailedInfo)==null?void 0:Ae.price)==null?void 0:we.commissionFare:"-"),1)]),t("div",Gr,[t("div",zr,r(a.$t("app.queryRtkt.commissionRate")),1),t("div",Jr,r(((Se=(Ne=a.rtktDetailedInfo)==null?void 0:Ne.ticket)==null?void 0:Se.internationalIndicator)==="I"?(Ee=(Fe=a.rtktDetailedInfo)==null?void 0:Fe.price)==null?void 0:Ee.commissionRate:"-"),1)]),t("div",Xr,[t("div",Zr,r(a.$t("app.queryRtkt.scny"))+"：",1),t("div",eo,r(((Be=(qe=a.rtktDetailedInfo)==null?void 0:qe.price)==null?void 0:Be.scny)||"-"),1)]),t("div",to,[t("div",so,r(a.$t("app.queryRtkt.fareAmount")),1),t("div",ao,r(((Pe=(He=a.rtktDetailedInfo)==null?void 0:He.price)==null?void 0:Pe.fareAmount)||"-"),1)]),t("div",ro,[t("div",oo,[be(r(a.$t("app.queryRtkt.issueAirlineCode"))+" ",1),t("span",no,r(((Me=(Qe=a.rtktDetailedInfo)==null?void 0:Qe.ticket)==null?void 0:Me.issueAirlineCode)||"-"),1)])]),t("div",io,[t("div",lo,r(a.$t("app.queryRtkt.gpSign")),1),t("div",co,r(((Ye=(Le=a.rtktDetailedInfo)==null?void 0:Le.ticket)==null?void 0:Ye.ticketManagementOrganizationCode)??"-")+" "+r(((Oe=(Ue=a.rtktDetailedInfo)==null?void 0:Ue.ticket)==null?void 0:Oe.ticketType)!=="NONE"?"-":"")+" "+r((((je=(Ve=a.rtktDetailedInfo)==null?void 0:Ve.ticket)==null?void 0:je.ticketType)==="NONE"?"":(Ke=(We=a.rtktDetailedInfo)==null?void 0:We.ticket)==null?void 0:Ke.ticketType)??""),1)]),t("div",po,[uo,t("div",fo,r(((ze=(Ge=a.rtktDetailedInfo)==null?void 0:Ge.ticket)==null?void 0:ze.fp)||"-"),1)]),t("div",go,[yo,t("div",ho,r(((Xe=(Je=a.rtktDetailedInfo)==null?void 0:Je.price)==null?void 0:Xe.tc)||"-"),1)]),t("div",xo,[t("div",_o,r(a.$t("app.queryRtkt.creditCard")),1),t("div",vo,r(((et=(Ze=a.rtktDetailedInfo)==null?void 0:Ze.price)==null?void 0:et.creditCardDetail)||"-"),1)]),t("div",bo,[t("div",mo,r(a.$t("app.queryRtkt.originalTicket")),1),t("div",ko,r(((st=(tt=a.rtktDetailedInfo)==null?void 0:tt.ticket)==null?void 0:st.originalTicket)||"-"),1)]),t("div",$o,[t("div",Do,r(a.$t("app.queryRtkt.detailsOfTaxRefund")),1),t("div",Ro,r(u.value),1)])])])}}});const Io=xe(To,[["__scopeId","data-v-fb40940e"]]),Co={class:"flex-grow max-h-[calc(100vh_-_120px)] overflow-y-auto"},Ao={key:1,class:"mt-[110px] text-center w-full"},wo=["alt"],No={class:"mt-[15px] text-gray-2 leading-6 text-lg font-bold"},So=ce({__name:"RtktDetailedInfo",props:{rtktDetailedInfo:{}},setup(e){return(o,u)=>(n(),l("div",Co,[o.rtktDetailedInfo.ticket?(n(),te(Io,{key:0,"rtkt-detailed-info":o.rtktDetailedInfo},null,8,["rtkt-detailed-info"])):(n(),l("div",Ao,[t("img",{src:_t,alt:o.$t("app.querySearch.airCityCommon.noData"),class:"inline"},null,8,wo),t("p",No,r(o.$t("app.querySearch.airCityCommon.noData")),1)]))]))}});const Fo=xe(So,[["__scopeId","data-v-05439fe0"]]),Eo=t("i",{class:"iconfont icon-windowed mr-[2px]"},null,-1),qo=ce({__name:"RtktWindoing",props:{rtktDetailedInfo:{}},emits:["openRtktDetailWindow"],setup(e,{emit:o}){const u=e,a=o,y=Gt(),g=async()=>{a("openRtktDetailWindow");const d=u.rtktDetailedInfo.ticket.ticketNumber.indexOf(u.rtktDetailedInfo.ticket.issueAirlineCode);let c="";d===0&&(c=u.rtktDetailedInfo.ticket.ticketNumber.substring(d+u.rtktDetailedInfo.ticket.issueAirlineCode.length)),c=`${u.rtktDetailedInfo.ticket.issueAirlineCode}-${c}`,await y.delRtktDetailInfoWindowsList(c),y.setRtktDetailInfoWindowsList({...u.rtktDetailedInfo,id:c}),y.closeFastQuery()};return(d,c)=>(n(),l("div",{class:"open-detail-dialog flex items-center text-brand-2 text-[12px] cursor-pointer",onClick:c[0]||(c[0]=x=>g())},[Eo,t("div",null,r(d.$t("app.fastQuery.windowing")),1)]))}}),Bo=(e,o)=>{const{t:u}=lt(),a=B(!1),y=B(),g=B(!0),d=B([{code:"D",showText:u("app.fareQuery.freightate.originalTextTicket"),isShow:!0},{code:"H",showText:u("app.fareQuery.freightate.history"),isShow:!0},{code:"F",showText:u("app.fareQuery.freightate.certAndOther"),isShow:!0},{code:"X",showText:u("app.fareQuery.freightate.taxDetail"),isShow:!0},{code:"R",showText:u("app.fareQuery.freightate.rtktDetail"),isShow:!0},{code:"N",showText:u("app.fareQuery.freightate.fsnTkt"),isShow:a.value}]),c=B("D"),x=B(""),p=ye([]),m=ye({}),k=ye(),R=B({}),D=B([]),N=B(!1),v=i=>{const s=(i??"").match(Et);return(s==null?void 0:s[1])??""},I=async i=>{var $,A;const s=($=(await Yt({detrType:c.value,ticketNo:e.ticketNumber,intl:qt()==="en",secondFactor:e.secondFactor},i)).data.value)==null?void 0:$.data,f=d.value.find(S=>S.code==="N");switch(f&&c.value==="D"&&(f.isShow=(s==null?void 0:s.international)??!1),c.value){case"F":x.value=ot(((A=s==null?void 0:s.credential)==null?void 0:A.certificatesText)??"");break;case"D":p.value=ot((s==null?void 0:s.openSourceText)??"").split(`\r
`),o("update:secondFactor",(s==null?void 0:s.secondFactor)??e.secondFactor);break;case"X":x.value=(s==null?void 0:s.ticketFareInfoText)??"";break;case"H":x.value=(s==null?void 0:s.tktHistoryText)??"";break}},T=async()=>{const i=await ke("searchLocalData"),s=[];(JSON.parse(i.localData??"")??[]).forEach(f=>{const $={code:f.airportCode,cnName:f.cityArray[1],enName:f.cityArray[0]};s.push($)}),k.value=s},H=i=>((i??[]).forEach((s,f)=>{var $;($=k.value)==null||$.forEach(A=>{A.code===s.departureCity&&(i[f]=Object.assign(i[f],{departureCityName:A.cnName})),A.code===s.arrivalCity&&(i[f]=Object.assign(i[f],{arrivalCityName:A.cnName}))})}),i),j=()=>{const{passenger:i}=m.value;i&&(i.segments=H(i==null?void 0:i.segments))},z=i=>((i==null?void 0:i.filter(f=>f.flightNo))??[]).map(f=>{var $,A,S;return($=f.fareBasis)!=null&&$.includes("/")?(S=f.fareBasis)==null?void 0:S.slice(0,(A=f.fareBasis)==null?void 0:A.indexOf("/")):f.fareBasis}).join("/"),q=i=>i.gpSign?i.gpSign.trim():i.negotiatedFareCode,W=i=>{const s=i.replace(/\s+/g,"");return s&&s!=="null"?i.replace(/\s+/g,"T"):""},J=i=>{const s=i==null?void 0:i.filter(f=>f.flightNo);D.value=(s??[]).map(f=>f.ticketNo)},re=i=>((i==null?void 0:i.filter(f=>f.flightNo))??[]).map(f=>{var $,A,S,F;return[{openFlag:(($=f.flightNo)==null?void 0:$.includes("OPEN"))||((A=f.flightNo)==null?void 0:A.length)<=2,companyCode:((S=f.flightNo)==null?void 0:S.slice(0,2))??"",flightNumber:((F=f.flightNo)==null?void 0:F.slice(2))??"",cabinCode:f.cabin??"",departureDateTime:W(f.departureDateTime??""),departureAirport:f.departureCity??"",arrivalDateTime:W(f.arrivalDateTime??""),arrivalAirport:f.arrivalCity??"",flightType:"",stopQuantity:"",stopFlag:""}]}),oe=i=>{var S,F,X,Z,ne,ie;J(i.passenger.conjunctionSegments);const s=[i.passenger.passengerType??""],{selectedPassengers:f,groupPassengers:$}=is(s);return y.value=`${((S=i.ticket)==null?void 0:S.issueDate)??""}${((F=i.ticket)==null?void 0:F.issueTime)??""}`,{creditCard:q(i.price)==="GP",fareType:"",farebasic:z(i.passenger.conjunctionSegments),currencyCode:((X=i.price.fareAmount)==null?void 0:X.slice(0,3))??"",selectedPassengers:f,company:i.ticket.issueAirline,farePricingSale:!0,negotiatedFareCode:q(i.price),queryExclusiveNegotiatedFare:i.price.queryExclusiveNegotiatedFare??!1,allb:"0",pricingSource:"Both",reprice:"0",voyages:re(i.passenger.conjunctionSegments),brand:"",baggage:"",altTicketingDateTime:`${((Z=i.ticket)==null?void 0:Z.issueDate.slice(0,-3))??""}${((ne=i.ticket)==null?void 0:ne.issueTime)??""}`,airportCode:"",groupPassengerType:$.filter(se=>se).map(se=>se.passengerType).join(",")??"",priceVerification:{ticketAmount:i.price.fsnAmount??"",taxAmount:i.price.totalTaxAmount??"",totalAmount:((ie=i.price.fareAmount)==null?void 0:ie.slice(3))??""}}},b=async i=>{var f,$;const s=((f=(await nt(e.ticketNumber,i)).data.value)==null?void 0:f.data)??{};return g.value=(($=s.price)==null?void 0:$.autoFareType)??!0,JSON.stringify(s)!=="{}"?oe(s):{}},K=async i=>{await b(i).then(async s=>{var f,$;return JSON.stringify(s)!=="{}"&&g.value?(($=(f=await zt(s,i))==null?void 0:f.data)==null?void 0:$.value)??{}:{}}).then(s=>{var f,$;if(s){let A=[];const S=(f=s.internationalPriceComputeResponse)==null?void 0:f.fareItems;Object.keys(S).forEach(F=>{if(S[F].length){const X=S[F].map(Z=>({...Z,passengerType:F}));A=A.concat(X)}}),R.value.fareData=($=A.map(F=>({...F,componentCode:"tktRule"}))??[])==null?void 0:$[0],R.value.fareData.ticketRegulation=s.rulesResponse,R.value.rulesResponse=s.rulesResponse}}).catch(s=>{throw s})},G=i=>{switch(i){case"D":return ue("091M0102");case"H":return ue("091M0103");case"F":return ue("091M0104");case"X":return ue("091M0105");case"R":return ue("091M0106");case"N":return ue("091M0107");default:return""}},_=async i=>{var s;try{if(c.value=i,N.value=!0,c.value==="R"){m.value=((s=(await nt(e.ticketNumber,G(i))).data.value)==null?void 0:s.data)??{},await j(),await Ft();const f=document.querySelector(`.${e.parentClassName}`),$=document.querySelector(`.${e.parentClassName} .bkc-el-popper__arrow`);if(f&&$&&["D","R"].includes(c.value)){const A=f.offsetTop,S=f.offsetHeight+A-window.innerHeight;if(A>S&&S>0){let F=A-S;F=F-2>0?F-2:2,f.style.top=`${F}px`,$.style.top=`${$.offsetTop+(A-F)}px`}}}else c.value==="N"?await K(G(i)):await I(G(i))}catch{R.value={},m.value={},x.value="",p.value=[]}finally{N.value=!1}},C=i=>{const s=i.match(me);return $e((s==null?void 0:s[1])??"")??""},Q=i=>{const s=i.split("/").length;o("clickTicketNo",v(i),s)};return he(async()=>{_("D"),await T()}),{ticketModelList:d,ticketModel:c,detrResultText:x,rtktDetailedInfo:m,detrResultList:p,loading:N,chooseTktModel:_,getExchTicket:C,clickTicketNo:Q,tktRulesData:R,fsnIssueDate:y,fsnTicketNos:D,isAutoFare:g}},Ho=Bo,Po={class:"w-[100%] h-[24px] justify-between items-center inline-flex"},Qo={class:"justify-start items-start flex border-collapse"},Mo=["data-gid","onClick"],Lo={key:1,class:"w-[100%] min-h-[420px] mt-2.5 p-2.5 bg-gray-7 rounded flex-col justify-start items-start gap-2.5 inline-flex overflow-auto pr-0.5"},Yo={class:"text-[14px] font-normal leading-relaxed"},Uo={key:1,class:"w-[100%] min-h-[420px] mt-2.5 p-2.5 bg-gray-7 rounded flex-col justify-start items-start gap-2.5 inline-flex overflow-auto pr-0.5"},Oo={class:"text-[14px] font-normal leading-relaxed"},Vo={key:2,class:"w-[100%] min-h-[420px] mt-2.5 p-2.5 bg-gray-7 rounded flex-col justify-start items-start gap-2.5 inline-flex overflow-auto pr-0.5 max-h-[calc(100vh_-_150px)]"},jo={key:0,class:"text-[14px] self-stretch grow shrink basis-0 text-gray-1 font-normal leading-relaxed"},Wo={key:0},Ko=["onClick"],Go={class:"inline-block"},zo={key:1},Jo={key:1,class:"text-xs font-normal leading-relaxed text-[14px]"},Xo={key:3,class:"w-[100%] min-h-[420px] mt-2.5 p-2.5 bg-gray-7 rounded flex-col justify-start items-start gap-2.5 inline-flex overflow-auto pr-0.5"},Zo={key:0,class:"self-stretch grow shrink basis-0 text-gray-1 text-[14px] font-normal leading-relaxed"},en={key:1,class:"text-[14px] font-normal leading-relaxed"},Fn=ce({__name:"TicketPopItem",props:{ticketNumber:{},secondFactor:{},isShowWindow:{type:Boolean}},emits:["clickTicketNo","openRtktDetailWindow","update:secondFactor"],setup(e,{emit:o}){const u=e,a=o,{ticketModelList:y,ticketModel:g,detrResultText:d,rtktDetailedInfo:c,detrResultList:x,loading:p,chooseTktModel:m,getExchTicket:k,clickTicketNo:R,tktRulesData:D,fsnIssueDate:N,fsnTicketNos:v,isAutoFare:I}=Ho(u,a);return(T,H)=>{const j=De,z=pt;return dt((n(),l("div",null,[t("div",Po,[t("div",Qo,[(n(!0),l(P,null,V(h(y),(q,W)=>(n(),l("div",{key:W},[q.isShow?(n(),l("div",{key:0,"data-gid":"091M010"+(W+2),class:de(["self-stretch px-3 py-0.5 border justify-center items-center gap-2.5 flex cursor-pointer first:rounded-l-sm last:rounded-r-sm",h(g)===q.code?"bg-brand-7 border-brand-2":"bg-gray-0 border-gray-6"]),onClick:J=>h(m)(q.code)},[t("div",{class:de(["text-center text-xs font-normal",h(g)===q.code?"leading-tight text-brand-2":"fleading-tight text-gray-2"])},r(q.showText),3)],10,Mo)):O("",!0)]))),128))]),!T.isShowWindow&&h(g)==="R"&&h(c).ticket?(n(),te(qo,{key:0,"rtkt-detailed-info":h(c),onOpenRtktDetailWindow:H[0]||(H[0]=q=>T.$emit("openRtktDetailWindow"))},null,8,["rtkt-detailed-info"])):O("",!0)]),t("div",null,[t("div",null,[h(g)==="R"?(n(),l("div",{key:0,class:de([T.isShowWindow?"":"max-h-[600px]","overflow-y-auto"])},[h(c).ticket?(n(),te(Fo,{key:0,class:"mt-2.5","rtkt-detailed-info":h(c)},null,8,["rtkt-detailed-info"])):(n(),l("div",Lo,[t("div",Yo,r(T.$t("app.queryRtkt.noData")),1)]))],2)):h(g)==="N"?(n(),l("div",{key:1,class:de([T.isShowWindow?"":"max-h-[600px]","overflow-y-auto"])},[JSON.stringify(h(D))!=="{}"&&h(I)?(n(),te(j,{key:0,ref:"scrollbar","min-height":"400"},{default:E(()=>[U(ga,{international:"","rule-info-data":h(D).rulesResponse,"detail-info":h(D).fareData,fsn:"","need-close-icon":"","issue-date":h(N),"fsn-ticket-nos":h(v),"is-show-window":T.isShowWindow},null,8,["rule-info-data","detail-info","issue-date","fsn-ticket-nos","is-show-window"])]),_:1},512)):(n(),l("div",Uo,[t("div",Oo,r(T.$t("app.queryRtkt.noFareTip")),1)]))],2)):h(g)==="D"?(n(),l("div",Vo,[h(x).length>0?(n(),l("div",jo,[(n(!0),l(P,null,V(h(x),(q,W)=>(n(),l(P,{key:W},[h(me).test(q)?(n(),l("span",Wo,[be("EXCH:  "),t("span",{class:"text-brand-2 font-bold cursor-pointer mr-5 underline",onClick:J=>h(R)(h(k)(q))},r(h(k)(q)),9,Ko),t("pre",Go,r(q.replace(h(me),"")),1)])):(n(),l("pre",zo,r(q),1))],64))),128))])):(n(),l("div",Jo,r(T.$t("app.queryRtkt.noData")),1))])):h(g)!=="D"&&h(g)!=="R"&&h(g)!=="N"?(n(),l("div",Xo,[h(d)?(n(),l("div",Zo,[t("pre",null,r(h(d)),1)])):(n(),l("div",en,r(T.$t("app.queryRtkt.noData")),1))])):O("",!0)])])])),[[z,h(p)]])}}});export{jt as F,yt as I,Wt as P,Fo as R,ga as T,qo as _,wn as a,An as b,gn as c,vn as d,_n as e,Io as f,Nn as g,is as h,Rn as i,xn as j,hn as k,Cn as l,mn as m,Dn as n,bn as o,yn as p,Sn as q,$n as r,kn as s,In as t,Gt as u,ss as v,Tn as w,Fn as x};
