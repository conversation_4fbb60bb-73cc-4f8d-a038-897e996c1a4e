import{ao as r,b2 as n,a_ as o,ap as t}from"./index-a2fbd71b.js";const a=(s,e)=>r(`${n}/query/pnr/detail`,{headers:{gid:e}}).post(s).json(),i=s=>r(`${n}/query/pnr/foreignRT`).post(s).json(),c=s=>r(`${n}/crs/pnr/query/flight`).post(s).json(),u=s=>r(`${n}/crs/pnr/query/time`).post(s).json(),d=s=>r(`${n}/crs/pnr/query/name`).post(s).json(),A=()=>r(`${n}/passenger/config/domesticairline`).get().json(),$=(s,e)=>r(`${n}/passenger/splitPnrByPassenger`,{headers:{gid:e}}).post(s).json(),l=(s,e)=>r(`${n}/crs/pnr/iet?airline=${s.airline}&&pnrNo=${s.pnrNo}`,{headers:{gid:e}}).get().json(),h=s=>r(`${n}/crs/other/otherDelete`).post(s).json(),j=(s,e)=>r(`${n}/passenger/removname`,{headers:{gid:e}}).post(s).json(),y=(s,e)=>r(`${n}/query/pnr/rtl`,{headers:{Globalid:e}}).post(s).json(),g=(s,e)=>r(`${n}/crs/pnr/xePnr`,{headers:{gid:e}}).post(s).json(),P=(s,e)=>r(`${n}/crs/pnr/strongRefresh`,{headers:{gid:e}}).post(s).json(),q=s=>r(`${n}/crs/rrt/convert`,{ignoreError:!0,originalValue:!0}).post(s).json(),f=s=>r(`${n}/crs/rrt/displayPNR`).post(s).json(),m=(s,e)=>r(`${n}/crs/query/pnr/history?pnrNo=${s.pnrNo}`,{headers:{Globalid:e}}).get().json(),B=s=>r(`${n}/pnr/deleteExpireSegment`).post(s).json(),R=(s,e)=>r(`${o}/book/pnr`,{headers:{gid:e}}).post(s).json(),S=s=>r(`${o}/book/groupPnr`).post(s).json(),v=(s,e)=>r(`${n}/crs/issue/retry`,{headers:{gid:e}}).post(s).json(),I=()=>r(`${n}/crs/pnr/queryDftBalance`).post().json(),_=(s,e)=>r(`${n}/crs/pnr/issue`,{headers:{gid:e}}).post(s).json(),b=(s,e)=>r(`${n}/passenger/seat/querySeatMap`,{headers:{gid:e}}).post(s).json(),E=s=>r(`${t}/crs/rval/rt`).post(s).json(),x=(s,e)=>r(`${n}/pnr/update`,{headers:{gid:e}}).post(s).json(),D=s=>r(`${n}/crs/issue/structured/preview`).post(s).json(),N=(s,e)=>r(`${n}/crs/pnr/auth/pull`,{headers:{gid:e}}).post(s).json(),T=(s,e)=>r(`${n}/crs/pnr/auth/push`,{headers:{gid:e}}).post(s).json(),k=(s,e)=>r(`${n}/crs/pnr/insuranceIPay`,{headers:{gid:e}}).post(s).json(),C=(s,e)=>r(`${n}/pnr/seat/book`,{headers:{gid:e}}).post(s).json(),L=(s,e)=>r(`${n}/sms/send`,{headers:{gid:e}}).post(s).json(),w=(s,e)=>r(`${n}/departureInfo/getDepartureInfo`,{headers:{gid:e}}).post({crsPnr:s}).json();export{D as A,E as B,L as C,q as D,f as E,_ as I,a,i as b,d as c,u as d,c as e,A as f,w as g,P as h,j as i,v as j,R as k,S as l,C as m,b as n,h as o,l as p,I as q,T as r,$ as s,N as t,x as u,k as v,m as w,g as x,y,B as z};
