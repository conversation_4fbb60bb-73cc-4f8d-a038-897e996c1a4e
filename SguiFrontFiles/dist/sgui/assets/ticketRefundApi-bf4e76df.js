import{ao as a,ap as s}from"./index-a2fbd71b.js";const r=(e,n)=>a(`${s}/apiRefundTicket/findRefundTicket`,{headers:{gid:n}},{originalValue:!0}).post(e).json(),i=e=>a(`${s}/apiRefundTicket/findRefundFee`,{originalValue:!0,ignoreError:!0}).post(e).json(),o=e=>a(`${s}/apiRefundTicket/autoRefund`,{originalValue:!0}).post(e).json(),u=e=>a(`${s}/apiRefundTicket/manualRefundTicket`,{originalValue:!0}).post(e).json(),p=(e,n)=>a(`${s}/apiRefundTicket/manualRefundTicket`,{headers:{gid:n}},{originalValue:!0}).post(e).json(),d=(e,n)=>a(`${s}/apiRefundTicket/batchManualRefundTicket`,{headers:{gid:n}},{originalValue:!0}).post(e).json(),c=e=>a(`${s}/pnrManager/deletePnr`).post({pnrNo:e}).json(),f=(e,n)=>a(`${s}/apiRefundTicket/previewRefundTicket`,{headers:{gid:n}}).post(e).json(),l=(e,n)=>a(`${s}/crs/involuntary/queryPnrMessage`,{headers:{gid:n}},{ignoreError:!0}).post(e).json(),R=(e,n)=>a(`${s}/pnrManager/deletePnrAndDeleteInfantInfo`,{headers:{gid:n}}).post(e).json(),k=(e,n)=>a(`${s}/apiRefundTicket/queryRtktDetail`,{headers:{gid:n}},{ignoreError:!0}).post(e).json(),A=(e,n)=>a(`${s}/apiRefundTicket/batchFindRefundFee`,{headers:{gid:n}}).post(e).json();export{l as a,A as b,d as c,o as d,f as e,i as f,p as g,u as m,r as o,R as p,k as q,c as x};
