import{bA as I,b5 as v,r as d,w as E,iH as m,iI as A,R as g,gM as h,iJ as D,c3 as u,bB as Y,iK as U,bH as y,at as T,aG as p,iL as C,dg as _}from"./index-a2fbd71b.js";var l=(t=>(t.ONE_DAYS="ONE_DAY",t.THREE_DAYS="THREE_DAYS",t.SEVEN_DAYS="SEVEN_DAYS",t.FOURTEEN_DAYS="FOURTEEN_DAYS",t.THIRTY_DAYS="THIRTY_DAYS",t.SIXTY_DAYS="SIXTY_DAYS",t))(l||{});const x=[{code:l.ONE_DAYS,min:0,max:1},{code:l.THREE_DAYS,min:1,max:3},{code:l.SEVEN_DAYS,min:3,max:7},{code:l.FOURTEEN_DAYS,min:7,max:14},{code:l.THIRTY_DAYS,min:14,max:30},{code:l.SIXTY_DAYS,min:30,max:60}],w=(t,...e)=>{if(e.length===0)return"";let a=t;for(let n=0;n<e.length;n+=1){const o=new RegExp(`\\{${n}\\}`,"gm");a=a.replace(o,e[n])}return a},L=(t,e)=>{let a=60;const n=d(!1),o=d(w(t,a));let s=-1;return{checkCodeInput:o,startCount:()=>{a=60,n.value=!0,s!==-1&&window.clearInterval(s),s=window.setInterval(()=>{a-=1,o.value=w(t,a),a<0&&(window.clearInterval(s),o.value=e,a=60,n.value=!1)},1e3)},isDisabled:n}},M=()=>{const t=d(!1),e=d(0),a=E(()=>{if(e.value===0)return"0s";const r=Math.floor(e.value/60),c=e.value%60,i=r===0?"":`${r}m`,f=c===0?"":`${c}s`;return`${i}${f}`});let n=null;const o=()=>{n!==null&&(clearInterval(n),n=null)};return{startCountdown:r=>{e.value=r,t.value=!1,n=setInterval(()=>{e.value>0?e.value--:(t.value=!0,e.value=0,o())},1e3)},timeOut:t,countdownText:a,stopCountDownInterval:o}},S=async(t,e,a,n=!1)=>await T.alert(p("div",{class:"text-lg text-gray-1"},t),{icon:p("em",{class:"iconfont icon-info-circle-line text-brand-2 !text-[32px]"}),customClass:"alert-message-common crs-btn-ui button-reverse crs-btn-message-ui",confirmButtonText:e,cancelButtonText:a,showCancelButton:n,showClose:!1}),b=async()=>{var s;const t=await h.getters.user;let e;try{e=await((s=window.electronAPI)==null?void 0:s.getVersion())??"1.0.1"}catch{e="1.0.1"}const a={app:"SGUI",module:"BKC",version:e,timestamp:"",versionAttr:"",userInfo:{office:t.defaultOffice,airline:"",airport:""}},{data:n}=await D(a);if(!n.value)return!1;const o=n.value;if(o.upgradeStrategy===2||o.upgradeStrategy===3)try{const r=g().add(o.daysRemaining,"day");return await S(u.global.t(`app.login.upgradeStrategyRemind${o.upgradeStrategy}`,{version:o.targetVersion,year:r.get("year"),month:r.get("month")+1,date:r.get("date")}),u.global.t("app.login.forUpdates"),u.global.t("app.login.notForUpdates"),!0),window.open(o.downloadUrl),!0}catch{return!0}else if(o.upgradeStrategy===4){Y(),await S(u.global.t(`app.login.upgradeStrategyRemind${o.upgradeStrategy}`,{version:o.targetVersion}),u.global.t("app.login.forUpdates"),u.global.t("app.login.notForUpdates"),!1),window.open(o.downloadUrl);try{return window.electronAPI.goDownloadBroswer({closedWindow:!0}),!1}catch{return!1}}return!0},R=async(t,e)=>{const a=navigator.userAgent.toLowerCase();if(m(t),a.includes("electron/")&&!await b())return Promise.reject("ForUpdate error");e.push("/")},N=async()=>{var n,o,s,r,c;const e=(await C()).data,a=(o=(n=((e==null?void 0:e.childNodeList)??[]).find(i=>i.currentNode.code==="SYSTEM"))==null?void 0:n.currentNode)==null?void 0:o.id;if(a){const i={pageNumber:1,pageSize:10,content:{typeId:a.toString(),name:"",code:"EXSYSTEM.SAT.EPID.UNUSE",sort:""}};return(((c=(r=(s=(await _(i)).data)==null?void 0:s.content)==null?void 0:r[0])==null?void 0:c.content)??"").toUpperCase()}return"ALL"},P=async(t,e)=>{var c,i;if(!t||(m(e),!(await U()).data))return!1;const n=await y();if(!((c=n==null?void 0:n.data)!=null&&c.crsSystem))return!1;const o=await N(),s=(((i=n==null?void 0:n.data)==null?void 0:i.defaultOffice)??"").toUpperCase(),r=o==="ALL"||o.includes(s);return r||A("afterLoginBeforeCheckChannel"),!r},O=async()=>{const t=navigator.userAgent.toLowerCase();let e="";return t.includes("electron/")&&(e=await window.electronAPI.getAuthInfo()),e},B=async t=>{if(I().includes("icrspsssell")){const a=t??await O();return a==="null"?"":v.decode(a??"").toUpperCase()??""}return""},H=async(t,e)=>{A(""),await R(t,e)},$=t=>{if(!t)return 0;const e=g(g(t)).diff(new Date,"days"),a=x.find(n=>!(e<n.min)&&e<n.max);return a?a.max:0};export{M as a,B as b,H as c,$ as e,P as i,R as l,L as u};
