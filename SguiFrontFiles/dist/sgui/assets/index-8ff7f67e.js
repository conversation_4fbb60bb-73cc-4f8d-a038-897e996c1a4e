import{L as I,M as X,e5 as le,q as F,dJ as K,O as Z,m as Q,v as U,r as T,ac as O,aF as ee,el as oe,x as re,B as ie,D as ce,A as $,C as ge,_ as ue,iw as Ne,ix as Te,w as M,o as de,dN as we,G as d,H as V,es as Ce,et as Pe,a1 as Se,V as D,a2 as Ee,W as $e,F as G,h6 as xe,em as be,ec as ae,dM as Be,e7 as ke,Y as Oe,iy as se,b0 as Re,ar as ze,a5 as Ae,a6 as Fe,J as Me,K as Le,Z as Ve}from"./index-a2fbd71b.js";import{c as k}from"./strings-cc725bd5.js";import{u as De}from"./index-a42e5d8f.js";const q=Symbol("tabsRootContextKey"),Ie=I({tabs:{type:X(Array),default:()=>le([])}}),ve="ElTabBar",Ke=F({name:ve}),Ue=F({...Ke,props:Ie,setup(e,{expose:o}){const p=e,R=K(),c=Z(q);c||Q(ve,"<el-tabs><el-tab-bar /></el-tabs>");const s=U("tabs"),b=T(),x=T(),u=()=>{let v=0,f=0;const r=["top","bottom"].includes(c.props.tabPosition)?"width":"height",n=r==="width"?"x":"y",B=n==="x"?"left":"top";return p.tabs.every(S=>{var t,C;const _=(C=(t=R.parent)==null?void 0:t.refs)==null?void 0:C[`tab-${S.uid}`];if(!_)return!1;if(!S.active)return!0;v=_[`offset${k(B)}`],f=_[`client${k(r)}`];const P=window.getComputedStyle(_);return r==="width"&&(p.tabs.length>1&&(f-=Number.parseFloat(P.paddingLeft)+Number.parseFloat(P.paddingRight)),v+=Number.parseFloat(P.paddingLeft)),!1}),{[r]:`${f}px`,transform:`translate${k(n)}(${v}px)`}},h=()=>x.value=u();return O(()=>p.tabs,async()=>{await ee(),h()},{immediate:!0}),oe(b,()=>h()),o({ref:b,update:h}),(v,f)=>(re(),ie("div",{ref_key:"barRef",ref:b,class:ce([$(s).e("active-bar"),$(s).is($(c).props.tabPosition)]),style:ge(x.value)},null,6))}});var qe=ue(Ue,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/tabs/src/tab-bar.vue"]]);const He=I({panes:{type:X(Array),default:()=>le([])},currentName:{type:[String,Number],default:""},editable:Boolean,type:{type:String,values:["card","border-card",""],default:""},stretch:Boolean}),We={tabClick:(e,o,p)=>p instanceof Event,tabRemove:(e,o)=>o instanceof Event},ne="ElTabNav",Je=F({name:ne,props:He,emits:We,setup(e,{expose:o,emit:p}){const R=K(),c=Z(q);c||Q(ne,"<el-tabs><tab-nav /></el-tabs>");const s=U("tabs"),b=Ne(),x=Te(),u=T(),h=T(),v=T(),f=T(),r=T(!1),n=T(0),B=T(!1),S=T(!0),t=M(()=>["top","bottom"].includes(c.props.tabPosition)?"width":"height"),C=M(()=>({transform:`translate${t.value==="width"?"X":"Y"}(-${n.value}px)`})),_=()=>{if(!u.value)return;const l=u.value[`offset${k(t.value)}`],i=n.value;if(!i)return;const a=i>l?i-l:0;n.value=a},P=()=>{if(!u.value||!h.value)return;const l=h.value[`offset${k(t.value)}`],i=u.value[`offset${k(t.value)}`],a=n.value;if(l-a<=i)return;const g=l-a>i*2?a+i:l-i;n.value=g},z=async()=>{const l=h.value;if(!r.value||!v.value||!u.value||!l)return;await ee();const i=v.value.querySelector(".is-active");if(!i)return;const a=u.value,g=["top","bottom"].includes(c.props.tabPosition),N=i.getBoundingClientRect(),y=a.getBoundingClientRect(),E=g?l.offsetWidth-y.width:l.offsetHeight-y.height,w=n.value;let m=w;g?(N.left<y.left&&(m=w-(y.left-N.left)),N.right>y.right&&(m=w+N.right-y.right)):(N.top<y.top&&(m=w-(y.top-N.top)),N.bottom>y.bottom&&(m=w+(N.bottom-y.bottom))),m=Math.max(m,0),n.value=Math.min(m,E)},L=()=>{var l;if(!h.value||!u.value)return;e.stretch&&((l=f.value)==null||l.update());const i=h.value[`offset${k(t.value)}`],a=u.value[`offset${k(t.value)}`],g=n.value;a<i?(r.value=r.value||{},r.value.prev=g,r.value.next=g+a<i,i-g<a&&(n.value=i-a)):(r.value=!1,g>0&&(n.value=0))},pe=l=>{const i=l.code,{up:a,down:g,left:N,right:y}=D;if(![a,g,N,y].includes(i))return;const E=Array.from(l.currentTarget.querySelectorAll("[role=tab]:not(.is-disabled)")),w=E.indexOf(l.target);let m;i===N||i===a?w===0?m=E.length-1:m=w-1:w<E.length-1?m=w+1:m=0,E[m].focus({preventScroll:!0}),E[m].click(),te()},te=()=>{S.value&&(B.value=!0)},H=()=>B.value=!1;return O(b,l=>{l==="hidden"?S.value=!1:l==="visible"&&setTimeout(()=>S.value=!0,50)}),O(x,l=>{l?setTimeout(()=>S.value=!0,50):S.value=!1}),oe(v,L),de(()=>setTimeout(()=>z(),0)),we(()=>L()),o({scrollToActiveTab:z,removeFocus:H}),O(()=>e.panes,()=>R.update(),{flush:"post",deep:!0}),()=>{const l=r.value?[d("span",{class:[s.e("nav-prev"),s.is("disabled",!r.value.prev)],onClick:_},[d(V,null,{default:()=>[d(Ce,null,null)]})]),d("span",{class:[s.e("nav-next"),s.is("disabled",!r.value.next)],onClick:P},[d(V,null,{default:()=>[d(Pe,null,null)]})])]:null,i=e.panes.map((a,g)=>{var N,y,E,w;const m=a.uid,W=a.props.disabled,J=(y=(N=a.props.name)!=null?N:a.index)!=null?y:`${g}`,Y=!W&&(a.isClosable||e.editable);a.index=`${g}`;const he=Y?d(V,{class:"is-icon-close",onClick:A=>p("tabRemove",a,A)},{default:()=>[d(Se,null,null)]}):null,ye=((w=(E=a.slots).label)==null?void 0:w.call(E))||a.props.label,_e=!W&&a.active?0:-1;return d("div",{ref:`tab-${m}`,class:[s.e("item"),s.is(c.props.tabPosition),s.is("active",a.active),s.is("disabled",W),s.is("closable",Y),s.is("focus",B.value)],id:`tab-${J}`,key:`tab-${m}`,"aria-controls":`pane-${J}`,role:"tab","aria-selected":a.active,tabindex:_e,onFocus:()=>te(),onBlur:()=>H(),onClick:A=>{H(),p("tabClick",a,J,A)},onKeydown:A=>{Y&&(A.code===D.delete||A.code===D.backspace)&&p("tabRemove",a,A)}},[ye,he])});return d("div",{ref:v,class:[s.e("nav-wrap"),s.is("scrollable",!!r.value),s.is(c.props.tabPosition)]},[l,d("div",{class:s.e("nav-scroll"),ref:u},[d("div",{class:[s.e("nav"),s.is(c.props.tabPosition),s.is("stretch",e.stretch&&["top","bottom"].includes(c.props.tabPosition))],ref:h,style:C.value,role:"tablist",onKeydown:pe},[e.type?null:d(qe,{ref:f,tabs:[...e.panes]},null),i])])])}}}),Ye=I({type:{type:String,values:["card","border-card",""],default:""},activeName:{type:[String,Number]},closable:Boolean,addable:Boolean,modelValue:{type:[String,Number]},editable:Boolean,tabPosition:{type:String,values:["top","right","bottom","left"],default:"top"},beforeLeave:{type:X(Function),default:()=>!0},stretch:Boolean}),j=e=>Be(e)||ke(e),je={[be]:e=>j(e),tabClick:(e,o)=>o instanceof Event,tabChange:e=>j(e),edit:(e,o)=>["remove","add"].includes(o),tabRemove:e=>j(e),tabAdd:()=>!0},Ge=F({name:"ElTabs",props:Ye,emits:je,setup(e,{emit:o,slots:p,expose:R}){var c,s;const b=U("tabs"),{children:x,addChild:u,removeChild:h}=De(K(),"ElTabPane"),v=T(),f=T((s=(c=e.modelValue)!=null?c:e.activeName)!=null?s:"0"),r=async(t,C=!1)=>{var _,P,z;if(!(f.value===t||ae(t)))try{await((_=e.beforeLeave)==null?void 0:_.call(e,t,f.value))!==!1&&(f.value=t,C&&(o(be,t),o("tabChange",t)),(z=(P=v.value)==null?void 0:P.removeFocus)==null||z.call(P))}catch{}},n=(t,C,_)=>{t.props.disabled||(r(C,!0),o("tabClick",t,_))},B=(t,C)=>{t.props.disabled||ae(t.props.name)||(C.stopPropagation(),o("edit",t.props.name,"remove"),o("tabRemove",t.props.name))},S=()=>{o("edit",void 0,"add"),o("tabAdd")};return Ee({from:'"activeName"',replacement:'"model-value" or "v-model"',scope:"ElTabs",version:"2.3.0",ref:"https://element-plus.org/en-US/component/tabs.html#attributes",type:"Attribute"},M(()=>!!e.activeName)),O(()=>e.activeName,t=>r(t)),O(()=>e.modelValue,t=>r(t)),O(f,async()=>{var t;await ee(),(t=v.value)==null||t.scrollToActiveTab()}),$e(q,{props:e,currentName:f,registerPane:u,unregisterPane:h}),R({currentName:f}),()=>{const t=p.addIcon,C=e.editable||e.addable?d("span",{class:b.e("new-tab"),tabindex:"0",onClick:S,onKeydown:z=>{z.code===D.enter&&S()}},[t?G(p,"addIcon"):d(V,{class:b.is("icon-plus")},{default:()=>[d(xe,null,null)]})]):null,_=d("div",{class:[b.e("header"),b.is(e.tabPosition)]},[C,d(Je,{ref:v,currentName:f.value,editable:e.editable,type:e.type,panes:x.value,stretch:e.stretch,onTabClick:n,onTabRemove:B},null)]),P=d("div",{class:b.e("content")},[G(p,"default")]);return d("div",{class:[b.b(),b.m(e.tabPosition),{[b.m("card")]:e.type==="card",[b.m("border-card")]:e.type==="border-card"}]},[...e.tabPosition!=="bottom"?[_,P]:[P,_]])}}}),Xe=I({label:{type:String,default:""},name:{type:[String,Number]},closable:Boolean,disabled:Boolean,lazy:Boolean}),Ze=["id","aria-hidden","aria-labelledby"],fe="ElTabPane",Qe=F({name:fe}),et=F({...Qe,props:Xe,setup(e){const o=e,p=K(),R=Oe(),c=Z(q);c||Q(fe,"usage: <el-tabs><el-tab-pane /></el-tabs/>");const s=U("tab-pane"),b=T(),x=M(()=>o.closable||c.props.closable),u=se(()=>{var n;return c.currentName.value===((n=o.name)!=null?n:b.value)}),h=T(u.value),v=M(()=>{var n;return(n=o.name)!=null?n:b.value}),f=se(()=>!o.lazy||h.value||u.value);O(u,n=>{n&&(h.value=!0)});const r=Re({uid:p.uid,slots:R,props:o,paneName:v,active:u,index:b,isClosable:x});return de(()=>{c.registerPane(r)}),ze(()=>{c.unregisterPane(r.uid)}),(n,B)=>$(f)?Ae((re(),ie("div",{key:0,id:`pane-${$(v)}`,class:ce($(s).b()),role:"tabpanel","aria-hidden":!$(u),"aria-labelledby":`tab-${$(v)}`},[G(n.$slots,"default")],10,Ze)),[[Fe,$(u)]]):Me("v-if",!0)}});var me=ue(et,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/tabs/src/tab-pane.vue"]]);const nt=Le(Ge,{TabPane:me}),lt=Ve(me);export{lt as E,nt as a};
