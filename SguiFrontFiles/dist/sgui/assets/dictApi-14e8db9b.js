import{bS as s,bT as e,ao as c}from"./index-a2fbd71b.js";const o=()=>s.get(`${e}/dict/getDictTypeTree`),p=t=>s.post(`${e}/dict/queryDictTypeList`,t),y=t=>s.post(`${e}/dict/queryDictEntryList`,t),a=t=>s.post(`${e}/dict/deleteDictType`,{dictTypeIds:t}),n=t=>s.post(`${e}/dict/deleteDictEntry`,{dictEntryIds:t}),D=t=>s.post(`${e}/dict/saveDictType`,t),d=t=>s.post(`${e}/dict/updateDictType`,t),r=t=>s.post(`${e}/dict/saveDictEntry`,t),T=t=>s.post(`${e}/dict/updateDictEntry`,t),E=t=>c(`${e}/dict/queryDictEntryList`).post(t).json();export{r as a,T as b,y as c,a as d,n as e,o as f,p as g,E as q,D as s,d as u};
