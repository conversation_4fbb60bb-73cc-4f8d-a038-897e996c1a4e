import{L as fs,M as pa,eE as ms,em as bn,dM as En,eF as Vn,en as $a,dF as gs,q as je,eG as ks,eH as ys,eI as hs,v as vs,r as H,w as Me,d1 as _s,eJ as bs,o as st,x as s,y as ae,z as l,P as t,D as Se,A as e,C as fa,G as c,B as m,H as Je,dI as xs,ai as ye,aj as Ne,F as Yt,ak as K,Q as n,ah as gt,X as Ts,b1 as yn,E as xn,dz as $s,_ as Ns,U as Fn,m as Rs,K as Cs,ab as We,a9 as Ct,b0 as tt,bu as Rn,b3 as Ve,aU as zn,J as Z,al as Ke,aw as Cn,bg as hn,eK as ma,ac as wt,bt as Mt,eL as ga,aY as Pe,b5 as Ft,ae as _n,a5 as Qe,am as bt,an as xt,s as Ae,aS as ft,at as Xe,aG as Le,ex as Na,aX as mt,b6 as en,aF as fn,eM as Gn,ez as ws,ad as wn,aE as Ra,eN as Nt,bs as ht,aZ as kt,eO as Mn,a6 as vn,R as et,ar as As,eP as Ss,c3 as Ca,c1 as Hn,eQ as Zt,eR as Ds,bv as Tn,eS as Wn,ey as un,eT as Ps,eU as Os,d6 as Es,eV as Fs,aW as wa,cH as Vs,aB as Ms,aC as js,aR as Aa,eW as Ls,eX as ka,eY as jn,a8 as Bs,c0 as Is,eZ as Us,au as Qs,aH as qs,b9 as zs}from"./index-a2fbd71b.js";import{P as Kt}from"./PrintNoSelect-2c6cab30.js";import{D as Gt,R as Gs,P as Yn,b as dt,c as Sa,d as Da,e as Hs,f as ya,g as $n,h as Kn,i as Xn,j as Pa,k as Ws,l as ha,m as Ys,n as Ks,o as Xs,p as Oa,q as Js,r as Zs,s as eo,t as to,S as no,u as ao,E as so}from"./regular-crs-531dcd3f.js";import{E as Pt,a as Ot}from"./index-2484b7a8.js";import{E as tn,a as nn}from"./index-68054860.js";import{E as ot,a as it}from"./index-96be5bee.js";import{u as oo,E as yt}from"./index-fe3402a7.js";import{E as An}from"./index-61b49c38.js";import{E as nt}from"./index-d88b3135.js";import{C as io,S as cn,P as va,g as Rt,E as Ea,a as _a,r as lo,b as ro}from"./jspdf.es.min-c4fb413d.js";import{a as Fa,b as co,c as uo,d as po,q as Va,e as ba,g as fo,h as dn,i as mo,r as go,j as ko,k as Ma,l as ja,m as Ln,n as Bn,s as yo,o as ho,p as vo,t as _o,u as bo,v as xo,w as To,x as xa,y as $o,z as No,A as Ro,f as Co,B as wo}from"./ticketOperationApi-4e3e854b.js";import{E as Sn}from"./index-35a05a15.js";import{_ as at}from"./_plugin-vue_export-helper-c27b6911.js";import{u as La,_ as Ao,f as So}from"./TicketPopItem.vue_vue_type_script_setup_true_lang-eb783de8.js";import{s as Jn}from"./index-dd804643.js";import{o as In,a as Do,b as Po,p as Zn,q as Ba,c as Oo,d as Eo,m as Fo,e as Ia,x as Vo,f as Mo,g as jo}from"./ticketRefundApi-bf4e76df.js";import{D as Ua,a as ea,p as Dn,h as Qa,f as Un,g as qa,b as Qn}from"./refundUtil-46d54931.js";import{a as qe}from"./add-2f19224f.js";import{T as mn,i as Lo}from"./TicketInfoPopover-9ac1ad15.js";import{D as gn,h as Bo}from"./html2canvas.esm-cf66fc0f.js";import{d as jt,m as Lt,f as pn,T as ta,R as Io,P as Uo}from"./TicketRefundForm-02cce8a1.js";import{s as Dt}from"./subtract-21d41b77.js";import{E as an,a as na}from"./index-e149ca58.js";import{E as za,a as Ga}from"./index-8ff7f67e.js";import{C as Ha,E as Bt,k as Qo}from"./config-6a4e2d9f.js";import{E as aa}from"./index-e246ba26.js";import{E as sa}from"./index-350461d4.js";import{g as qo}from"./index-ff93035e.js";import{u as oa}from"./usePersonalization-e583570d.js";import{_ as ia}from"./theme-light_empty-0081a108.js";import{E as zo}from"./index-3fe919b5.js";import{E as Go}from"./exceljs.min-bcd6e76f.js";import{E as Ho}from"./index-f34f831f.js";import"./index-ac58700d.js";import"./strings-cc725bd5.js";import"./isEqual-c950930c.js";import"./index-4e9a4449.js";import"./castArray-2ae6cabb.js";import"./isUndefined-aa0326a0.js";import"./refs-b36b7130.js";import"./browser-6cfa1fde.js";import"./dropdown-7f4180b8.js";import"./time-04300430.js";import"./_createMathOperation-9624aa42.js";import"./dictApi-14e8db9b.js";import"./throttle-d22b7fb0.js";import"./flatten-b73d2287.js";import"./index-a42e5d8f.js";import"./index-e5c2c3ff.js";import"./index-819b3a8a.js";const Wo=fs({valueKey:{type:String,default:"value"},modelValue:{type:[String,Number],default:""},debounce:{type:Number,default:300},placement:{type:pa(String),values:["top","top-start","top-end","bottom","bottom-start","bottom-end"],default:"bottom-start"},fetchSuggestions:{type:pa([Function,Array]),default:ms},popperClass:{type:String,default:""},triggerOnFocus:{type:Boolean,default:!0},selectWhenUnmatched:{type:Boolean,default:!1},hideLoading:{type:Boolean,default:!1},label:{type:String},teleported:oo.teleported,highlightFirstItem:{type:Boolean,default:!1},fitInputWidth:{type:Boolean,default:!1},clearable:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},name:String}),Yo={[bn]:a=>En(a),[Vn]:a=>En(a),[$a]:a=>En(a),focus:a=>a instanceof FocusEvent,blur:a=>a instanceof FocusEvent,clear:()=>!0,select:a=>gs(a)},Ko=["aria-expanded","aria-owns"],Xo={key:0},Jo=["id","aria-selected","onClick"],Wa="ElAutocomplete",Zo=je({name:Wa,inheritAttrs:!1}),ei=je({...Zo,props:Wo,emits:Yo,setup(a,{expose:o,emit:u}){const h=a,f=ks(),p=ys(),i=hs(),_=vs("autocomplete"),d=H(),x=H(),T=H(),k=H();let R=!1,v=!1;const r=H([]),y=H(-1),C=H(""),$=H(!1),P=H(!1),M=H(!1),X=Me(()=>_.b(String(qo()))),ue=Me(()=>p.style),E=Me(()=>(r.value.length>0||M.value)&&$.value),j=Me(()=>!h.hideLoading&&M.value),oe=Me(()=>d.value?Array.from(d.value.$el.querySelectorAll("input")):[]),D=()=>{E.value&&(C.value=`${d.value.$el.offsetWidth}px`)},b=()=>{y.value=-1},ne=_s(async w=>{if(P.value)return;const g=B=>{M.value=!1,!P.value&&(Fn(B)?(r.value=B,y.value=h.highlightFirstItem?0:-1):Rs(Wa,"autocomplete suggestions must be an array"))};if(M.value=!0,Fn(h.fetchSuggestions))g(h.fetchSuggestions);else{const B=await h.fetchSuggestions(w,g);Fn(B)&&g(B)}},h.debounce),ie=w=>{const g=!!w;if(u(Vn,w),u(bn,w),P.value=!1,$.value||($.value=g),!h.triggerOnFocus&&!w){P.value=!0,r.value=[];return}ne(w)},U=w=>{var g;i.value||(((g=w.target)==null?void 0:g.tagName)!=="INPUT"||oe.value.includes(document.activeElement))&&($.value=!0)},A=w=>{u($a,w)},le=w=>{v?v=!1:($.value=!0,u("focus",w),h.triggerOnFocus&&!R&&ne(String(h.modelValue)))},he=w=>{setTimeout(()=>{var g;if((g=T.value)!=null&&g.isFocusInsideContent()){v=!0;return}$.value&&Q(),u("blur",w)})},ce=()=>{$.value=!1,u(bn,""),u("clear")},L=async()=>{E.value&&y.value>=0&&y.value<r.value.length?J(r.value[y.value]):h.selectWhenUnmatched&&(u("select",{value:h.modelValue}),r.value=[],y.value=-1)},se=w=>{E.value&&(w.preventDefault(),w.stopPropagation(),Q())},Q=()=>{$.value=!1},S=()=>{var w;(w=d.value)==null||w.focus()},G=()=>{var w;(w=d.value)==null||w.blur()},J=async w=>{u(Vn,w[h.valueKey]),u(bn,w[h.valueKey]),u("select",w),r.value=[],y.value=-1},z=w=>{if(!E.value||M.value)return;if(w<0){y.value=-1;return}w>=r.value.length&&(w=r.value.length-1);const g=x.value.querySelector(`.${_.be("suggestion","wrap")}`),F=g.querySelectorAll(`.${_.be("suggestion","list")} li`)[w],de=g.scrollTop,{offsetTop:ve,scrollHeight:O}=F;ve+O>de+g.clientHeight&&(g.scrollTop+=O),ve<de&&(g.scrollTop-=O),y.value=w,d.value.ref.setAttribute("aria-activedescendant",`${X.value}-item-${y.value}`)};return bs(k,()=>{E.value&&Q()}),st(()=>{d.value.ref.setAttribute("role","textbox"),d.value.ref.setAttribute("aria-autocomplete","list"),d.value.ref.setAttribute("aria-controls","id"),d.value.ref.setAttribute("aria-activedescendant",`${X.value}-item-${y.value}`),R=d.value.ref.hasAttribute("readonly")}),o({highlightedIndex:y,activated:$,loading:M,inputRef:d,popperRef:T,suggestions:r,handleSelect:J,handleKeyEnter:L,focus:S,blur:G,close:Q,highlight:z}),(w,g)=>(s(),ae(e(yt),{ref_key:"popperRef",ref:T,visible:e(E),placement:w.placement,"fallback-placements":["bottom-start","top-start"],"popper-class":[e(_).e("popper"),w.popperClass],teleported:w.teleported,"gpu-acceleration":!1,pure:"","manual-mode":"",effect:"light",trigger:"click",transition:`${e(_).namespace.value}-zoom-in-top`,persistent:"",role:"listbox",onBeforeShow:D,onHide:b},{content:l(()=>[t("div",{ref_key:"regionRef",ref:x,class:Se([e(_).b("suggestion"),e(_).is("loading",e(j))]),style:fa({[w.fitInputWidth?"width":"minWidth"]:C.value,outline:"none"}),role:"region"},[c(e(sa),{id:e(X),tag:"ul","wrap-class":e(_).be("suggestion","wrap"),"view-class":e(_).be("suggestion","list"),role:"listbox"},{default:l(()=>[e(j)?(s(),m("li",Xo,[c(e(Je),{class:Se(e(_).is("loading"))},{default:l(()=>[c(e(xs))]),_:1},8,["class"])])):(s(!0),m(ye,{key:1},Ne(r.value,(B,F)=>(s(),m("li",{id:`${e(X)}-item-${F}`,key:F,class:Se({highlighted:y.value===F}),role:"option","aria-selected":y.value===F,onClick:de=>J(B)},[Yt(w.$slots,"default",{item:B},()=>[K(n(B[w.valueKey]),1)])],10,Jo))),128))]),_:3},8,["id","wrap-class","view-class"])],6)]),default:l(()=>[t("div",{ref_key:"listboxRef",ref:k,class:Se([e(_).b(),w.$attrs.class]),style:fa(e(ue)),role:"combobox","aria-haspopup":"listbox","aria-expanded":e(E),"aria-owns":e(X)},[c(e(gt),Ts({ref_key:"inputRef",ref:d},e(f),{clearable:w.clearable,disabled:e(i),name:w.name,"model-value":w.modelValue,onInput:ie,onChange:A,onFocus:le,onBlur:he,onClear:ce,onKeydown:[g[0]||(g[0]=yn(xn(B=>z(y.value-1),["prevent"]),["up"])),g[1]||(g[1]=yn(xn(B=>z(y.value+1),["prevent"]),["down"])),yn(L,["enter"]),yn(Q,["tab"]),yn(se,["esc"])],onMousedown:U}),$s({_:2},[w.$slots.prepend?{name:"prepend",fn:l(()=>[Yt(w.$slots,"prepend")])}:void 0,w.$slots.append?{name:"append",fn:l(()=>[Yt(w.$slots,"append")])}:void 0,w.$slots.prefix?{name:"prefix",fn:l(()=>[Yt(w.$slots,"prefix")])}:void 0,w.$slots.suffix?{name:"suffix",fn:l(()=>[Yt(w.$slots,"suffix")])}:void 0]),1040,["clearable","disabled","name","model-value","onKeydown"])],14,Ko)]),_:3},8,["visible","placement","popper-class","teleported","transition"]))}});var ti=Ns(ei,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/autocomplete/src/autocomplete.vue"]]);const Ya=Cs(ti),ni=a=>{const{t:o}=We(),u=Ct(),h=Me(()=>{var C;return(C=u.state.user)==null?void 0:C.entityType}),f=H(),p=H("1"),i=tt({printerNo:"",ticketNo:"",refundNo:"",domestic:!0,ticketOrganization:""}),_={ticketOrganization:[{required:!0,message:o("app.ticketStatus.deviceNumNull"),trigger:"blur"}],printerNo:[{required:!0,message:o("app.ticketStatus.deviceNumNull"),trigger:"blur"},{pattern:Gt,trigger:"blur",message:o("app.ticketStatus.deviceError")}],ticketNo:[{required:!0,message:o("app.agentTicketQuery.validate.tktNoNull"),trigger:"blur"},{pattern:Rn,message:o("app.agentTicketQuery.validate.tktNoError"),trigger:"blur"}],domestic:[{required:!0,message:o("app.agentTicketQuery.validate.required"),trigger:"blur"}],refundNo:[{required:!0,message:o("app.agentTicketQuery.validate.required"),trigger:"blur"},{pattern:Gs,message:o("app.agentTicketQuery.validate.refundNoError"),trigger:"blur"}]},d=H([]),x={BSP:{label:o("app.agentTicketQuery.BSPTicket"),value:"BSP"},GPBSP:{label:o("app.agentTicketQuery.GPTicket"),value:"GPBSP"},BOPBSP:{label:o("app.agentTicketQuery.BOPTicket"),value:"BOPBSP"},CDS:{label:o("app.agentTicketQuery.CDSTicket"),value:"CDS"},GPCDS:{label:o("app.agentTicketQuery.GP_CDSTicket"),value:"GPCDS"},ARL:{label:o("app.agentTicketQuery.OWNTicket"),value:"ARL"}},T=Me(()=>!["CDS","GPCDS"].includes(i.ticketOrganization)),k=C=>{i.domestic=C==="D"||!C},R=()=>{f.value.validate(C=>{C&&a("openRefundDialog",i.ticketNo,i.refundNo,i.domestic,i.printerNo,i.ticketOrganization)})},v=()=>{a("update:modelValue",!1)},r=()=>{p.value==="1"&&(i.refundNo=""),p.value==="2"&&(i.ticketNo="")},y=()=>{var C,$,P,M,X,ue,E,j,oe,D,b,N;((C=h.value)!=null&&C.includes("$$$")||($=h.value)!=null&&$.includes("BSP"))&&(d.value.push(x.BSP),d.value.push(x.GPBSP)),!((P=h.value)!=null&&P.includes("BSP"))&&((M=h.value)!=null&&M.includes("GP"))&&d.value.push(x.GPBSP),((X=h.value)!=null&&X.includes("$$$")||(ue=h.value)!=null&&ue.includes("BOP"))&&d.value.push(x.BOPBSP),((E=h.value)!=null&&E.includes("$$$")||(j=h.value)!=null&&j.includes("CDS"))&&(d.value.push(x.CDS),d.value.push(x.GPCDS)),((oe=h.value)!=null&&oe.includes("$$$")||(D=h.value)!=null&&D.includes("本票"))&&d.value.push(x.ARL),i.ticketOrganization=((N=(b=d.value)==null?void 0:b[0])==null?void 0:N.value)??""};return st(()=>{y()}),{refundType:p,formDate:f,printNoFrom:i,PRINTER_NO_RULES:_,ticketOrganizationList:d,isShowPrintNo:T,confirmPrinterNo:R,closeDialog:v,deliverPrintType:k,changeRefundType:r}},ai=ni,si=t("i",{class:"iconfont icon-close"},null,-1),oi=[si],ii={class:"carType-option-panel"},li={key:0,class:"flex"},ri=t("br",null,null,-1),ci=t("i",{class:"iconfont icon-info-circle-line ticket-icon ml-[5px] mt-1"},null,-1),ui={key:1,class:"flex"},di=t("br",null,null,-1),pi=t("i",{class:"iconfont icon-info-circle-line ticket-icon ml-[5px] mt-1"},null,-1),fi=je({__name:"RefundParameterDialog",emits:["update:modelValue","openRefundDialog"],setup(a,{emit:o}){const u=o,{refundType:h,formDate:f,printNoFrom:p,PRINTER_NO_RULES:i,ticketOrganizationList:_,isShowPrintNo:d,confirmPrinterNo:x,closeDialog:T,deliverPrintType:k,changeRefundType:R}=ai(u);return(v,r)=>{const y=Pt,C=Ot,$=Je,P=tn,M=nn,X=ot,ue=gt,E=yt,j=An,oe=it,D=Ke,b=nt;return s(),ae(b,{title:v.$t("app.agentTicketQuery.queryRefundTitle"),width:"680px",class:"print-no-dialog","show-close":!1,"close-on-click-modal":!1,onClose:e(T)},{footer:l(()=>[t("div",null,[c(D,{type:"primary","data-gid":"091T0107",onClick:r[8]||(r[8]=N=>e(x)())},{default:l(()=>[K(n(v.$t("app.ticketStatus.confirmBtn")),1)]),_:1}),c(D,{onClick:e(T)},{default:l(()=>[K(n(v.$t("app.ticketStatus.cancelBtn")),1)]),_:1},8,["onClick"])])]),default:l(()=>[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:r[0]||(r[0]=(...N)=>e(T)&&e(T)(...N))},oi),c(oe,{ref_key:"formDate",ref:f,model:e(p),rules:e(i),"label-position":"left","require-asterisk-position":"right"},{default:l(()=>[c(C,{modelValue:e(h),"onUpdate:modelValue":r[1]||(r[1]=N=>Ve(h)?h.value=N:null),onChange:e(R)},{default:l(()=>[c(y,{label:"1"},{default:l(()=>[K(n(v.$t("app.agentTicketQuery.useTicketNo")),1)]),_:1}),c(y,{label:"2"},{default:l(()=>[K(n(v.$t("app.agentTicketQuery.useRefundNo")),1)]),_:1})]),_:1},8,["modelValue","onChange"]),c(X,{prop:"ticketOrganization",label:v.$t("app.agentTicketQuery.ticketOrganization")},{default:l(()=>[c(M,{modelValue:e(p).ticketOrganization,"onUpdate:modelValue":r[2]||(r[2]=N=>e(p).ticketOrganization=N),class:"ticket-management-organization",disabled:e(p).ticketOrganization==="",placeholder:e(p).ticketOrganization===""?v.$t("app.agentTicketQuery.noData"):""},{default:l(()=>[(s(!0),m(ye,null,Ne(e(_),N=>(s(),ae(P,{key:N.value,label:N.label,value:N.value},{default:l(()=>[t("div",ii,[t("div",{class:Se(e(p).ticketOrganization===N.value?"show-select":"hidden-select")},[c($,null,{default:l(()=>[c(e(zn))]),_:1})],2),t("span",null,n(N.label),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled","placeholder"])]),_:1},8,["label"]),e(h)==="1"?(s(),m("div",li,[c(X,{prop:"ticketNo",label:v.$t("app.agentTicketQuery.ticketNo")},{default:l(()=>[c(ue,{modelValue:e(p).ticketNo,"onUpdate:modelValue":r[3]||(r[3]=N=>e(p).ticketNo=N),modelModifiers:{trim:!0},clearable:""},null,8,["modelValue"])]),_:1},8,["label"]),c(E,{placement:"top",effect:"dark","popper-class":"ticket-conditon-popper"},{content:l(()=>[K(n(v.$t("app.agentTicketQuery.ticketNumberTips")),1),ri,K(" "+n(v.$t("app.agentTicketQuery.forExample"))+"：999-1234567890"+n(v.$t("app.agentTicketQuery.or"))+"9991234567890 ",1)]),default:l(()=>[ci]),_:1})])):(s(),m("div",ui,[c(X,{prop:"refundNo",label:v.$t("app.agentTicketQuery.rtNum")},{default:l(()=>[c(ue,{modelValue:e(p).refundNo,"onUpdate:modelValue":r[4]||(r[4]=N=>e(p).refundNo=N),modelModifiers:{trim:!0},clearable:""},null,8,["modelValue"])]),_:1},8,["label"]),c(E,{placement:"top",effect:"dark","popper-class":"ticket-conditon-popper"},{content:l(()=>[K(n(v.$t("app.agentTicketQuery.refundNoError")),1),di,K(" "+n(v.$t("app.agentTicketQuery.forExample"))+"：999-123456789"+n(v.$t("app.agentTicketQuery.or"))+"999123456789"+n(v.$t("app.agentTicketQuery.or"))+"123456789 ",1)]),default:l(()=>[pi]),_:1})])),e(d)?(s(),ae(X,{key:2,prop:"printerNo",label:v.$t("app.ticketStatus.deviceNum")},{default:l(()=>[c(Kt,{modelValue:e(p).printerNo,"onUpdate:modelValue":[r[5]||(r[5]=N=>e(p).printerNo=N),r[6]||(r[6]=N=>e(f).validateField("printerNo"))],"select-class":"w-[340px]",onDeliverPrintType:e(k)},null,8,["modelValue","onDeliverPrintType"])]),_:1},8,["label"])):Z("",!0),c(X,{class:"inline-flex mr-[40px] mb-[10px] w-[60px]",prop:"domestic",label:v.$t("app.agentTicketQuery.printType")},{default:l(()=>[c(j,{modelValue:e(p).domestic,"onUpdate:modelValue":r[7]||(r[7]=N=>e(p).domestic=N),"inline-prompt":"","active-text":v.$t("app.issue.dom"),"inactive-text":v.$t("app.issue.intr")},null,8,["modelValue","active-text","inactive-text"])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),_:1},8,["title","onClose"])}}});const mi=a=>{const{t:o}=We(),u=H();let h=null;const f=H(!1),p=Cn(),i=tt({optionType:"1",selectType:"NI",tktNo:"",pnrNo:"",ticketHistory:!1,passengerName:"",certificateNo:"",secondFactorType:"certificate",secondFactorCode:"NI",secondFactorValue:""}),_={3:[{label:`NI ${o("app.agentTicketQuery.certs.by_IDAndResidence")}`,value:"NI"},{label:`PP ${o("app.agentTicketQuery.certs.by_PassportAndOther")}`,value:"PP"},{label:`UU ${o("app.agentTicketQuery.certs.by_Unable")}`,value:"UU"}]},d=(b,N,ne)=>{if(!(i.optionType==="3"&&i.selectType==="NI")){ne();return}ga.test(N)?ne():ne(o("app.agentTicketQuery.validate.enterCorrectIDTips"))},x=(b,N,ne)=>{N?i.secondFactorType==="PNR"&&!hn.test(N)?ne(o("app.agentTicketQuery.validate.enterCorrectPNRnumber")):i.secondFactorType==="name"&&!ma.test(N)?ne(o("app.agentTicketQuery.validate.passengerNameError")):i.secondFactorType==="certificate"&&i.secondFactorCode==="NI"&&!ga.test(N)&&ne(o("app.agentTicketQuery.validate.enterCorrectIDTips")):i.secondFactorType==="PNR"?ne(o("app.agentTicketQuery.validate.inputPNRNo")):i.secondFactorType==="name"?ne(o("app.agentTicketQuery.validate.inputPassengerName")):ne(o("app.agentTicketQuery.validate.inputCertificateNo")),ne()},T={optionType:[{required:!0,message:o("app.agentTicketQuery.validate.required"),trigger:"change"}],selectType:[{required:!0,message:o("app.agentTicketQuery.validate.required"),trigger:"change"}],tktNo:[{required:!0,message:o("app.agentTicketQuery.validate.tktNoNull"),trigger:"blur"},{pattern:Rn,message:o("app.agentTicketQuery.validate.tktNoError"),trigger:"blur"}],pnrNo:[{required:!0,message:o("app.agentTicketQuery.validate.inputPNRNo"),trigger:"blur"},{pattern:hn,message:o("app.agentTicketQuery.validate.enterCorrectPNRnumber"),trigger:"blur"}],certificateNo:[{required:!0,message:o("app.agentTicketQuery.validate.inputCertificateNo"),trigger:"blur"},{validator:d,trigger:"blur"}],passengerName:[{required:!0,message:o("app.agentTicketQuery.validate.inputPassengerName"),trigger:"blur"},{pattern:ma,message:o("app.agentTicketQuery.validate.passengerNameError"),trigger:"blur"}],secondFactorValue:[{validator:x,trigger:"blur"}]},k=b=>{i.secondFactorType=b.secondFactorType,i.secondFactorCode=b.secondFactorCode,i.secondFactorValue=b.secondFactorValue},R=()=>{const{tktNo:b,secondFactorCode:N,secondFactorValue:ne}=i;return{ticketNo:b,detrType:"CONJUNCTIVE_TICKET",secondFactor:{secondFactorCode:N,secondFactorValue:ne}}},v=async()=>{try{const b=Pe("091M0101"),N=(await Fa(R(),b)).data.value;a("handleQueryTicket",N,i.optionType)}catch{X()}},r=async()=>{try{const b=Pe("091M0108"),N=(await uo({certNo:i.certificateNo,certCode:i.selectType},b)).data.value;a("handleQueryTicket",N,i.optionType)}catch{X()}},y=async()=>{try{const b=Pe("091M0110"),N=(await co(i.pnrNo,b)).data.value;a("handleQueryTicket",N,i.optionType)}catch{X()}},C=async()=>{try{const b=Pe("091M0109"),N=(await po({certCode:"NM",certNo:Ft.encode(i.passengerName.trim())},b)).data.value;a("handleQueryTicket",N,i.optionType)}catch{X()}},$=async()=>{var b;(b=u.value)==null||b.validate(async N=>{if(N){switch(h=Mt.service({fullscreen:!0}),i.optionType){case"1":v();break;case"2":y();break;case"3":r();break;case"4":C();break}p.replace("/v2/crs/ticketOperation")}})},P=()=>{var b,N;i.selectType=((N=(b=_[i.optionType])==null?void 0:b[0])==null?void 0:N.value)??"TICKET",i.pnrNo="",i.tktNo="",i.ticketHistory=!1,i.certificateNo="",i.passengerName=""},M=()=>{var b;(b=u.value)==null||b.clearValidate("certificateNo")},X=()=>{h&&h.close()},ue=(b,N)=>{N==="1"?(i.optionType=N,i.selectType="TICKET",i.tktNo=b):N==="2"?(i.optionType=N,i.pnrNo=b):N==="3"&&(i.optionType=N,i.selectType="NI",i.certificateNo=b),$(),p.replace("/v2/crs/ticketOperation")},E=b=>{b.get("secondFactorCode")&&b.get("secondFactorValue")?(i.secondFactorType="PNR",i.secondFactorCode=b.get("secondFactorCode")??"CN",i.secondFactorValue=b.get("secondFactorValue")??""):(i.secondFactorType="certificate",i.secondFactorCode="NI",i.secondFactorValue="")},j=()=>{f.value=!0},oe=()=>{f.value=!1},D=(b,N,ne,ie,U)=>{a("openRefundDialog",b,N,ne,ie,U)};return wt(()=>p.currentRoute.value.fullPath,b=>{const N=new URLSearchParams(window.location.search);b.includes("ticketOperation")&&(N.get("pnrNo")&&(i.pnrNo=N.get("pnrNo")??"",i.optionType="2",h=Mt.service({fullscreen:!0}),y()),N.get("ticketNumber")&&(i.tktNo=N.get("ticketNumber")??"",i.optionType="1",E(N),i.secondFactorCode&&i.secondFactorValue&&(h=Mt.service({fullscreen:!0}),v())),p.replace("/v2/crs/ticketOperation"))},{immediate:!0,deep:!0}),{formRef:u,queryForm:i,queryTkt:$,SELECT_OPTION:_,FORM_RULES:T,updateSecondFactorFormData:k,clearOtherInput:P,clearCertValid:M,queryTktByRoute:ue,closeLoading:X,refundView:j,showPrintNoDialog:f,queryRefundMessage:D,closeRefundView:oe}},gi=mi,sn=a=>(bt("data-v-b6748420"),a=a(),xt(),a),ki={class:"query-condition-container bg-gray-0 p-[10px] rounded-t-lg"},yi={class:"inline-block mr-[10px] mb-[10px]"},hi={class:"inline-block text-gray-3 text-xs font-normal leading-tight min-w-[36px]"},vi=sn(()=>t("span",{class:"text-red-1 text-xs font-normal"},"*",-1)),_i=sn(()=>t("br",null,null,-1)),bi=sn(()=>t("i",{class:"iconfont icon-info-circle-line ticket-icon mr-[10px]"},null,-1)),xi=sn(()=>t("span",{class:"text-red-1 text-xs font-normal leading-tight mr-[3px]"},"*",-1)),Ti={class:"inline-block text-gray-3 text-xs font-normal leading-tight min-w-[47px]"},$i=sn(()=>t("span",{class:"text-red-1 text-xs font-normal"},"*",-1)),Ni=sn(()=>t("span",{class:"text-red-1 text-xs font-normal leading-tight"},"*",-1)),Ri={key:0,class:"iconfont icon-right-line text-[12px]"},Ci={key:1,class:"inline-block w-[12px] mr-[4px]"},wi={class:"inline-block text-gray-3 text-xs font-normal leading-tight min-w-[35px] -mt-[2px]"},Ai=sn(()=>t("span",{class:"text-red-1 text-xs font-normal leading-tight"},"*",-1)),Si=je({__name:"TicketQueryCondition",emits:["handleQueryTicket","addNewTab","openAuthOffice","openBatchRefund","openBopRefund","openManualRefund","openRefundDialog","openRtkt","openCccf"],setup(a,{expose:o,emit:u}){const h=u,{formRef:f,queryForm:p,queryTkt:i,SELECT_OPTION:_,FORM_RULES:d,updateSecondFactorFormData:x,clearOtherInput:T,clearCertValid:k,queryTktByRoute:R,closeLoading:v,refundView:r,showPrintNoDialog:y,queryRefundMessage:C,closeRefundView:$}=gi(h);return o({queryTkt:i,queryTktByRoute:R,closeLoading:v,closeRefundView:$}),(P,M)=>{const X=Pt,ue=Ot,E=ot,j=gt,oe=yt,D=tn,b=nn,N=Ke,ne=Sn,ie=it,U=_n("permission");return s(),m(ye,null,[t("div",ki,[c(ie,{ref_key:"formRef",ref:f,inline:!0,model:e(p),rules:e(d),class:"query-condition-form"},{default:l(()=>[c(E,{prop:"optionType",class:"query-left mb-[10px]"},{default:l(()=>[c(ue,{modelValue:e(p).optionType,"onUpdate:modelValue":M[0]||(M[0]=A=>e(p).optionType=A),class:"ml-4",onChange:e(T)},{default:l(()=>[c(X,{label:"1",size:"large"},{default:l(()=>[K(n(P.$t("app.agentTicketQuery.ticketNumber")),1)]),_:1}),c(X,{label:"2",size:"large"},{default:l(()=>[K(n(P.$t("app.agentTicketQuery.pnrNumber")),1)]),_:1}),c(X,{label:"3",size:"large"},{default:l(()=>[K(n(P.$t("app.agentTicketQuery.idNumber")),1)]),_:1}),c(X,{label:"4",size:"large"},{default:l(()=>[K(n(P.$t("app.agentTicketQuery.passengerName")),1)]),_:1})]),_:1},8,["modelValue","onChange"])]),_:1}),t("div",yi,[e(p).optionType==="1"?(s(),m(ye,{key:0},[t("div",hi,[K(n(P.$t("app.agentTicketQuery.ticketAuth.ticketNo"))+" ",1),vi]),c(E,{prop:"tktNo"},{default:l(()=>[c(j,{modelValue:e(p).tktNo,"onUpdate:modelValue":M[1]||(M[1]=A=>e(p).tktNo=A),modelModifiers:{trim:!0},placeholder:P.$t("app.agentTicketQuery.inputEmdNo"),class:"ticket-input",clearable:"",onInput:M[2]||(M[2]=A=>{var le;return e(p).tktNo=((le=e(p).tktNo)==null?void 0:le.toUpperCase())??""})},null,8,["modelValue","placeholder"])]),_:1}),c(oe,{placement:"top",effect:"dark","popper-class":"ticket-conditon-popper"},{content:l(()=>[K(n(P.$t("app.agentTicketQuery.ticketNumberTips")),1),_i,K(" "+n(P.$t("app.agentTicketQuery.suchAs"))+"：999-1234567890 ",1)]),default:l(()=>[bi]),_:1}),xi,c(io,{"item-props":"secondFactorValue","second-factor-type":e(p).secondFactorType,"second-factor-code":e(p).secondFactorCode,"second-factor-value":e(p).secondFactorValue,onUpdateFormData:e(x),onValidateProp:M[3]||(M[3]=A=>{var le;return(le=e(f))==null?void 0:le.validateField("secondFactorValue")}),onClearPropValidate:M[4]||(M[4]=A=>{var le;return(le=e(f))==null?void 0:le.clearValidate("secondFactorValue")})},null,8,["second-factor-type","second-factor-code","second-factor-value","onUpdateFormData"])],64)):Z("",!0),e(p).optionType==="2"?(s(),m(ye,{key:1},[t("div",Ti,[K(n(P.$t("app.agentTicketQuery.pnrNo"))+" ",1),$i]),c(E,{prop:"pnrNo"},{default:l(()=>[c(j,{modelValue:e(p).pnrNo,"onUpdate:modelValue":M[5]||(M[5]=A=>e(p).pnrNo=A),modelModifiers:{trim:!0},placeholder:P.$t("app.agentTicketQuery.inputPNRNo"),class:"pnr-input",clearable:"",onInput:M[6]||(M[6]=A=>{var le;return e(p).pnrNo=((le=e(p).pnrNo)==null?void 0:le.toUpperCase())??""})},null,8,["modelValue","placeholder"])]),_:1})],64)):Z("",!0),e(p).optionType==="3"?(s(),m(ye,{key:2},[c(E,{prop:"selectType"},{default:l(()=>[Ni,c(b,{modelValue:e(p).selectType,"onUpdate:modelValue":M[7]||(M[7]=A=>e(p).selectType=A),"popper-class":"doctype-selector-popper",class:"doctype-select",teleported:!1,onChange:e(k)},{default:l(()=>[(s(!0),m(ye,null,Ne(e(_)[e(p).optionType],A=>(s(),ae(D,{key:A.value,label:A.label,value:A.value},{default:l(()=>[t("span",null,[e(p).selectType===A.value?(s(),m("i",Ri)):(s(),m("span",Ci)),t("span",null,n(A.label),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])]),_:1}),c(E,{prop:"certificateNo"},{default:l(()=>[c(j,{modelValue:e(p).certificateNo,"onUpdate:modelValue":M[8]||(M[8]=A=>e(p).certificateNo=A),modelModifiers:{trim:!0},placeholder:P.$t("app.agentTicketQuery.inputCertificateNo"),class:"cert-input",clearable:"",onInput:M[9]||(M[9]=A=>{var le;return e(p).certificateNo=((le=e(p).certificateNo)==null?void 0:le.toUpperCase())??""})},null,8,["modelValue","placeholder"])]),_:1})],64)):Z("",!0),e(p).optionType==="4"?(s(),m(ye,{key:3},[t("div",wi,[K(n(P.$t("app.agentTicketQuery.name"))+" ",1),Ai]),c(E,{prop:"passengerName"},{default:l(()=>[c(j,{modelValue:e(p).passengerName,"onUpdate:modelValue":M[10]||(M[10]=A=>e(p).passengerName=A),class:"name-input",clearable:"",onInput:M[11]||(M[11]=A=>{var le;return e(p).passengerName=((le=e(p).passengerName)==null?void 0:le.toUpperCase())??""})},null,8,["modelValue"])]),_:1})],64)):Z("",!0)]),c(E,null,{default:l(()=>[c(N,{type:"primary","data-gid":"091M0101",onClick:e(i)},{default:l(()=>[K(n(P.$t("app.agentTicketQuery.queryBtn")),1)]),_:1},8,["onClick"]),Qe((s(),ae(N,{onClick:M[12]||(M[12]=A=>h("openAuthOffice"))},{default:l(()=>[K(n(P.$t("app.agentTicketQuery.ticketAuthBtn")),1)]),_:1})),[[U,"crs-tc-ticketOperation-ticketQuery-ticketAuth-button"]]),Qe((s(),ae(N,{onClick:M[13]||(M[13]=A=>h("openBatchRefund"))},{default:l(()=>[K(n(P.$t("app.batchRefund.batchRefund")),1)]),_:1})),[[U,"crs-tc-ticketOperation-ticketQuery-refundTicket-button"]]),Qe((s(),ae(N,{onClick:M[14]||(M[14]=A=>h("openBopRefund"))},{default:l(()=>[K(n(P.$t("app.agentTicketQuery.bopRefund")),1)]),_:1})),[[U,"crs-ticket-manage-ticket-query-page-bop-refund-button"]]),Qe((s(),ae(N,{onClick:M[15]||(M[15]=A=>h("openManualRefund"))},{default:l(()=>[K(n(P.$t("app.agentTicketRefund.manualRefundBtn")),1)]),_:1})),[[U,"crs-ticket-manage-ticket-query-page-manual-refund-button"]]),c(N,{onClick:e(r)},{default:l(()=>[K(n(P.$t("app.agentTicketQuery.queryRefundBtn")),1)]),_:1},8,["onClick"]),c(N,{onClick:M[16]||(M[16]=A=>h("openRtkt"))},{default:l(()=>[K("RTKT")]),_:1}),c(ne,{"popper-class":"credit-card-receipt-print-popover",placement:"top",effect:"dark",width:"auto",content:P.$t("app.cccf.creditCardReceiptPrint")},{reference:l(()=>[c(N,{onClick:M[17]||(M[17]=A=>h("openCccf"))},{default:l(()=>[K("CCCF")]),_:1})]),_:1},8,["content"])]),_:1})]),_:1},8,["model","rules"])]),e(y)?(s(),ae(fi,{key:0,modelValue:e(y),"onUpdate:modelValue":M[18]||(M[18]=A=>Ve(y)?y.value=A:null),onOpenRefundDialog:e(C)},null,8,["modelValue","onOpenRefundDialog"])):Z("",!0)],64)}}});const Di=at(Si,[["__scopeId","data-v-b6748420"]]),Pi=(a,o)=>{var da;const u=H(),h=Ct(),f=La(),{t:p}=We(),i=H(!1),_=H(!1),d=H(!0),x=H(!1),T=H([]),k=H([]),R=Ae(!1),v=H({}),r=H([]),y=H(!1),C=H(!1),$=H(!1),P=tt({isAlreadyClick:!1,isAlreadySuccessSearch:!1}),M=Me(()=>{const q=T.value.filter(ee=>ee.infants).length;return qe(T.value.length,q)===k.value.length}),X=Ae(""),ue=Ae(""),E=Ae(""),j=H(""),oe=Ae(a.tktNo),D=H(!1),b=H([]),N=H(!1),ne=H("auto"),ie=Ae(!0),U=Ae(""),A=H(),le=H([]),he=tt({pnrNo:""}),ce=H(!1),L={pnrNo:[{required:!0,message:p("app.agentTicketQuery.validate.inputPNRNo"),trigger:"blur"},{pattern:hn,message:p("app.agentTicketQuery.validate.enterCorrectPNRnumber"),trigger:"blur"}]},se=Me(()=>k.value.length>0?k.value.every(q=>{var ee;return(ee=q.ticket.segment)==null?void 0:ee.some(pe=>["OPEN FOR USE","AIRPORT CNTL"].includes(pe.ticketStatus??""))}):!1),Q=q=>({passengerNameSuffix:q.passengerNameSuffix??"",passengerName:q.name,specialPassengerType:q.specialPassengerType,passengerType:q.specialPassengerType??"",index:"0",secondFactor:q.secondFactor,ticketNos:q.ticketNo.includes("-")?[q.ticketNo.split("-")[0],q.ticketNo.split("-")[1]]:[q.ticketNo],isChecked:!0}),S=H({}),G=H({}),J=H(""),{defaultOffice:z,office:w,defaultRoleWithPid:g}=h.state.user,F=(g?z:((da=w==null?void 0:w.split(";"))==null?void 0:da[0])??"")===Ua,de=(q,ee)=>{const pe=new Map;return q.forEach(ke=>{var Ge;const De=(Ge=ke[ee])==null?void 0:Ge.replace(/-/,"");pe.set(De,pe.get(De)||[]),pe.get(De).push(ke)}),pe},ve=async(q,ee,pe,ke)=>{const De=k.value.some(Ge=>Ge.ticket.ticketNo.includes(q));if(pe&&De)return"";if(pe)try{_.value=!0;const ze=(await In({tktNo:q,secondFactor:ee,refundQuery:!0},ke)).data.value;return ze!=null&&ze.data?(ze.data.ticket.ticketSegment=de(ze.data.ticket.segment??[],"tktTag"),G.value=ze==null?void 0:ze.data,k.value.push(ze.data),d.value&&T.value.push(Q(ze.data.ticket)),ze.data.ticket.crsPnrNo??""):""}finally{_.value=!1}else return k.value=k.value.filter(Ge=>!Ge.ticket.ticketNo.includes(q)),k.value.length===1&&(G.value=k.value[0]),""},O=q=>q==null?void 0:q.replace("-",""),fe=q=>{q.ticketNos=q.ticketNos.map(O),q.infants&&(q.infants.ticketNos=q.infants.ticketNos.map(O))},Y=q=>{q.forEach(fe)},Te=q=>{var pe,ke;const ee=(pe=a.tktNo)==null?void 0:pe.replace("-","");return(ke=q.ticketNos)==null?void 0:ke.includes(ee)},V=(q,ee)=>(ie.value=q.some(Te),ie.value?E.value=ee:U.value=(q==null?void 0:q.length)===0?p("app.agentTicketRefund.queryPnrFailed"):p("app.agentTicketRefund.currentTicketNotMatchPnr"),ie.value),ge=q=>{var pe,ke,De;T.value.splice(0,1);const ee=T.value.findIndex(Ge=>Ge.index===(q==null?void 0:q.index));T.value.unshift(T.value.splice(ee,1)[0]),(De=(ke=(pe=T.value[0])==null?void 0:pe.infants)==null?void 0:ke.ticketNos)!=null&&De.length&&(T.value[0].infants.isChecked=!0)},me=async(q,ee)=>{var pe,ke,De;if(T.value[0].infants=q==null?void 0:q.infants,T.value[0].specialPassengerType=q==null?void 0:q.specialPassengerType,T.value[0].index=(q==null?void 0:q.index)??"",T.value[0].secondFactor=q==null?void 0:q.secondFactor,(De=(ke=(pe=T.value[0])==null?void 0:pe.infants)==null?void 0:ke.ticketNos)!=null&&De.length){T.value[0].infants.isChecked=!0;const{ticketNos:Ge,secondFactor:ze={}}=T.value[0].infants;await ve(Ge[0],ze,!0,ee)}},Re=async(q,ee)=>{const pe=q.find(Te)??{};q.forEach(ke=>{!Te(ke)&&(ke.specialPassengerType!=="INF"||ke.passengerType!=="INF")&&T.value.push(ke)}),(pe==null?void 0:pe.specialPassengerType)==="INF"?ge(pe):await me(pe,ee)},Oe=async(q,ee,pe)=>{var ke,De;try{pe&&await(pe==null?void 0:pe.validate()),_.value=!0;const Ge=((De=(ke=await Do({pnrNo:q},ee))==null?void 0:ke.data)==null?void 0:De.value)??{},{passengers:ze=[]}=Ge;if(Y(ze),!V(ze,q))return;await Re(ze,ee)}finally{_.value=!1}},we=q=>{const ee={pnrHandleType:q,pnrNo:M.value?"":E.value,xePnr:Be()?"":!M.value&&!Be()?j.value:E.value,passengerInfoList:[]};return ct().length?ct().forEach(pe=>{var ke,De;ee.passengerInfoList.push({name:Ft.encode(((ke=pe==null?void 0:pe.infants)==null?void 0:ke.passengerNameSuffix)??""),psgType:"INF",ticketNo:((De=pe==null?void 0:pe.infants)==null?void 0:De.ticketNos[0])??""})}):k.value.forEach(pe=>{!M.value&&pe.ticket.psgType==="INF"&&ee.passengerInfoList.push({name:Ft.encode(pe.ticket.name),psgType:pe.ticket.psgType,ticketNo:pe.ticket.ticketNo})}),ee},Ee=()=>k.value.map(q=>{var ee,pe,ke;return{ticketNo:((pe=(ee=q.ticket.segment)==null?void 0:ee[0])==null?void 0:pe.tktTag)??"",domestic:(ke=q.ticket.tktType)==null?void 0:ke.includes("D"),printNo:q.printerNo}}),Be=()=>k.value.every(q=>q.ticket.psgType==="INF"),Ze=async q=>{await cn(p("app.agentTicketRefund.refundSuccess")),$.value=!0,x.value=!0;const ee=ft(k.value);k.value=[],ee.forEach(async pe=>{var ke,De;await ve(((De=(ke=pe.ticket.segment)==null?void 0:ke[0])==null?void 0:De.tktTag)??"",pe.ticket.secondFactor,!0,q)})},I=q=>(q??[]).reduce((ee,pe)=>{let ke="";ee||(ke=`<p class="text-gray-1 text-lg font-normal pb-2.5">${p("app.agentTicketRefund.refundPartFail")}</p>`);const De=pe.success?'<i class="iconfont icon-ticket text-green-2 mr-2.5"></i>':'<i class="iconfont icon-close text-red-1 mr-2.5"></i>',Ge=`${ke}<p class="text-sm font-bold leading-normal mt-4">${De}${pe.ticketNo}</p>`;return ee+Ge},""),_e=async(q,ee)=>{Xe.confirm(I(q),{icon:Le(Je,{color:"#FF3636",size:32},()=>Le(Na)),customClass:"invalidated-warning-msg crs-btn-ui",closeOnClickModal:!1,showClose:!0,showCancelButton:!1,confirmButtonText:p("app.agentTicketRefund.confirm"),dangerouslyUseHTMLString:!0,draggable:!0}),x.value=!0;const pe=ft(k.value);k.value=[];for(let ke=0;ke<pe.length;ke++)try{_.value=!0;const De=(await In({tktNo:pe[ke].ticket.ticketNo,secondFactor:pe[ke].ticket.secondFactor,refundQuery:!0},ee)).data.value;if(!(De!=null&&De.data))return;De.data.ticket.ticketSegment=de(De.data.ticket.segment??[],"tktTag"),k.value.push(De.data)}finally{_.value=!1}k.value.forEach(ke=>{ke.ticket.isRefundFail=q.some(De=>De.ticketNo.replace("-","").includes(ke.ticket.ticketNo)&&!De.success)})},$e=q=>{N.value=!0,ne.value="auto",b.value=(q??[]).map(ee=>({ticketNo:ee.ticketNo,trfdNo:ee.trfdno??""})),(q??[]).forEach(ee=>{ee.amount&&le.value.push(ee.amount)})},re=q=>{ne.value="manual",b.value=q??[]},Ce=q=>{const ee=q.indexOf("("),pe=q.indexOf("*");if(ee>-1&&pe>-1){const ke=Math.min(ee,pe);return(q==null?void 0:q.substring(0,ke))??""}else{if(ee===-1&&pe>-1)return(q==null?void 0:q.substring(0,pe))??"";if(ee>-1&&pe===-1)return(q==null?void 0:q.substring(0,ee))??""}return q.trim()},Fe=(q,ee)=>{const pe=ft(k.value);k.value=[],pe.forEach(async ke=>{var Ge,ze;const De={secondFactorCode:"NM",secondFactorValue:Ce(ke.ticket.name)};q&&j.value&&(E.value=j.value),await ve(((ze=(Ge=ke.ticket.segment)==null?void 0:Ge[0])==null?void 0:ze.tktTag)??"",De,!0,ee)})},W=(q,ee)=>{Fe(!0,ee);const pe=`/v2/crs/pnrManagement?pnrNo=${q}`;wn.setLink(pe)},Ye=q=>q&&(M.value||Be()||ct().length===0),lt=q=>{$.value=!0,x.value=!0;const ee=k.value[0].ticket.tktType==="I";if(ee||(cn(p("app.agentTicketRefund.refundSuccess")),Fe(!1,q)),Ye(ee)){let pe="C",ke=j.value?j.value:E.value,De=p("app.agentTicketRefund.cancelPnr"),Ge=p("app.agentTicketRefund.refundSuccessCanclePnrTips",{pnrNo:j.value?j.value:E.value});Be()&&(ke=E.value,pe="D",De=p("app.agentTicketRefund.xePassenger"),Ge=p("app.agentTicketRefund.refundSuccessDeleteTips",{pnrNo:E.value}));const ze=Xe;ze.confirm(Le("div",{},[Le("span",{className:"text-[18px] text-gray-1"},Ge),Le("span",{className:"text-[18px] underline text-brand-2 cursor-pointer",onClick:()=>{W(ke,q),ze.close()}},p("app.agentTicketRefund.toOrder")),Le("span",{className:"text-[18px] text-gray-1"},p("app.agentTicketRefund.handlePnr"))]),{icon:Le(Je,{color:ws("--bkc-tw-green-2",null).value,size:32},()=>Le(Gn)),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",confirmButtonText:De,cancelButtonText:p("app.agentTicketRefund.cancel"),showClose:!1}).then(async()=>{await Ht(pe,pe==="C"?ke:"",q),Fe(pe==="C",q)}).catch(()=>{Fe(pe==="C",q)})}else ee&&!M.value&&(ce.value=!0)},be=async q=>{let ee=p("app.agentTicketRefund.cancelPnr"),pe="C";const ke=k.value[0].ticket.tktType==="I";let De=ct().length&&!ke?p("app.agentTicketRefund.refundSeatTipsNonCarryInft",{pnrNo:j.value?j.value:E.value,ticketNo:Xt(ct())}):p("app.agentTicketRefund.refundSeatTips",{pnrNo:j.value?j.value:E.value});Be()&&(pe="D",ee=p("app.agentTicketRefund.xePassenger"),De=p("app.agentTicketRefund.refundSeatDeleteTips",{pnrNo:E.value})),Xe.confirm(Le("div",{className:"whitespace-pre-line"},De),{icon:Le("em",{class:"iconfont icon-info-circle-line"}),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",confirmButtonText:ee,cancelButtonText:p("app.agentTicketRefund.cancel"),showClose:!1,closeOnClickModal:!1}).then(async()=>{await Ht(pe,pe==="C"?j.value?j.value:E.value:"",q),(X.value==="S"||ue.value==="S")&&pt(q)}).catch(async Ge=>{var ze,Et,qt;if(Ge==="cancel"&&j.value){const zt=(qt=(Et=(ze=k.value.filter(ps=>ps.ticket.psgType!=="INF"))==null?void 0:ze[0])==null?void 0:Et.ticket)==null?void 0:qt.ticketNo,Wt={secondFactorCode:"CN",secondFactorValue:j.value},ds={title:`${p("app.agentTicketRefund.newRefund")}${zt}`,name:`newRefund?type=ticketNo&sign=${zt}`,content:en(Nn)};o("addNewTab",ds,"",Wt),await fn(),o("removeTab",-1,`newRefund?type=ticketNo&sign=${oe.value}`)}})},vt=async q=>{_.value=!0;const ee=(await ba({ticketList:Ee(),save:"NO"},q)).data.value;ee!=null&&ee.allSuccess&&Ze(q),!(ee!=null&&ee.allSuccess)&&!(ee!=null&&ee.allFail)&&_e((ee==null?void 0:ee.refundResult)??[],q),$e((ee==null?void 0:ee.refundResult)??[])},pt=async q=>{_.value=!0;let ee;try{ee=(await ba({ticketList:Ee(),save:"NO"},q)).data.value}finally{_.value=!1}if(ee!=null&&ee.needSeatVacated){be(q);return}ee!=null&&ee.allSuccess&&lt(q),!(ee!=null&&ee.allSuccess)&&!(ee!=null&&ee.allFail)&&_e((ee==null?void 0:ee.refundResult)??[],q),$e((ee==null?void 0:ee.refundResult)??[])},Tt=async q=>{if(q&&X.value==="S"&&k.value[0].ticket.tktType==="I"){await cn(p("app.agentTicketRefund.pnrCancelSuccess",{pnrNo:q}));return}if(Be()&&ue.value==="S"&&k.value[0].ticket.tktType==="I"){await cn(p("app.agentTicketRefund.cancelPassengerSuccess"));return}const ee=ue.value==="S"||X.value==="S";await mt({message:p(ee?"app.agentTicketRefund.xeSuccess":"app.agentTicketRefund.xeFaild"),type:ee?"success":"error",customClass:"z-3000"})},$t=q=>({fullName:q.passengerName,paxId:Number(q.index.replace("P","")),unMinor:!1,unMinorAge:0}),It=()=>{const q=[];return T.value.forEach(ee=>{ee.isChecked&&q.push($t(ee))}),q},Vt=async q=>{var ke;const ee={orderId:"",count:0,passengerRecordLocator:E.value,travellers:It(),isGrp:!1},pe=(await Jn(ee,q)).data.value;j.value=((ke=pe==null?void 0:pe.splitedOrder)==null?void 0:ke.passengerRecordLocator)??"",mt({message:p("app.agentTicketRefund.splitSuccess"),type:"success"})},Ht=async(q,ee,pe)=>{_.value=!0;try{const ke=(await Zn(we(q),pe)).data.value;X.value=(ke==null?void 0:ke.xePnrExecutionStatus)??"N",ue.value=(ke==null?void 0:ke.deleteInfantExecutionStatus)??"N"}finally{_.value=!1}await Tt(ee)},Xt=q=>{let ee=[];return q.forEach(pe=>{var ke;ee=ee.concat(((ke=pe.infants)==null?void 0:ke.ticketNos)??[])}),ee.join("、")},He=async()=>{let q=!1;if(!M.value&&!Be()){const ee=k.value[0].ticket.tktType==="I",pe=ct().length&&!ee?p("app.agentTicketRefund.splitTipNonCarryInf",{ticketNo:Xt(ct())}):p("app.agentTicketRefund.splitTip");await Xe.confirm(Le("div",{className:"whitespace-pre-line"},pe),{icon:Le("em",{class:"iconfont icon-info-circle-line"}),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",confirmButtonText:p("app.agentTicketRefund.sure"),cancelButtonText:p("app.agentTicketRefund.cancel"),showClose:!1,closeOnClickModal:!1}).catch(async ke=>{var De,Ge,ze;if(q=!0,ke==="cancel"&&ct().length&&k.value[0].ticket.tktType!=="I"){const Et=(ze=(Ge=(De=k.value.filter(Wt=>Wt.ticket.psgType!=="INF"))==null?void 0:De[0])==null?void 0:Ge.ticket)==null?void 0:ze.ticketNo,qt={secondFactorCode:"CN",secondFactorValue:E.value},zt={title:`${p("app.agentTicketRefund.newRefund")}${Et}`,name:`newRefund?type=ticketNo&sign=${Et}`,content:en(Nn)};o("addNewTab",zt,"",qt),await fn(),o("removeTab",-1,`newRefund?type=ticketNo&sign=${oe.value}`)}})}return q},ct=()=>T.value.filter(ee=>!ee.isChecked&&ee.infants&&ee.infants.isChecked),Ut=q=>{var pe;return((pe=k.value.filter(ke=>ke.ticket.ticketNo===q)[0])==null?void 0:pe.printerNo)??""},ln=()=>ct().map(ee=>{var pe,ke;return{ticketNo:(pe=ee.infants)==null?void 0:pe.ticketNos[0],domestic:k.value[0].ticket.tktType==="D",printNo:Ut(((ke=ee.infants)==null?void 0:ke.ticketNos[0])??"")}}),Jt=async q=>{if(q){const ee=p("app.agentTicketRefund.refundSeatDeleteTips",{pnrNo:E.value});await Xe.confirm(Le("div",{className:"whitespace-pre-line"},ee),{icon:Le("em",{class:"iconfont icon-info-circle-line"}),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",confirmButtonText:p("app.agentTicketRefund.xePassenger"),cancelButtonText:p("app.agentTicketRefund.cancel"),showClose:!1,closeOnClickModal:!1})}},te=async q=>{if(!M.value&&!Be()&&ct().length&&k.value[0].ticket.tktType!=="I"){let pe;try{_.value=!0,pe=(await fo({ticketList:ln(),save:"NO"},q)).data.value}finally{_.value=!1}await Jt((pe==null?void 0:pe.needSeatVacated)??!1),pe!=null&&pe.needSeatVacated&&await Ht("D","",q)}if(!await He())try{_.value=!0,!M.value&&!Be()&&await Vt(q),await pt(q)}finally{_.value=!1}},xe=()=>{const q=T.value.filter(ke=>ke.isChecked&&ke.passengerType==="ADT"),ee=T.value.filter(ke=>!ke.isChecked&&ke.passengerType==="INF");return(q??[]).some(ke=>ee.some(De=>{var Ge,ze;return De.ticketNos.includes(((ze=(Ge=ke.infants)==null?void 0:Ge.ticketNos)==null?void 0:ze[0])??"")}))},Ue=async q=>{if(E.value)te(q);else try{_.value=!0,await vt(q)}finally{_.value=!1}},Ie=async q=>{if(xe()){const ee=p("app.agentTicketRefund.refundWithBabyTip");await Xe.confirm(ee,{icon:Le("em",{class:"iconfont icon-info-circle-line"}),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",confirmButtonText:p("app.agentTicketRefund.sure"),cancelButtonText:p("app.agentTicketRefund.cancel"),showClose:!1,closeOnClickModal:!1})}q()},ut=()=>k.value.some(ee=>!((ee==null?void 0:ee.ticketManagementOrganizationCode)??"").includes("CDS")&&!ee.printerNo?(ee.printError=p("app.ticketStatus.deviceNumNull"),!0):!1),rt=async()=>{if(ut())return;const q=p("app.agentTicketRefund.autoRefundTip");await Xe.confirm(q,{icon:Le("em",{class:"iconfont icon-info-circle-line"}),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",confirmButtonText:p("app.agentTicketRefund.sure"),cancelButtonText:p("app.agentTicketRefund.cancel"),showClose:!1,closeOnClickModal:!1});const ee=Pe("091Q0105");Ie(()=>{Ue(ee)})},Qt=async()=>{R.value=!0},_t=async()=>{ut()||Ie(Qt)},At=q=>{P.isAlreadyClick=q,On()},os=()=>{var q,ee,pe,ke,De,Ge,ze,Et;return{tktNos:k.value.map(qt=>{var zt,Wt;return((Wt=(zt=qt.ticket.segment)==null?void 0:zt[0])==null?void 0:Wt.tktTag)??""}),tktType:((pe=(ee=(q=k.value)==null?void 0:q[0])==null?void 0:ee.ticket)==null?void 0:pe.tktType)??"D",cityCode:((Et=(ze=(Ge=(De=(ke=k.value)==null?void 0:ke[0])==null?void 0:De.ticket)==null?void 0:Ge.segment)==null?void 0:ze[0])==null?void 0:Et.departureCode)??""}},On=async()=>{var ee,pe;const q=Pe("091Q0103");if(k.value.length!==0){v.value={};try{_.value=!0,v.value=(pe=(ee=await Po(os(),q))==null?void 0:ee.data)==null?void 0:pe.value}catch{v.value.status="FAILURE"}finally{P.isAlreadySuccessSearch=v.value.status==="SUCCESS",_.value=!1}}},is=q=>{var ke;const ee=T.value[q].infants&&((ke=T.value[q].infants)==null?void 0:ke.isChecked)&&T.value[q].isChecked,pe=T.value[q].infants&&!T.value[q].isChecked;return ee||pe},ls=async q=>{var pe,ke,De,Ge,ze,Et,qt;const ee=Pe("091Q0101");if(is(q)){const zt=(pe=T.value[q].infants)==null?void 0:pe.ticketNos[0].replace("-",""),Wt=T.value[q].ticketNos[0].replace("-","");await Promise.all([ve(Wt,(ke=T.value[q])==null?void 0:ke.secondFactor,T.value[q].isChecked??!1,ee),ve(zt??"",(ze=(Ge=(De=T.value)==null?void 0:De[q])==null?void 0:Ge.infants)==null?void 0:ze.secondFactor,((Et=T.value[q].infants)==null?void 0:Et.isChecked)??!1,ee)])}else await ve(T.value[q].ticketNos[0].replace("-",""),(qt=T.value[q])==null?void 0:qt.secondFactor,T.value[q].isChecked??!1,ee);P.isAlreadySuccessSearch&&On()},rs=(q,ee)=>{y.value=ee,C.value=!ee,$.value=ee,r.value=q.map(pe=>{const ke=pe.resultpre;return{...ke.amount,...ke.ticket}})},cs=async()=>{const q=ft(k.value);k.value=[],b.value=[],N.value=!1;const ee=Pe("091Q0101");q.forEach(async pe=>{var ke,De;await ve(((De=(ke=pe.ticket.segment)==null?void 0:ke[0])==null?void 0:De.tktTag)??"",pe.ticket.secondFactor,!0,ee)})},us=async q=>{var ee,pe;try{_.value=!0;const ke=await Va(q);i.value=((pe=(ee=ke==null?void 0:ke.data)==null?void 0:ee.value)==null?void 0:pe.data)??!1}finally{_.value=!1}};return wt(()=>R.value,q=>{f.setShowManualRefund(q)}),st(async()=>{const q=Pe("091Q0101");await us(q),E.value=await ve(a.tktNo,a.factor,!0,q),[he.pnrNo,d.value]=[E.value,!1],E.value&&await Oe(E.value,q)}),{splitPnrNo:j,isDragonBoatOffice:F,pnrNo:E,amountRef:u,fullscreenLoading:_,allPassengerList:T,ticketDetailList:k,manualDisabled:D,manualDialogVisible:R,packageData:S,clacAmountInfosRes:v,clacAmountInfo:P,partSuccess:C,isAutoRefundFinished:x,isXePnr:M,currentTktNo:oe,deviceNum:J,isFinishManualRefund:y,manualRefundAmountInfo:r,isCanRefund:se,queryTicketDetail:ve,manualRefund:_t,handleAutoRefund:rt,getCalcInfo:At,queryCalcAmount:On,queryTicketAndCalcAmount:ls,getManualRefundAmountDetail:rs,refresh:cs,ticketTrfdNoDetails:b,isShowTrfdNo:N,getTicketTrfdNoDetailsFromManualRefund:re,refundType:ne,isRelatedCorrectPnr:ie,updatePnrForm:A,updatePnrFormData:he,updatePnrFormRules:L,queryAllPassenger:Oe,queryPnrTip:U,showInterRefundSuccess:ce,refreshTicketDetail:Fe,refundTicketSuccess:$,isAuthToShowWindow:i,batchAutoAmount:le}},Oi=a=>({confirm:()=>{a("executionMethod"),a("update:modelValue",!1)},closeDialog:()=>{a("update:modelValue",!1)}}),Ei=a=>(bt("data-v-31acb56d"),a=a(),xt(),a),Fi={class:"flex items-center"},Vi=Ei(()=>t("div",{class:"iconfont icon-info-circle-line text-brand-2 mr-[16px]"},null,-1)),Mi={class:"text-lg text-gray-1"},ji=je({__name:"TipDialog",props:{tipMessage:{},globalId:{},permission:{}},emits:["update:modelValue","executionMethod"],setup(a,{emit:o}){const u=a,h=o,{confirm:f,closeDialog:p}=Oi(h);return(i,_)=>{const d=Ke,x=nt,T=_n("permission");return s(),ae(x,{class:"common-dialog","show-close":!1,width:"500px",top:"40vh","close-on-click-modal":!1},{footer:l(()=>[u.permission?Qe((s(),ae(d,{key:0,"data-gid":i.globalId,type:"primary",onClick:e(f)},{default:l(()=>[K(n(i.$t("app.button.ensure")),1)]),_:1},8,["data-gid","onClick"])),[[T,u.permission]]):(s(),ae(d,{key:1,"data-gid":i.globalId,type:"primary",onClick:e(f)},{default:l(()=>[K(n(i.$t("app.button.ensure")),1)]),_:1},8,["data-gid","onClick"])),c(d,{onClick:e(p)},{default:l(()=>[K(n(i.$t("app.button.cancel")),1)]),_:1},8,["onClick"])]),default:l(()=>[t("div",Fi,[Vi,t("div",Mi,n(u.tipMessage),1)])]),_:1})}}});const Li=at(ji,[["__scopeId","data-v-31acb56d"]]),Bi=(a,o)=>{var ve;const{t:u}=We(),h=Ct(),f=(ve=navigator==null?void 0:navigator.userAgent)==null?void 0:ve.toLowerCase(),p=Me(()=>f==null?void 0:f.includes("electron/")),i=(O,fe,Y)=>window.electronAPI.openTicketDetailWindow(O,fe,Y),_=Ae(!1),d=H(),x=27,T=Me(()=>a.reuseTicketInfo),k=H(""),R=H([]),v=O=>{var Y,Te;return(R.value??[]).some(V=>O===V.value)&&O?O:((Te=(Y=R.value)==null?void 0:Y[0])==null?void 0:Te.value)??""},r=tt({...a.refundData}),y={BSP:{label:u("app.agentTicketQuery.BSPTicket"),value:"BSP"},GPBSP:{label:u("app.agentTicketQuery.GPTicket"),value:"GPBSP"},BOPBSP:{label:u("app.agentTicketQuery.BOPTicket"),value:"BOPBSP"},CDS:{label:u("app.agentTicketQuery.CDSTicket"),value:"CDS"},GPCDS:{label:u("app.agentTicketQuery.GP_CDSTicket"),value:"GPCDS"},ARL:{label:u("app.agentTicketQuery.OWNTicket"),value:"ARL"}},C=Me(()=>{var O;return(O=h.state.user)==null?void 0:O.entityType}),$=Me(()=>!["CDS","GPCDS"].includes(r.ticketManagementOrganizationCode)),P=Me(()=>{var O;return((O=a.refundData)==null?void 0:O.tktType)==="I"}),M=(O,fe,Y)=>{var V;const Te=Number(O.field.split(".")[1]);r.taxs[Te].name&&!fe?Y(u("app.agentTicketRefund.taxAmount")):!r.taxs[Te].name&&!fe&&((V=d.value)==null||V.clearValidate(`taxs.${Te}.name`),Y()),Y()},X=(O,fe,Y)=>{var V;const Te=Number(O.field.split(".")[1]);r.taxs[Te].value&&!fe?Y(u("app.agentTicketRefund.taxes")):!r.taxs[Te].value&&!fe&&((V=d.value)==null||V.clearValidate(`taxs.${Te}.value`),Y()),Y()},ue=(O,fe,Y)=>{r.payType==="TC"&&(fe?!r.isDragonBoatOffice&&!Kn.test(fe)?Y(u("app.agentTicketRefund.creditCardInput")):r.isDragonBoatOffice&&!Xn.test(fe)&&Y(u("app.agentTicketRefund.dragonBoatOfficeInput")):Y(u("app.agentTicketRefund.creditCardNotEmpty"))),Y()},E=(O,fe,Y)=>{r.remark&&!ya.test(fe)&&Y(u("app.agentTicketRefund.formatError")),Y()},j=(O,fe,Y)=>{var Te;fe?((Te=d.value)==null||Te.validateField("remarkCode"),r.remarkCode==="IC"?(Pa.test(`${r.remarkCode}${fe}`)||Y(u("app.agentTicketRefund.remarkIC")),Y()):r.remarkCode&&!$n.test(`${r.remarkCode}${fe}`)&&Y(u("app.agentTicketRefund.remarkHint")),Y()):r.remarkCode&&Y(u("app.agentTicketRefund.remarkHint")),Y()},oe={currency:[{required:!0,message:u("app.agentTicketRefund.currencyNotEmpty"),trigger:"change"}],payType:[{required:!0,message:u("app.agentTicketRefund.paymentSel"),trigger:"change"},{pattern:Yn,message:u("app.agentTicketRefund.paymentInput"),trigger:"change"}],totalAmount:[{pattern:dt,message:u("app.agentTicketRefund.correctPrice"),trigger:"change"}],taxValue:[{pattern:Sa,message:u("app.agentTicketRefund.correctPrice"),trigger:"change"},{validator:M,trigger:"change"}],taxName:[{pattern:Da,message:u("app.agentTicketRefund.taxes"),trigger:"change"},{validator:X,trigger:"change"}],commisionRate:[{pattern:dt,message:u("app.agentTicketRefund.correctRate"),trigger:"change"}],otherDeductionRate:[{pattern:dt,message:u("app.agentTicketRefund.correctRate"),trigger:"change"}],commision:[{pattern:dt,message:u("app.agentTicketRefund.correctPrice"),trigger:"change"}],psdName:[{pattern:Hs,message:u("app.agentTicketRefund.psgNameError"),trigger:"change"}],creditCard:[{validator:ue,trigger:"change"}],remarkCode:[{pattern:ya,message:u("app.agentTicketRefund.formatError"),trigger:["change","blur"]},{validator:E,trigger:["change","blur"]}],remark:[{validator:j,trigger:["change","blur"]}],remarkInfo:[{pattern:$n,message:u("app.agentTicketRefund.remarkHint"),trigger:["change","blur"]}],netRefund:[{required:!0,message:u("app.agentTicketRefund.prntNoNotEmpty"),trigger:"blur"},{pattern:dt,message:u("app.agentTicketRefund.onlySupportDigits"),trigger:"blur"}],ticketManagementOrganizationCode:[{required:!0,message:u("app.agentTicketRefund.prntNoNotEmpty"),trigger:"blur"}]},D=()=>{if(r.taxs.length!==x)if(r.taxs.length>22&&r.taxs.length<x){const O=new Array(x-r.taxs.length).fill({name:"",value:""});r.taxs=r.taxs.concat(O).map(fe=>({...fe}))}else{const O=new Array(5).fill({name:"",value:""});r.taxs=r.taxs.concat(O).map(fe=>({...fe}))}},b=()=>{const{totalAmount:O,totalTaxs:fe,otherDeduction:Y,commision:Te,commisionRate:V}=r;if(A()){Q();return}if(V){const ge=jt(Lt(Number(O),Number(V)),100).toString(),me=pn(Number(ge),2).toString();r.commision=me;const Re=`${Dt(qe(Number(O),Number(fe)),qe(Number(Y),Number(me)))}`;r.netRefund=Number(Re).toFixed(2)}else{const ge=`${Dt(qe(Number(O),Number(fe)),qe(Number(Y),Number(Te)))}`;r.netRefund=Number(ge).toFixed(2)}r.commision!==""&&(r.commision=Number(r.commision).toFixed(2))},N=()=>{r.commisionRate||(r.commision="")},ne=()=>{let O=new gn(0);r.taxs.forEach((fe,Y)=>{var Te;(Te=d.value)==null||Te.validateField(`taxs.${Y}.value`).then(V=>{V&&(O=O.add(new gn(fe.value?fe.value:0)),r.totalTaxs=Nt(r.currency)?O.toString():Number(O).toFixed(2),Nt(r.currency)?le(""):b())})})},ie=async()=>{const O=[];r.taxs.forEach((fe,Y)=>{var Te,V;O.push((Te=d.value)==null?void 0:Te.validateField(`taxs.${Y}.name`)),O.push((V=d.value)==null?void 0:V.validateField(`taxs.${Y}.value`))}),await Promise.all(O),r.taxs.forEach((fe,Y)=>{r.taxs[Y].value&&(r.taxs[Y].value=Nt(r.currency)?r.taxs[Y].value??0:Number(r.taxs[Y].value??0).toFixed(2))}),ne(),Q()},U=O=>O&&!dt.test(O),A=()=>{const{totalAmount:O,otherDeductionRate:fe,otherDeduction:Y,commision:Te,commisionRate:V}=r;return U(O??"")||U(fe??"")||U(Y??"")||U(Te??"")||U(V??"")},le=O=>{if(O==="otherDeductionRate"&&r.otherDeductionRate){const me=jt(Lt(Number(r.totalAmount),Number(r.otherDeductionRate)),100).toString();r.otherDeduction=pn(Number(me),2).toString()}const{totalAmount:fe,totalTaxs:Y,otherDeduction:Te,commision:V,commisionRate:ge}=r;if(!A())if(ge){const me=jt(Lt(Number(fe),Number(ge)),100).toString(),Re=pn(Number(me),2).toString();r.commision=Re.endsWith(".00")?Re.slice(0,-3):Re;const Oe=`${Dt(qe(Number(fe),Number(Y)),qe(Number(Te),Number(Re)))}`;r.netRefund=Number(Oe).toFixed(2),r.netRefund.endsWith(".00")&&(r.netRefund=r.netRefund.slice(0,-3))}else r.netRefund=`${Dt(qe(Number(fe),Number(Y)),qe(Number(Te),Number(V)))}`},he=async O=>{if(A()){Q();return}if(Nt(r.currency)){le(O),Q();return}if(O==="otherDeductionRate"&&r.otherDeductionRate){r.otherDeductionRate=Number(r.otherDeductionRate).toFixed(2);const fe=jt(Lt(Number(r.totalAmount),Number(r.otherDeductionRate)),100).toString();r.otherDeduction=Number(fe).toFixed(2)}b(),r.totalAmount!==""&&(r.totalAmount=Number(r.totalAmount).toFixed(2)),r.commision!==""&&(r.commision=Number(r.commision).toFixed(2)),r.commisionRate!==""&&(r.commisionRate=Number(r.commisionRate).toFixed(2)),r.otherDeduction!==""&&(r.otherDeduction=Number(r.otherDeduction).toFixed(2)),Q()},ce=()=>{d.value.clearValidate("creditCard"),r.payType.toUpperCase()==="TC"&&(r.creditCard=r.isDragonBoatOffice?ea:"")},L=O=>{O.target.value!==""&&!Dn.some(fe=>fe.label===O.target.value)&&(r.payType=O.target.value),Q()},se=async()=>{var O;try{return await((O=d.value)==null?void 0:O.validate())}catch{return!1}},Q=async()=>{const O=await se();o("validateChange",a.ticketIndex,O)},S=()=>r,G=O=>O.length<10?O.concat(new Array(10-O.length).fill({name:"",value:""})).map(fe=>({...fe})):O,J=async()=>{var O,fe;try{const Y=Pe("091Q0203");_.value=!0;const Te=r.ticketNo.indexOf("-"),V=Te!==-1?r.ticketNo.substring(0,Te):r.ticketNo,ge=(fe=(O=await Ba({ticketNo:V},Y))==null?void 0:O.data)==null?void 0:fe.value;if(!(ge!=null&&ge.rtKTTaxes)||(ge==null?void 0:ge.rtKTTaxes.length)===0)return;r.taxs=[],((ge==null?void 0:ge.rtKTTaxes)??[]).forEach(me=>{r.taxs.push({name:me.taxType,value:me.taxAmount})}),r.taxs=G(r.taxs),ne()}finally{_.value=!1}},z=O=>{o("reuseTicket",O)},w=O=>{const fe=O.indexOf("("),Y=O.indexOf("*");if(fe>-1&&Y>-1){const Te=Math.min(fe,Y);return(O==null?void 0:O.substring(0,Te))??""}else{if(fe===-1&&Y>-1)return(O==null?void 0:O.substring(0,Y))??"";if(fe>-1&&Y===-1)return(O==null?void 0:O.substring(0,fe))??""}return O.trim()};Ra(()=>{[...r.segment.values()].forEach(O=>{let fe="";const Y=[];O.forEach(Te=>{fe=Te.tktTag,Y.push({...Te,isAllowCheck:Te.isAble==="1"})}),r.segment.set(fe,Y)})});const g=(O,fe)=>Nt(fe)?O.endsWith(".00")?O.slice(0,-3):O:O&&Number(O).toFixed(2),B=(O,fe)=>O.map(Y=>({name:Y.name,value:g(Y.value,fe)}));wt([()=>a.reuseTimes,()=>T.value],async()=>{if(!T.value||a.currentTicket!==r.ticketNo)return;const O=ft(T.value);r.commision=g((O==null?void 0:O.commision)??"",(O==null?void 0:O.currency)??""),r.taxs=B(O==null?void 0:O.taxs,(O==null?void 0:O.currency)??""),r.otherDeduction=g((O==null?void 0:O.otherDeduction)??"",(O==null?void 0:O.currency)??""),r.totalAmount=g((O==null?void 0:O.totalAmount)??"",(O==null?void 0:O.currency)??""),r.totalTaxs=g((O==null?void 0:O.totalTaxs)??"",(O==null?void 0:O.currency)??""),r.currency=(O==null?void 0:O.currency)??"",r.payType=(O==null?void 0:O.payType)??"",r.etTag=(O==null?void 0:O.etTag)??"",r.remarkInfo=(O==null?void 0:O.remarkInfo)??"",r.creditCard=(O==null?void 0:O.creditCard)??"",r.otherDeductionRate=g((O==null?void 0:O.otherDeductionRate)??"",(O==null?void 0:O.currency)??""),r.commisionRate=g((O==null?void 0:O.commisionRate)??"",(O==null?void 0:O.currency)??""),ne();const fe=await se();o("validateChange",a.ticketIndex,fe)});const F=()=>{r.commision=g(r.commision,r==null?void 0:r.currency),r.taxs=B(r.taxs,r==null?void 0:r.currency),r.otherDeduction=g(r==null?void 0:r.otherDeduction,r==null?void 0:r.currency),r.totalAmount=g(r==null?void 0:r.totalAmount,r==null?void 0:r.currency),r.totalTaxs=g(r==null?void 0:r.totalTaxs,r==null?void 0:r.currency),r.otherDeductionRate=g((r==null?void 0:r.otherDeductionRate)??"",r==null?void 0:r.currency),r.commisionRate=g(r==null?void 0:r.commisionRate,r==null?void 0:r.currency)},de=()=>{var O,fe,Y,Te,V,ge,me,Re,Oe,we;((O=C.value)!=null&&O.includes("$$$")||(fe=C.value)!=null&&fe.includes("BSP"))&&(R.value.push(y.BSP),R.value.push(y.GPBSP)),!((Y=C.value)!=null&&Y.includes("BSP"))&&((Te=C.value)!=null&&Te.includes("GP"))&&R.value.push(y.GPBSP),((V=C.value)!=null&&V.includes("$$$")||(ge=C.value)!=null&&ge.includes("BOP"))&&R.value.push(y.BOPBSP),((me=C.value)!=null&&me.includes("$$$")||(Re=C.value)!=null&&Re.includes("CDS"))&&(R.value.push(y.CDS),R.value.push(y.GPCDS)),((Oe=C.value)!=null&&Oe.includes("$$$")||(we=C.value)!=null&&we.includes("本票"))&&R.value.push(y.ARL),r.ticketManagementOrganizationCode=v(r.ticketManagementOrganizationCode)};return st(()=>{F(),Nt(r==null?void 0:r.currency)?le(""):b(),r.name=w(r.name),de(),Q()}),{refundFormRef:d,refundFormData:r,fullscreenLoading:_,FORM_RULES:oe,MAX_TAX_NUM:x,reuseValue:k,addTax:D,checkTax:ie,calcAmount:he,changePayType:ce,bindPaymentValue:L,deliverValid:Q,getEditRefundData:S,getTaxAll:J,changeReuseTicket:z,commisionRateChange:N,isInternational:P,ticketOrganizationList:R,isShowPrintNo:$,isClient:p,openTicketDetailWindow:i}},on=a=>(bt("data-v-ce9fbf31"),a=a(),xt(),a),Ii={class:"refund-form"},Ui={class:"relative"},Qi={class:"h-[24px] my-[10px] flex justify-center items-center text-gray-2 text-[16px] font-bold"},qi={key:0,class:"text-gray-3 text-xs absolute top-0 right-[0]"},zi={class:"w-full flex-col justify-start items-start inline-flex border-b border-dashed border-gray-6"},Gi={class:"self-stretch justify-start items-start gap-5 inline-flex"},Hi={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Wi={class:"w-[84px] text-gray-3 text-xs shrink-0"},Yi=on(()=>t("div",{class:"justify-start items-start flex text-gray-2 text-xs font-bold"},"-",-1)),Ki={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Xi={class:"w-[84px] text-gray-3 text-xs shrink-0"},Ji=on(()=>t("div",{class:"justify-start items-start flex text-gray-2 text-xs font-bold"},"-",-1)),Zi={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] ticketManagementOrganizationCode"},el={key:1,class:"inline-block w-[12px]"},tl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},nl={class:"w-[84px] text-gray-3 text-xs shrink-0"},al={key:0,class:"justify-start items-start flex text-gray-2 text-xs font-bold"},sl={key:1,class:"justify-start items-start flex text-gray-2 text-xs font-bold"},ol={class:"self-stretch justify-start items-start gap-5 inline-flex"},il={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},ll={class:"w-[84px] text-gray-3 text-xs shrink-0"},rl={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},cl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},ul={class:"w-[84px] text-gray-3 text-xs shrink-0"},dl={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},pl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},fl={class:"w-[84px] text-gray-3 text-xs shrink-0"},ml={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},gl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},kl={class:"w-[84px] text-gray-3 text-xs shrink-0"},yl={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},hl={class:"w-full flex-col justify-start items-start inline-flex border-b border-dashed border-gray-6 mt-[10px]"},vl={class:"self-stretch justify-start items-start gap-5 inline-flex"},_l={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},bl={class:"w-[84px] text-gray-3 text-xs shrink-0"},xl={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},Tl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},$l={class:"w-[84px] text-gray-3 text-xs shrink-0"},Nl={class:"text-brand-2 text-xs font-normal leading-tight cursor-pointer"},Rl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Cl={class:"text-gray-3 text-xs shrink-0 w-[84px]"},wl={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},Al=on(()=>t("div",{class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},null,-1)),Sl={class:"self-stretch justify-start items-start gap-5 inline-flex"},Dl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] passenger-name"},Pl={class:"grow shrink basis-0 min-h-[32px] justify-start gap-1 flex mb-[10px]"},Ol={class:"w-[84px] text-gray-3 text-xs h-[32px] flex items-center shrink-0"},El={class:"justify-start items-start text-gray-2 text-xs font-bold"},Fl={class:"justify-start gap-4 flex h-[20px]"},Vl={class:"text-gray-2 text-xs leading-tight"},Ml={class:"flex items-center h-[20px] text-gray-3 font-normal text-xs leading-tight"},jl={class:"justify-start gap-4 flex h-[20px]"},Ll={class:"text-gray-2 text-xs leading-tight"},Bl={class:"justify-start items-start gap-5 inline-flex"},Il={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] require amount"},Ul={key:0,class:"not-required-tip"},Ql={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] require pay-type"},ql={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] require refund-type"},zl={class:"w-full flex-col justify-start items-start inline-flex border-b border-dashed border-gray-6 mt-[10px]"},Gl={class:"self-stretch justify-start items-start gap-5 inline-flex"},Hl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Wl={class:"w-full mb-[10px]"},Yl={class:"flex justify-between text-gray-3 text-xs leading-[20px] mb-[6px]"},Kl={class:"ml-[20px]"},Xl={class:"text-gray-2 font-[700]"},Jl={class:"w-full grow self-stretch justify-start items-start gap-[10px] gap-x-[20px] flex flex-wrap"},Zl={class:"w-[20px] text-gray-3 text-xs shrink-0 leading-8"},er={class:"w-[40px] mr-[6px] shrink-0"},tr={class:"w-full flex-col justify-start items-start inline-flex mt-[10px]"},nr={class:"self-stretch justify-start items-start gap-5 inline-flex"},ar={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},sr={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] rate"},or=on(()=>t("div",{class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight ml-[4px]"},"%",-1)),ir={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] rate"},lr=on(()=>t("div",{class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight ml-[4px]"},"%",-1)),rr={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},cr={class:"self-stretch justify-start items-start gap-5 inline-flex"},ur={class:"grow shrink basis-0 h-[32px] justify-start items-center flex mb-[10px]"},dr={class:"w-[72px] text-gray-3 text-xs shrink-0"},pr=on(()=>t("span",{class:"iconfont icon-info-circle-line absolute ml-[4px]"},null,-1)),fr={class:"justify-start items-center flex text-gray-2 text-xs relative"},mr={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] rate"},gr={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},kr=on(()=>t("div",{class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},null,-1)),yr={class:"ml-[260px] text-yellow-1"},hr=je({__name:"ManualRefundForm",props:{refundData:{},ticketIndex:{},ticketList:{},reuseTicketInfo:{},reuseTimes:{},currentTicket:{},isAuthToShowWindow:{type:Boolean}},emits:["validateChange","reuseTicket"],setup(a,{expose:o,emit:u}){const h=a,f=u,{refundFormRef:p,refundFormData:i,fullscreenLoading:_,FORM_RULES:d,MAX_TAX_NUM:x,reuseValue:T,addTax:k,checkTax:R,calcAmount:v,changePayType:r,bindPaymentValue:y,deliverValid:C,getEditRefundData:$,getTaxAll:P,changeReuseTicket:M,commisionRateChange:X,isInternational:ue,ticketOrganizationList:E,isShowPrintNo:j,isClient:oe,openTicketDetailWindow:D}=Bi(h,f);return o({getEditRefundData:$}),(b,N)=>{const ne=tn,ie=nn,U=Je,A=ot,le=gt,he=an,ce=An,L=Ke,se=yt,Q=it,S=kt;return s(),m("div",Ii,[t("div",Ui,[Qe((s(),m("div",Qi,[K(n(b.$t("app.agentTicketRefund.refundInformationForm")),1)])),[[S,e(_),void 0,{fullscreen:!0,lock:!0}]]),b.ticketList.length>1?(s(),m("div",qi,[K(n(b.$t("app.agentTicketRefund.reuse"))+" ",1),c(ie,{modelValue:e(T),"onUpdate:modelValue":N[0]||(N[0]=G=>Ve(T)?T.value=G:null),class:"reuse w-[174px] mx-[6px]",onChange:e(M)},{default:l(()=>[(s(),ae(ne,{key:0,label:"",value:""})),(s(!0),m(ye,null,Ne(b.ticketList.filter(G=>{var J;return G!==((J=e(i))==null?void 0:J.ticketNo)}),(G,J)=>(s(),ae(ne,{key:J+1,label:G,value:G},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"]),K(" "+n(b.$t("app.agentTicketRefund.refundInfo")),1)])):Z("",!0)]),c(Q,{ref_key:"refundFormRef",ref:p,model:e(i),"require-asterisk-position":"right"},{default:l(()=>{var G,J,z,w,g,B,F,de,ve,O,fe;return[t("div",zi,[t("div",Gi,[t("div",Hi,[t("div",Wi,n(b.$t("app.agentTicketRefund.refundTicketNumber")),1),Yi]),t("div",Ki,[t("div",Xi,n(b.$t("app.agentTicketRefund.rtType")),1),Ji]),t("div",Zi,[c(A,{label:b.$t("app.refundForm.ticketManagementOrganizationCode"),prop:"ticketManagementOrganizationCode",rules:e(d).ticketManagementOrganizationCode},{default:l(()=>[c(ie,{modelValue:e(i).ticketManagementOrganizationCode,"onUpdate:modelValue":N[1]||(N[1]=Y=>e(i).ticketManagementOrganizationCode=Y),disabled:!e(i).ticketManagementOrganizationCode,placeholder:e(i).ticketManagementOrganizationCode?"":b.$t("app.agentTicketQuery.noData")},{default:l(()=>[(s(!0),m(ye,null,Ne(e(E),Y=>(s(),ae(ne,{key:Y.value,label:Y.label,value:Y.value},{default:l(()=>[t("span",null,[e(i).ticketManagementOrganizationCode===Y.value?(s(),ae(U,{key:0,size:12,class:"iconfont icon-right-line"})):(s(),m("span",el))]),K(" "+n(Y.label),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled","placeholder"])]),_:1},8,["label","rules"])]),t("div",tl,[t("div",nl,n(b.$t("app.agentTicketRefund.prntNo")),1),e(j)?(s(),m("div",al,n(e(i).ticketManagementOrganizationCode==="CDS"?"-":((G=e(i))==null?void 0:G.printNo)??"-"),1)):(s(),m("div",sl,"-"))])]),t("div",ol,[t("div",il,[t("div",ll,n(b.$t("app.agentTicketRefund.refundAgent")),1),t("div",rl,n(((J=e(i))==null?void 0:J.agent)??"-"),1)]),t("div",cl,[t("div",ul,n(b.$t("app.agentTicketRefund.refundIataNo")),1),t("div",dl,n(((z=e(i))==null?void 0:z.iata)??"-"),1)]),t("div",pl,[t("div",fl,n(b.$t("app.agentTicketRefund.refundOffice")),1),t("div",ml,n(((w=e(i))==null?void 0:w.office)??"-"),1)]),t("div",gl,[t("div",kl,n(b.$t("app.agentTicketRefund.refundDate")),1),t("div",yl,n(((g=e(i))==null?void 0:g.refundDate)??"-"),1)])])]),t("div",hl,[t("div",vl,[t("div",_l,[t("div",bl,n(b.$t("app.agentTicketRefund.refundAirlineSettlementCode")),1),t("div",xl,n(((B=e(i))==null?void 0:B.airline)??"-"),1)]),t("div",Tl,[t("div",$l,n(b.$t("app.agentTicketRefund.refundTicketNo")),1),e(oe)&&b.isAuthToShowWindow?(s(),m("div",{key:0,onClick:N[2]||(N[2]=Y=>e(D)(e(i).ticketNo,e(i).secondFactor.secondFactorCode,e(i).secondFactor.secondFactorValue))},[t("span",Nl,n(e(ht)(((F=e(i))==null?void 0:F.ticketNo)??"")),1)])):(s(),ae(mn,{key:1,"is-international":e(ue),"second-factor":e(i).secondFactor,"tkt-index":((de=e(i))==null?void 0:de.ticketNo)??"-","ticket-number":((ve=e(i))==null?void 0:ve.ticketNo)??"-","conjunction-ticket-nos":e(i).conjunctionTicketNos},null,8,["is-international","second-factor","tkt-index","ticket-number","conjunction-ticket-nos"]))]),t("div",Rl,[t("div",Cl,n(b.$t("app.agentTicketRefund.numberOfCombinedTickets")),1),t("div",wl,n(e(i).tktType==="I"?(O=e(i))==null?void 0:O.conjunction:1),1)]),Al]),t("div",Sl,[t("div",Dl,[c(A,{label:b.$t("app.agentTicketRefund.passName"),prop:"name",rules:e(d).psdName},{default:l(()=>[c(le,{modelValue:e(i).name,"onUpdate:modelValue":N[3]||(N[3]=Y=>e(i).name=Y),clearable:"",onInput:N[4]||(N[4]=Y=>e(i).name=e(i).name.toUpperCase()),onBlur:e(C)},null,8,["modelValue","onBlur"])]),_:1},8,["label","rules"])]),t("div",Pl,[t("div",Ol,n(b.$t("app.agentTicketRefund.refundSeg")),1),t("div",El,[e(i).tktType==="D"?(s(!0),m(ye,{key:0},Ne([...e(i).segment.values()][0],(Y,Te)=>(s(),m("div",{key:Te,class:"inline-block leading-[32px] dom-tikcet"},[c(A,{prop:`segment[${Te}]`},{default:l(()=>[t("div",Fl,[c(he,{modelValue:Y.isAllowCheck,"onUpdate:modelValue":V=>Y.isAllowCheck=V,disabled:"",label:Y},{default:l(()=>[t("div",Vl,n((Y==null?void 0:Y.departureCode)??"")+"-"+n((Y==null?void 0:Y.arriveCode)??""),1)]),_:2},1032,["modelValue","onUpdate:modelValue","label"]),t("div",{class:Se([Y.isAllowCheck?"text-brand-2":"text-gray-5","text-[14px] justify-center items-center flex relative right-[12px] h-[16px] w-[16px] top-[5px]"])},n(b.$t(`app.queryRefunds.number_${Te+1}`)),3)])]),_:2},1032,["prop"])]))),128)):(s(!0),m(ye,{key:1},Ne([...e(i).segment.values()],(Y,Te)=>(s(),m("div",{key:Te,class:Se({"mb-[10px]":Te<[...e(i).segment.values()].length-1})},[t("div",Ml,n(b.$t(`app.agentTicketRefund.couponNo_${Te+1}`)),1),c(A,{prop:`segment[${Te}]`},{default:l(()=>[t("div",jl,[(s(!0),m(ye,null,Ne(Y,(V,ge)=>(s(),m("div",{key:ge+"segmet",class:"justify-start items-center gap-[2px] flex h-[20px]"},[c(he,{modelValue:V.isAllowCheck,"onUpdate:modelValue":me=>V.isAllowCheck=me,label:V,disabled:""},{default:l(()=>[t("div",Ll,n(V.departureCode)+"-"+n(V.arriveCode),1)]),_:2},1032,["modelValue","onUpdate:modelValue","label"]),t("div",{class:Se([V.isAllowCheck?"text-brand-2":"text-gray-5","text-[14px] justify-center items-center flex relative top-[-1px] h-[20px]"])},n(b.$t(`app.queryRefunds.number_${ge+1}`)),3)]))),128))])]),_:2},1032,["prop"])],2))),128))])])]),t("div",Bl,[t("div",Il,[c(A,{label:b.$t("app.agentTicketRefund.totalTicketAmount"),prop:"totalAmount",rules:e(d).totalAmount,class:Se({"not-required-container":!e(i).totalAmount})},{default:l(()=>[c(le,{modelValue:e(i).totalAmount,"onUpdate:modelValue":N[5]||(N[5]=Y=>e(i).totalAmount=Y),modelModifiers:{trim:!0},clearable:"",onBlur:N[6]||(N[6]=Y=>e(v)("totalAmount"))},null,8,["modelValue"]),e(i).totalAmount?Z("",!0):(s(),m("div",Ul,n(b.$t("app.agentTicketRefund.totalAmountNotRequired")),1))]),_:1},8,["label","rules","class"])]),t("div",Ql,[c(A,{label:b.$t("app.agentTicketRefund.refundPayType"),prop:"payType",rules:e(d).payType},{default:l(()=>[c(ie,{modelValue:e(i).payType,"onUpdate:modelValue":N[7]||(N[7]=Y=>e(i).payType=Y),modelModifiers:{trim:!0},filterable:"","allow-create":"","default-first-option":"","automatic-dropdown":"",placeholder:b.$t("app.agentTicketRefund.paymentSel"),clearable:"",onChange:e(r),onBlur:e(y)},{default:l(()=>[(s(!0),m(ye,null,Ne(e(Dn),(Y,Te)=>(s(),ae(ne,{key:Te,label:Y.label,value:Y.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder","onChange","onBlur"])]),_:1},8,["label","rules"])]),t("div",ql,[c(A,{label:b.$t("app.agentTicketRefund.refundCurrency"),prop:"currency",rules:e(d).currency},{default:l(()=>[c(le,{modelValue:e(i).currency,"onUpdate:modelValue":N[8]||(N[8]=Y=>e(i).currency=Y),modelModifiers:{trim:!0},clearable:"",onInput:N[9]||(N[9]=Y=>e(i).currency=e(i).currency.toUpperCase()),onBlur:e(C)},null,8,["modelValue","onBlur"])]),_:1},8,["label","rules"])])])]),t("div",zl,[t("div",Gl,[t("div",Hl,[c(A,{label:b.$t("app.agentTicketRefund.etTag")},{default:l(()=>[c(ce,{modelValue:e(i).etTag,"onUpdate:modelValue":N[10]||(N[10]=Y=>e(i).etTag=Y),"inline-prompt":"","active-text":"Y","inactive-text":"N","active-value":"1","inactive-value":"0"},null,8,["modelValue"])]),_:1},8,["label"])])]),t("div",Wl,[t("div",Yl,[t("div",null,[t("span",null,n(b.$t("app.agentTicketRefund.refundTax")),1),t("span",Kl,n(b.$t("app.fare.singleFare.totalTax")),1),t("span",Xl," "+n(e(i).currency)+" "+n(e(i).totalTaxs),1)]),t("div",null,[c(L,{link:"",type:"primary","data-gid":"091Q0203",size:"small",onClick:e(P)},{default:l(()=>[K(n(b.$t("app.agentTicketRefund.rtktTax")),1)]),_:1},8,["onClick"]),c(L,{link:"",type:"primary",size:"small",disabled:((fe=e(i).taxs)==null?void 0:fe.length)===e(x),onClick:e(k)},{default:l(()=>[K(n(b.$t("app.agentTicketRefund.addTaxs")),1)]),_:1},8,["disabled","onClick"])])]),t("div",Jl,[(s(!0),m(ye,null,Ne(e(i).taxs,(Y,Te)=>(s(),m("div",{key:Te,class:"grow shrink-0 basis-0 h-[32px] justify-start flex w-[calc((100%_-_80px)_/_5)] min-w-[calc((100%_-_80px)_/_5)] max-w-[calc((100%_-_80px)_/_5)]"},[t("div",Zl,n(Te+1),1),t("div",er,[c(A,{prop:"taxs."+Te+".name",rules:e(d).taxName},{default:l(()=>[c(le,{modelValue:Y.name,"onUpdate:modelValue":V=>Y.name=V,modelModifiers:{trim:!0},onInput:V=>Y.name=Y.name.toUpperCase(),onBlur:e(R)},null,8,["modelValue","onUpdate:modelValue","onInput","onBlur"])]),_:2},1032,["prop","rules"])]),c(A,{prop:"taxs."+Te+".value",rules:e(d).taxValue},{default:l(()=>[c(le,{modelValue:Y.value,"onUpdate:modelValue":V=>Y.value=V,modelModifiers:{trim:!0},onBlur:e(R)},null,8,["modelValue","onUpdate:modelValue","onBlur"])]),_:2},1032,["prop","rules"])]))),128))])])]),t("div",tr,[t("div",nr,[t("div",ar,[c(A,{label:b.$t("app.agentTicketRefund.commision"),prop:"commision",rules:e(d).commision},{default:l(()=>[c(le,{modelValue:e(i).commision,"onUpdate:modelValue":N[11]||(N[11]=Y=>e(i).commision=Y),modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",onBlur:N[12]||(N[12]=Y=>e(v)("commision"))},null,8,["modelValue"])]),_:1},8,["label","rules"])]),t("div",sr,[c(A,{label:b.$t("app.agentTicketRefund.commissionRate"),prop:"commisionRate",rules:e(d).commisionRate},{default:l(()=>[c(le,{modelValue:e(i).commisionRate,"onUpdate:modelValue":N[13]||(N[13]=Y=>e(i).commisionRate=Y),modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",onBlur:N[14]||(N[14]=Y=>e(v)("commisionRate")),onInput:e(X)},null,8,["modelValue","onInput"]),or]),_:1},8,["label","rules"])]),t("div",ir,[c(A,{label:b.$t("app.agentTicketRefund.inputOtherDeductionRate"),prop:"otherDeductionRate",rules:e(d).otherDeductionRate},{default:l(()=>[c(le,{modelValue:e(i).otherDeductionRate,"onUpdate:modelValue":N[15]||(N[15]=Y=>e(i).otherDeductionRate=Y),modelModifiers:{trim:!0},placeholder:"1-100",onBlur:N[16]||(N[16]=Y=>e(v)("otherDeductionRate"))},null,8,["modelValue"]),lr]),_:1},8,["label","rules"])]),t("div",rr,[c(A,{label:b.$t("app.agentTicketRefund.otherDeduction"),prop:"otherDeduction",rules:e(d).totalAmount},{default:l(()=>[c(le,{modelValue:e(i).otherDeduction,"onUpdate:modelValue":N[17]||(N[17]=Y=>e(i).otherDeduction=Y),modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",onBlur:N[18]||(N[18]=Y=>e(v)("otherDeduction"))},null,8,["modelValue"])]),_:1},8,["label","rules"])])]),t("div",cr,[t("div",ur,[t("div",dr,[K(n(b.$t("app.agentTicketRefund.remark"))+" ",1),c(se,{placemant:"top",content:b.$t("app.agentTicketRefund.remarkTips")},{default:l(()=>[pr]),_:1},8,["content"])]),t("div",fr,[c(A,{prop:"remarkInfo",rules:e(d).remarkInfo},{default:l(()=>[c(le,{modelValue:e(i).remarkInfo,"onUpdate:modelValue":N[19]||(N[19]=Y=>e(i).remarkInfo=Y),clearable:"",placeholder:b.$t("app.agentTicketRefund.remarkPleaceHolder"),onInput:N[20]||(N[20]=Y=>e(i).remarkInfo=e(i).remarkInfo.toUpperCase()),onBlur:e(C)},null,8,["modelValue","placeholder","onBlur"])]),_:1},8,["rules"])])]),t("div",mr,[c(A,{label:b.$t("app.agentTicketRefund.totalRefund"),prop:"netRefund",rules:e(d).netRefund},{default:l(()=>[c(le,{modelValue:e(i).netRefund,"onUpdate:modelValue":N[21]||(N[21]=Y=>e(i).netRefund=Y),modelModifiers:{trim:!0},clearable:"",onBlur:e(C)},null,8,["modelValue","onBlur"])]),_:1},8,["label","rules"])]),t("div",gr,[c(A,{label:b.$t("app.agentTicketRefund.creditCardInfo"),prop:"creditCard",rules:e(d).creditCard},{default:l(()=>[c(le,{modelValue:e(i).creditCard,"onUpdate:modelValue":N[22]||(N[22]=Y=>e(i).creditCard=Y),modelModifiers:{trim:!0},clearable:"",onInput:N[23]||(N[23]=Y=>e(i).creditCard=e(i).creditCard.toUpperCase()),onBlur:e(C)},null,8,["modelValue","onBlur"])]),_:1},8,["label","rules"])]),kr]),t("div",yr,n(b.$t("app.agentTicketRefund.netRefundTip")),1)])]}),_:1},8,["model"])])}}});const vr=at(hr,[["__scopeId","data-v-ce9fbf31"]]),_r=(a,o)=>{const u=H(0),h=H([]);return{currentTicketIndex:u,formRefs:h,validateChange:(_,d)=>{o("validateChange",a.passengerIndex,_,d)},getEditRefundDatas:()=>(h.value??[]).map(d=>d.getEditRefundData()),reuseTicket:_=>{o("reuseTicket",_)}}},br={class:"border px-[8px] py-[4px] rounded-[1px] cursor-pointer ticket-number"},xr=je({__name:"RefundItem",props:{ticketInfos:{},passengerIndex:{},ticketList:{},reuseTicketInfo:{},reuseTimes:{},activeName:{},currentTicket:{},isAuthToShowWindow:{type:Boolean}},emits:["validateChange","reuseTicket"],setup(a,{expose:o,emit:u}){const h=a,f=u,{currentTicketIndex:p,formRefs:i,getEditRefundDatas:_,validateChange:d,reuseTicket:x}=_r(h,f);return o({getEditRefundDatas:_,currentTicketIndex:p}),(T,k)=>{const R=Je,v=za,r=Ga;return s(),ae(r,{modelValue:e(p),"onUpdate:modelValue":k[0]||(k[0]=y=>Ve(p)?p.value=y:null),class:"ticket-container"},{default:l(()=>[(s(!0),m(ye,null,Ne(T.ticketInfos,(y,C)=>(s(),ae(v,{key:y.ticketNo,label:y.ticketNo,name:C},{label:l(()=>[t("div",br,[K(n(e(ht)(y.ticketNo))+" ",1),Qe(c(R,{class:"pass-icon"},{default:l(()=>[c(e(Mn))]),_:2},1536),[[vn,y.validate]])])]),default:l(()=>[t("div",null,[(s(),ae(vr,{ref_for:!0,ref:$=>{$&&(e(i)[C]=$)},key:C,"refund-data":y.refundData,"ticket-index":C,"ticket-list":T.ticketList,"reuse-ticket-info":T.reuseTicketInfo,"reuse-times":T.reuseTimes,"current-ticket":T.currentTicket,"is-auth-to-show-window":T.isAuthToShowWindow,onValidateChange:e(d),onReuseTicket:e(x)},null,8,["refund-data","ticket-index","ticket-list","reuse-ticket-info","reuse-times","current-ticket","is-auth-to-show-window","onValidateChange","onReuseTicket"]))])]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])}}});const Tr=at(xr,[["__scopeId","data-v-fd19d875"]]),$r=Me(()=>{const a=Ct().getters.userPreferences.backfieldEnName;return a===null||a}),Nr=a=>$r.value&&!Ws.test(a)?a:"",Rr=a=>{let o=[];return o=a.map(u=>({name:u.name,value:u.value})),o.length<10?o.concat(new Array(10-o.length).fill({name:"",value:""})).map(u=>({...u})):o},Ta=(a,o,u,h,f)=>{var k,R;const p=et().format("DDMMMYY/HHmm").toUpperCase();let i="";if((k=a.ticket)!=null&&k.payType){const v=a.ticket.payType.toUpperCase();v.startsWith("CASH")?i="CASH":(v.startsWith("CC")||v.startsWith("TC"))&&(i="TC")}const _=(v,r)=>v&&v!=="0.00"?v:v==="0.00"&&r!=="0.00"?"":v??"",d=(v,r)=>r&&r!=="0.00"&&v==="0.00"?r:"",x=(v,r,y)=>{const C=qe(Number(v),Number(r??"0"));return Nt(y)?C.toString():qe(Number(v),Number(r??"0")).toFixed(2)};return{iata:a.iata,agent:a.agent,office:a.office,volunteer:"VOLUNTEER_MANUAL",createUser:a.operator,printNo:a.printerNo,marketAirline:a.ticket.marketAirline,currency:a.ticket.currency,name:Nr(a.ticket.passengerNameSuffix??""),psgType:a.ticket.psgType,etTag:(R=a.ticket)==null?void 0:R.etTag,remark:"",remarkCode:"",remarkInfo:"",creditCard:"",conjunction:a.conjunction,airline:a.ticket.airline,tktType:a.ticket.tktType,payType:i,secondFactor:a.ticket.secondFactor,ticketNo:h,totalAmount:Number(a.conjunction)>1&&a.ticket.tktType==="D"?"":a.ticket.totalAmount,commision:_(a.ticket.commission,a.ticket.commissionRate),commisionRate:d(a.ticket.commission,a.ticket.commissionRate),otherDeduction:Nt(a.ticket.currency)?"0":"0.00",otherDeductionRate:"",netRefund:x(a.ticket.totalAmount,a.ticket.totalTaxs,a.ticket.currency),totalTaxs:a.ticket.totalTaxs??"0",taxs:Rr(a.ticket.taxs??[]),rate:"0",receiptPrinted:a.receiptPrinted,segment:u,crsPnrNo:a.ticket.crsPnrNo,pnr:a.ticket.pnr,isCoupon:a.ticket.isCoupon,isDragonBoatOffice:o,refundDate:p,conjunctionTicketNos:f,ticketManagementOrganizationCode:a.ticketManagementOrganizationCode??""}},Cr=a=>[...a.segment.values()].filter(u=>u.some(h=>h.isAllowCheck)).length.toString(),wr=(a,o)=>a.map(h=>({modificationType:"ONLY_REFUND",prntNo:h.ticketManagementOrganizationCode==="CDS"?"":h.printNo,ticketManagementOrganizationCode:h.ticketManagementOrganizationCode??"",resultpre:{amount:{commision:h.commision!==""&&Number(h.commision)>0?h.commision:"0",commisionRate:h.commisionRate??"",netRefund:h.netRefund,otherDeduction:h.otherDeduction||"0",taxs:h.taxs.filter(f=>f.value!==""),totalAmount:h.totalAmount||"",totalTaxs:h.totalTaxs},conjunction:h.tktType==="D"?h.conjunction:Cr(h),creditCard:h.creditCard,isCoupon:h.isCoupon,office:h.office,operator:h.createUser,remark:h.remarkInfo??"",segList:[],ticket:{airline:h.airline,crsPnrNo:o||h.crsPnrNo,currency:h.currency,etTag:h.etTag,marketAirline:h.marketAirline,name:Ft.encode(h.name.trim()),payType:h.payType.toUpperCase(),pnr:h.pnr,psgType:h.psgType,segment:[...h.segment.values()].flatMap(f=>f.map(p=>({arriveCode:p.arriveCode,departureCode:p.departureCode,e8Rph:p.e8Rph,isAble:p.isAble,rph:p.rph,tktTag:p.tktTag,isCheck:p.isAble}))),ticketNo:h.tktType==="I"?[...h.segment.keys()].join("-"):h.ticketNo??"",secondFactor:h.secondFactor,tktType:h.tktType},volunteer:"NON_VOLUNTEER_MANUAL"}})),Ar=(a,o)=>{const{t:u}=We(),h=H(0),f=H(0),p=H([]),i=H([{label:"ONLY_REFUND",text:u("app.agentTicketRefund.onlyRt")}]),_=H(i.value[0].label),d=Me(()=>{const w=[];return p.value.forEach(g=>(g.ticketInfos??[]).forEach(B=>w.push(B.ticketNo??""))),w}),x=H(),T=H(0),k=H(!1),R=H([]),v=Ae("N"),r=Ae("N"),y=Ae(a.pnrNo),C=Ae(!1),$=Ae(""),P=H([]),M=()=>{o("update:modelValue",!1)},X=(w,g,B)=>{p.value[w].ticketInfos[g].validate=B},ue=()=>{if(a.pnrNo){if(_.value==="XE_PNR")return v.value==="S"?u("app.agentTicketRefund.refundCancelPnrSuccess",{pnr:a.pnrNo}):u("app.agentTicketRefund.refundCancelPnrFail",{pnr:a.pnrNo});if(_.value==="SPLIT_AND_XE_PNR"){if(r.value==="N")return v.value==="S"?u("app.agentTicketRefund.refundCancelPnrSuccess",{pnr:y.value}):u("app.agentTicketRefund.refundCancelPnrFail",{pnr:y.value});if(r.value==="F")return v.value==="S"?u("app.agentTicketRefund.refundCancelPnrSuccessXePassengerFail",{pnr:y.value}):u("app.agentTicketRefund.refundCancelPnrFailXePassengerFail",{pnr:y.value});if(r.value==="S")return v.value==="S"?u("app.agentTicketRefund.refundCancelPnrSuccessXePassengerSuccess",{pnr:y.value}):u("app.agentTicketRefund.refundCancelPnrFailXePassengerSuccess",{pnr:y.value})}if(_.value==="DELETE_PASSENGER")return r.value==="S"?u("app.agentTicketRefund.refundXePassengerSuccess"):u("app.agentTicketRefund.refundXePassengerFail")}return u("app.agentTicketRefund.refundSuccessManual")},E=()=>ue().replace(u("app.agentTicketRefund.refundSuccessManual"),u("app.agentTicketRefund.refundPartFail")),j=w=>{P.value=(w??[]).map(g=>({ticketNo:g.ticketNo,trfdNo:g.trfdno??""})),o("getTicketTrfdNoDetails",P.value),o("update:isShowTrfdNo",!0)},oe=()=>{const w=[];return R.value.forEach(g=>{w.push(...g.getEditRefundDatas())}),{refundList:wr(w,a.pnrNo)}},D=(w,g)=>{o("getManualRefundAmountDetail",w,g)},b=w=>{const g=ft(a.ticketDetailList);o("update:ticketDetailList",[]),g.forEach(async B=>{var F;await o("queryTicketDetail",((F=B.ticket.segment)==null?void 0:F[0].tktTag)??"",B.ticket.secondFactor,!0,w)})},N=(w,g,B)=>{D(w,g),b(B),M()},ne=()=>{ie(Pe("091Q0202"))},ie=async w=>{var F,de;const g=Date.now();let B=null;try{k.value=!0;const ve=oe(),O=(de=(F=await Oo(ve,w))==null?void 0:F.data)==null?void 0:de.value;if(B=Date.now(),Ss(g,B,"手工退票"),(O==null?void 0:O.data.status)==="ALL_SUCCESS"&&(await cn(ue()),N(ve.refundList,!0,w)),(O==null?void 0:O.data.status)==="PARTIAL_SUCCESS"){const{passengerStatuses:fe}=O.data;await va(fe??[],E(),"error"),N(ve.refundList,!1,w)}j((O==null?void 0:O.data.passengerStatuses)??[])}finally{k.value=!1}},U=w=>{if(!w){x.value=null;return}const g=[];R.value.forEach(B=>{g.push(...B.getEditRefundDatas())}),x.value=ft(g.find(B=>B.ticketNo===w)),T.value+=1},A=()=>{const w=(p.value??[]).every(g=>(g.ticketInfos??[]).every(B=>B.validate));if(!w){const g=(p.value??[]).flatMap(B=>(B.ticketInfos??[]).map(F=>({ticketNo:F.ticketNo??"",passengerName:B.name??"",success:F.validate})));va(g,u("app.agentTicketRefund.pleaseCompleteInfo"),"warn")}return w},le=()=>{$.value=a.isXepnr?u("app.agentTicketRefund.notDeletePnrTip"):u("app.agentTicketRefund.notDeletePassengerTip"),C.value=!0},he=()=>{const w=[];return(a.allPassengers??[]).map(g=>{g.isChecked&&w.push({fullName:g.passengerName,paxId:Number(g.index.replace("P","")),unMinor:!1,unMinorAge:0})}),w},ce=()=>({orderId:"",count:0,passengerRecordLocator:a.pnrNo,travellers:he()}),L=async w=>{var g,B;try{k.value=!0;const F=ce(),de=await Jn(F,w);y.value=((B=(g=de.data.value)==null?void 0:g.splitedOrder)==null?void 0:B.passengerRecordLocator)??""}finally{k.value=!1}},se=()=>{const w=[];return _.value!=="XE_PNR"&&(a.ticketDetailList??[]).forEach(g=>{g.ticket.psgType==="INF"&&w.push({name:Ft.encode(g.ticket.name),psgType:g.ticket.psgType,ticketNo:g.ticket.ticketNo})}),w},Q=()=>({pnrNo:_.value!=="XE_PNR"?a.pnrNo:"",xePnr:_.value!=="DELETE_PASSENGER"?y.value:"",passengerInfoList:se()}),S=async w=>{var g,B;try{if(k.value=!0,a.pnrNo){const F=Q(),de=await Zn(F,w);v.value=((g=de.data.value)==null?void 0:g.xePnrExecutionStatus)??"N",r.value=((B=de.data.value)==null?void 0:B.deleteInfantExecutionStatus)??"N"}await ie(w)}finally{k.value=!1}},G=async()=>{if(!A())return;const w=Pe("091Q0202");if(_.value==="ONLY_REFUND"){a.pnrNo?le():ie(w);return}_.value==="SPLIT_AND_XE_PNR"&&await L(w),await S(w)},J=w=>[...new Set((w??[]).map(g=>g.tktTag))],z=()=>((a==null?void 0:a.ticketDetailList)??[]).map(w=>{const g=[],B=J(w.ticket.segment);if(w.ticket.tktType==="I"){const F=new Map;B.forEach(O=>{const fe=(w.ticket.segment??[]).filter(Y=>Y.tktTag===O);F.set(O,fe)});const de=(B==null?void 0:B.length)>1?B:[],ve=Ta(w,a.isDragonBoatOffice,F,w.ticket.ticketNo,de);g.push({validate:!0,ticketNo:w.ticket.ticketNo,refundData:ve})}else B.forEach(F=>{const de=(w.ticket.segment??[]).filter(Y=>Y.tktTag===F);if(!(de??[]).some(Y=>Ha.includes(Y.ticketStatus??"")))return;const O=new Map;O.set(F,de);const fe=Ta(w,a.isDragonBoatOffice,O,F,[]);g.push({ticketNo:F,refundData:fe,validate:!0})});return{ticketInfos:g,tktType:w.ticket.tktType,psgType:w.ticket.psgType,name:w.ticket.passengerNameSuffix??""}});return st(()=>{p.value=z()}),As(()=>{R.value=[]}),{tipMessage:$,tipDialogVisible:C,passengers:p,activeName:h,refundOperation:i,closeDialog:M,refundType:_,refundItemRefs:R,validateChange:X,submitRefund:G,fullscreenLoading:k,onlyRefundWithGid:ne,ticketList:d,reuseTicket:U,reuseTicketInfo:x,reuseTimes:T,tabIndex:f}},Ka=a=>(bt("data-v-2639238c"),a=a(),xt(),a),Sr=Ka(()=>t("i",{class:"iconfont icon-close"},null,-1)),Dr=[Sr],Pr={class:"title relative"},Or={class:"text-[16px] max-w-[150px] font-bold cursor-pointer inline-block whitespace-nowrap overflow-hidden text-ellipsis"},Er=Ka(()=>t("span",{class:"line"},null,-1)),Fr={class:"flex justify-center pt-[10px] w-full mt-[10px] footer items-center crs-btn-dialog-ui"},Vr=je({__name:"ManualRefundDialog",props:{packageData:{},allPassengers:{},deviceNum:{},pnrNo:{},tktNo:{},ticketDetailList:{},isXepnr:{type:Boolean},isDragonBoatOffice:{type:Boolean},isAuthToShowWindow:{type:Boolean}},emits:["update:ticketDetailList","update:modelValue","queryTicketDetail","getManualRefundAmountDetail","getTicketTrfdNoDetails","reuseTicket"],setup(a,{emit:o}){const u=a,h=o,{tipMessage:f,tipDialogVisible:p,passengers:i,activeName:_,refundOperation:d,closeDialog:x,refundType:T,refundItemRefs:k,validateChange:R,submitRefund:v,fullscreenLoading:r,onlyRefundWithGid:y,ticketList:C,reuseTicket:$,reuseTicketInfo:P,reuseTimes:M}=Ar(u,h);return(X,ue)=>{const E=yt,j=za,oe=Ga,D=Pt,b=Ot,N=Ke,ne=nt,ie=_n("debounce"),U=kt;return s(),ae(ne,{width:"1040",title:X.$t("app.agentTicketRefund.manualRefundBtn"),"show-close":!1,"close-on-click-modal":!1,class:"manual-refund-dialog","align-center":"true",onClose:e(x)},{default:l(()=>[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:ue[0]||(ue[0]=(...A)=>e(x)&&e(x)(...A))},Dr),Qe((s(),m("div",null,[c(oe,{modelValue:e(_),"onUpdate:modelValue":ue[1]||(ue[1]=A=>Ve(_)?_.value=A:null),class:"tabs-container"},{default:l(()=>[(s(!0),m(ye,null,Ne(e(i),(A,le)=>(s(),ae(j,{key:le,name:le,class:"test"},{label:l(()=>[t("span",Pr,[c(E,{effect:"dark",content:A.name,placement:"top"},{default:l(()=>[t("span",Or,n(A.name),1)]),_:2},1032,["content"]),Er])]),default:l(()=>{var he,ce,L,se,Q,S;return[(s(),ae(Tr,{ref_for:!0,ref:G=>{G&&(e(k)[le]=G)},key:le,"ticket-infos":A.ticketInfos,lazy:!0,"passenger-index":le,"ticket-list":e(C),"reuse-ticket-info":e(P),"reuse-times":e(M),"active-name":e(_),"current-ticket":((S=(Q=(ce=(he=e(i))==null?void 0:he[e(_)])==null?void 0:ce.ticketInfos)==null?void 0:Q[((se=(L=e(k))==null?void 0:L[e(_)??0])==null?void 0:se.currentTicketIndex)??0])==null?void 0:S.ticketNo)??"","is-auth-to-show-window":X.isAuthToShowWindow,onValidateChange:e(R),onReuseTicket:e($)},null,8,["ticket-infos","passenger-index","ticket-list","reuse-ticket-info","reuse-times","active-name","current-ticket","is-auth-to-show-window","onValidateChange","onReuseTicket"]))]}),_:2},1032,["name"]))),128))]),_:1},8,["modelValue"]),t("div",Fr,[c(b,{modelValue:e(T),"onUpdate:modelValue":ue[2]||(ue[2]=A=>Ve(T)?T.value=A:null)},{default:l(()=>[(s(!0),m(ye,null,Ne(e(d),A=>(s(),ae(D,{key:A.label,label:A.label},{default:l(()=>[K(n(A.text),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"]),Qe((s(),ae(N,{type:"primary"},{default:l(()=>[K(n(X.$t("app.agentTicketRefund.refund")),1)]),_:1})),[[ie,e(v)]])])])),[[U,e(r)]]),e(p)?(s(),ae(Li,{key:0,modelValue:e(p),"onUpdate:modelValue":ue[3]||(ue[3]=A=>Ve(p)?p.value=A:null),"global-id":"091Q0202","tip-message":e(f),onExecutionMethod:e(y)},null,8,["modelValue","tip-message","onExecutionMethod"])):Z("",!0)]),_:1},8,["title","onClose"])}}});const Mr=at(Vr,[["__scopeId","data-v-2639238c"]]),jr=(a,o)=>{const u=Me(()=>a.allPassengerList),h=i=>i.ticketNos.length===0||a.refundTicketSuccess,f=i=>{var _;return h(i)||i.isChecked&&((_=i==null?void 0:i.infants)==null?void 0:_.isChecked)},p=async(i,_,d)=>{!_&&(d!=null&&d.infants)&&(d.infants.isChecked=!0),!(d.isChecked&&_)&&await o("queryTicketAndCalcAmount",i)};return st(async()=>{}),{allPnrPassengerList:u,judgeIsDisabled:h,infantIsDisabled:f,handleCheckPassenger:p}},Lr={class:"new-refund-passenger-card"},Br={class:"text-gray-1 text-sm font-bold leading-normal pt-2.5 pb-[4px]"},Ir={class:"flex flex-row flex-wrap"},Ur={class:"text-center text-gray-3 text-xs font-normal leading-tight px-1 py-[2px] bg-gray-7 rounded-sm"},Qr={key:0,class:"iconfont icon-connect mr-[-5px] ml-[-5px] mt-[5px]"},qr={class:"text-center text-gray-3 text-xs font-normal leading-tight px-1 py-[2px] bg-gray-7 rounded-sm"},zr=je({__name:"Passenger",props:{allPassengerList:{},clacAmountInfo:{},refundTicketSuccess:{type:Boolean}},emits:["queryTicketDetail","queryTicketAndCalcAmount"],setup(a,{emit:o}){const u=a,h=o,{allPnrPassengerList:f,judgeIsDisabled:p,handleCheckPassenger:i}=jr(u,h);return(_,d)=>{const x=yt,T=an;return s(),m("div",Lr,[t("div",Br,n(_.$t("app.agentTicketRefund.rtPassenger")),1),t("div",Ir,[(s(!0),m(ye,null,Ne(e(f),(k,R)=>(s(),m("div",{key:R+k.index,class:Se([[k.isChecked?"refund-passenger":"grey-connect"],"flex"])},[t("div",{class:Se(["flex items-center rounded border mb-2.5",[k.isChecked?"border-brand-2 bg-brand-4":"border-3",k.infants?"mr-0":"mr-2.5"]])},[c(T,{modelValue:k.isChecked,"onUpdate:modelValue":v=>k.isChecked=v,disabled:e(p)(k),onChange:v=>e(i)(R,!1,k)},{default:l(()=>[c(x,{effect:"dark",placement:"top",content:k.passengerNameSuffix},{default:l(()=>[t("span",{class:Se(["text-gray-1 text-xs font-bold leading-tight mr-1 inline-block overflow-hidden whitespace-nowrap text-ellipsis",e(Rt)(k.specialPassengerType).length>2?"max-w-[100px]":"max-w-[120px]"])},n(k.passengerNameSuffix),3)]),_:2},1032,["content"]),t("div",Ur,n(e(Rt)(k.specialPassengerType)),1)]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])],2),k.infants?(s(),m("div",Qr)):Z("",!0),k.infants?(s(),m("div",{key:1,class:Se(["flex items-center rounded border mr-2.5 mb-2.5",[k.infants.isChecked?"border-brand-2 bg-brand-4":"border-3"]])},[c(T,{modelValue:k.infants.isChecked,"onUpdate:modelValue":v=>k.infants.isChecked=v,disabled:e(p)(k.infants)||k.isChecked,onChange:v=>e(i)(R,!0,k)},{default:l(()=>[c(x,{effect:"dark",placement:"top",content:k.infants.passengerNameSuffix},{default:l(()=>[t("span",{class:Se(["text-gray-1 text-xs font-bold leading-tight mr-1 inline-block overflow-hidden whitespace-nowrap text-ellipsis",e(Rt)(k.infants.specialPassengerType).length>2?"max-w-[100px]":"max-w-[120px]"])},n(k.infants.passengerNameSuffix),3)]),_:2},1032,["content"]),t("div",qr,n(e(Rt)(k.infants.specialPassengerType)),1)]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])],2)):Z("",!0)],2))),128))])])}}});const Gr=(a,o)=>{const u=H(0),h=H(0),f=H(""),p=H(!1),i=()=>{p.value=!0,a("get-calc-info",p.value)},_=()=>{var x,T;if(u.value=0,h.value=0,f.value=((T=(x=o.amountData)==null?void 0:x[0])==null?void 0:T.currency)??"",o.isAutoRefundFinished){(o.batchAutoAmount??[]).forEach(k=>{u.value=qe(Number((k==null?void 0:k.otherDeduction)??"0"),u.value),h.value=qe(Number((k==null?void 0:k.netRefund)??"0"),h.value)});return}(o.amountData??[]).forEach(k=>{var R,v;u.value=qe(Number(((R=k==null?void 0:k.amount)==null?void 0:R.otherDeduction)??"0"),u.value),h.value=qe(Number(((v=k==null?void 0:k.amount)==null?void 0:v.netRefund)??"0"),h.value)})},d=()=>{var x,T;u.value=0,h.value=0,f.value=((T=(x=o.manualRefundAmountInfo)==null?void 0:x[0])==null?void 0:T.currency)??"",(o.manualRefundAmountInfo??[]).forEach(k=>{u.value=qe(Number((k==null?void 0:k.otherDeduction)??"0"),u.value),h.value=qe(Number((k==null?void 0:k.netRefund)??"0"),h.value)})};return wt([()=>o.amountData,()=>o.isFinishManualRefund],()=>{o.isFinishManualRefund?d():_()},{deep:!0}),st(()=>{p.value=!1}),{otherDeduction:u,totalAmount:h,currency:f,isAlreadyClickCalcAmount:p,clacAmount:i}},Hr={class:"w-[1820px] h-8 justify-start items-center gap-2.5 inline-flex mt-[14px]"},Wr={class:"justify-start items-center gap-0.5 flex"},Yr={class:"text-gray-1 text-sm font-bold leading-snug"},Kr={key:0,class:"text-red-1 text-base font-bold leading-normal"},Xr={key:0,class:"text-red-1 text-base font-bold leading-normal"},Jr={key:1,class:"text-red-1 text-base font-bold leading-normal"},Zr={key:2,class:"text-red-1 text-base font-bold leading-normal"},ec={class:"justify-start items-center gap-0.5 flex"},tc={class:"text-gray-1 text-sm font-bold leading-snug"},nc={key:0,class:"text-red-1 text-base font-bold leading-normal"},ac={key:0,class:"text-red-1 text-base font-bold leading-normal"},sc={key:1,class:"text-red-1 text-base font-bold leading-normal"},oc={key:2,class:"text-red-1 text-base font-bold leading-normal"},ic={key:0,class:"px-2.5 py-[5px] justify-center items-center gap-2.5 flex"},lc=je({__name:"RefundAmount",props:{amountData:{},status:{},isAutoRefundFinished:{type:Boolean},isFinishManualRefund:{type:Boolean},manualRefundAmountInfo:{},isCanRefund:{type:Boolean},batchAutoAmount:{}},emits:["get-calc-info"],setup(a,{emit:o}){const u=o,h=a,{otherDeduction:f,totalAmount:p,currency:i,isAlreadyClickCalcAmount:_,clacAmount:d}=Gr(u,h);return(x,T)=>{const k=Ke;return s(),m("div",null,[t("div",Hr,[t("div",Wr,[t("div",Yr,n(x.$t("app.agentTicketRefund.charge")),1),x.isFinishManualRefund?(s(),m("div",Kr,n(e(i))+" "+n(e(f).toFixed(2)),1)):(s(),m(ye,{key:1},[!e(_)||e(_)&&!x.status?(s(),m("div",Xr,"--")):Z("",!0),e(_)&&x.status&&x.status!=="SUCCESS"?(s(),m("div",Jr,n(x.$t("app.agentTicketRefund.calcFail")),1)):Z("",!0),e(_)&&x.status==="SUCCESS"?(s(),m("div",Zr,n(e(i))+" "+n(e(f).toFixed(2)),1)):Z("",!0)],64))]),t("div",ec,[t("div",tc,n(x.$t("app.agentTicketRefund.TotalAmountToBeRefunded")),1),x.isFinishManualRefund?(s(),m("div",nc,n(e(i))+" "+n(e(p).toFixed(2)),1)):(s(),m(ye,{key:1},[!e(_)||e(_)&&!x.status?(s(),m("div",ac,"--")):Z("",!0),e(_)&&x.status&&x.status!=="SUCCESS"?(s(),m("div",sc,n(x.$t("app.agentTicketRefund.calcFail")),1)):Z("",!0),e(_)&&x.status==="SUCCESS"?(s(),m("div",oc,n(e(i))+" "+n(e(p).toFixed(2)),1)):Z("",!0)],64))]),!x.isAutoRefundFinished&&!x.isFinishManualRefund?(s(),m("div",ic,[c(k,{disabled:!x.isCanRefund,"data-gid":"091Q0103",onClick:e(d)},{default:l(()=>[K(n(e(_)?x.$t("app.agentTicketRefund.recalc"):x.$t("app.agentTicketRefund.calcAmount")),1)]),_:1},8,["disabled","onClick"])])):Z("",!0)])])}}});const rc=at(lc,[["__scopeId","data-v-208a0628"]]),rn=Ca.global.t,cc={BSP:{label:rn("app.agentTicketQuery.BSPTicket"),value:"BSP"},GPBSP:{label:rn("app.agentTicketQuery.GPTicket"),value:"GPBSP"},BOPBSP:{label:rn("app.agentTicketQuery.BOPTicket"),value:"BOPBSP"},CDS:{label:rn("app.agentTicketQuery.CDSTicket"),value:"CDS"},GPCDS:{label:rn("app.agentTicketQuery.GP_CDSTicket"),value:"GPCDS"},ARL:{label:rn("app.agentTicketQuery.OWNTicket"),value:"ARL"}},uc={key:0},dc={class:"font-bold leading-5 text-gray-0"},pc={class:"flex tx-xs text-gray-6"},fc={class:"w-[42px] leading-6"},mc={class:"leading-6"},gc={class:"max-h-[120px] overflow-auto tax-content text-gray-0"},kc={class:"w-[42px] leading-6"},yc={class:"leading-6"},hc={key:1},vc={class:"font-bold leading-5 text-gray-0"},_c={class:"flex tx-xs text-gray-6"},bc={class:"w-[42px] leading-6"},xc={class:"leading-6"},Tc={class:"max-h-[120px] overflow-auto tax-content text-gray-0"},$c={class:"w-[42px] leading-6"},Nc={class:"leading-6"},Rc={key:1,class:"flex tx-xs"},Cc=t("div",{class:"w-[42px] leading-6"},"-",-1),wc=t("div",{class:"leading-6"},"-",-1),Ac=[Cc,wc],Sc={key:2},Dc={class:"font-bold leading-5 text-gray-0"},Pc={class:"flex tx-xs text-gray-6"},Oc={class:"w-[42px] leading-6"},Ec={class:"leading-6"},Fc={class:"max-h-[120px] overflow-auto tax-content text-gray-0"},Vc={class:"w-[42px] leading-6"},Mc={class:"leading-6"},jc={key:3},Lc={class:"text-gray-0"},qn=je({__name:"RefundTaxDetailPopover",props:{ticket:{},taxs:{},isFinishManualRefund:{type:Boolean},partSuccess:{type:Boolean},isAutoRefundFinished:{type:Boolean},batchAutoTaxes:{}},setup(a){return(o,u)=>{const h=Sn;return s(),ae(h,{placement:"top",width:150,trigger:"hover","popper-class":`refund-tax-detail-popper_${o.ticket.ticketNo} refund-tax-detail-popper`},{reference:l(()=>[Yt(o.$slots,"tax-deatil")]),default:l(()=>{var f,p;return[((f=o.ticket.taxs)==null?void 0:f.length)>0&&!(o.isFinishManualRefund||o.partSuccess)&&!o.isAutoRefundFinished?(s(),m("div",uc,[t("div",dc,n(o.$t("app.agentTicketRefund.taxDetails")),1),t("div",null,[t("div",pc,[t("div",fc,n(o.$t("app.agentTicketRefund.taxes")),1),t("div",mc,n(o.$t("app.agentTicketRefund.taxAmount")),1)]),t("div",gc,[(s(!0),m(ye,null,Ne(o.ticket.taxs,(i,_)=>(s(),m("div",{key:_,class:"flex tx-xs"},[t("div",kc,n(i.name),1),t("div",yc,n(o.ticket.currency)+" "+n(Number(i.value).toFixed(2)),1)]))),128))])])])):o.isFinishManualRefund||o.partSuccess?(s(),m("div",hc,[t("div",vc,n(o.$t("app.agentTicketRefund.taxDetails")),1),t("div",null,[t("div",_c,[t("div",bc,n(o.$t("app.agentTicketRefund.taxes")),1),t("div",xc,n(o.$t("app.agentTicketRefund.taxAmount")),1)]),t("div",Tc,[(o.taxs??[]).length!==0?(s(!0),m(ye,{key:0},Ne(o.taxs,(i,_)=>(s(),m("div",{key:_,class:"flex tx-xs"},[t("div",$c,n(i.name),1),t("div",Nc,n(o.ticket.currency)+" "+n(Number(i.value).toFixed(2)),1)]))),128)):(s(),m("div",Rc,Ac))])])])):o.batchAutoTaxes&&((p=o.batchAutoTaxes)==null?void 0:p.length)>0&&o.isAutoRefundFinished?(s(),m("div",Sc,[t("div",Dc,n(o.$t("app.agentTicketRefund.taxDetails")),1),t("div",null,[t("div",Pc,[t("div",Oc,n(o.$t("app.agentTicketRefund.taxes")),1),t("div",Ec,n(o.$t("app.agentTicketRefund.taxAmount")),1)]),t("div",Fc,[(s(!0),m(ye,null,Ne(o.batchAutoTaxes,(i,_)=>(s(),m("div",{key:_,class:"flex tx-xs"},[t("div",Vc,n(i.name),1),t("div",Mc,n(o.ticket.currency)+" "+n(Number(i.value).toFixed(2)),1)]))),128))])])])):(s(),m("div",jc,[t("span",Lc,n(o.$t("app.agentTicketRefund.noData")),1)]))]}),_:3},8,["popper-class"])}}});const Bc=(a,o)=>{const{t:u}=We(),h=Ct(),f=Me(()=>{var P;return(P=h.state.user)==null?void 0:P.entityType}),p=H(),i=H(""),_=H([]),d=tt({printerNo:"",ticketManagementOrganizationCode:""}),x={ticketManagementOrganizationCode:[{required:!0,message:u("app.agentTicketQuery.repelTicket.plsInputTicketMachineNumber"),trigger:"blur"}],printerNo:[{required:!0,message:u("app.ticketStatus.deviceNumNull"),trigger:"blur"},{pattern:Gt,trigger:"blur",message:u("app.ticketStatus.deviceError")}]},T=Me(()=>!["CDS","GPCDS"].includes(d.ticketManagementOrganizationCode)),k=P=>{i.value=P},R=P=>{var X,ue;return(_.value??[]).some(E=>P===E.value)&&P?P:((ue=(X=_.value)==null?void 0:X[0])==null?void 0:ue.value)??""},v=async()=>{p.value.validate(async P=>{if(!P)return;const M=Pe("091T0104");o("openDialog",M,d.printerNo,i.value,d.ticketManagementOrganizationCode),o("update:modelValue",!1)})},r=()=>{o("update:modelValue",!1)},y=()=>{T.value||(d.printerNo="")},C={BSP:{label:u("app.agentTicketQuery.BSPTicket"),value:"BSP"},GPBSP:{label:u("app.agentTicketQuery.GPTicket"),value:"GPBSP"},BOPBSP:{label:u("app.agentTicketQuery.BOPTicket"),value:"BOPBSP"},CDS:{label:u("app.agentTicketQuery.CDSTicket"),value:"CDS"},GPCDS:{label:u("app.agentTicketQuery.GP_CDSTicket"),value:"GPCDS"},ARL:{label:u("app.agentTicketQuery.OWNTicket"),value:"ARL"}},$=()=>{var P,M,X,ue,E,j,oe,D,b,N;((P=f.value)!=null&&P.includes("$$$")||(M=f.value)!=null&&M.includes("BSP"))&&(_.value.push(C.BSP),_.value.push(C.GPBSP)),!((X=f.value)!=null&&X.includes("BSP"))&&((ue=f.value)!=null&&ue.includes("GP"))&&_.value.push(C.GPBSP),((E=f.value)!=null&&E.includes("$$$")||(j=f.value)!=null&&j.includes("BOP"))&&_.value.push(C.BOPBSP),((oe=f.value)!=null&&oe.includes("$$$")||(D=f.value)!=null&&D.includes("CDS"))&&(_.value.push(C.CDS),_.value.push(C.GPCDS)),((b=f.value)!=null&&b.includes("$$$")||(N=f.value)!=null&&N.includes("本票"))&&_.value.push(C.ARL),d.ticketManagementOrganizationCode=R(a.ticketManagementOrganizationCode??"")};return st(async()=>{$()}),{formDate:p,printNoFrom:d,PRINTER_NO_RULES:x,ticketOrganizationList:_,confirmPrinterNo:v,closeDialog:r,deliverPrintType:k,isShowPrintNo:T,changeTicketManagementOrganizationCode:y}},Ic=t("i",{class:"iconfont icon-close"},null,-1),Uc=[Ic],Qc={class:"carType-option-panel"},Xa=je({__name:"PrintNoDialog",props:{ticketManagementOrganizationCode:{}},emits:["openDialog","update:modelValue","update:showTicketRefundFormDialog"],setup(a,{emit:o}){const u=o,h=a,{formDate:f,printNoFrom:p,PRINTER_NO_RULES:i,ticketOrganizationList:_,confirmPrinterNo:d,closeDialog:x,deliverPrintType:T,isShowPrintNo:k,changeTicketManagementOrganizationCode:R}=Bc(h,u);return(v,r)=>{const y=Je,C=tn,$=nn,P=ot,M=it,X=Ke,ue=nt;return s(),ae(ue,{title:v.$t("app.ticketStatus.selectTicket"),width:"680px",class:"print-no-dialog","show-close":!1,"close-on-click-modal":!1,onClose:e(x)},{footer:l(()=>[t("div",null,[c(X,{type:"primary",onClick:r[4]||(r[4]=E=>e(d)())},{default:l(()=>[K(n(v.$t("app.ticketStatus.confirmBtn")),1)]),_:1}),c(X,{onClick:e(x)},{default:l(()=>[K(n(v.$t("app.ticketStatus.cancelBtn")),1)]),_:1},8,["onClick"])])]),default:l(()=>[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:r[0]||(r[0]=(...E)=>e(x)&&e(x)(...E))},Uc),c(M,{ref_key:"formDate",ref:f,model:e(p),rules:e(i),"label-position":"left","require-asterisk-position":"right"},{default:l(()=>[c(P,{prop:"ticketManagementOrganizationCode",label:v.$t("app.agentTicketQuery.ticketOrganization")},{default:l(()=>[c($,{modelValue:e(p).ticketManagementOrganizationCode,"onUpdate:modelValue":r[1]||(r[1]=E=>e(p).ticketManagementOrganizationCode=E),class:"ticket-management-organization",disabled:e(p).ticketManagementOrganizationCode==="",placeholder:e(p).ticketManagementOrganizationCode===""?v.$t("app.agentTicketQuery.noData"):"",onChange:e(R)},{default:l(()=>[(s(!0),m(ye,null,Ne(e(_),E=>(s(),ae(C,{key:E.value,label:E.label,value:E.value},{default:l(()=>[t("div",Qc,[t("div",{class:Se(e(p).ticketManagementOrganizationCode===E.value?"show-select":"hidden-select")},[c(y,null,{default:l(()=>[c(e(zn))]),_:1})],2),t("span",null,n(E.label),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled","placeholder","onChange"])]),_:1},8,["label"]),e(k)?(s(),ae(P,{key:0,prop:"printerNo",label:v.$t("app.ticketStatus.deviceNum")},{default:l(()=>[c(Kt,{modelValue:e(p).printerNo,"onUpdate:modelValue":[r[2]||(r[2]=E=>e(p).printerNo=E),r[3]||(r[3]=E=>e(f).validateField("printerNo"))],"select-class":"w-[340px]",onDeliverPrintType:e(T)},null,8,["modelValue","onDeliverPrintType"])]),_:1},8,["label"])):Z("",!0)]),_:1},8,["model","rules"])]),_:1},8,["title","onClose"])}}});const qc=a=>{var se;const{t:o}=We(),u=Ae(!1),h=(se=navigator==null?void 0:navigator.userAgent)==null?void 0:se.toLowerCase(),f=Me(()=>h==null?void 0:h.includes("electron/")),p=(Q,S,G)=>window.electronAPI.openTicketDetailWindow(Q,S,G),i=Ae(!1),_=Ae(!1),d=Ae(""),x=Ae(""),T=H({ticketType:"",ticketNo:""}),k=H({}),R=H(a.ticketDetailList??[]),v=(Q,S)=>{const G=new Map;return Q.forEach(J=>{var w;const z=(w=J[S])==null?void 0:w.replace(/-/,"");G.set(z,G.get(z)||[]),G.get(z).push(J)}),G},r=Me(()=>{const Q=a.isAutoRefundFinished?ft(a.batchAutoAmount??[]):ft(a.ticketAmountList??[]).map(S=>S.amount);return v(Q,"ticketNo")}),y=Me(()=>{const Q=new Map;return(ft(a.ticketTrfdNoDetails)??[]).forEach(G=>{var J,z,w,g;if(((J=ht(G.ticketNo))==null?void 0:J.length)===14)Q.set((z=G.ticketNo)==null?void 0:z.replace(/-/,""),G.trfdNo);else{const B=(g=(w=G.ticketNo)==null?void 0:w.split("-"))==null?void 0:g[0];Q.set(`${B}`,G.trfdNo)}}),Q}),C=Q=>Number(Q.conjunction)>1&&Q.ticket.tktType==="D"&&a.refundType==="manual",$=Q=>Q.ticket.tktType==="I",P=Q=>{var S,G,J;return(S=Q==null?void 0:Q.toUpperCase())!=null&&S.startsWith("CA")?o("app.agentTicketRefund.pay.cash"):(G=Q==null?void 0:Q.toUpperCase())!=null&&G.startsWith("CC")?o("app.agentTicketRefund.pay.tc"):(J=Q==null?void 0:Q.toUpperCase())!=null&&J.startsWith("CHECK")?o("app.agentTicketRefund.pay.check"):Q},M=Q=>{var S,G,J;return(S=Q==null?void 0:Q.toUpperCase())!=null&&S.startsWith("CC")&&`${((G=Q==null?void 0:Q.split("/"))==null?void 0:G[1])??""}${((J=Q==null?void 0:Q.split("/"))==null?void 0:J[2])??""}`||"--"},X=Q=>{var G,J;const S=(J=r.value.get(((G=Q.segment)==null?void 0:G[0].tktTag)??""))==null?void 0:J[0];return a.ticketAmountList&&S?Q.currency:""},ue=(Q,S)=>{var G;if(a.ticketAmountList){const J=(G=r.value.get(Q))==null?void 0:G[0];return J?J[S]:o("app.agentTicketRefund.calcFail")}return"--"},E=(Q,S)=>{var G;if(a.ticketAmountList){const J=(G=r.value.get(Q))==null?void 0:G[0],z=J?J[S]:[];return typeof z=="string"?[]:z}return[]},j=Q=>[...Q.keys()].map(S=>a.manualRefundAmountInfo.find(G=>{var J,z;return((z=(J=a.manualRefundAmountInfo)==null?void 0:J[0])==null?void 0:z.tktType)==="D"?G.ticketNo===S:G.ticketNo.split("-")[0]===S})),oe=Q=>a.manualRefundAmountInfo.find(S=>S.ticketNo===Q),D=Q=>{var S;return((S=oe(Q))==null?void 0:S.currency)??""},b=(Q,S)=>(Q??[]).map(G=>({...G,currency:S})),N=Q=>(Q??[]).reduce((S,G)=>S+Number(G.value),0).toFixed(2),ne=(Q,S)=>{var G;if(a.isFinishManualRefund||a.partSuccess){const J=j(Q),z=((G=J==null?void 0:J[0])==null?void 0:G.currency)??"";let w=0;const g=S==="commision"&&J.every(B=>(B==null?void 0:B.commision)==="0")&&J.every(B=>(B==null?void 0:B.commisionRate)!=="");return(J??[]).forEach(B=>{S==="commision"&&!g?w=qe(Number((B==null?void 0:B.commision)??"0"),w):S==="commision"&&g&&(w=qe(Number((B==null?void 0:B.commisionRate)??"0"),w)),S==="otherDeduction"&&(w=qe(Number((B==null?void 0:B.otherDeduction)??"0"),w)),S==="netRefund"&&(w=qe(Number((B==null?void 0:B.netRefund)??"0"),w)),S==="totalAmount"&&(w=qe(Number((B==null?void 0:B.totalAmount)??"0"),w)),S==="tax"&&(w=qe(Number((B==null?void 0:B.totalTaxs)??"0"),w))}),g?`${Number(w).toFixed(2)}%`:`${z} ${Number(w).toFixed(2)}`}return"--"},ie=Q=>{if(a.isFinishManualRefund||a.partSuccess){const S=[...j(Q).values()],G=[];S.forEach(z=>{((z==null?void 0:z.taxs)??[]).forEach(w=>{const g={...w,currency:z.currency};G.push(g)})});const J=(G??[]).reduce((z,w)=>(z[w.name]?z[w.name]+=Number(w.value):z[w.name]=Number(w.value),z),{});return G.forEach(z=>{z.value=J[z.name]}),[...new Set(G.map(z=>JSON.stringify(z)))].map(z=>JSON.parse(z))}},U=()=>({ticketNo:T.value.ticketNo,ticketType:x.value?x.value:T.value.ticketType,ticketManagementOrganizationCode:T.value.ticketManagementOrganizationCode,printerNo:d.value,refundNo:T.value.refundNo,secondFactor:T.value.secondFactor}),A=async Q=>{var G;const S=U();try{u.value=!0;const{data:J}=await dn(S,Q);k.value=(G=J.value)==null?void 0:G.data,k.value.ticketManagementOrganizationCode=T.value.ticketManagementOrganizationCode??"",i.value=!0}finally{u.value=!1}},le=async(Q,S,G,J)=>{d.value=S,x.value=G,T.value.ticketManagementOrganizationCode=J,await A(Q)},he=async(Q,S,G,J,z,w)=>{if(T.value={ticketNo:Q,ticketType:S,secondFactor:z,ticketManagementOrganizationCode:G,refundNo:w},x.value=S,d.value=J??"",T.value.ticketManagementOrganizationCode&&J){const g=Pe("091T0104");await A(g)}else _.value=!0},ce=Q=>{if(!R.value[Q].printerNo){R.value[Q].printError=o("app.ticketStatus.deviceNumNull");return}if(!Gt.test(R.value[Q].printerNo)){R.value[Q].printError=o("app.ticketStatus.deviceError");return}R.value[Q].printError=""},L=(Q,S)=>S&&(S==null?void 0:S.length)>=9&&Q!=="ARL"?S.slice(-9):S;return wt(()=>a.ticketDetailList,()=>{R.value=a.ticketDetailList??[]},{deep:!0}),{loading:u,ticketList:R,isManualDomesticConjunctionTicket:C,showRefundFormDialog:i,printNo:d,printType:x,refundOperationCondition:T,refundFormData:k,getPayType:P,getCreditCard:M,getCurrencyByTicket:X,getRefundFeeByTicketNo:ue,getAmountAndCurrency:ne,getManualTax:ie,getDomManualCurrency:D,getDomManualTicket:oe,getDomManualTax:b,dealTaxTotal:N,ticketTrfdNo:y,openRefundFormDialog:he,judgeInter:$,checkPrinterNo:ce,showPrintNoDialog:_,openDialog:le,isClient:f,openTicketDetailWindow:p,getRefundFormNumber:L,getBatchAutoTaxes:E}},la=a=>(bt("data-v-00da54bf"),a=a(),xt(),a),zc={class:"w-full h-[44px] p-[10px] bg-brand-4 border-b border-brand-3 justify-between items-center inline-flex"},Gc={class:"justify-start items-center flex"},Hc={class:"justify-start items-start gap-2 flex mr-2.5"},Wc={key:0,class:"text-brand-2 text-base font-bold leading-normal"},Yc={key:1},Kc=["onClick"],Xc={class:"px-1 bg-gray-7 rounded-sm justify-start items-start gap-2.5 flex"},Jc={class:"text-center text-gray-3 text-xs font-normal leading-tight"},Zc={class:"justify-start items-center gap-2.5 flex ml-2.5"},eu={class:"justify-start items-center gap-1 flex"},tu={key:0,class:"iconfont icon-inf mr-[4px] text-gray-4"},nu={key:1,class:"iconfont icon-user-fill mr-[4px] text-gray-4"},au={class:"text-gray-1 text-base font-bold leading-normal"},su={class:"px-1 bg-gray-7 rounded-sm justify-start items-start gap-2.5 flex"},ou={class:"text-center text-gray-3 text-xs font-normal leading-tight"},iu={class:"justify-start items-center gap-3.5 flex"},lu={key:0,class:"justify-start items-start flex"},ru={class:"text-gray-3 text-sm font-normal leading-snug"},cu=["onClick"],uu={class:"justify-start items-start flex"},du={class:"text-gray-3 text-sm font-normal leading-snug"},pu={class:"text-gray-1 text-sm font-normal leading-snug"},fu={class:"justify-start items-start flex"},mu={class:"text-gray-3 text-sm font-normal leading-snug"},gu={class:"text-gray-1 text-sm font-normal leading-snug"},ku={class:"justify-start items-start flex"},yu={class:"text-gray-3 text-sm font-normal leading-snug"},hu={class:"text-gray-1 text-sm font-normal leading-snug"},vu={key:1,class:"justify-start items-center flex"},_u={class:"text-gray-3 text-sm font-normal leading-snug"},bu={class:"text-gray-1 text-sm font-normal leading-snug"},xu={key:0,class:"w-full h-5 px-[10px] justify-start items-center gap-2.5 inline-flex align-center"},Tu=["onClick"],$u=la(()=>t("div",{class:"grow border-t-[1px] border-dashed border-gray-6"},null,-1)),Nu={key:2,class:"text-xs w-[150px]"},Ru=["onClick"],Cu={key:0,class:"w-[135px] flex items-center"},wu={key:0,class:"text-gray-1 text-sm font-normal leading-snug mr-2.5"},Au={class:"text-yellow-1 px-1.5 py-0.5 rounded-sm bg-yellow-2 mr-2.5 min-w-50 text-xs"},Su={class:"text-gray-1 text-sm font-normal leading-snug"},Du={key:1,class:"w-[135px] flex items-center"},Pu={class:"text-gray-1 text-sm font-normal leading-snug mr-2.5"},Ou={class:"text-gray-1 text-sm font-normal leading-snug"},Eu={key:2,class:"w-[200px]"},Fu={class:"text-yellow-1 px-1.5 py-0.5 rounded-sm bg-yellow-2 mr-2.5 min-w-50 text-xs"},Vu={key:3,class:"w-[200px] self-stretch justify-start items-center gap-5 flex"},Mu={class:"w-[78px] text-gray-1 text-sm font-normal leading-snug"},ju={class:"text-gray-3 text-sm font-normal leading-snug"},Lu={class:"w-[220px] self-stretch justify-start items-center gap-2.5 flex"},Bu={class:"text-gray-1 text-sm font-normal leading-snug"},Iu={class:"text-gray-3 text-sm font-normal leading-snug"},Uu={key:0},Qu={class:"w-[400px] self-stretch px-1 justify-start items-center gap-3 flex"},qu={key:1,class:"w-full h-[42px] p-[10px] justify-between items-center inline-flex border-gray-6"},zu={class:"justify-start items-center gap-3.5 flex"},Gu={class:"justify-start items-start gap-0.5 flex"},Hu={class:"text-gray-3 text-sm font-normal leading-snug mr-[4px]"},Wu={key:0,class:"text-gray-1 text-sm font-normal leading-snug"},Yu={key:1,class:"text-gray-1 text-sm font-normal leading-snug"},Ku={class:"justify-start items-start gap-0.5 flex"},Xu={class:"text-gray-3 text-sm font-normal leading-snug mr-[4px]"},Ju={class:"text-sm font-normal leading-snug cursor-pointer border-b-[1.5px] tax-detail"},Zu={class:"justify-start items-start gap-2.5 flex"},ed={class:"justify-start items-center gap-0.5 flex"},td={class:"text-gray-1 text-sm font-normal leading-snug"},nd={class:"text-red-1 text-sm font-normal leading-snug"},ad={class:"justify-start items-center gap-0.5 flex"},sd={class:"text-gray-1 text-sm font-normal leading-snug"},od={class:"text-red-1 text-sm font-normal leading-snug"},id={class:"justify-start items-center gap-0.5 flex"},ld={class:"text-gray-1 text-sm font-normal leading-snug"},rd={class:"text-red-1 text-sm font-normal leading-snug"},cd={key:0,class:"justify-start items-start gap-2.5 flex"},ud={class:"text-red-1 text-[14px] font-[700]"},dd=la(()=>t("div",{class:"w-full border-t-[1px] border-dashed border-gray-6"},null,-1)),pd={class:"w-full h-[52px] p-[10px] justify-between items-center inline-flex"},fd={class:"justify-start items-center gap-3.5 flex"},md={key:0,class:"justify-start items-start gap-0.5 flex"},gd={class:"text-gray-3 text-sm font-normal leading-snug mr-[4px]"},kd={class:"text-gray-1 text-sm font-normal leading-snug"},yd={key:1,class:"justify-start items-start gap-0.5 flex"},hd={class:"text-gray-3 text-sm font-normal leading-snug mr-[4px]"},vd={key:0,class:"text-gray-1 text-sm font-normal leading-snug"},_d={key:1,class:"text-gray-1 text-sm font-normal leading-snug"},bd={class:"justify-start items-start gap-0.5 flex"},xd={class:"text-gray-3 text-sm font-normal leading-snug mr-[4px]"},Td={key:0,class:"text-sm font-normal leading-snug cursor-pointer border-b-[1.5px] tax-detail"},$d={key:1,class:"text-sm font-normal leading-snug cursor-pointer border-b-[1.5px] tax-detail"},Nd={key:2},Rd={class:"text-gray-2"},Cd=la(()=>t("span",{class:"text-red-1 mx-[3px]"},"*",-1)),wd={key:0,class:"justify-start items-start gap-2.5 flex"},Ad={class:"justify-start items-center gap-0.5 flex"},Sd={class:"text-gray-1 text-sm font-normal leading-snug"},Dd={key:0,class:"text-red-1 text-sm font-normal leading-snug"},Pd={key:1,class:"text-red-1 text-sm font-normal leading-snug"},Od={class:"justify-start items-center gap-0.5 flex"},Ed={class:"text-gray-1 text-sm font-normal leading-snug"},Fd={class:"text-red-1 text-sm font-normal leading-snug"},Vd={class:"justify-start items-center gap-0.5 flex"},Md={class:"text-gray-1 text-sm font-normal leading-snug"},jd={key:0,class:"text-red-1 text-sm font-normal leading-snug"},Ld={key:1,class:"text-red-1 text-sm font-normal leading-snug"},Bd={class:"justify-start items-center gap-0.5 flex"},Id={class:"text-gray-1 text-sm font-normal leading-snug"},Ud={key:0,class:"text-red-1 text-sm font-normal leading-snug"},Qd={key:1,class:"text-red-1 text-sm font-normal leading-snug"},qd={key:1,class:"justify-start items-start gap-2.5 flex"},zd={class:"text-red-1 text-[14px] font-[700]"},Gd=je({__name:"TicketDetailInfo",props:{ticketDetailList:{},ticketAmountList:{},isFinishManualRefund:{type:Boolean},partSuccess:{type:Boolean},manualRefundAmountInfo:{},isShowTrfdNo:{type:Boolean},ticketTrfdNoDetails:{},refundType:{},deviceNum:{},refundTicketSuccess:{type:Boolean},isAuthToShowWindow:{type:Boolean},batchAutoAmount:{},isAutoRefundFinished:{type:Boolean}},setup(a){const o=a,{loading:u,ticketList:h,isManualDomesticConjunctionTicket:f,showRefundFormDialog:p,printNo:i,printType:_,refundOperationCondition:d,refundFormData:x,getPayType:T,getCreditCard:k,getCurrencyByTicket:R,getRefundFeeByTicketNo:v,getAmountAndCurrency:r,getManualTax:y,getDomManualCurrency:C,getDomManualTicket:$,getDomManualTax:P,dealTaxTotal:M,ticketTrfdNo:X,openRefundFormDialog:ue,judgeInter:E,checkPrinterNo:j,showPrintNoDialog:oe,openDialog:D,isClient:b,openTicketDetailWindow:N,getRefundFormNumber:ne,getBatchAutoTaxes:ie}=qc(o);return(U,A)=>{const le=ot,he=it,ce=kt;return s(),m(ye,null,[(s(!0),m(ye,null,Ne(e(h),(L,se)=>{var Q,S,G,J,z,w,g,B,F,de,ve,O,fe,Y,Te,V,ge,me,Re,Oe,we,Ee,Be,Ze,I,_e,$e;return Qe((s(),m("div",{key:se,class:Se(["rounded border border-brand-3 overflow-hidden",se!==((Q=e(h))==null?void 0:Q.length)-1?"mb-[10px]":"mb-[0px]"])},[t("div",zc,[t("div",Gc,[t("div",Hc,[Number(L.conjunction)>1?(s(),m("div",Wc,n(e(ht)(L.ticket.ticketNo)),1)):(s(),m("div",Yc,[e(b)&&U.isAuthToShowWindow?(s(),m("span",{key:0,class:"text-brand-2 text-base font-bold leading-normal cursor-pointer",onClick:re=>{var Ce,Fe;return e(N)(L.ticket.ticketNo,((Ce=L.ticket.secondFactor)==null?void 0:Ce.secondFactorCode)??"",((Fe=L.ticket.secondFactor)==null?void 0:Fe.secondFactorValue)??"")}},n(e(ht)(L.ticket.ticketNo??"")),9,Kc)):(s(),ae(mn,{key:1,"is-international":e(E)(L),"is-cds-ticket":L.ticket.cdsTicket??!1,"second-factor":L.ticket.secondFactor,"tkt-index":se,"ticket-number":L.ticket.ticketNo,"refund-class-type":"0"},null,8,["is-international","is-cds-ticket","second-factor","tkt-index","ticket-number"]))]))]),t("div",Xc,[t("div",Jc,n(L!=null&&L.ticketManagementOrganizationCode?e(cc)[L==null?void 0:L.ticketManagementOrganizationCode].label:""),1)]),t("div",Zc,[t("div",eu,[((S=L==null?void 0:L.ticket)==null?void 0:S.specialPassengerType)==="INF"?(s(),m("em",tu)):(s(),m("em",nu)),t("div",au,n(L.ticket.passengerNameSuffix),1),t("div",su,[t("div",ou,n(e(Rt)(((G=L==null?void 0:L.ticket)==null?void 0:G.specialPassengerType)??"ADT")),1)])])])]),t("div",iu,[!e(f)(L)&&U.isShowTrfdNo?(s(),m("div",lu,[t("div",ru,n(U.$t("app.agentTicketRefund.refundTicketNumber"))+"：",1),t("div",{class:"text-brand-2 text-sm font-bold leading-snug cursor-pointer",onClick:re=>{var Ce,Fe,W,Ye;return e(ue)((Fe=(Ce=L.ticket.ticketNo)==null?void 0:Ce.split("-"))==null?void 0:Fe[0],L.ticket.tktType,L.ticketManagementOrganizationCode??"",L==null?void 0:L.refundPrintNumber,L.ticket.secondFactor,e(X).get((Ye=(W=L.ticket.ticketNo)==null?void 0:W.split("-"))==null?void 0:Ye[0])??"")}},n(e(ne)((L==null?void 0:L.ticketManagementOrganizationCode)??"",e(X).get(((z=(J=L.ticket.ticketNo)==null?void 0:J.split("-"))==null?void 0:z[0])??"")??"")||"--"),9,cu)])):Z("",!0),t("div",uu,[t("div",du,n(U.$t("app.agentTicketRefund.oldTicketNo"))+"：",1),t("div",pu,n(e(ht)(L.ticket.exchangeTktNo??"")||"--"),1)]),t("div",fu,[t("div",mu,n(U.$t("app.agentTicketRefund.electronic"))+"：",1),t("div",gu,n(U.$t("app.agentTicketRefund.yes")),1)]),t("div",ku,[t("div",yu,n(U.$t("app.agentTicketRefund.payment"))+"：",1),t("div",hu,n(e(T)(L.ticket.payType)),1)]),(g=(w=L.ticket.payType)==null?void 0:w.toUpperCase())!=null&&g.startsWith("CC")?(s(),m("div",vu,[t("div",_u,n(U.$t("app.agentTicketRefund.creditCard"))+"：",1),t("div",bu,n(e(k)(L.ticket.payType)),1)])):Z("",!0)])]),(s(!0),m(ye,null,Ne(L.ticket.ticketSegment,(re,Ce)=>{var Fe,W,Ye,lt,be,vt,pt,Tt,$t,It,Vt,Ht,Xt;return s(),m(ye,{key:Ce},[Number(L.conjunction)>1?(s(),m("div",xu,[e(b)&&U.isAuthToShowWindow?(s(),m("div",{key:0,class:"text-brand-2 text-xs font-bold leading-tight cursor-pointer w-[125px]",onClick:He=>{var ct,Ut;return e(N)(re==null?void 0:re[0],((ct=L.ticket.secondFactor)==null?void 0:ct.secondFactorCode)??"",((Ut=L.ticket.secondFactor)==null?void 0:Ut.secondFactorValue)??"")}},n(e(ht)((re==null?void 0:re[0])??"")),9,Tu)):(s(),ae(mn,{key:1,"is-international":e(E)(L),"tkt-index":`${se}_${Ce}`,"is-cds-ticket":L.ticket.cdsTicket??!1,"second-factor":L.ticket.secondFactor,"ticket-number":(re==null?void 0:re[0])??"","refund-class-type":"1"},null,8,["is-international","tkt-index","is-cds-ticket","second-factor","ticket-number"])),$u,e(f)(L)&&U.isShowTrfdNo?(s(),m("div",Nu,[K(n(U.$t("app.agentTicketRefund.refundTicketNumber"))+"：",1),t("span",{class:"text-brand-2 font-bold cursor-pointer",onClick:He=>e(ue)(re==null?void 0:re[0],L.ticket.tktType,L.ticketManagementOrganizationCode??"",L==null?void 0:L.refundPrintNumber,L.ticket.secondFactor,e(X).get(re==null?void 0:re[0])??"")},n(e(ne)((L==null?void 0:L.ticketManagementOrganizationCode)??"",e(X).get(re==null?void 0:re[0])??"")||"--"),9,Ru)])):Z("",!0)])):Z("",!0),t("div",{class:Se(["w-full px-[10px] flex-col justify-start items-start inline-flex",Number(L.conjunction)>1?"py-[4px]":"py-[8px]"])},[(s(!0),m(ye,null,Ne(re==null?void 0:re[1],(He,ct)=>{var Ut,ln,Jt;return s(),m("div",{key:ct,class:"self-stretch h-7 justify-between items-center inline-flex"},[He.segmentType!=="2"?(s(),m("div",Cu,[He.segmentType==="3"&&He.airline?(s(),m("span",wu,n(He.airline),1)):Z("",!0),t("span",Au,n(He.segmentType==="3"?"OPEN":"ARNK"),1),t("span",Su,n(He.cabinCode),1)])):(s(),m("div",Du,[t("div",Pu,n(He.flightNo),1),t("div",Ou,n(He.cabinCode),1)])),He.segmentType!=="2"?(s(),m("div",Eu,[t("span",Fu,n(He.segmentType==="3"?"OPEN":"ARNK"),1)])):Z("",!0),He.departureDate||He.departureTime?(s(),m("div",Vu,[t("div",Mu,n(He.departureDate),1),t("div",ju,n(((Ut=He.departureTime)==null?void 0:Ut.substring(0,5))??""),1)])):Z("",!0),t("div",Lu,[t("div",Bu,n(He.departureCode)+"-"+n(He.arriveCode),1),t("div",Iu,[K(n(L.ticket.pnr),1),L.ticket.pnr&&L.ticket.crsPnrNo?(s(),m("span",Uu,"/")):Z("",!0),K(n(L.ticket.crsPnrNo),1)])]),t("div",Qu,[t("div",{class:Se(["text-sm font-bold leading-snug",e(Bt)[((ln=He.ticketStatus)==null?void 0:ln.trim())??""]?e(Bt)[((Jt=He.ticketStatus)==null?void 0:Jt.trim())??""].color:""])},n(He.ticketStatus),3)])])}),128))],2),L.ticket.tktType==="D"&&(U.isFinishManualRefund||U.partSuccess)&&Number(L.conjunction)>1?(s(),m("div",qu,[t("div",zu,[t("div",Gu,[t("div",Hu,n(U.$t("app.agentTicketRefund.totalTicketAmount")),1),(Fe=e($)(re==null?void 0:re[0]))!=null&&Fe.totalAmount?(s(),m("div",Yu,n(e(C)(re==null?void 0:re[0]))+" "+n(((W=e($)(re==null?void 0:re[0]))==null?void 0:W.totalAmount)??""),1)):(s(),m("div",Wu,"--"))]),t("div",Ku,[t("div",Xu,n(U.$t("app.agentTicketRefund.totalTaxAmount")),1),(s(),ae(qn,{key:`DomesticRefundTaxDetailPopover${se}_${Ce}`,ticket:L.ticket,taxs:e(P)((Ye=e($)(re==null?void 0:re[0]))==null?void 0:Ye.taxs,(lt=e($)(re==null?void 0:re[0]))==null?void 0:lt.currency),"is-finish-manual-refund":U.isFinishManualRefund,"part-success":U.partSuccess},{"tax-deatil":l(()=>{var He;return[t("div",Ju,n(e(C)(re==null?void 0:re[0]))+" "+n(e(M)((He=e($)(re==null?void 0:re[0]))==null?void 0:He.taxs)),1)]}),_:2},1032,["ticket","taxs","is-finish-manual-refund","part-success"]))])]),t("div",Zu,[t("div",ed,[t("div",td,n(!Number((be=e($)(re==null?void 0:re[0]))==null?void 0:be.commision)&&((vt=e($)(re==null?void 0:re[0]))==null?void 0:vt.commisionRate)!==""?U.$t("app.agentTicketRefund.commissionRate"):U.$t("app.agentTicketRefund.commission")),1),t("div",nd,n(!Number((pt=e($)(re==null?void 0:re[0]))==null?void 0:pt.commision)&&((Tt=e($)(re==null?void 0:re[0]))==null?void 0:Tt.commisionRate)!==""?`${(($t=e($)(re==null?void 0:re[0]))==null?void 0:$t.commisionRate)??""}%`:`${(It=e($)(re==null?void 0:re[0]))==null?void 0:It.currency} ${Number((Vt=e($)(re==null?void 0:re[0]))==null?void 0:Vt.commision).toFixed(2)}`),1)]),t("div",ad,[t("div",sd,n(U.$t("app.agentTicketRefund.charge")),1),t("div",od,n(e(C)(re==null?void 0:re[0]))+" "+n(((Ht=e($)(re==null?void 0:re[0]))==null?void 0:Ht.otherDeduction)??""),1)]),t("div",id,[t("div",ld,n(U.$t("app.agentTicketRefund.totalRefund")),1),t("div",rd,n(e(C)(re==null?void 0:re[0]))+" "+n(((Xt=e($)(re==null?void 0:re[0]))==null?void 0:Xt.netRefund)??""),1)])]),L.ticket.isRefundFail?(s(),m("div",cd,[t("span",ud,n(U.$t("app.agentTicketRefund.refundFailMsg")),1)])):Z("",!0)])):Z("",!0)],64)}),128)),L.ticket.tktType==="D"&&(U.isFinishManualRefund||U.partSuccess)&&Number(L.conjunction)>1?Z("",!0):(s(),m(ye,{key:0},[dd,t("div",pd,[t("div",fd,[U.isAutoRefundFinished?(s(),m("div",md,[t("div",gd,n(U.$t("app.agentTicketRefund.totalTicketAmount")),1),t("div",kd,n(`${L.ticket.currency??"CNY"} ${e(v)(((F=(B=L.ticket.segment)==null?void 0:B[0])==null?void 0:F.tktTag)??"","totalAmount")}`),1)])):(s(),m("div",yd,[t("div",hd,n(U.$t("app.agentTicketRefund.totalTicketAmount")),1),L.ticket.totalAmount?(s(),m("div",_d,n(U.isFinishManualRefund||U.partSuccess?e(r)(L.ticket.ticketSegment,"totalAmount"):`${L.ticket.currency??"CNY"} ${L.ticket.totalAmount}`),1)):(s(),m("div",vd,"--"))])),t("div",bd,[t("div",xd,n(U.$t("app.agentTicketRefund.totalTaxAmount")),1),(s(),ae(qn,{key:`InternationalRefundTaxDetailPopover${se}`,"is-auto-refund-finished":U.isAutoRefundFinished,"batch-auto-taxes":e(ie)(((ve=(de=L.ticket.segment)==null?void 0:de[0])==null?void 0:ve.tktTag)??"","taxes"),ticket:L.ticket,taxs:e(y)(L.ticket.ticketSegment),"is-finish-manual-refund":U.isFinishManualRefund,"part-success":U.partSuccess},{"tax-deatil":l(()=>{var re,Ce;return[U.isAutoRefundFinished?(s(),m("div",Td,n(`${L.ticket.currency??"CNY"} ${e(v)(((Ce=(re=L.ticket.segment)==null?void 0:re[0])==null?void 0:Ce.tktTag)??"","totalTaxs")}`),1)):(s(),m("div",$d,n(U.isFinishManualRefund||U.partSuccess?e(r)(L.ticket.ticketSegment,"tax"):`${L.ticket.currency??"CNY"} ${L.ticket.totalTaxs}`),1))]}),_:2},1032,["is-auto-refund-finished","batch-auto-taxes","ticket","taxs","is-finish-manual-refund","part-success"]))]),!U.refundTicketSuccess&&!((L==null?void 0:L.ticketManagementOrganizationCode)??"").includes("CDS")?(s(),m("div",Nd,[c(he,{model:L,"require-asterisk-position":"right",class:"device-form"},{default:l(()=>[c(le,{prop:"printNo",error:L.printError},{default:l(()=>[t("div",Rd,[K(n(U.$t("app.agentTicketRefund.prntNo")),1),Cd]),c(Kt,{modelValue:L.printerNo,"onUpdate:modelValue":re=>L.printerNo=re,"select-class":"w-[100px]",onBlur:re=>e(j)(se)},null,8,["modelValue","onUpdate:modelValue","onBlur"])]),_:2},1032,["error"])]),_:2},1032,["model"])])):Z("",!0)]),L.ticket.isRefundFail?Z("",!0):(s(),m("div",wd,[t("div",Ad,[t("div",Sd,n(e(r)(L.ticket.ticketSegment,"commision").includes("%")?U.$t("app.agentTicketRefund.commissionRate"):U.$t("app.agentTicketRefund.commission")),1),!U.isFinishManualRefund&&!U.partSuccess?(s(),m("div",Dd,n(e(v)(((fe=(O=L.ticket.segment)==null?void 0:O[0])==null?void 0:fe.tktTag)??"","commision")&&e(v)(((Te=(Y=L.ticket.segment)==null?void 0:Y[0])==null?void 0:Te.tktTag)??"","commision")!=="--"?`${e(R)(L.ticket)} ${e(v)(((ge=(V=L.ticket.segment)==null?void 0:V[0])==null?void 0:ge.tktTag)??"","commision")}`:"--"),1)):(s(),m("div",Pd,n(e(r)(L.ticket.ticketSegment,"commision")),1))]),t("div",Od,[!U.isFinishManualRefund&&!U.partSuccess?(s(),m(ye,{key:0},[t("div",Ed,n(U.$t("app.agentTicketRefund.commissionRate")),1),t("div",Fd,n(isNaN(Number(e(v)(((Re=(me=L.ticket.segment)==null?void 0:me[0])==null?void 0:Re.tktTag)??"","commisionRate")))?e(v)(((Be=(Ee=L.ticket.segment)==null?void 0:Ee[0])==null?void 0:Be.tktTag)??"","commisionRate"):`${e(v)(((we=(Oe=L.ticket.segment)==null?void 0:Oe[0])==null?void 0:we.tktTag)??"","commisionRate")??""}%`),1)],64)):Z("",!0)]),t("div",Vd,[t("div",Md,n(U.$t("app.agentTicketRefund.charge")),1),!U.isFinishManualRefund&&!U.partSuccess?(s(),m("div",jd,n(e(R)(L.ticket))+" "+n(e(v)(((I=(Ze=L.ticket.segment)==null?void 0:Ze[0])==null?void 0:I.tktTag)??"","otherDeduction")),1)):(s(),m("div",Ld,n(e(r)(L.ticket.ticketSegment,"otherDeduction")),1))]),t("div",Bd,[t("div",Id,n(U.$t("app.agentTicketRefund.totalRefund")),1),!U.isFinishManualRefund&&!U.partSuccess?(s(),m("div",Ud,n(e(R)(L.ticket))+" "+n(e(v)((($e=(_e=L.ticket.segment)==null?void 0:_e[0])==null?void 0:$e.tktTag)??"","netRefund")),1)):(s(),m("div",Qd,n(e(r)(L.ticket.ticketSegment,"netRefund")),1))])])),L.ticket.isRefundFail?(s(),m("div",qd,[t("span",zd,n(U.$t("app.agentTicketRefund.refundFailMsg")),1)])):Z("",!0)])],64))],2)),[[ce,e(u),void 0,{fullscreen:!0,lock:!0}]])}),128)),e(oe)?(s(),ae(Xa,{key:0,modelValue:e(oe),"onUpdate:modelValue":A[0]||(A[0]=L=>Ve(oe)?oe.value=L:null),"ticket-management-organization-code":e(d).ticketManagementOrganizationCode??"",onOpenDialog:e(D)},null,8,["modelValue","ticket-management-organization-code","onOpenDialog"])):Z("",!0),e(p)?(s(),ae(ta,{key:1,modelValue:e(p),"onUpdate:modelValue":A[1]||(A[1]=L=>Ve(p)?p.value=L:null),"printer-no":e(i),"printer-type":e(_),"is-supplement-refund":!1,"refund-operation-condition":e(d),"refund-ticket-data":e(x)},null,8,["modelValue","printer-no","printer-type","refund-operation-condition","refund-ticket-data"])):Z("",!0)],64)}}});const Hd=at(Gd,[["__scopeId","data-v-00da54bf"]]),Wd={class:"flex text-[16px] text-[#000]"},Yd={class:"ml-[10px] w-full"},Kd={class:"pb-[6px]"},Xd={key:0,class:"flex"},Jd={class:"w-[294px]"},Zd={key:1,class:"text-green-1"},ep={key:2,class:"flex"},tp={class:"w-[294px]"},np={key:3,class:"text-green-1"},ap={key:4,class:"mt-[12px] mb-[12px]"},sp={class:"text-end w-full"},op=je({__name:"interRefundSuccessdialog",props:{newPnr:{},oldPnr:{},ticketDetailList:{}},emits:["update:modelValue","refresh"],setup(a,{emit:o}){const u=a,h=o,{t:f}=We(),p=Cn(),i=H(!0),_=H(!1),d=H(!1),x=H(!1),T=async $=>{p.push({name:"PnrManagement",query:{pnrNo:$,time:new Date().getTime()}});const P=Pe("091Q0104");h("refresh",!0,P),h("update:modelValue",!1)},k=($,P)=>{mt({message:$?f("app.agentTicketRefund.cancelPnrSuccess",{pnr:P}):f("app.agentTicketRefund.manualDeleteSuccess"),type:"success"})},R=$=>{const P={pnrNo:$?"":u.oldPnr,xePnr:$?u.newPnr:"",passengerInfoList:[],pnrHandleType:$?"C":"D"};return(u.ticketDetailList??[]).forEach(M=>{!$&&M.ticket.psgType==="INF"&&P.passengerInfoList.push({name:Ft.encode(M.ticket.name),psgType:M.ticket.psgType,ticketNo:M.ticket.ticketNo})}),P},v=async $=>{x.value=!0;let P;try{P=(await Zn(R($),"")).data.value}finally{x.value=!1}$&&(P!=null&&P.xePnrExecutionStatus.includes("S"))?(k($,u.newPnr),d.value=!0):!$&&(P!=null&&P.deleteInfantExecutionStatus.includes("S"))&&(k($),_.value=!0)},r=()=>{v(!1)},y=()=>{v(!0)},C=()=>{const $=Pe("091Q0104");h("refresh",!0,$),h("update:modelValue",!1)};return($,P)=>{const M=Je,X=Ke,ue=nt,E=kt;return Qe((s(),m("div",null,[c(ue,{modelValue:i.value,"onUpdate:modelValue":P[2]||(P[2]=j=>i.value=j),"show-close":!1,"close-on-click-modal":!1,modal:!1,"close-on-press-escape":!1,class:"refund-success-dialog",width:"440"},{default:l(()=>[t("div",Wd,[t("div",null,[c(M,{class:"top-[calc(50%-30px)]",size:30,color:"var(--bkc-color-special-green-2)"},{default:l(()=>[c(e(Gn))]),_:1})]),t("div",Yd,[t("p",Kd,n(e(f)("app.agentTicketRefund.autoRefundSuccess")),1),_.value?(s(),m("div",Zd,"PNR : "+n($.oldPnr)+n(e(f)("app.agentTicketRefund.passengerBeDelete")),1)):(s(),m("div",Xd,[t("div",Jd,[K(n(e(f)("app.agentTicketRefund.whetherAutoDelete")),1),t("span",{class:"underline text-brand-2 cursor-pointer",onClick:P[0]||(P[0]=j=>T($.oldPnr))},"PNR : "+n($.oldPnr),1),K(n(e(f)("app.agentTicketRefund.refundTicketTip")),1)]),c(X,{class:"operate-btn",type:"primary",onClick:r},{default:l(()=>[K(n(e(f)("app.agentTicketRefund.deletePassengerOperate")),1)]),_:1})])),d.value?(s(),m("div",np,"PNR : "+n($.newPnr)+" "+n(e(f)("app.agentTicketRefund.canceled")),1)):(s(),m("div",ep,[t("div",tp,[K(n(e(f)("app.agentTicketRefund.whetherAutoCancel")),1),t("span",{class:"underline text-brand-2 cursor-pointer",onClick:P[1]||(P[1]=j=>T($.newPnr))},"PNR : "+n($.newPnr),1),K(" ？ ")]),c(X,{class:"operate-btn",type:"primary",onClick:y},{default:l(()=>[K(n(e(f)("app.agentTicketRefund.cancelPnr")),1)]),_:1})])),d.value&&_.value?Z("",!0):(s(),m("div",ap,n(e(f)("app.agentTicketRefund.goToOrder")),1))])]),t("div",sp,[d.value&&_.value?(s(),ae(X,{key:0,type:"primary",onClick:C},{default:l(()=>[K(n(e(f)("app.agentTicketRefund.sure")),1)]),_:1})):(s(),ae(X,{key:1,onClick:C},{default:l(()=>[K(n(e(f)("app.agentTicketRefund.cancel")),1)]),_:1}))])]),_:1},8,["modelValue"])])),[[E,x.value,void 0,{fullscreen:!0,lock:!0}]])}}});const ip={class:"bg-[var(--bkc-el-bg-color)] p-[10px] rounded-lg mt-[10px] min-h-[calc(100vh_-_33vh)] shadow-[0_0_8px_0_rgba(109,117,151,0.2)]"},lp={class:"flex items-center"},rp={class:"text-gray-1 text-base font-bold leading-normal"},cp={key:0,class:"ml-[14px] p-[4px] rounded-[2px] bg-brand-3 text-brand-1 text-[14px]"},up={class:"inline-block h-full border-yellow-2 border-solid bg-yellow-3 text-yellow-1 text-[12px] rounded-[2px] py-[8px] px-[10px] flex items-center"},dp={key:1,class:"ml-[14px] p-[4px] rounded-[2px] bg-brand-3 text-brand-1 text-[14px]"},pp={class:"footer"},fp=je({__name:"TicketRefundContainer",props:{tktNo:{},factor:{}},emits:["addNewTab","removeTab"],setup(a,{emit:o}){const u=a,h=o,{splitPnrNo:f,isDragonBoatOffice:p,pnrNo:i,amountRef:_,fullscreenLoading:d,allPassengerList:x,ticketDetailList:T,manualDialogVisible:k,packageData:R,clacAmountInfosRes:v,clacAmountInfo:r,isAutoRefundFinished:y,isXePnr:C,currentTktNo:$,deviceNum:P,isFinishManualRefund:M,manualRefundAmountInfo:X,partSuccess:ue,isCanRefund:E,queryTicketDetail:j,manualRefund:oe,handleAutoRefund:D,getCalcInfo:b,queryTicketAndCalcAmount:N,getManualRefundAmountDetail:ne,refresh:ie,ticketTrfdNoDetails:U,isShowTrfdNo:A,getTicketTrfdNoDetailsFromManualRefund:le,refundType:he,isRelatedCorrectPnr:ce,updatePnrForm:L,updatePnrFormData:se,updatePnrFormRules:Q,queryAllPassenger:S,queryPnrTip:G,showInterRefundSuccess:J,refreshTicketDetail:z,refundTicketSuccess:w,isAuthToShowWindow:g,batchAutoAmount:B}=Pi(u,h);return(F,de)=>{const ve=gt,O=ot,fe=Ke,Y=Je,Te=it,V=_n("permission"),ge=kt;return Qe((s(),m("div",ip,[t("div",lp,[t("div",rp,n(F.$t("app.agentTicketRefund.refund")),1),t("div",null,[e(i)?(s(),m(ye,{key:0},[e(ce)?(s(),m("span",cp,"PNR："+n(e(i)),1)):(s(),ae(Te,{key:1,ref_key:"updatePnrForm",ref:L,model:e(se),inline:!0,rules:e(Q),"require-asterisk-position":"right",class:"ml-[14px] h-[32px] flex items-center updatePnrForm"},{default:l(()=>[c(O,{prop:"pnrNo",label:F.$t("app.agentTicketQuery.pnrNumber")},{default:l(()=>[c(ve,{modelValue:e(se).pnrNo,"onUpdate:modelValue":de[0]||(de[0]=me=>e(se).pnrNo=me),modelModifiers:{trim:!0},clearable:""},null,8,["modelValue"])]),_:1},8,["label"]),c(O,null,{default:l(()=>[c(fe,{type:"primary",onClick:de[1]||(de[1]=me=>e(S)(e(se).pnrNo,"091Q0101",e(L)))},{default:l(()=>[K(n(F.$t("app.agentTicketQuery.queryBtn")),1)]),_:1})]),_:1}),t("span",up,[c(Y,{size:"14px",class:"mr-[5px]"},{default:l(()=>[c(e(Hn))]),_:1}),K(n(e(G)),1)])]),_:1},8,["model","rules"]))],64)):(s(),m("span",dp,"PNR：-"))])]),c(zr,{"all-passenger-list":e(x),"clac-amount-info":e(r),"refund-ticket-success":e(w),onQueryTicketDetail:e(j),onQueryTicketAndCalcAmount:e(N)},null,8,["all-passenger-list","clac-amount-info","refund-ticket-success","onQueryTicketDetail","onQueryTicketAndCalcAmount"]),e(T).length?(s(),ae(Hd,{key:0,"ticket-detail-list":e(T),"refund-ticket-success":e(w),"is-finish-manual-refund":e(M),"is-show-trfd-no":e(A),"part-success":e(ue),"manual-refund-amount-info":e(X),"ticket-amount-list":e(v).queryRefundFeeAggregateRespDTOList,"ticket-trfd-no-details":e(U),"refund-type":e(he),"device-num":e(P),"is-auth-to-show-window":e(g),"is-auto-refund-finished":e(y),"batch-auto-amount":e(B)},null,8,["ticket-detail-list","refund-ticket-success","is-finish-manual-refund","is-show-trfd-no","part-success","manual-refund-amount-info","ticket-amount-list","ticket-trfd-no-details","refund-type","device-num","is-auth-to-show-window","is-auto-refund-finished","batch-auto-amount"])):Z("",!0),c(rc,{ref_key:"amountRef",ref:_,"batch-auto-amount":e(B),"amount-data":e(v).queryRefundFeeAggregateRespDTOList,status:e(v).status,"is-auto-refund-finished":e(y),"is-finish-manual-refund":e(M),"manual-refund-amount-info":e(X),"is-can-refund":e(E),onGetCalcInfo:e(b)},null,8,["batch-auto-amount","amount-data","status","is-auto-refund-finished","is-finish-manual-refund","manual-refund-amount-info","is-can-refund","onGetCalcInfo"]),t("div",pp,[e(w)?Z("",!0):(s(),m(ye,{key:0},[Qe((s(),ae(fe,{"data-gid":"091Q0105",disabled:!e(r).isAlreadySuccessSearch||!e(E),onClick:e(D)},{default:l(()=>[K(n(F.$t("app.agentTicketRefund.refund")),1)]),_:1},8,["disabled","onClick"])),[[V,"crs-ticket-manage-refund-page-refund-button"]]),Qe((s(),ae(fe,{disabled:!e(E),onClick:e(oe)},{default:l(()=>[K(n(F.$t("app.agentTicketRefund.manualRefundBtn")),1)]),_:1},8,["disabled","onClick"])),[[V,"crs-ticket-manage-refund-page-manual-refund-button"]])],64)),c(fe,{"data-gid":"091Q0104",onClick:e(ie)},{default:l(()=>[K(n(F.$t("app.agentTicketRefund.refresh")),1)]),_:1},8,["onClick"])]),e(k)?(s(),ae(Mr,{key:1,modelValue:e(k),"onUpdate:modelValue":de[2]||(de[2]=me=>Ve(k)?k.value=me:null),"ticket-detail-list":e(T),"onUpdate:ticketDetailList":de[3]||(de[3]=me=>Ve(T)?T.value=me:null),isShowTrfdNo:e(A),"onUpdate:isShowTrfdNo":de[4]||(de[4]=me=>Ve(A)?A.value=me:null),"all-passengers":e(x),"is-dragon-boat-office":e(p),"pnr-no":e(i),"is-xepnr":e(C),"tkt-no":e($),"package-data":e(R),"device-num":e(P),"is-auth-to-show-window":e(g),onQueryTicketDetail:e(j),onGetManualRefundAmountDetail:e(ne),onGetTicketTrfdNoDetails:e(le)},null,8,["modelValue","ticket-detail-list","isShowTrfdNo","all-passengers","is-dragon-boat-office","pnr-no","is-xepnr","tkt-no","package-data","device-num","is-auth-to-show-window","onQueryTicketDetail","onGetManualRefundAmountDetail","onGetTicketTrfdNoDetails"])):Z("",!0),e(J)?(s(),ae(op,{key:2,modelValue:e(J),"onUpdate:modelValue":de[5]||(de[5]=me=>Ve(J)?J.value=me:null),"ticket-detail-list":e(T),"new-pnr":e(f),"old-pnr":e(i),onRefresh:e(z)},null,8,["modelValue","ticket-detail-list","new-pnr","old-pnr","onRefresh"])):Z("",!0)])),[[ge,e(d),void 0,{fullscreen:!0,lock:!0}]])}}});const Nn=at(fp,[["__scopeId","data-v-53dd8c11"]]),mp=(a,o)=>{const{t:u}=We(),h=Ct(),f=Me(()=>{var g;return(g=h.state.user)==null?void 0:g.entityType}),p=tt({pnrNo:""}),i=H(),_=H(),d=H({travellerInfoList:[]}),x=H([]),T=H(!1),k=Ae(!1),R=H([]),v={ticketManagementOrganizationCode:[{required:!0,message:u("app.agentTicketQuery.repelTicket.plsInputTicketMachineNumber"),trigger:"blur"}],ticketMachineNumber:[{required:!0,message:u("app.agentTicketQuery.repelTicket.plsInputTicketMachineNumber"),trigger:"blur"},{pattern:Gt,trigger:"blur",message:u("app.agentTicketQuery.repelTicket.ticketMachineNumberReg")}],pnrNo:[{required:!0,message:u("app.agentTicketQuery.repelTicket.pnrNotAllowNull"),trigger:"blur"},{pattern:hn,message:u("app.agentTicketQuery.repelTicket.pnrReg"),trigger:"blur"}]},r=Me(()=>{var g;return!(p.pnrNo&&((g=x.value)==null?void 0:g.length)>0)}),y=Me(()=>{var B,F;const g=d.value.travellerInfoList.filter(de=>de.alreadyInvalid)??[];return!!((F=(B=x.value)==null?void 0:B[0])!=null&&F.crsPnrNo)&&d.value.travellerInfoList.length>0&&x.value.length>0&&d.value.travellerInfoList.length===x.value.length+g.length}),C=Me(()=>{const g=d.value.travellerInfoList.filter(B=>B.invalid)??[];return d.value.travellerInfoList.length>0&&x.value.length>0&&g.length===x.value.length}),$=Me(()=>d.value.travellerInfoList.every(g=>!g.invalid)),P=Me(()=>!p.pnrNo),M=g=>{if(!g){x.value=[];return}x.value=d.value.travellerInfoList.filter(B=>B.invalid)??[]},X=()=>({pnrNo:p.pnrNo??""}),ue=g=>{var F,de;return(R.value??[]).some(ve=>g===ve.value)&&g?g:((de=(F=R.value)==null?void 0:F[0])==null?void 0:de.value)??""},E=g=>{var B;[d.value.travellerInfoList,x.value,k.value]=[[],[],!0],d.value.travellerInfoList=ft(g)??[],d.value.travellerInfoList.forEach(F=>{F.ticketManagementOrganizationCode=ue(F.ticketManagementOrganizationCode),F.ticketMachineNumber="",F.ticketMachineNumberError="",F.ticketOrganizationError=""}),((B=d.value.travellerInfoList)==null?void 0:B.length)===1&&d.value.travellerInfoList[0].invalid&&x.value.push(d.value.travellerInfoList[0])},j=async()=>{i.value.validate(async g=>{if(!g)return;const B=(await Zt(mo(X()))).data.value;E(B)})},oe=()=>x.value.map(g=>({etNo:g.ticketNo??"",passengerName:Ft.encode(g.passengerNameSuffix)??"",etType:g.etType??"",passengerType:g.specialPassengerType??"",printerNo:g.ticketMachineNumber??"",ticketTypeCode:g.ticketTypeCode,governmentPurchase:g.governmentPurchase,pnrNo:g.crsPnrNo||p.pnrNo,paymentBOP:g.paymentBOP,ticketManagementOrganizationCode:g.ticketManagementOrganizationCode})),D=()=>({invalidTicketDetails:oe(),xePnr:T.value}),b=()=>{Xe.close();const g=`/v2/crs/pnrManagement?pnrNo=${p.pnrNo}`;wn.setLink(g),a("update:modelValue",!1)},N=async g=>Xe({message:g,icon:Le("em",{class:"iconfont icon-info-circle-line text-brand-2"}),customClass:"success-message-common crs-btn-ui",dangerouslyUseHTMLString:!0,closeOnClickModal:!1,showClose:!1,showCancelButton:!1,confirmButtonText:u("app.agentTicketQuery.repelTicket.confirm")}),ne=async g=>{T.value&&g?await cn(u("app.agentTicketQuery.repelTicket.repelTicketXEPnrSuccess")):T.value&&!g?await N(Le("div",null,[Le("span",{class:"block"},u("app.agentTicketQuery.repelTicket.repelTicketSuccess")),Le("span",null,u("app.agentTicketQuery.repelTicket.repelTicketSuccessXEPnrFail_1")),Le("span",{class:"text-brand-2 cursor-pointer",onClick:b},u("app.agentTicketQuery.repelTicket.repelTicketSuccessXEPnrFail_2")),Le("span",null,u("app.agentTicketQuery.repelTicket.repelTicketSuccessXEPnrFail_3"))])):await N(Le("div",null,[Le("span",{class:"block"},u("app.agentTicketQuery.repelTicket.repelTicketSuccess")),Le("span",null,u("app.agentTicketQuery.repelTicket.repelTicketSuccessNoXEPnrTip_1"))])),a("update:modelValue",!1),a("reQueryTicket")},ie=g=>(g??[]).reduce((B,F)=>{let de="";B||(de=`<p class="text-gray-1 text-lg font-normal pb-2.5">${u("app.agentTicketQuery.repelTicket.partRepelTicketSuccess")}</p>`);const ve=F.vtSuccess?'<i class="iconfont icon-ticket text-emerald-600 mr-2.5"></i>':'<i class="iconfont icon-close text-rose-600 mr-2.5"></i>',O=`${de}<p class="mt-4">${ve}${F.passengerNameSuffix}<span class="text-center text-gray-3 text-xs font-normal inline-block bg-gray-7 px-1 py-0.5 text-gray-3 leading-tight ml-2.5"><span>${Rt(F.specialPassengerType)}</span></span></p>`;return B+O},""),U=g=>{Xe.confirm(ie(g),{icon:Le(Je,{color:"#FF3636",size:32},()=>Le(Na)),customClass:"invalidated-warning-msg",closeOnClickModal:!1,showClose:!0,showCancelButton:!1,confirmButtonText:u("app.agentTicketQuery.repelTicket.confirm"),dangerouslyUseHTMLString:!0,draggable:!0}).then(()=>{a("update:modelValue",!1),a("reQueryTicket")})},A=g=>{d.value.travellerInfoList[g].ticketMachineNumberError="",d.value.travellerInfoList=[...d.value.travellerInfoList]},le=()=>{const g=x.value.map(B=>B.ticketNo);d.value.travellerInfoList.forEach(B=>{g.includes(B.ticketNo)||(B.ticketMachineNumberError="")})},he=()=>{const g=x.value.map(B=>B.ticketNo);for(let B=0;B<d.value.travellerInfoList.length;B++){const F=d.value.travellerInfoList[B];if(g.includes(F.ticketNo)&&Q(F.ticketManagementOrganizationCode)){if(!F.ticketMachineNumber){F.ticketMachineNumberError=u("app.agentTicketQuery.repelTicket.plsInputTicketMachineNumber");continue}if(!Gt.test(F.ticketMachineNumber)){F.ticketMachineNumberError=u("app.agentTicketQuery.repelTicket.ticketMachineNumberReg");continue}}else F.ticketMachineNumberError=""}return d.value.travellerInfoList.every(B=>!B.ticketMachineNumberError)},ce=()=>{const g=x.value.map(B=>B.ticketNo);for(let B=0;B<d.value.travellerInfoList.length;B++){const F=d.value.travellerInfoList[B];if(g.includes(F.ticketNo)&&!F.ticketManagementOrganizationCode){F.ticketOrganizationError=u("app.agentTicketQuery.repelTicket.plsInputTicketMachineNumber");continue}}return d.value.travellerInfoList.every(B=>!B.ticketOrganizationError)},L=()=>{const g=Pe("091P0102");x.value.length!==0&&_.value.validate(async B=>{if(!B||!ce()||!he())return;const{entireSuccess:F,invalidTicketItems:de,xePnrSuccess:ve}=(await Zt(go(D(),g))).data.value;F?ne(ve):!F&&de.length>0&&U(de)})},se=()=>{a("update:modelValue",!1)},Q=g=>!["CDS","GPCDS"].includes(g),S={BSP:{label:u("app.agentTicketQuery.BSPTicket"),value:"BSP"},GPBSP:{label:u("app.agentTicketQuery.GPTicket"),value:"GPBSP"},BOPBSP:{label:u("app.agentTicketQuery.BOPTicket"),value:"BOPBSP"},CDS:{label:u("app.agentTicketQuery.CDSTicket"),value:"CDS"},GPCDS:{label:u("app.agentTicketQuery.GP_CDSTicket"),value:"GPCDS"},ARL:{label:u("app.agentTicketQuery.OWNTicket"),value:"ARL"}},G=()=>{var g,B,F,de,ve,O,fe,Y,Te,V;((g=f.value)!=null&&g.includes("$$$")||(B=f.value)!=null&&B.includes("BSP"))&&(R.value.push(S.BSP),R.value.push(S.GPBSP)),!((F=f.value)!=null&&F.includes("BSP"))&&((de=f.value)!=null&&de.includes("GP"))&&R.value.push(S.GPBSP),((ve=f.value)!=null&&ve.includes("$$$")||(O=f.value)!=null&&O.includes("BOP"))&&R.value.push(S.BOPBSP),((fe=f.value)!=null&&fe.includes("$$$")||(Y=f.value)!=null&&Y.includes("CDS"))&&(R.value.push(S.CDS),R.value.push(S.GPCDS)),((Te=f.value)!=null&&Te.includes("$$$")||(V=f.value)!=null&&V.includes("本票"))&&R.value.push(S.ARL)},J=g=>{var F,de,ve,O,fe,Y,Te,V,ge,me,Re,Oe,we;return{passengerName:((F=g==null?void 0:g[0])==null?void 0:F.passengerName)??"",passengerNameSuffix:((de=g==null?void 0:g[0])==null?void 0:de.passengerNameSuffix)??"",passengerType:((ve=g==null?void 0:g[0])==null?void 0:ve.passengerType)??"",pnrPsgType:"",specialPassengerType:((O=g==null?void 0:g[0])==null?void 0:O.specialPassengerType)??"",ticketNo:((fe=g==null?void 0:g[0])==null?void 0:fe.ticketNo)??"",invalid:(Y=g==null?void 0:g[0])==null?void 0:Y.invalid,etType:(Te=g==null?void 0:g[0])==null?void 0:Te.etType,alreadyInvalid:(V=g==null?void 0:g[0])==null?void 0:V.alreadyInvalid,ticketMachineNumber:"",crsPnrNo:((ge=g==null?void 0:g[0])==null?void 0:ge.crsPnrNo)??"",ticketTypeCode:((me=g==null?void 0:g[0])==null?void 0:me.ticketTypeCode)??"D",governmentPurchase:((Re=g==null?void 0:g[0])==null?void 0:Re.governmentPurchase)??!1,paymentBOP:((Oe=g==null?void 0:g[0])==null?void 0:Oe.paymentBOP)??!1,ticketManagementOrganizationCode:ue((we=g==null?void 0:g[0])==null?void 0:we.ticketManagementOrganizationCode),ticketMachineNumberError:"",ticketOrganizationError:""}},z=async()=>{var O,fe;const g=o.invalidatedTicketQueryGid,{etNumber:B,secondFactor:F}=o.ticketOperationCondition,de=(await Zt(ko({ticketNo:B,secondFactor:F},g))).data.value,ve=J(de);ve.invalid&&(d.value.travellerInfoList.push(ve),x.value.push(ve),p.pnrNo=((fe=(O=x.value)==null?void 0:O[0])==null?void 0:fe.crsPnrNo)??"")},w=async(g,B)=>{var ve;const F=Pe("091P0103"),de=((ve=(await Zt(Ma(B.split("-").length>2?B.substring(0,14):B,F))).data.value)==null?void 0:ve.data)??{};d.value.travellerInfoList[g].ticketMachineNumber=de.ticket.printNumber,d.value.travellerInfoList[g].paymentBOP=de.ticket.ticketManagementOrganizationCode==="BOP",d.value.travellerInfoList[g].governmentPurchase=de.ticket.ticketManagementOrganizationCode==="GP",d.value.travellerInfoList=[...d.value.travellerInfoList],de.ticket.printNumber&&(d.value.travellerInfoList[g].ticketMachineNumberError="")};return st(async()=>{var g,B;G(),(g=o.ticketOperationCondition)!=null&&g.pnrNo&&(p.pnrNo=(B=o.ticketOperationCondition)==null?void 0:B.pnrNo),z()}),wt(()=>y.value,()=>{y.value||(T.value=!1)}),{closeDialog:se,formData:p,REPEL_TICKET_RULES:v,travellerInfos:d,repelTicketBtnDisabled:r,isCheckAll:C,isCheckAllDisabled:$,queryBtnDisabled:P,showXEPnr:y,checkedXEPnr:T,isQueriedTravelers:k,ticketOrganizationList:R,dealCheckAllOperate:M,checkTravellerIndexList:x,repelTicketFormRef:i,repelTicketPassengerFormRef:_,confirmSearchOperate:j,confirmRepelTicketOperate:L,queryRtkt:w,clearRepelTicketFormItemValidate:A,clearRepelTicketFormValidate:le,isShowPrintNo:Q}},gp=t("i",{class:"iconfont icon-close"},null,-1),kp=[gp],yp={class:"w-full px-2.5 py-2 bg-yellow-3 rounded border border-yellow-2 flex-col justify-start items-start gap-2.5 inline-flex"},hp={class:"justify-start items-center gap-1 inline-flex"},vp=t("div",{class:"w-4 h-4 relative"},[t("em",{class:"u-icon iconfont icon-warning-circle-fill text-yellow-1 relative bottom-1"})],-1),_p={class:"text-yellow-1 text-xs font-normal leading-tight"},bp={class:"mt-5 mb-[10px]"},xp={class:"w-full h-[auto] p-2.5 bg-gray-0 rounded border border-brand-3 flex-col justify-start items-start inline-flex"},Tp={key:0},$p=t("span",null,null,-1),Np={class:"flex items-center w-[176px]"},Rp={class:"text-center text-gray-3 text-xs font-normal inline-block bg-gray-7 px-1 py-0.5 text-gray-3 leading-tight"},Cp={class:"h-[auto] text-gray-2 text-sm font-normal leading-snug grow flex flex-inline items-center"},wp=t("i",{class:"iconfont icon-ticket-fill mr-1.5 text-gray-400"},null,-1),Ap={class:"carType-option-panel"},Sp={key:1,class:"w-full text-center text-gray-4"},Dp={class:"text-center mt-4 flex justify-center items-center"},Pp={class:"crs-btn-dialog-ui repel-ticket"},Op=je({__name:"RepelTicketDialog",props:{invalidatedTicketQueryGid:{},ticketOperationCondition:{}},emits:["update:modelValue","reQueryTicket"],setup(a,{emit:o}){const u=o,h=a,{closeDialog:f,formData:p,REPEL_TICKET_RULES:i,travellerInfos:_,repelTicketBtnDisabled:d,queryBtnDisabled:x,isCheckAll:T,isCheckAllDisabled:k,showXEPnr:R,checkedXEPnr:v,isQueriedTravelers:r,dealCheckAllOperate:y,checkTravellerIndexList:C,repelTicketFormRef:$,repelTicketPassengerFormRef:P,ticketOrganizationList:M,confirmSearchOperate:X,confirmRepelTicketOperate:ue,queryRtkt:E,clearRepelTicketFormItemValidate:j,clearRepelTicketFormValidate:oe,isShowPrintNo:D}=mp(u,h);return(b,N)=>{const ne=gt,ie=ot,U=Ke,A=it,le=an,he=aa,ce=yt,L=Je,se=tn,Q=nn,S=na,G=Pt,J=Ot,z=nt;return s(),ae(z,{title:b.$t("app.agentTicketQuery.repelTicket.repelTicket"),width:"720px","close-on-click-modal":!1,"show-close":!1,"align-center":!0,class:"repel-tikect-dialog tc-input-pad-init",onClose:e(f)},{default:l(()=>{var w;return[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:N[0]||(N[0]=(...g)=>e(f)&&e(f)(...g))},kp),t("div",yp,[t("div",hp,[vp,t("div",_p,n(b.$t("app.agentTicketQuery.repelTicket.repelTicketTip")),1)])]),t("div",bp,[c(A,{ref_key:"repelTicketFormRef",ref:$,model:e(p),rules:e(i),inline:!0,"label-position":"left","require-asterisk-position":"right",class:"w-full"},{default:l(()=>[c(ie,{prop:"pnrNo",label:"PNR"},{default:l(()=>[c(ne,{modelValue:e(p).pnrNo,"onUpdate:modelValue":N[1]||(N[1]=g=>e(p).pnrNo=g),placeholder:"PNR",clearable:""},null,8,["modelValue"])]),_:1}),c(ie,null,{default:l(()=>[c(U,{type:"primary","data-gid":"091P0101",disabled:e(x),onClick:e(X)},{default:l(()=>[K(n(b.$t("app.agentTicketQuery.queryBtn")),1)]),_:1},8,["disabled","onClick"])]),_:1})]),_:1},8,["model","rules"])]),t("div",xp,[((w=e(_).travellerInfoList)==null?void 0:w.length)>0?(s(),m(ye,{key:0},[e(r)?(s(),m(ye,{key:0},[e(r)?(s(),m("p",Tp,[c(le,{modelValue:e(T),"onUpdate:modelValue":N[2]||(N[2]=g=>Ve(T)?T.value=g:null),disabled:e(k),label:b.$t("app.agentTicketQuery.repelTicket.checkAll"),onChange:e(y)},null,8,["modelValue","disabled","label","onChange"])])):Z("",!0),c(he,{"border-style":"dashed"})],64)):Z("",!0),c(S,{modelValue:e(C),"onUpdate:modelValue":N[3]||(N[3]=g=>Ve(C)?C.value=g:null),onChange:e(oe)},{default:l(()=>[c(A,{ref_key:"repelTicketPassengerFormRef",ref:P,model:e(_),rules:e(i),"require-asterisk-position":"right",class:"passengr-form"},{default:l(()=>[(s(!0),m(ye,null,Ne(e(_).travellerInfoList,(g,B)=>(s(),m("div",{key:B+new Date,class:"h-auto w-[100%] flex items-center justify-between mb-[10px] last:mb-0"},[c(le,{label:g,class:"single-check",disabled:!g.invalid},{default:l(()=>[$p]),_:2},1032,["label","disabled"]),t("div",Np,[c(ce,{effect:"dark",placement:"top",content:g.passengerNameSuffix},{default:l(()=>[t("span",{class:Se(["text-gray-1 text-sm font-normal leading-snug mr-2 inline-block overflow-hidden whitespace-nowrap text-ellipsis",e(Rt)(g.specialPassengerType).length>2?"max-w-[100px]":"max-w-[120px]"])},n(g.passengerNameSuffix),3)]),_:2},1032,["content"]),t("span",Rp,n(e(Rt)(g.specialPassengerType)),1)]),t("div",Cp,[wp,K(" "+n(g.ticketNo||"--")+" ",1),c(ie,{prop:"travellerInfoList."+B+".ticketManagementOrganizationCode",error:g.ticketOrganizationError,class:"w-[90px] ml-2.5"},{default:l(()=>[c(Q,{modelValue:g.ticketManagementOrganizationCode,"onUpdate:modelValue":F=>g.ticketManagementOrganizationCode=F,class:"ticket-management-organization",disabled:g.ticketManagementOrganizationCode==="",placeholder:g.ticketManagementOrganizationCode===""?b.$t("app.agentTicketQuery.noData"):""},{default:l(()=>[(s(!0),m(ye,null,Ne(e(M),F=>(s(),ae(se,{key:F.value,label:F.label,value:F.value},{default:l(()=>[t("div",Ap,[t("div",{class:Se(g.ticketManagementOrganizationCode===F.value?"show-select":"hidden-select")},[c(L,null,{default:l(()=>[c(e(zn))]),_:1})],2),t("span",null,n(F.label),1)])]),_:2},1032,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","placeholder"])]),_:2},1032,["prop","error"])]),e(D)(g.ticketManagementOrganizationCode)?(s(),ae(ie,{key:0,prop:"travellerInfoList."+B+".ticketMachineNumber",label:b.$t("app.agentTicketQuery.repelTicket.ticketMachineNumber"),error:g.ticketMachineNumberError},{default:l(()=>[c(Kt,{modelValue:e(_).travellerInfoList[B].ticketMachineNumber,"onUpdate:modelValue":F=>e(_).travellerInfoList[B].ticketMachineNumber=F,"select-class":"w-[120px]",onBlur:F=>e(j)(B)},null,8,["modelValue","onUpdate:modelValue","onBlur"]),c(ce,{content:b.$t("app.agentTicketQuery.repelTicket.queryIssueDeviceNo"),placement:"top"},{default:l(()=>[c(L,{class:"ml-[4px] cursor-pointer",color:"#455AF7","data-gid":"091P0103",onClick:F=>e(E)(B,g.ticketNo??"")},{default:l(()=>[c(e(Ds))]),_:2},1032,["onClick"])]),_:2},1032,["content"])]),_:2},1032,["prop","label","error"])):Z("",!0)]))),128))]),_:1},8,["model","rules"])]),_:1},8,["modelValue","onChange"])],64)):(s(),m("div",Sp,n(b.$t("app.agentTicketRefund.noPassengers")),1))]),t("div",Dp,[Qe(c(J,{modelValue:e(v),"onUpdate:modelValue":N[4]||(N[4]=g=>Ve(v)?v.value=g:null),class:"ml-4"},{default:l(()=>[c(G,{label:!1},{default:l(()=>[K(n(b.$t("app.agentTicketQuery.repelTicket.onlyRepelTicket")),1)]),_:1}),c(G,{label:!0},{default:l(()=>{var g;return[K(n(b.$t("app.agentTicketQuery.repelTicket.xePnrAndRepelTicket"))+"(PNR:"+n((g=e(C)[0])==null?void 0:g.crsPnrNo)+")",1)]}),_:1})]),_:1},8,["modelValue"]),[[vn,e(R)]]),t("span",Pp,[c(U,{type:"primary",disabled:e(d),onClick:e(ue)},{default:l(()=>[K(n(b.$t("app.agentTicketQuery.repelTicket.confirmRepelTicket")),1)]),_:1},8,["disabled","onClick"]),c(U,{onClick:e(f)},{default:l(()=>[K(n(b.$t("app.agentTicketQuery.repelTicket.cancel")),1)]),_:1},8,["onClick"])])])]}),_:1},8,["title","onClose"])}}});const Ep=a=>{const{t:o}=We(),u=Ct(),h=H(!0),f=H(),p=H(""),i=H(!1),_=H(),d=H({authLevel:"oneLevel",office:""}),x={office:[{required:!0,message:o("app.agentTicketQuery.ticketAuth.officeTipOne"),trigger:"blur"},{pattern:Tn,message:o("app.agentTicketQuery.ticketAuth.officeTipTwo")}]},T=H(!1),k=H(),R=Me(()=>{var E;return((E=u.state.user)==null?void 0:E.agent)??""}),v=async()=>{var E,j,oe,D,b,N,ne;try{const ie=Pe("091N0203");i.value=!0;const U={ticketNumber:a.ticketNo},A=await ja(U,ie);((E=A==null?void 0:A.statusCode)==null?void 0:E.value)===200&&(p.value=((D=(oe=(j=A.data)==null?void 0:j.value)==null?void 0:oe.data.ticketDisplayAuthInfo)==null?void 0:D.bookOffice)??"",f.value=(ne=(N=(b=A.data)==null?void 0:b.value)==null?void 0:N.data.ticketDisplayAuthInfo)==null?void 0:ne.authInfoList.filter(le=>le.authTo!==""))}finally{i.value=!1}},r=async E=>{var j;try{i.value=!0;const oe={removeOffice:E,ticketNumber:a.ticketNo},D=Pe("091N0202");((j=(await Ln(oe,D)).data.value)==null?void 0:j.code)==="200"&&(await mt({message:o("app.agentTicketQuery.ticketAuth.removeAuthSuccess"),type:"success"}),await v())}finally{i.value=!1}},y=()=>{T.value=!0},C=async E=>{E&&await E.validate(async j=>{if(j)try{const oe=Pe("091N0201");i.value=!0;let D=!0;d.value.authLevel==="oneLevel"?D=!1:D=!0;const b={accreditOffice:d.value.office,reAuth:D,ticketNumber:a.ticketNo},N=[];N.push(Bn(b,oe)),(await Promise.allSettled(N)).filter(U=>U.value.data.value.code!=="200").length<1&&(await mt({message:o("app.agentTicketQuery.ticketAuth.addAuthSuccess"),type:"success"}),await v())}finally{i.value=!1}})},$=async()=>{T.value=!1},P=()=>`TICKET_AUTH_OFFICE_${R.value}_`??"",M=()=>{try{return(JSON.parse(localStorage.getItem(`${P()}add`)??"")||[]).map(j=>({value:j}))}catch{return[]}},X=()=>{if(Tn.test(d.value.office)){const E=M().map(oe=>oe.value).filter(oe=>oe!==d.value.office);E.unshift(d.value.office);const j=E.slice(0,5);localStorage.setItem(`${P()}add`,JSON.stringify(j))}},ue=E=>{var j;d.value.office=E.value,(j=_.value)==null||j.blur()};return st(async()=>{h.value=a.modelValue,v()}),wt(()=>d.value.office,E=>{d.value.office=(E==null?void 0:E.toUpperCase())??""}),{dialogTableVisible:h,tableData:f,authDelete:r,authFormRules:x,authForm:d,authSubmit:C,authTicketFormRef:k,showAdd:y,addAuthVisible:T,office:p,authCancel:$,showLoading:i,officeHistoryRef:_,loadOfficeHistory:M,saveOfficeHistory:X,selectOfficeHistory:ue}},Ja=a=>(bt("data-v-3b5cf850"),a=a(),xt(),a),Fp=Ja(()=>t("i",{class:"iconfont icon-close"},null,-1)),Vp=[Fp],Mp={class:"w-full px-0 pt-0.5 pb-5 bg-gray-0 rounded-md flex-col justify-start gap-5 inline-flex"},jp={class:"self-stretch justify-between items-center inline-flex"},Lp={class:"text-gray-1 text-lg font-bold leading-normal"},Bp={class:"self-stretch h-full flex-col justify-center items-center gap-2.5 flex w-[632px]"},Ip={class:"self-stretch justify-between items-center inline-flex"},Up={class:"justify-start items-center gap-[68px] flex"},Qp={class:"text-gray-1 text-sm font-normal leading-snug"},qp={class:"text-gray-1 text-sm font-normal leading-snug"},zp={class:"flex-col justify-start items-start inline-flex"},Gp={class:"self-stretch p-2.5 rounded border border-brand-3 justify-start items-start inline-flex"},Hp={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},Wp={class:"self-stretch h-[30px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},Yp={class:"grow shrink basis-0 h-[22px] text-gray-4 text-sm font-normal leading-snug"},Kp={class:"grow shrink basis-0 h-[22px] text-gray-1 text-sm font-normal leading-snug"},Xp={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},Jp={class:"self-stretch h-[30px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},Zp={class:"grow shrink basis-0 h-[22px] text-gray-4 text-sm font-normal leading-snug"},ef={class:"grow shrink basis-0 h-[22px] text-gray-1 text-sm font-normal leading-snug"},tf={key:0},nf={key:1},af={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},sf={class:"self-stretch h-[30px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},of={class:"grow shrink basis-0 h-[22px] text-gray-4 text-sm font-normal leading-snug"},lf=Ja(()=>t("i",{class:"cursor-pointer primary-color iconfont icon-delete text-brand-2"},null,-1)),rf={key:0,class:"self-stretch flex-col justify-start items-start gap-3.5 flex add-auth-tkt"},cf={class:"self-stretch h-9 px-2.5 py-2 bg-yellow-3 rounded border border-yellow-2 flex-col justify-start items-start gap-2.5 flex"},uf={class:"h-5 justify-start items-center inline-flex text-yellow-1"},df={class:"text-yellow-1 text-xs font-normal leading-tight"},pf={class:"w-[632px] justify-start items-center gap-2.5 inline-flex"},ff={class:"justify-start items-center gap-2.5 flex"},mf={class:"justify-start items-center flex"},gf={class:"text-gray-2 text-xs font-normal leading-tight"},kf={class:"text-gray-2 text-xs font-normal leading-tight"},yf={class:"justify-start items-center flex crs-btn-dialog-ui"},hf=je({__name:"AuthTicketDialog",props:{ticketNo:{}},emits:["update:modelValue"],setup(a){const o=a,{dialogTableVisible:u,tableData:h,authDelete:f,authForm:p,authFormRules:i,authSubmit:_,authTicketFormRef:d,showAdd:x,addAuthVisible:T,office:k,authCancel:R,showLoading:v,officeHistoryRef:r,loadOfficeHistory:y,saveOfficeHistory:C,selectOfficeHistory:$}=Ep(o);return(P,M)=>{const X=Ke,ue=Je,E=Ea,j=Pt,oe=Ot,D=ot,b=Ya,N=it,ne=nt,ie=kt;return s(),ae(ne,{modelValue:e(u),"onUpdate:modelValue":M[4]||(M[4]=U=>Ve(u)?u.value=U:null),"align-center":"true",width:"680px","close-on-click-modal":!1,class:"preview-dialog tickets-empower","show-close":!1},{default:l(()=>[Qe((s(),m("div",null,[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:M[0]||(M[0]=U=>P.$emit("update:modelValue",!1))},Vp),t("div",Mp,[t("div",jp,[t("div",Lp,n(P.$t("app.agentTicketQuery.ticketAuth.title")),1)]),t("div",Bp,[t("div",Ip,[t("div",Up,[t("div",Qp,n(P.$t("app.agentTicketQuery.ticketAuth.ticketNo"))+"："+n(P.ticketNo),1),t("div",qp,n(P.$t("app.agentTicketQuery.ticketAuth.ticketingOfficeNumber"))+"："+n(e(k)),1)]),t("div",zp,[c(X,{size:"small",type:"primary",link:"",onClick:e(x)},{default:l(()=>[K(n(P.$t("app.agentTicketQuery.ticketAuth.addingEntitlement")),1)]),_:1},8,["onClick"])])]),t("div",Gp,[t("div",Hp,[t("div",Wp,[t("div",Yp,n(P.$t("app.agentTicketQuery.ticketAuth.officeNum")),1)]),(s(!0),m(ye,null,Ne(e(h),(U,A)=>(s(),m("div",{key:A,class:"self-stretch h-[50px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},[t("div",Kp,n(U.authTo),1)]))),128))]),t("div",Xp,[t("div",Jp,[t("div",Zp,n(P.$t("app.agentTicketQuery.ticketAuth.authAuthority")),1)]),(s(!0),m(ye,null,Ne(e(h),(U,A)=>(s(),m("div",{key:A,class:"self-stretch h-[50px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},[t("div",ef,[U.reAuth?(s(),m("span",tf,[c(ue,null,{default:l(()=>[c(e(Wn))]),_:1})])):(s(),m("span",nf,n(P.$t("app.agentTicketQuery.ticketAuth.noAuth")),1))])]))),128))]),t("div",af,[t("div",sf,[t("div",of,n(P.$t("app.agentTicketQuery.ticketAuth.operation")),1)]),(s(!0),m(ye,null,Ne(e(h),(U,A)=>(s(),m("div",{key:A,class:"self-stretch h-[50px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-5 inline-flex"},[c(E,{title:P.$t("app.agentTicketQuery.ticketAuth.removeConfirm"),icon:e(un),teleported:!1,width:"160","icon-color":"var(--bkc-el-color-primary)","cancel-button-type":"button",onConfirm:le=>e(f)(U.authTo)},{reference:l(()=>[lf]),_:2},1032,["title","icon","onConfirm"])]))),128))])]),e(T)?(s(),m("div",rf,[t("div",cf,[t("div",uf,[c(ue,{class:"tooltip-icon mr-[8px]"},{default:l(()=>[c(e(Hn))]),_:1}),t("div",df,n(P.$t("app.agentTicketQuery.ticketAuth.addTipsTwo")),1)])]),t("div",pf,[t("div",ff,[c(N,{ref_key:"authTicketFormRef",ref:d,model:e(p),inline:"",size:"small",rules:e(i),"require-asterisk-position":"right"},{default:l(()=>[c(D,null,{default:l(()=>[c(oe,{modelValue:e(p).authLevel,"onUpdate:modelValue":M[1]||(M[1]=U=>e(p).authLevel=U)},{default:l(()=>[t("div",mf,[c(j,{label:"oneLevel",size:"large"},{default:l(()=>[t("span",gf,n(P.$t("app.agentTicketQuery.ticketAuth.oneAuthorization")),1)]),_:1}),c(j,{label:"twoLevel",size:"large"},{default:l(()=>[t("span",kf,n(P.$t("app.agentTicketQuery.ticketAuth.secondaryAuthorization")),1)]),_:1})])]),_:1},8,["modelValue"])]),_:1}),c(D,{label:P.$t("app.agentTicketQuery.ticketAuth.officeNum"),prop:"office"},{default:l(()=>[c(b,{ref_key:"officeHistoryRef",ref:r,modelValue:e(p).office,"onUpdate:modelValue":M[2]||(M[2]=U=>e(p).office=U),modelModifiers:{trim:!0},"fetch-suggestions":e(y),placeholder:P.$t("app.agentTicketQuery.ticketAuth.officeTipOne"),class:"w-full",debounce:"",onSelect:e($),onBlur:e(C)},null,8,["modelValue","fetch-suggestions","placeholder","onSelect","onBlur"])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),t("div",yf,[e(T)?(s(),ae(X,{key:0,type:"primary","data-gid":"091N0206",onClick:M[3]||(M[3]=U=>e(_)(e(d)))},{default:l(()=>[K(n(P.$t("app.agentTicketQuery.ticketAuth.confirm")),1)]),_:1})):Z("",!0),e(T)?(s(),ae(X,{key:1,onClick:e(R)},{default:l(()=>[K(n(P.$t("app.agentTicketQuery.ticketAuth.cancel")),1)]),_:1},8,["onClick"])):Z("",!0)])])])):Z("",!0)])])])),[[ie,e(v)]])]),_:1},8,["modelValue"])}}});const vf=at(hf,[["__scopeId","data-v-3b5cf850"]]),_f="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";let bf=(a=21)=>{let o="",u=crypto.getRandomValues(new Uint8Array(a|=0));for(;a--;)o+=_f[u[a]&63];return o};const xf=(a,o)=>{var r;const{t:u}=We(),h=Ct(),f=H(),p=tt(a.queryForm),{defaultOffice:i,defaultRoleWithPid:_,office:d}=h.getters.user,x=_?i:((r=d==null?void 0:d.split(";"))==null?void 0:r[0])??"",T=tt({prntNo:[{required:!0,message:u("app.agentTicketRefund.prntNoNotEmpty"),trigger:"change"},{pattern:ha,message:u("app.agentTicketRefund.prntNoError"),trigger:"change"}]}),k=()=>{var y;(y=f.value)==null||y.validate(C=>{var $;if(C){if((($=h.getters.user)==null?void 0:$.entityType)==="CDS"){Xe.alert(u("app.agentTicketRefund.bspTip"),{icon:Le("em",{class:"iconfont icon-close-circle-line"}),customClass:"agent-refund-msg-box",confirmButtonText:u("app.agentTicketRefund.sure"),showClose:!1,draggable:!0});return}o("refund",p)}})},R=y=>{y&&ha.test(y)&&Os(y,x)},v=()=>{var y;(y=f.value)==null||y.validate(C=>{C&&o("openPreview",!0)})};return st(async()=>{const y=Ps(x);p.prntNo=y??""}),{paramFormRef:f,forms:p,rules:T,refund:k,setDevice:R,openPreview:v}},Pn=a=>(bt("data-v-58b70490"),a=a(),xt(),a),Tf=Pn(()=>t("div",{class:"relative top-[8px] text-[var(--bkc-color-special-red-1)] text-xs font-normal leading-tight"},"*",-1)),$f=Pn(()=>t("span",{class:"iconfont icon-info-circle-line"},null,-1)),Nf=Pn(()=>t("div",{class:"relative top-[8px] text-[var(--bkc-color-special-red-1)] text-xs font-normal leading-tight"},"*",-1)),Rf=Pn(()=>t("div",{class:"relative top-[8px] text-[var(--bkc-color-special-red-1)] text-xs font-normal leading-tight"},"*",-1)),Cf=je({__name:"TicketRefundCondition",props:{queryForm:{}},emits:["refund","openPreview"],setup(a,{emit:o}){const u=o,h=a,{paramFormRef:f,forms:p,rules:i,refund:_,setDevice:d}=xf(h,u);return(x,T)=>{const k=yt,R=gt,v=ot,r=Pt,y=Ot,C=Ke,$=it;return s(),ae($,{ref_key:"paramFormRef",ref:f,inline:!0,class:"ticket-refund-condition",model:e(p),"label-position":"left","require-asterisk-position":"right"},{default:l(()=>[c(v,{prop:"tktNo",class:"ticket-refund-condition-tkt"},{label:l(()=>[t("label",null,n(x.$t("app.agentTicketRefund.tktNo")),1),Tf,c(k,{effect:"dark",content:x.$t("app.agentTicketRefund.conjunctionTip"),placement:"bottom-start"},{default:l(()=>[$f]),_:1},8,["content"])]),default:l(()=>[c(R,{modelValue:e(p).tktNo,"onUpdate:modelValue":T[0]||(T[0]=P=>e(p).tktNo=P),modelModifiers:{trim:!0},disabled:""},null,8,["modelValue"])]),_:1}),c(v,{prop:"prntNo",class:"ticket-refund-condition-prnt",rules:e(i).prntNo},{label:l(()=>[t("label",null,n(x.$t("app.agentTicketRefund.prntNo")),1),Nf]),default:l(()=>[c(R,{modelValue:e(p).prntNo,"onUpdate:modelValue":T[1]||(T[1]=P=>e(p).prntNo=P),modelModifiers:{trim:!0},onBlur:T[2]||(T[2]=P=>e(d)(e(p).prntNo))},null,8,["modelValue"])]),_:1},8,["rules"]),c(v,null,{label:l(()=>[t("label",null,n(x.$t("app.agentTicketRefund.rtType")),1),Rf]),default:l(()=>[c(y,{modelValue:e(p).volunteer,"onUpdate:modelValue":T[3]||(T[3]=P=>e(p).volunteer=P)},{default:l(()=>[c(r,{label:"VOLUNTEER_AUTO"},{default:l(()=>[K(n(x.$t("app.agentTicketRefund.voluntary")),1)]),_:1}),c(r,{label:"NON_VOLUNTEER_MANUAL"},{default:l(()=>[K(n(x.$t("app.agentTicketRefund.involuntary")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1}),c(v,null,{default:l(()=>[c(C,{type:"primary",onClick:e(_)},{default:l(()=>[K(n(x.$t("app.agentTicketRefund.refund")),1)]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])}}});const wf=at(Cf,[["__scopeId","data-v-58b70490"]]),Af=a=>(bt("data-v-4a677a5f"),a=a(),xt(),a),Sf={class:"flex items-center"},Df=Af(()=>t("div",{class:"iconfont icon-info-circle-line text-[var(--bkc-el-color-primary)] mr-[16px]"},null,-1)),Pf={class:"text-lg text-[var(--bkc-color-gray-1)]"},Of=je({__name:"RefundSeatDialog",emits:["handle-xepnr","handle-manual-refund","handle-cancel"],setup(a,{emit:o}){const u=o;return(h,f)=>{const p=Ke,i=nt;return s(),ae(i,{class:"refund-seat-dialog","show-close":!1,width:"500px",top:"40vh","close-on-click-modal":!1},{footer:l(()=>[c(p,{type:"primary",onClick:f[0]||(f[0]=_=>u("handle-xepnr"))},{default:l(()=>[K("XEPNR")]),_:1}),c(p,{type:"primary",onClick:f[1]||(f[1]=_=>u("handle-manual-refund"))},{default:l(()=>[K(n(h.$t("app.agentTicketRefund.manualRefundBtn")),1)]),_:1}),c(p,{onClick:f[2]||(f[2]=_=>u("handle-cancel"))},{default:l(()=>[K(n(h.$t("app.agentTicketRefund.cancel")),1)]),_:1})]),default:l(()=>[t("div",Sf,[Df,t("div",Pf,n(h.$t("app.agentTicketRefund.noSeatWithdRawal")),1)])]),_:1})}}});const Ef=at(Of,[["__scopeId","data-v-4a677a5f"]]),Ff={class:"flex-col justify-start items-start gap-1 inline-flex mt-[14px] w-full"},Vf={class:"self-stretch justify-start items-start inline-flex"},Mf={class:"self-stretch p-2.5 rounded border border-[var(--bkc-theme-2)] justify-start items-start gap-5 inline-flex"},jf={key:0,class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},Lf={class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},Bf={class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},If={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},Uf={class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},Qf=t("div",{class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},"-",-1),qf={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},zf={class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},Gf=t("div",{class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},"-",-1),Hf={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},Wf=t("div",{class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},"Office",-1),Yf={class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},Kf={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},Xf={class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},Jf={class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},ra=je({__name:"RefundInfo",props:{packageData:{},titleNormal:{type:Boolean},hideRefundType:{type:Boolean}},setup(a){return(o,u)=>(s(),m("div",Ff,[t("div",Vf,[t("div",{class:Se(["text-[var(--bkc-color-gray-1)] text-base leading-normal",o.titleNormal?"font-normal":"font-bold"])},n(o.$t("app.agentTicketRefund.refundTicketInfo")),3)]),t("div",Mf,[o.hideRefundType?Z("",!0):(s(),m("div",jf,[t("div",Lf,n(o.$t("app.agentTicketRefund.refundTicketType")),1),t("div",Bf,n(o.packageData.volunteer?o.$t("app.agentTicketRefund.refundType."+o.packageData.volunteer):""),1)])),t("div",If,[t("div",Uf,n(o.$t("app.agentTicketRefund.refundTicketNumber")),1),Qf]),t("div",qf,[t("div",zf,n(o.$t("app.agentTicketRefund.date")),1),Gf]),t("div",Hf,[Wf,t("div",Yf,n(o.packageData.office),1)]),t("div",Kf,[t("div",Xf,n(o.$t("app.agentTicketRefund.user")),1),t("div",Jf,n(o.packageData.createUser),1)])])]))}}),Zf={class:"flex-col justify-start items-start gap-1 inline-flex w-full mt-[14px]"},em={class:"self-stretch justify-start items-start inline-flex"},tm={class:"self-stretch p-2.5 rounded border border-[var(--bkc-theme-2)] flex-col justify-start items-start gap-2.5 flex"},nm={class:"self-stretch justify-start items-start gap-2.5 inline-flex border-dashed border-b border-[var(--bkc-color-gray-5)]"},am={class:"pr-10 pb-[6px] justify-start items-start gap-3.5 flex"},sm={class:"justify-start items-center gap-1 flex"},om={class:"text-[var(--bkc-el-color-primary)] text-sm font-normal leading-snug"},im=t("em",{class:"iconfont icon-loginuser"},null,-1),lm={class:"justify-start items-start flex"},rm={class:"text-[var(--bkc-color-gray-3)] text-sm font-normal leading-snug"},cm={class:"text-[var(--bkc-color-gray-1)] text-sm font-normal leading-snug"},um={class:"justify-start items-start flex"},dm={class:"text-[var(--bkc-color-gray-3)] text-sm font-normal leading-snug"},pm={class:"text-[var(--bkc-color-gray-1)] text-sm font-normal leading-snug"},fm={class:"justify-start items-start flex"},mm={class:"text-[var(--bkc-color-gray-3)] text-sm font-normal leading-snug"},gm={class:"text-[var(--bkc-color-gray-1)] text-sm font-normal leading-snug"},km={class:"justify-start items-start flex"},ym={class:"text-[var(--bkc-color-gray-3)] text-sm font-normal leading-snug"},hm={class:"text-[var(--bkc-color-gray-1)] text-sm font-normal leading-snug"},vm={key:0,class:"justify-start items-start flex"},_m=t("div",{class:"text-red-1 text-sm font-normal leading-snug"},"RECEIPT PRINTED",-1),bm=[_m],xm={class:"self-stretch flex-col justify-start items-start gap-2.5 flex"},Tm={class:"self-stretch justify-start items-start gap-20 inline-flex"},$m={class:"w-[120px] flex-col justify-start items-start inline-flex"},Nm={class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},Rm={class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},Cm={class:"w-[120px] flex-col justify-start items-start inline-flex"},wm={class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},Am={class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},Sm={class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},Dm={class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},Pm={class:"w-[150px] flex-col justify-start items-start inline-flex"},Om={class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},Em={class:"text-[var(--bkc-el-color-primary)] text-xs font-normal leading-tight mt-1"},Fm={class:"w-[120px] flex-col justify-start items-start inline-flex"},Vm={class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},Mm={class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},jm={class:"self-stretch justify-start items-start inline-flex"},Lm={class:"text-[var(--bkc-color-gray-2)] text-xs font-bold leading-tight"},Bm={class:"self-stretch flex-col justify-start items-start flex mt-1"},Im={class:"justify-start items-start gap-5 inline-flex"},Um={class:"justify-start items-center flex mt-[3px] mr-[4px]"},Qm={key:0,class:"bg-[#EAEAEA] rounded-sm border border-[var(--bkc-color-gray-6)] justify-start items-center flex"},qm={class:"text-[var(--bkc-color-gray-2)] text-xs font-normal leading-tight"},zm={class:"text-[var(--bkc-color-gray-5)] text-xs justify-center items-center leading-tight"},Za=je({__name:"RefundPassenger",props:{packageData:{},titleNormal:{type:Boolean},hideCheck:{type:Boolean}},setup(a){return(o,u)=>{var f,p,i,_,d,x,T,k,R,v,r,y,C,$,P,M,X,ue,E,j,oe,D,b,N,ne,ie,U,A,le,he,ce;const h=Je;return s(),m("div",Zf,[t("div",em,[t("div",{class:Se(["text-[var(--bkc-color-gray-1)] text-base font-bold leading-normal",o.titleNormal?"font-normal":"font-bold"])},n(o.$t("app.agentTicketRefund.rtPassenger")),3)]),t("div",tm,[t("div",nm,[t("div",am,[t("div",sm,[t("div",om,[im,K(n((p=(f=o.packageData)==null?void 0:f.formInfo)==null?void 0:p.name),1)])]),t("div",lm,[t("div",rm,n(o.$t("app.agentTicketRefund.ticketNum")),1),t("div",cm,n((_=(i=o.packageData)==null?void 0:i.formInfo)==null?void 0:_.ticketNo),1)]),t("div",um,[t("div",dm,n(o.$t("app.agentTicketRefund.airlineNum")),1),t("div",pm,n((x=(d=o.packageData)==null?void 0:d.formInfo)==null?void 0:x.airline),1)]),t("div",fm,[t("div",mm,n(o.$t("app.agentTicketRefund.ticketType")),1),t("div",gm,n((k=(T=o.packageData)==null?void 0:T.formInfo)==null?void 0:k.tktType),1)]),t("div",km,[t("div",ym,n(o.$t("app.agentTicketRefund.conjunction")),1),t("div",hm,n((v=(R=o.packageData)==null?void 0:R.formInfo)==null?void 0:v.conjunction),1)]),((y=(r=o.packageData)==null?void 0:r.formInfo)==null?void 0:y.receiptPrinted)==="1"?(s(),m("div",vm,bm)):Z("",!0)])]),t("div",xm,[t("div",Tm,[t("div",$m,[t("div",Nm,n(o.$t("app.agentTicketRefund.electronic")),1),t("div",Rm,n((($=(C=o.packageData)==null?void 0:C.formInfo)==null?void 0:$.etTag)==="1"?o.$t("app.agentTicketRefund.yes"):o.$t("app.agentTicketRefund.no")),1)]),t("div",Cm,[t("div",wm,n(o.$t("app.agentTicketRefund.currency")),1),t("div",Am,n((M=(P=o.packageData)==null?void 0:P.formInfo)==null?void 0:M.currency),1)]),t("div",{class:Se([{"w-[200px]":((E=(ue=(X=o.packageData)==null?void 0:X.formInfo)==null?void 0:ue.payType)==null?void 0:E.length)>6},"w-[120px] flex-col justify-start items-start inline-flex"])},[t("div",Sm,n(o.$t("app.agentTicketRefund.payment")),1),t("div",Dm,n(e(Qa)((oe=(j=o.packageData)==null?void 0:j.formInfo)==null?void 0:oe.payType)),1)],2),t("div",Pm,[t("div",Om,n(o.$t("app.agentTicketRefund.creditCard")),1),t("div",Em,n((b=(D=o.packageData)==null?void 0:D.formInfo)!=null&&b.creditCard?o.packageData.formInfo.creditCard:"-"),1)]),t("div",Fm,[t("div",Vm,n(o.$t("app.agentTicketRefund.remark")),1),t("div",Mm,n((ne=(N=o.packageData)==null?void 0:N.formInfo)!=null&&ne.remark?(U=(ie=o.packageData)==null?void 0:ie.formInfo)==null?void 0:U.remark:"-"),1)])]),(s(!0),m(ye,null,Ne(e(qa)((le=(A=o.packageData)==null?void 0:A.formInfo)==null?void 0:le.ticketNo,((ce=(he=o.packageData)==null?void 0:he.formInfo)==null?void 0:ce.segment)??[]),(L,se)=>(s(),m("div",{key:se+"seg",class:"self-stretch flex-col justify-start items-start flex"},[t("div",jm,[t("div",Lm,n(o.$t("app.agentTicketRefund.couponNo",{a:e(Un)(se)})),1)]),t("div",Bm,[t("div",Im,[(s(!0),m(ye,null,Ne(L,(Q,S)=>(s(),m("div",{key:S+"segmet",class:"justify-start items-center flex"},[t("div",Um,[o.hideCheck?Z("",!0):(s(),m("div",Qm,[c(h,{size:"14"},{default:l(()=>[Q.isAble==="1"?(s(),ae(e(Wn),{key:0})):Z("",!0)]),_:2},1024)])),t("div",qm,n(Q.departureCode)+"-"+n(Q.arriveCode),1)]),t("div",zm,n(o.$t(`app.refundForm.number_${S+1}`)),1)]))),128))])])]))),128))])])])}}}),Gm={class:"flex-col justify-start items-start gap-1 inline-flex mt-[14px] w-full"},Hm={class:"justify-between items-start inline-flex"},Wm={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},Ym={class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},Km={class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},Xm={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},Jm={class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},Zm={class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},eg={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},tg={class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},ng={class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},ag={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},sg={class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},og={key:0,class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},ig={key:1,class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},lg={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},rg={class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},cg={class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},ug={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},dg={class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight"},pg={class:"justify-center items-center gap-0.5 inline-flex"},fg={class:"text-[var(--bkc-color-special-red-1)] text-xs font-normal leading-tight mt-1"},mg={class:"u-tax-cont"},gg={class:"u-tax-title"},kg={key:1,class:"u-tax-item"},yg={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},hg={class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight"},vg={class:"justify-start items-center gap-1 inline-flex"},_g={class:"text-[var(--bkc-color-special-red-1)] text-xs font-normal leading-tight mt-1"},bg={class:"self-stretch justify-start items-start gap-5 inline-flex"},xg={class:"justify-start items-start gap-1 flex"},Tg={class:"text-[var(--bkc-color-gray-1)] text-base font-normal leading-normal"},$g={class:"text-[var(--bkc-color-special-red-1)] text-base font-bold leading-normal"},Ng={class:"justify-start items-start gap-0.5 flex"},Rg={class:"text-[var(--bkc-color-gray-1)] text-base font-normal leading-normal"},Cg={class:"text-[var(--bkc-color-special-red-1)] text-base font-bold leading-normal"},wg=je({__name:"RefundAmount",props:{packageData:{},titleNormal:{type:Boolean},refundFormStyle:{type:Boolean}},setup(a){return(o,u)=>{var i,_,d,x,T,k,R,v;const h=Je,f=sa,p=Sn;return s(),m("div",Gm,[t("div",Hm,[t("div",{class:Se(["text-[var(--bkc-color-gray-1)] text-base leading-normal",o.titleNormal?"font-normal":"font-bold"])},n(o.$t("app.agentTicketRefund.amount")),3)]),t("div",{class:Se(["self-stretch py-2.5 px-2.5 rounded border border-[var(--bkc-theme-2)] flex-col justify-start items-center gap-2.5 flex",{"gap-0":o.refundFormStyle}])},[Yt(o.$slots,"refund-amount",{},()=>{var r,y,C,$,P,M,X,ue,E,j,oe,D,b,N,ne,ie,U,A,le,he,ce,L,se,Q,S,G,J,z;return[t("div",{class:Se(["self-stretch justify-start items-start gap-5 inline-flex border-b border-solid border-[var(--bkc-theme-2)]",{"border-b-0 pb-0":o.refundFormStyle,"pb-[10px]":!o.refundFormStyle}])},[t("div",Wm,[t("div",Ym,n(o.$t("app.agentTicketRefund.passName")),1),t("div",Km,n((y=(r=o.packageData)==null?void 0:r.formInfo)==null?void 0:y.name),1)]),t("div",Xm,[t("div",Jm,n(o.$t("app.agentTicketRefund.passType")),1),t("div",Zm,n(e(Qn)(($=(C=o.packageData)==null?void 0:C.formInfo)==null?void 0:$.psgType)),1)]),t("div",eg,[t("div",tg,n(o.$t("app.agentTicketRefund.totalAmount")),1),t("div",ng,n((M=(P=o.packageData)==null?void 0:P.formInfo)==null?void 0:M.currency)+" "+n((ue=(X=o.packageData)==null?void 0:X.formInfo)==null?void 0:ue.totalAmount),1)]),t("div",ag,[t("div",sg,n(o.$t("app.agentTicketRefund.commission")),1),((j=(E=o.packageData)==null?void 0:E.formInfo)==null?void 0:j.rate)==="1"?(s(),m("div",og,n((D=(oe=o.packageData)==null?void 0:oe.formInfo)==null?void 0:D.commisionRate)+"%",1)):(s(),m("div",ig,n((N=(b=o.packageData)==null?void 0:b.formInfo)==null?void 0:N.currency)+" "+n((ie=(ne=o.packageData)==null?void 0:ne.formInfo)==null?void 0:ie.commision),1))]),t("div",lg,[t("div",rg,n(o.$t("app.agentTicketRefund.charge")),1),t("div",cg,n((A=(U=o.packageData)==null?void 0:U.formInfo)==null?void 0:A.currency)+" "+n((he=(le=o.packageData)==null?void 0:le.formInfo)==null?void 0:he.otherDeduction),1)]),t("div",ug,[t("div",dg,n(o.$t("app.agentTicketRefund.totalTax")),1),t("div",pg,[t("div",fg,n((L=(ce=o.packageData)==null?void 0:ce.formInfo)==null?void 0:L.currency)+" "+n((Q=(se=o.packageData)==null?void 0:se.formInfo)==null?void 0:Q.totalTaxs),1),o.refundFormStyle?Z("",!0):(s(),ae(p,{key:0,placement:"top",title:o.$t("app.agentTicketRefund.taxDetails"),width:176,trigger:"hover","popper-class":"u-detail-tax-popper-crs"},{reference:l(()=>[c(h,{class:"tooltip-icon"},{default:l(()=>[c(e(un))]),_:1})]),default:l(()=>{var w,g;return[t("div",mg,[((g=(w=o.packageData)==null?void 0:w.formInfo)==null?void 0:g.taxs.length)>0?(s(),m(ye,{key:0},[t("div",gg,[t("span",null,n(o.$t("app.agentTicketRefund.taxes")),1),t("span",null,n(o.$t("app.agentTicketRefund.taxAmount")),1)]),c(f,{"max-height":"120px",always:!0},{default:l(()=>{var B,F;return[(s(!0),m(ye,null,Ne((F=(B=o.packageData)==null?void 0:B.formInfo)==null?void 0:F.taxs,de=>{var ve,O;return s(),m("div",{key:de.name,class:"u-tax-item"},[t("span",null,n(de.name),1),t("span",null,n(de.value)+" "+n((O=(ve=o.packageData)==null?void 0:ve.formInfo)==null?void 0:O.currency),1)])}),128))]}),_:1})],64)):(s(),m("div",kg,[t("span",null,n(o.$t("app.agentTicketRefund.noData")),1)]))])]}),_:1},8,["title"]))])]),t("div",yg,[t("div",hg,n(o.$t("app.agentTicketRefund.actualSettlementAmount")),1),t("div",vg,[t("div",_g,n((G=(S=o.packageData)==null?void 0:S.formInfo)==null?void 0:G.currency)+" "+n((z=(J=o.packageData)==null?void 0:J.formInfo)==null?void 0:z.netRefund),1)])])],2)]},!0),Yt(o.$slots,"taxes",{},void 0,!0),t("div",bg,[t("div",xg,[t("div",Tg,n(o.$t("app.agentTicketRefund.totalCharge")),1),t("div",$g,n((_=(i=o.packageData)==null?void 0:i.formInfo)==null?void 0:_.currency)+" "+n((x=(d=o.packageData)==null?void 0:d.formInfo)==null?void 0:x.otherDeduction),1)]),t("div",Ng,[t("div",Rg,n(o.$t("app.agentTicketRefund.actualAmount")),1),t("div",Cg,n((k=(T=o.packageData)==null?void 0:T.formInfo)==null?void 0:k.currency)+" "+n((v=(R=o.packageData)==null?void 0:R.formInfo)==null?void 0:v.netRefund),1)])])],2)])}}});const es=at(wg,[["__scopeId","data-v-ec135b40"]]),Ag={class:"agent-refund-btn flex pt-[10px] border-t-[1px] border-[var(--bkc-color-gray-5)] border-dashed w-full mt-[10px]"},Sg=je({__name:"TicketRefundBtn",props:{refundType:{},ticketType:{},isReset:{}},emits:["submit","modify","cancel","reset"],setup(a,{emit:o}){const u=a,h=o,f=H("ONLY_REFUND");return wt(()=>u.isReset,()=>{f.value="ONLY_REFUND"}),(p,i)=>{const _=Pt,d=Ot,x=Ke;return s(),m("div",Ag,[p.refundType==="Manual"||p.ticketType==="I"?(s(),ae(d,{key:0,modelValue:f.value,"onUpdate:modelValue":i[0]||(i[0]=T=>f.value=T)},{default:l(()=>[c(_,{label:"ONLY_REFUND"},{default:l(()=>[K(n(p.$t("app.agentTicketRefund.onlyRt")),1)]),_:1}),c(_,{label:"PNR_CANCEL"},{default:l(()=>[K(n(p.$t("app.agentTicketRefund.rtCancelPnr")),1)]),_:1}),c(_,{label:"PASSENGER_DELETE"},{default:l(()=>[K(n(p.$t("app.agentTicketRefund.rtDeletePass")),1)]),_:1})]),_:1},8,["modelValue"])):Z("",!0),c(x,{type:"primary",onClick:i[1]||(i[1]=T=>h("submit",f.value))},{default:l(()=>[K(n(p.$t("app.agentTicketRefund.submit")),1)]),_:1}),p.refundType!=="Auto"?(s(),ae(x,{key:1,type:"primary",onClick:i[2]||(i[2]=T=>h("reset"))},{default:l(()=>[K(n(p.$t("app.agentTicketRefund.reset")),1)]),_:1})):(s(),ae(x,{key:2,type:"primary",onClick:i[3]||(i[3]=T=>h("modify"))},{default:l(()=>[K(n(p.$t("app.agentTicketRefund.edit")),1)]),_:1})),c(x,{onClick:i[4]||(i[4]=T=>h("cancel"))},{default:l(()=>[K(n(p.$t("app.agentTicketRefund.cancel")),1)]),_:1})])}}});const ts=at(Sg,[["__scopeId","data-v-dd74d716"]]),ca={salesTicketManagementRefundedAig:"ticket-management-refunded-aig",salesTicketManagementDelRefundTicketAig:"ticket-management-del-refund-ticket-aig",salesTicketManagementEditRefundAig:"ticket-management-edit-refund-aig"},Dg=a=>{const o=(f,p,i)=>{const _=[],d={appendData:JSON.stringify({segments:(a.formInfo.segment??[]).map(x=>({segmentIndex:x.rph,segmentStatus:"REFUNDED"}))}),description:JSON.stringify({airline:a.formInfo.airline,amount:{commision:(a.formInfo.rate==="0"||a.formInfo.rate==="")&&a.formInfo.commision||"0.0",commisionRate:a.formInfo.rate==="1"&&a.formInfo.commisionRate||"0.0",netRefund:a.formInfo.netRefund,otherDeduction:a.formInfo.otherDeduction||"0.0",totalAmount:a.formInfo.totalAmount||"0.0",totalTaxs:a.formInfo.totalTaxs},createLevel:"WHITE_SCREEN_CMD",creditCardNo:"",currency:a.formInfo.currency,detail:{conjunction:a.formInfo.conjunction,iata:i,office:a.office,printNo:a.prntNo,refundType:a.volunteer},etTag:a.formInfo.etTag,international:a.formInfo.tktType==="I"?"INTL":"DOM",operator:a.createUser,passenger:{name:a.formInfo.name,type:a.formInfo.psgType},payType:a.formInfo.payType,refundNo:p,refundTime:Date.parse(new Date(f).toString()).toString(),segments:(a.formInfo.segment??[]).map(x=>({arriveCode:x.arriveCode,departureCode:x.departureCode,e8Rph:x.e8Rph,isAble:x.isAble,isCheck:x.isAble==="1"?"1":"0",segmentIndex:x.rph,tktTag:x.tktTag})),ticketNo:a.formInfo.ticketNo,tktType:a.formInfo.tktType}),itemDetailNo:0,itemType:"REFUND"};return _.push(d),_},u=()=>{var i;const f=[],p={orderItems:[{actualAmount:(i=a==null?void 0:a.formInfo)!=null&&i.netRefund?Number(a.formInfo.netRefund):0,currency:a.formInfo.currency,itemDetailNo:0,payStatus:"REFUNDED",status:"COMPLETE"}]};return f.push(p),f};return{buildRefundAigStore:(f,p,i)=>{var d;return{actualAmount:(d=a==null?void 0:a.formInfo)!=null&&d.netRefund?Number(a.formInfo.netRefund):0,fromSystem:"SGUI",payStatus:"REFUNDED",status:"COMPLETE",supplyChainOrder:f??"",itemDetails:o(et(p.slice(0,19)).format("YYYY-MM-DD HH:mm:ss"),f,i),orders:u()}}}},Pg=(a,o)=>{const{t:u}=We(),h=Ct(),f=Ae(!1),{postToAIGPersonalization:p}=oa(ca.salesTicketManagementRefundedAig),i=()=>{o("update:volunteerFlag","AUTO"),o("update:modelValue",!1),o("cleanData")},_=()=>({commision:["0",""].includes(a.formInfo.rate)?a.formInfo.commision||"0":"",commisionRate:a.formInfo.rate==="1"?a.formInfo.commisionRate||"0":"",netRefund:a.formInfo.netRefund,otherDeduction:a.formInfo.otherDeduction||"0",taxs:a.formInfo.taxs,totalAmount:a.formInfo.totalAmount||"0",totalTaxs:a.formInfo.totalTaxs}),d=()=>({airline:a.formInfo.airline,crsPnrNo:a.formInfo.crsPnrNo,currency:a.formInfo.currency,etTag:a.formInfo.etTag,marketAirline:a.formInfo.marketAirline,name:Ft.encode(a.formInfo.name),payType:a.formInfo.payType,pnr:a.formInfo.pnr,psgType:a.formInfo.psgType,ticketNo:a.formInfo.ticketNo,tktType:a.formInfo.tktType}),x=v=>({modificationType:v,prntNo:a.prntNo,resultpre:{amount:_(),conjunction:a.formInfo.conjunction,creditCard:a.formInfo.creditCard,isCoupon:a.formInfo.isCoupon,office:a.office,operator:a.createUser,remark:a.formInfo.remark,ticket:d(),volunteer:a.volunteer}}),T=(v,r)=>{var C,$,P;const y=((C=h.state.user)==null?void 0:C[v])??"";return y||(((P=((($=h.state.user)==null?void 0:$[r])??"").split(";"))==null?void 0:P[0])??"")};return{fullscreenLoading:f,submitAutoRefund:async v=>{var C;if((a.formInfo.segment??[]).filter($=>$.isAble==="1").length===0){mt({type:"warning",message:u("app.agentTicketRefund.selSeg")});return}f.value=!0;const{buildRefundAigStore:r}=Dg(a);let y;try{y=(await Eo(x(v))).data.value}finally{f.value=!1}(y==null?void 0:y.code)==="200"&&Xe.alert(y==null?void 0:y.data.msg,{icon:Le("em",{class:"iconfont icon-check-circle"}),customClass:"agent-refund-msg-box",confirmButtonText:u("app.agentTicketRefund.sure"),showClose:!1,callback:()=>{i()}});try{const $=r(((C=y==null?void 0:y.data.refundNo)==null?void 0:C[0])??"",(y==null?void 0:y.time)||"",T("defaultSellingGuiIataNum","sellingGuiIataNum"));await p($)}catch($){console.error("自动退票入库异常:",$.message)}},closeRefundInfo:i,modify:()=>{o("update:modelValue",!0),o("update:volunteerFlag","MANUAL")}}},Og={class:"flex flex-col"},Eg=je({__name:"TicketRefundAuto",props:{packageData:{}},emits:["update:volunteerFlag","update:modelValue","cleanData"],setup(a,{emit:o}){const u=a,h=o,{fullscreenLoading:f,submitAutoRefund:p,closeRefundInfo:i,modify:_}=Pg(u.packageData,h);return(d,x)=>{var k,R;const T=kt;return Qe((s(),m("div",Og,[c(ra,{"package-data":d.packageData},null,8,["package-data"]),c(Za,{"package-data":d.packageData},null,8,["package-data"]),c(es,{"package-data":d.packageData},null,8,["package-data"]),c(ts,{"ticket-type":((R=(k=d.packageData)==null?void 0:k.formInfo)==null?void 0:R.tktType)??"D","refund-type":"Auto",onSubmit:e(p),onModify:e(_),onCancel:e(i)},null,8,["ticket-type","onSubmit","onModify","onCancel"])])),[[T,e(f),void 0,{fullscreen:!0,lock:!0}]])}}}),Fg=(a,o)=>{const{t:u}=We(),h=Me(()=>tt(JSON.parse(JSON.stringify(a.params.taxs)))),f=H([]),p={taxValue:[{required:!0,message:u("app.agentTicketRefund.taxFeesNotEmpty"),trigger:"change"},{pattern:Ys,message:u("app.agentTicketRefund.correctPrice"),trigger:"change"}],taxName:[{required:!0,message:u("app.agentTicketRefund.taxNotEmpty"),trigger:"change"}]},i=y=>{const C=y<=9?`0${y}`:`${y}`;return`${u("app.agentTicketRefund.taxes")} ${C}`},_=y=>{y&&f.value.push(y)},d=()=>new Promise(y=>{const C=[];f.value.forEach($=>{C.push($.validate(P=>P))}),y(C)}).then(y=>Promise.all(y)).then(y=>Promise.resolve(y.every(C=>C)));return{renderTax:h,taxRule:p,bindFormRef:_,formatTaxName:i,handleConfirm:()=>{d().then(y=>{y&&o("confirm",h.value)})},handleClose:()=>{o("update:modelValue",!1)},handleDelete:y=>{h.value.splice(y,1)},handleAdd:()=>{h.value.push({name:"",value:""})},handleTaxItemInput:(y,C)=>{h.value[C].name=y==null?void 0:y.toUpperCase()},changeVal:(y,C)=>{y[C].value=Math.floor(Number(y[C].value)).toFixed(1)}}},ns=a=>(bt("data-v-703e8cc9"),a=a(),xt(),a),Vg={class:"tax-view"},Mg={class:"text-gray-1 text-lg font-bold leading-normal"},jg={class:"py-1.5 px-1.5 mr-2.5 mb-4 rounded bg-[var(--bkc-color-special-yellow-3)] text-[var(--bkc-color-special-yellow-1)]"},Lg=ns(()=>t("em",{class:"iconfont icon-warning-circle-fill mr-2"},null,-1)),Bg={class:"content pl-[75px]"},Ig={class:"tax-input rounded-t-sm border border-solid border-[var(--bkc-el-border-color-lighter)]"},Ug=ns(()=>t("span",{class:"tax-input-line border border-solid border-l-[var(--bkc-el-border-color-lighter)]"},null,-1)),Qg=["onClick"],qg=je({__name:"TaxDialog",props:{params:{}},emits:["update:modelValue","confirm","close"],setup(a,{emit:o}){const u=a,h=o,{renderTax:f,taxRule:p,bindFormRef:i,formatTaxName:_,handleConfirm:d,handleClose:x,handleDelete:T,handleAdd:k,handleTaxItemInput:R,changeVal:v}=Fg(u,h);return(r,y)=>{const C=gt,$=ot,P=Je,M=it,X=Ke,ue=nt;return s(),m("div",Vg,[c(ue,{"model-value":u.params.on,"before-close":e(x),class:"tax-dialog","close-on-click-modal":!1},{header:l(()=>[t("div",Mg,n(r.$t("app.agentTicketRefund.taxEdit")),1)]),footer:l(()=>[t("span",null,[c(X,{type:"primary",onClick:e(d)},{default:l(()=>[K(n(r.$t("app.agentTicketRefund.confirm")),1)]),_:1},8,["onClick"]),c(X,{onClick:e(x)},{default:l(()=>[K(n(r.$t("app.agentTicketRefund.cancel")),1)]),_:1},8,["onClick"])])]),default:l(()=>[t("div",jg,[Lg,t("span",null,n(r.$t("app.agentTicketRefund.taxExemptTips")),1)]),t("div",Bg,[(s(!0),m(ye,null,Ne(e(f),(E,j)=>(s(),ae(M,{key:j,ref_for:!0,ref:e(i),model:E,class:"all-tax-input ml-[50px]"},{default:l(()=>[c($,{label:e(_)(j+1)},{default:l(()=>[t("div",Ig,[c($,{class:"tax-input-one w-[80px]",prop:"name",rules:e(p).taxName},{default:l(()=>[c(C,{modelValue:E.name,"onUpdate:modelValue":oe=>E.name=oe,placeholder:r.$t("app.agentTicketRefund.taxes"),onInput:oe=>e(R)(E.name,j)},null,8,["modelValue","onUpdate:modelValue","placeholder","onInput"])]),_:2},1032,["rules"]),Ug,c($,{class:"tax-input-two w-[190px]",prop:"value",rules:e(p).taxValue},{default:l(()=>[c(C,{modelValue:E.value,"onUpdate:modelValue":oe=>E.value=oe,modelModifiers:{trim:!0},placeholder:r.$t("app.agentTicketRefund.taxFees"),onChange:oe=>e(v)(e(f),j)},null,8,["modelValue","onUpdate:modelValue","placeholder","onChange"])]),_:2},1032,["rules"])]),e(f).length>1?(s(),m("span",{key:0,onClick:oe=>e(T)(j)},[c(P,{size:20,class:"delete-icon text-brand-2 ml-2.5 cursor-pointer"},{default:l(()=>[c(e(Es))]),_:1})],8,Qg)):Z("",!0)]),_:2},1032,["label"])]),_:2},1032,["model"]))),128)),e(f).length<27?(s(),m("div",{key:0,class:"add-tax text-xs mb-2.5 w-[80px] text-[var(--bkc-el-color-primary)] ml-[194px] cursor-pointer",onClick:y[0]||(y[0]=(...E)=>e(k)&&e(k)(...E))},[t("span",null,n(r.$t("app.agentTicketRefund.taxAdd")),1)])):Z("",!0)])]),_:1},8,["model-value","before-close"])])}}});const zg=at(qg,[["__scopeId","data-v-703e8cc9"]]),Gg=(a,o,u)=>{const h=()=>{let _=[];const d={};return[...a.segment??[],...a.checkSeg].forEach(T=>{d[T.rph]?d[T.rph].isCheck="1":(d[T.rph]=T,d[T.rph].isCheck="0")}),_=Object.values(d),_},f=(_,d,x,T)=>{const k=[],R={appendData:JSON.stringify({segments:h().map(v=>({segmentIndex:v.rph,segmentStatus:"REFUNDED"}))}),description:JSON.stringify({airline:a.airline,amount:{commision:(a.rate==="0"||a.rate==="")&&a.commision||"0.0",commisionRate:a.rate==="1"&&a.commisionRate||"0.0",netRefund:a.netRefund,otherDeduction:a.otherDeduction||"0.0",totalAmount:a.totalAmount||"0.0",totalTaxs:a.totalTaxs},createLevel:"WHITE_SCREEN_CMD",creditCardNo:"",currency:a.currency,detail:{conjunction:a.conjunction,iata:x,office:o.office,printNo:T,refundType:o.volunteer},etTag:a.etTag,international:a.tktType==="I"?"INTL":"DOM",operator:o.createUser,passenger:{name:a.name,type:a.psgType},payType:a.payType,refundNo:d,refundTime:Date.parse(new Date(_).toString()).toString(),segments:h().map(v=>({arriveCode:v.arriveCode,departureCode:v.departureCode,e8Rph:v.e8Rph,isAble:v.isAble,isCheck:v.isCheck,segmentIndex:v.rph,tktTag:v.tktTag})),ticketNo:a.tktType==="I"?a.ticketNo:u.split("-").join(""),tktType:a.tktType}),itemDetailNo:0,itemType:"REFUND"};return k.push(R),k},p=()=>{const _=[],d={orderItems:[{actualAmount:a!=null&&a.netRefund?Number(a.netRefund):0,currency:a.currency,itemDetailNo:0,payStatus:"REFUNDED",status:"COMPLETE"}]};return _.push(d),_};return{getSegmentInfo:h,buildRefundAigStore:(_,d,x,T)=>({actualAmount:a!=null&&a.netRefund?Number(a.netRefund):0,fromSystem:"SGUI",payStatus:"REFUNDED",status:"COMPLETE",supplyChainOrder:_??"",itemDetails:f(et(d.slice(0,19)).format("YYYY-MM-DD HH:mm:ss"),_,x,T),orders:p()})}},Hg=(a,o)=>{var U,A,le,he;const{t:u}=We(),h=Ct(),f=tt({...a.packageData.formInfo,segmentShow:qa((A=(U=a.packageData)==null?void 0:U.formInfo)==null?void 0:A.ticketNo,((he=(le=a.packageData)==null?void 0:le.formInfo)==null?void 0:he.segment)??[])}),p=Ae(a.queryForm.tktNo.split("-").join("")),i=Me(()=>f.commisionRate&&dt.test(f.commisionRate)&&Number(f.commisionRate)===0),_=Me(()=>{var ce;return f.commision&&((ce=f.commision)==null?void 0:ce.toString())==="0"}),d=Ae(Math.random()),x=H(),T=tt({taxs:[],on:!1}),{postToAIGPersonalization:k}=oa(ca.salesTicketManagementRefundedAig),R=Ae(!1),v=(ce,L,se)=>{L===""?se():L.slice(0,2)==="IC"?Pa.test(L)?se():se(u("app.agentTicketRefund.remarkIC")):$n.test(L)?se():se(u("app.agentTicketRefund.remarkHint"))},r=(ce,L,se)=>{f.payType==="TC"&&(L.length===0?se(u("app.agentTicketRefund.creditCardNotEmpty")):!a.isDragonBoatOffice&&!Kn.test(L)?se(u("app.agentTicketRefund.creditCardInput")):a.isDragonBoatOffice&&!Xn.test(L)&&se(u("app.agentTicketRefund.dragonBoatOfficeInput"))),se()},y=tt({etTag:[{required:!0,message:u("app.agentTicketRefund.electronicSel"),trigger:"change"}],currency:[{required:!0,message:u("app.agentTicketRefund.currencyNotEmpty"),trigger:"change"}],payType:[{required:!0,message:u("app.agentTicketRefund.paymentSel"),trigger:"change"},{pattern:Yn,message:u("app.agentTicketRefund.paymentInput"),trigger:"change"}],remark:[{validator:v,trigger:"change"}],creditCard:[{validator:r,required:!0,trigger:"change"}],price:[{pattern:dt,message:u("app.agentTicketRefund.correctPrice"),trigger:"change"}],rate:[{pattern:dt,message:u("app.agentTicketRefund.correctRate"),trigger:"change"}],taxValue:[{required:!0,message:u("app.agentTicketRefund.taxFeesNotEmpty"),trigger:"change"},{pattern:dt,message:u("app.agentTicketRefund.correctPrice"),trigger:"change"}],taxName:[{required:!0,message:u("app.agentTicketRefund.taxNotEmpty"),trigger:"change"}],commision:[{pattern:Ks,message:u("app.agentTicketRefund.correctPrice"),trigger:"change"}]}),C=()=>{var ce;((ce=f.payType)==null?void 0:ce.toUpperCase())==="TC"&&(f.creditCard=a.isDragonBoatOffice?ea:"")},$=ce=>{ce.target.value!==""&&!Dn.some(L=>L.label===ce.target.value)&&(f.payType=ce.target.value)},P=()=>{const{totalAmount:ce,totalTaxs:L,otherDeduction:se,commision:Q,commisionRate:S,rate:G}=f;if(G==="1"){const J=jt(Lt(Number(ce),Number(S)),100).toString(),z=J.includes(".")?J.substring(0,J.indexOf(".")+3):J;f.netRefund=`${Dt(qe(Number(ce),Number(L)),qe(Number(se),Number(z)))}`}else f.netRefund=`${Dt(qe(Number(ce),Number(L)),qe(Number(se),Number(Q)))}`},M=()=>{const{commision:ce,commisionRate:L}=f;f.rate=L?"1":f.rate=ce?"0":"",P()},X=()=>{let ce=new gn(0);f.taxs.forEach(L=>{ce=ce.add(new gn(L.value))}),f.totalTaxs=Number(ce).toFixed(2),P()},ue=ce=>{T.on=!T.on,f.taxs=ce,X()},E=()=>{T.taxs=[],T.on=!1},j=()=>{x.value&&(x.value.resetFields(),f!=null&&f.commision&&(f.commision=Math.round(Number(f.commision)).toString()),f.taxs=a.packageData.formInfo.taxs,f.checkSeg=a.packageData.formInfo.checkSeg,T.taxs=[],P(),d.value=Math.random())},oe=(ce,L)=>{var Q,S,G;const se=((Q=h.state.user)==null?void 0:Q[ce])??"";return se||(((G=(((S=h.state.user)==null?void 0:S[L])??"").split(";"))==null?void 0:G[0])??"")},D=(ce,L)=>({modificationType:ce,prntNo:a.queryForm.prntNo,resultpre:{amount:{commision:f.rate==="0"||f.rate===""?f.commision||"0":"",commisionRate:f.rate==="1"?f.commisionRate||"0":"",netRefund:f.netRefund,otherDeduction:f.otherDeduction||"0",taxs:f.taxs,totalAmount:f.totalAmount||"0",totalTaxs:f.totalTaxs},conjunction:f.conjunction,creditCard:f.creditCard,isCoupon:f.isCoupon,office:a.packageData.office,operator:a.packageData.createUser,remark:f.remark,segList:f.segList,ticket:{airline:f.airline,crsPnrNo:f.crsPnrNo,currency:f.currency,etTag:f.etTag,marketAirline:f.marketAirline,name:Ft.encode(f.name),payType:f.payType.toUpperCase(),pnr:f.pnr,psgType:f.psgType,segment:L.map(Q=>({arriveCode:Q.arriveCode,departureCode:Q.departureCode,e8Rph:Q.e8Rph,isAble:Q.isAble,rph:Q.rph,tktTag:Q.tktTag,isCheck:Q.isCheck})),ticketNo:f.tktType==="I"?f.ticketNo:p.value.split("-").join(""),tktType:f.tktType},volunteer:a.packageData.volunteer}}),b=async ce=>{var G;R.value=!0;const{getSegmentInfo:L,buildRefundAigStore:se}=Gg(f,a.packageData,p.value),Q=L();let S;try{S=(await await Fo(D(ce,Q))).data.value}finally{R.value=!1}Xe.alert(S==null?void 0:S.data.msg,{icon:Le("em",{class:"iconfont icon-check-circle"}),customClass:"u-msg-box-icon",confirmButtonText:u("app.agentTicketRefund.sure"),showClose:!1,callback:()=>{o("cancel")}});try{const J=se(((G=S==null?void 0:S.data.refundNo)==null?void 0:G[0])??"",(S==null?void 0:S.time)||"",oe("defaultSellingGuiIataNum","sellingGuiIataNum"),a.queryForm.prntNo);await k(J)}catch(J){console.error("手工退票入库异常:",J.message)}},N=async ce=>{f.taxs.find(se=>se.name==="XT")?Xe.confirm(u("app.agentTicketRefund.xtTaxsMsg"),{icon:Le("em",{class:"iconfont icon-info-circle-line"}),customClass:"u-msg-box-icon",confirmButtonText:u("app.agentTicketRefund.sure"),showClose:!1,showCancelButton:!1}):b(ce)},ne=async ce=>{x.value&&await x.value.validate(L=>{if(L){if((f.checkSeg??[]).length<1){mt.error(u("app.agentTicketRefund.selSeg"));return}N(ce)}})},ie=()=>{T.taxs=f.taxs,T.on=!T.on};return st(()=>{f!=null&&f.commision&&(f.commision=Math.round(Number(f.commision)).toString()),f!=null&&f.payType&&(f!=null&&f.payType.startsWith("CASH")?f.payType="CASH":f!=null&&f.payType.startsWith("CC")||f!=null&&f.payType.startsWith("TC")?f.payType="TC":f.payType="")}),{fullscreenLoading:R,isReset:d,formRef:x,taxForm:T,judgeCommissionRate:i,judgeCommission:_,editData:f,tktNoTag:p,setCreditCard:C,rules:y,bindPaymentValue:$,handleNetRefund:P,handleRate:M,handleTaxConfirm:ue,handleTaxClose:E,reset:j,submit:ne,openTax:ie}},kn=a=>(bt("data-v-42b33ae9"),a=a(),xt(),a),Wg={class:"flex-col justify-start items-start gap-1 inline-flex w-full"},Yg={class:"w-full self-stretch justify-start items-start inline-flex"},Kg={class:"text-[var(--bkc-color-gray-1)] text-base font-bold leading-normal"},Xg={class:"w-full self-stretch p-2.5 rounded border border-[var(--bkc-theme-2)] flex-col justify-start items-start gap-2.5 flex"},Jg={class:"self-stretch justify-start items-start gap-2.5 inline-flex h-8 border-dashed border-b border-gray-5"},Zg={class:"h-[22px] pr-10 justify-start items-start gap-3.5 flex"},ek={class:"justify-start items-center gap-1 flex h-4"},tk={class:"text-[var(--bkc-el-color-primary)] text-sm font-normal leading-snug"},nk=kn(()=>t("em",{class:"iconfont icon-loginuser"},null,-1)),ak={class:"justify-start items-start flex"},sk={class:"text-[var(--bkc-color-gray-3)] text-sm font-normal leading-snug"},ok={class:"text-[var(--bkc-color-gray-1)] text-sm font-normal leading-snug"},ik={class:"justify-start items-start flex"},lk={class:"text-[var(--bkc-color-gray-3)] text-sm font-normal leading-snug"},rk={class:"text-[var(--bkc-color-gray-1)] text-sm font-normal leading-snug"},ck={class:"justify-start items-start flex"},uk={class:"text-[var(--bkc-color-gray-3)] text-sm font-normal leading-snug"},dk={class:"text-[var(--bkc-color-gray-1)] text-sm font-normal leading-snug"},pk={class:"justify-start items-start flex"},fk={class:"text-[var(--bkc-color-gray-3)] text-sm font-normal leading-snug"},mk={class:"text-[var(--bkc-color-gray-1)] text-sm font-normal leading-snug"},gk={key:0,class:"justify-start items-start flex"},kk=kn(()=>t("div",{class:"text-[var(--bkc-color-special-red-1)] text-sm font-normal leading-snug"},"RECEIPT PRINTED",-1)),yk=[kk],hk={class:"self-stretch flex-col justify-start items-start"},vk={class:"w-full flex gap-x-[20px]"},_k={class:"w-[20%] electric"},bk={class:"w-[20%]"},xk={class:"w-[20%] pay-type"},Tk={class:"w-[20%]"},$k={class:"w-[20%]"},Nk={class:"remark-label text-[var(--bkc-color-gray-3)]"},Rk=kn(()=>t("span",{class:"iconfont icon-info-circle-line text-[var(--bkc-color-gray-4)]"},null,-1)),Ck={key:0,class:"w-[20%]"},wk={class:"ticket"},Ak={key:0},Sk={class:"self-stretch justify-start items-start gap-1 inline-flex"},Dk={class:"text-[var(--bkc-color-gray-2)] text-xs font-bold leading-tight"},Pk={class:"self-stretch flex-col justify-start items-start gap-2.5 flex mt-1"},Ok={class:"justify-start items-start gap-5 inline-flex"},Ek={class:"justify-start items-center gap-1 flex"},Fk={class:"text-[var(--bkc-color-gray-2)] text-xs font-normal leading-tight"},Vk={class:"self-stretch justify-start items-start gap-1 inline-flex"},Mk={class:"text-[var(--bkc-color-gray-2)] text-xs font-bold leading-tight"},jk={class:"self-stretch flex-col justify-start items-start gap-2.5 flex mt-1"},Lk={class:"justify-start items-start gap-5 inline-flex"},Bk={class:"justify-start items-center gap-1 flex"},Ik={class:"text-[var(--bkc-color-gray-2)] text-xs font-normal leading-tight"},Uk={class:"w-full flex-col justify-start items-start gap-1 inline-flex refund-amount mt-[14px]"},Qk={class:"self-stretch flex-col justify-start items-start gap-2.5 flex"},qk={class:"justify-between items-start inline-flex"},zk={class:"text-[var(--bkc-color-gray-1)] text-base font-bold leading-normal"},Gk={class:"self-stretch py-2.5 bg-[var(--bkc-color-blackout-input-bg)] rounded border border-[var(--bkc-theme-2)] flex-col justify-start items-start gap-2.5 flex"},Hk={class:"self-stretch justify-start items-start gap-2.5 inline-flex h-5"},Wk={class:"px-3 justify-start items-start gap-3.5 flex"},Yk={class:"justify-start items-center gap-1 flex h-4"},Kk={class:"text-[var(--bkc-el-color-primary)] text-sm font-normal leading-snug"},Xk=kn(()=>t("em",{class:"iconfont icon-loginuser"},null,-1)),Jk={class:"h-[52px] self-stretch px-2.5 justify-start items-start inline-flex border-solid border-[var(--bkc-theme-2)] border-b gap-x-[20px]"},Zk={class:"w-[20%]"},ey={class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight"},ty={class:"w-[20%]"},ny={class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight"},ay={class:"w-[20%] rate"},sy={class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight"},oy=kn(()=>t("div",{class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight ml-[6px]"},"%",-1)),iy={key:0,class:"text-[12px] relative bottom-[15px] left-[72px] text-[var(--bkc-color-special-yellow-1)]"},ly={class:"w-[20%]"},ry={class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight"},cy={key:0,class:"text-[11px] relative bottom-[15px] left-[72px] text-[var(--bkc-color-special-yellow-1)]"},uy={class:"w-[20%] total-tax"},dy={class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight"},py={class:"self-stretch px-2.5 justify-start items-start gap-5 inline-flex"},fy={class:"justify-center items-center gap-0.5 flex"},my={class:"text-[var(--bkc-color-gray-1)] text-base font-normal leading-normal"},gy={class:"text-[var(--bkc-color-special-red-1)] text-base font-bold leading-normal"},ky={class:"text-[var(--bkc-color-gray-4)] rotate-180"},yy=kn(()=>t("span",{class:"iconfont icon-info-circle-line"},null,-1)),hy={class:"my-2.5 flex-col justify-start items-start gap-1 inline-flex w-full"},vy=je({__name:"RefundEdit",props:{packageData:{},queryForm:{},isDragonBoatOffice:{type:Boolean}},emits:["update:volunteerFlag","update:modelValue","cleanData","cancel"],setup(a,{emit:o}){const u=a,h=o,{rules:f,fullscreenLoading:p,isReset:i,formRef:_,taxForm:d,judgeCommissionRate:x,judgeCommission:T,editData:k,tktNoTag:R,setCreditCard:v,bindPaymentValue:r,handleNetRefund:y,handleRate:C,handleTaxConfirm:$,handleTaxClose:P,reset:M,submit:X,openTax:ue}=Hg(u,h);return(E,j)=>{const oe=Pt,D=Ot,b=ot,N=gt,ne=tn,ie=nn,U=yt,A=an,le=na,he=Je,ce=it,L=kt;return s(),m(ye,null,[c(ce,{ref_key:"formRef",ref:_,class:"manual-refund-edit-form mt-[14px]",model:e(k),"label-position":"left","require-asterisk-position":"right"},{default:l(()=>{var se,Q,S,G,J,z,w,g,B,F,de,ve,O,fe,Y,Te;return[Qe((s(),m("div",Wg,[t("div",Yg,[t("div",Kg,n(E.$t("app.agentTicketRefund.rtPassenger")),1)]),t("div",Xg,[t("div",Jg,[t("div",Zg,[t("div",ek,[t("div",tk,[nk,K(n((Q=(se=E.packageData)==null?void 0:se.formInfo)==null?void 0:Q.name),1)])]),t("div",ak,[t("div",sk,n(E.$t("app.agentTicketRefund.ticketNum")),1),t("div",ok,n((G=(S=E.packageData)==null?void 0:S.formInfo)==null?void 0:G.ticketNo),1)]),t("div",ik,[t("div",lk,n(E.$t("app.agentTicketRefund.airlineNum")),1),t("div",rk,n((z=(J=E.packageData)==null?void 0:J.formInfo)==null?void 0:z.airline),1)]),t("div",ck,[t("div",uk,n(E.$t("app.agentTicketRefund.ticketType")),1),t("div",dk,n((g=(w=E.packageData)==null?void 0:w.formInfo)==null?void 0:g.tktType),1)]),t("div",pk,[t("div",fk,n(E.$t("app.agentTicketRefund.conjunction")),1),t("div",mk,n((F=(B=E.packageData)==null?void 0:B.formInfo)==null?void 0:F.conjunction),1)]),((ve=(de=E.packageData)==null?void 0:de.formInfo)==null?void 0:ve.receiptPrinted)==="1"?(s(),m("div",gk,yk)):Z("",!0)])]),t("div",hk,[t("div",vk,[t("div",_k,[c(b,{label:E.$t("app.agentTicketRefund.electronic"),prop:"etTag",rules:e(f).etTag},{default:l(()=>[c(D,{modelValue:e(k).etTag,"onUpdate:modelValue":j[0]||(j[0]=V=>e(k).etTag=V),class:"u-radio"},{default:l(()=>[c(oe,{label:"1",border:""},{default:l(()=>[K(n(E.$t("app.agentTicketRefund.yes")),1)]),_:1}),c(oe,{label:"0",border:""},{default:l(()=>[K(n(E.$t("app.agentTicketRefund.no")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["label","rules"])]),t("div",bk,[c(b,{label:E.$t("app.agentTicketRefund.currency"),prop:"currency",rules:e(f).currency},{default:l(()=>[c(N,{modelValue:e(k).currency,"onUpdate:modelValue":j[1]||(j[1]=V=>e(k).currency=V),clearable:"",placeholder:E.$t("app.agentTicketRefund.currency"),onInput:j[2]||(j[2]=V=>e(k).currency=e(k).currency.toLocaleUpperCase())},null,8,["modelValue","placeholder"])]),_:1},8,["label","rules"])]),t("div",xk,[c(b,{label:E.$t("app.agentTicketRefund.payment"),prop:"payType",rules:e(f).payType},{default:l(()=>[c(ie,{modelValue:e(k).payType,"onUpdate:modelValue":j[3]||(j[3]=V=>e(k).payType=V),"popper-class":"popper-payType",class:"select-input",filterable:"","allow-create":"","default-first-option":"","automatic-dropdown":"",placeholder:E.$t("app.agentTicketRefund.paymentSel"),clearable:"",onChange:e(v),onBlur:e(r)},{default:l(()=>[(s(!0),m(ye,null,Ne(e(Dn),(V,ge)=>(s(),ae(ne,{key:ge,label:V.label,value:V.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder","onChange","onBlur"])]),_:1},8,["label","rules"])]),Qe(t("div",Tk,[c(b,{label:E.$t("app.agentTicketRefund.creditCard"),prop:"creditCard",rules:e(f).creditCard},{default:l(()=>[c(N,{modelValue:e(k).creditCard,"onUpdate:modelValue":j[4]||(j[4]=V=>e(k).creditCard=V),clearable:"",placeholder:E.$t("app.agentTicketRefund.creditCard")},null,8,["modelValue","placeholder"])]),_:1},8,["label","rules"])],512),[[vn,e(k).payType==="TC"]]),t("div",$k,[c(b,{label:E.$t("app.agentTicketRefund.remark"),prop:"remark",rules:e(f).remark},{label:l(({label:V})=>[t("span",Nk,n(V),1),c(U,{class:"box-item",effect:"dark",content:E.$t("app.agentTicketRefund.remarkTool"),placement:"top"},{default:l(()=>[Rk]),_:1},8,["content"])]),default:l(()=>[c(N,{modelValue:e(k).remark,"onUpdate:modelValue":j[5]||(j[5]=V=>e(k).remark=V),maxlength:"16",clearable:"",placeholder:E.$t("app.agentTicketRefund.remarkPlaceholder"),onInput:j[6]||(j[6]=V=>e(k).remark=e(k).remark.toUpperCase())},null,8,["modelValue","placeholder"])]),_:1},8,["label","rules"])]),e(k).payType!=="TC"?(s(),m("div",Ck)):Z("",!0)]),t("div",wk,[e(k).tktType==="D"?(s(!0),m(ye,{key:0},Ne(e(k).segmentShow,(V,ge)=>(s(),m(ye,{key:ge},[e(R)===V[0].tktTag?(s(),m("div",Ak,[t("div",Sk,[t("div",Dk,n(E.$t("app.agentTicketRefund.couponNo",{a:e(Un)(ge)})),1)]),t("div",Pk,[t("div",Ok,[c(b,{prop:`segment[${ge}]`},{default:l(()=>[c(le,{modelValue:e(k).checkSeg,"onUpdate:modelValue":j[7]||(j[7]=me=>e(k).checkSeg=me)},{default:l(()=>[(s(!0),m(ye,null,Ne(V,(me,Re)=>(s(),m("div",{key:Re+"segmet",class:"justify-start items-center gap-1 flex"},[t("div",Ek,[c(A,{disabled:me.isAble==="0",label:me},{default:l(()=>[t("div",Fk,n(me.departureCode)+"-"+n(me.arriveCode),1)]),_:2},1032,["disabled","label"])]),t("div",{class:Se([{"icon-grey":me.isAble==="0","icon-blue":me.isAble==="1"},"text-xs justify-center items-center w-[14px] h-[14px] border-[1px] rounded-full flex"])},n(Re+1),3)]))),128))]),_:2},1032,["modelValue"])]),_:2},1032,["prop"])])])])):Z("",!0)],64))),128)):(s(!0),m(ye,{key:1},Ne(e(k).segmentShow,(V,ge)=>(s(),m(ye,{key:ge},[t("div",Vk,[t("div",Mk,n(E.$t("app.agentTicketRefund.couponNo",{a:e(Un)(ge)})),1)]),t("div",jk,[t("div",Lk,[c(b,{prop:`segment[${ge}]`},{default:l(()=>[c(le,{modelValue:e(k).checkSeg,"onUpdate:modelValue":j[8]||(j[8]=me=>e(k).checkSeg=me)},{default:l(()=>[(s(!0),m(ye,null,Ne(V,(me,Re)=>(s(),m("div",{key:Re+"segmet",class:"justify-start items-center gap-1 flex"},[t("div",Bk,[c(A,{disabled:"",label:me},{default:l(()=>[t("div",Ik,n(me.departureCode)+"-"+n(me.arriveCode),1)]),_:2},1032,["label"])]),t("div",{class:Se([{"icon-grey":me.isAble==="0","icon-blue":me.isAble==="1"},"text-xs justify-center items-center w-[14px] h-[14px] border-[1px] rounded-full flex"])},n(Re+1),3)]))),128))]),_:2},1032,["modelValue"])]),_:2},1032,["prop"])])])],64))),128))])])])])),[[L,e(p),void 0,{fullscreen:!0,lock:!0}]]),t("div",Uk,[t("div",Qk,[t("div",qk,[t("div",zk,n(E.$t("app.agentTicketRefund.amount")),1)]),t("div",Gk,[t("div",Hk,[t("div",Wk,[t("div",Yk,[t("div",Kk,[Xk,K(n((fe=(O=E.packageData)==null?void 0:O.formInfo)==null?void 0:fe.name),1)])])])]),t("div",Jk,[t("div",Zk,[t("div",ey,[c(b,{label:E.$t("app.agentTicketRefund.totalAmount"),prop:"totalAmount",rules:e(f).price},{default:l(()=>[c(N,{modelValue:e(k).totalAmount,"onUpdate:modelValue":j[9]||(j[9]=V=>e(k).totalAmount=V),modelModifiers:{trim:!0},clearable:"",onChange:j[10]||(j[10]=V=>e(y)())},null,8,["modelValue"])]),_:1},8,["label","rules"])])]),t("div",ty,[t("div",ny,[c(b,{label:E.$t("app.agentTicketRefund.charge"),prop:"otherDeduction",rules:e(f).price},{default:l(()=>[c(N,{modelValue:e(k).otherDeduction,"onUpdate:modelValue":j[11]||(j[11]=V=>e(k).otherDeduction=V),modelModifiers:{trim:!0},clearable:"",onChange:j[12]||(j[12]=V=>e(y)())},null,8,["modelValue"])]),_:1},8,["label","rules"])])]),t("div",ay,[t("div",sy,[c(b,{label:E.$t("app.agentTicketRefund.commissionRate"),prop:"commisionRate",rules:e(f).rate},{default:l(()=>[c(N,{modelValue:e(k).commisionRate,"onUpdate:modelValue":j[13]||(j[13]=V=>e(k).commisionRate=V),modelModifiers:{trim:!0},disabled:e(k).rate==="0",placeholder:E.$t("app.agentTicketRefund.rate"),clearable:"",class:Se([e(x)?"warning-input":""]),onChange:j[14]||(j[14]=V=>e(C)())},null,8,["modelValue","disabled","placeholder","class"]),oy]),_:1},8,["label","rules"]),e(x)?(s(),m("span",iy,n(E.$t("app.agentTicketRefund.commissionRateTip")),1)):Z("",!0)])]),t("div",ly,[t("div",ry,[c(b,{label:E.$t("app.agentTicketRefund.commission"),prop:"commision",rules:e(f).commision},{default:l(()=>[c(N,{modelValue:e(k).commision,"onUpdate:modelValue":j[15]||(j[15]=V=>e(k).commision=V),modelModifiers:{trim:!0},disabled:e(k).rate==="1",placeholder:E.$t("app.agentTicketRefund.commission"),clearable:"",class:Se([e(T)?"warning-input":""]),onChange:j[16]||(j[16]=V=>e(C)())},null,8,["modelValue","disabled","placeholder","class"])]),_:1},8,["label","rules"]),e(T)?(s(),m("span",cy,n(E.$t("app.agentTicketRefund.commissionTip")),1)):Z("",!0)])]),t("div",uy,[t("div",dy,[c(b,{label:E.$t("app.agentTicketRefund.totalTax"),prop:"totalTaxs",rules:e(f).price},{default:l(()=>[c(N,{modelValue:e(k).totalTaxs,"onUpdate:modelValue":j[17]||(j[17]=V=>e(k).totalTaxs=V),modelModifiers:{trim:!0},disabled:"",placeholder:E.$t("app.agentTicketRefund.taxEditBox"),clearable:""},null,8,["modelValue","placeholder"]),t("div",{class:"whitespace-nowrap text-[var(--bkc-el-color-primary)] cursor-pointer text-xs font-normal leading-tight ml-[6px]",onClick:j[18]||(j[18]=(...V)=>e(ue)&&e(ue)(...V))},[c(he,null,{default:l(()=>[c(e(Fs))]),_:1}),K(n(E.$t("app.agentTicketRefund.taxEdit")),1)])]),_:1},8,["label","rules"])])])]),t("div",py,[t("div",fy,[t("div",my,n(E.$t("app.agentTicketRefund.actualSettlementAmount")),1),t("div",gy,n(e(k).currency)+" "+n(Number(e(k).netRefund).toFixed(2)),1),t("div",ky,[c(U,{class:"box-item",effect:"dark",content:E.$t("app.agentTicketRefund.amountReference"),placement:"top"},{default:l(()=>[yy]),_:1},8,["content"])])])])])])]),t("div",hy,[c(ts,{"ticket-type":((Te=(Y=E.packageData)==null?void 0:Y.formInfo)==null?void 0:Te.tktType)??"D","is-reset":e(i),"refund-type":"Manual",onReset:e(M),onCancel:j[19]||(j[19]=V=>h("cancel")),onSubmit:e(X)},null,8,["ticket-type","is-reset","onReset","onSubmit"])])]}),_:1},8,["model"]),e(d).on?(s(),ae(zg,{key:0,modelValue:e(d).on,"onUpdate:modelValue":j[20]||(j[20]=se=>e(d).on=se),params:e(d),onConfirm:e($),onClose:e(P)},null,8,["modelValue","params","onConfirm","onClose"])):Z("",!0)],64)}}});const _y=at(vy,[["__scopeId","data-v-42b33ae9"]]),by={class:"flex flex-col"},xy={class:"w-full h-[36px] px-[10px] mt-[10px] bg-yellow-3 rounded border border-yellow-2 flex items-center bg-[var(--bkc-color-special-yellow-3)] text-[var(--bkc-color-special-yellow-1)]"},Ty=t("em",{class:"iconfont icon-warning-circle-fill mr-2"},null,-1),$y={class:"text-yellow-1 text-xs font-normal leading-tight"},Ny=je({__name:"TicketRefundManual",props:{packageData:{},queryForm:{},isDragonBoatOffice:{type:Boolean}},emits:["update:volunteerFlag","update:modelValue","cleanData","cancel"],setup(a,{emit:o}){const u=o,h=()=>{u("update:volunteerFlag","AUTO"),u("update:modelValue",!1),u("cleanData")};return(f,p)=>(s(),m("div",by,[t("div",xy,[Ty,t("span",$y,n(f.$t("app.agentTicketRefund.manualNotice")),1)]),c(ra,{"package-data":f.packageData},null,8,["package-data"]),c(_y,{"package-data":f.packageData,"query-form":f.queryForm,"is-dragon-boat-office":f.isDragonBoatOffice,onCancel:h},null,8,["package-data","query-form","is-dragon-boat-office"])]))}}),Ry=a=>{var J;const{t:o}=We(),u=Ct(),{defaultOffice:h,office:f,defaultRoleWithPid:p}=u.state.user,_=(p?h:((J=f==null?void 0:f.split(";"))==null?void 0:J[0])??"")===Ua,d=tt({tktNo:a.tktNo,prntNo:"",volunteer:"VOLUNTEER_AUTO"}),x=Ae(!1),T=Ae("AUTO"),k=Ae(!1),R=H({}),v=H({}),r=H({}),y={ticketSucc:!1},C=Ae(!1),$=Ae(!1),P=H({}),M=()=>{R.value={},v.value={},r.value={},T.value="AUTO"},X=async z=>{try{const w=Pe("091Q0101"),g=(await In(z,w)).data.value;return(g==null?void 0:g.code)!=="200"?(y.ticketSucc=!1,Promise.reject(new Error(g==null?void 0:g.msg))):(y.ticketSucc=!0,g.data)}catch{return Promise.reject({type:"BACK"})}},ue=async z=>{const w=(await Mo(z)).data.value;return(w==null?void 0:w.code)!=="200"?Promise.reject(new Error(w==null?void 0:w.msg)):w.data},E=async()=>{R.value.ticket.isAirportCntl==="1"&&(x.value=!1,await Xe.confirm(o("app.agentTicketRefund.apc"),{icon:Le("em",{class:"iconfont icon-info-circle-line"}),customClass:"agent-refund-msg-box",confirmButtonText:o("app.agentTicketRefund.continue"),cancelButtonText:o("app.agentTicketRefund.cancel"),showClose:!1}).catch(()=>Promise.reject({type:"BACK"})))},j=async()=>{d.tktNo.length>14&&R.value.ticket.tktType==="D"&&(x.value=!1,await Xe.alert(o("app.agentTicketRefund.singleDomestic"),{icon:Le("em",{class:"iconfont icon-close-circle-line"}),customClass:"agent-refund-msg-box",confirmButtonText:o("app.agentTicketRefund.sure"),showClose:!1,draggable:!0}).then(()=>Promise.reject({type:"BACK"})))},oe=async z=>{await Xe.confirm(z,{icon:Le("em",{class:"iconfont icon-close-circle-line"}),customClass:"agent-refund-msg-box",confirmButtonText:o("app.agentTicketRefund.sure"),cancelButtonText:o("app.agentTicketRefund.cancel"),showClose:!1,draggable:!0})},D=z=>{const w=R.value.ticket.segment;return(w??[]).forEach(g=>{z.forEach(B=>{g.rph===B.rph&&(g.isAble=B.isAble)})}),w},b=z=>(z==null?void 0:z.startsWith("CC"))||(z==null?void 0:z.startsWith("TC")),N=()=>{r.value.formInfo.totalTaxs=v.value.msg?R.value.ticket.totalTaxs??"0":v.value.amount.totalTaxs??"0",r.value.formInfo.taxs=v.value.msg?R.value.ticket.taxs??[]:v.value.amount.taxs??[],r.value.formInfo.remark=v.value.remark??"",r.value.formInfo.totalAmount=v.value.amount.totalAmount??"0",r.value.formInfo.commision=v.value.amount.commision,r.value.formInfo.commisionRate=v.value.amount.commisionRate,r.value.formInfo.rate=v.value.amount.commisionRate?"1":"0",v.value.amount.commisionRate==="0"&&v.value.amount.commision==="0"&&(r.value.formInfo.commisionRate="",r.value.formInfo.rate="0"),r.value.formInfo.otherDeduction=d.volunteer==="NON_VOLUNTEER_MANUAL"?"0":v.value.amount.otherDeduction??"0",r.value.formInfo.segList=v.value.segList??[],r.value.formInfo.netRefund=v.value.amount.netRefund??"0",r.value.formInfo.payType=R.value.ticket.payType??v.value.payType,r.value.formInfo.creditCard=_&&b(r.value.formInfo.payType)?ea:v.value.creditCard??"",r.value.formInfo.segment=v.value.segList?D(v.value.segList??[]):R.value.ticket.segment,r.value.formInfo.currency=R.value.ticket.currency??v.value.currency},ne=z=>{const{totalAmount:w,totalTaxs:g,otherDeduction:B,commision:F,commisionRate:de,rate:ve}=z;if(ve==="1"){const fe=jt(Lt(Number(w),Number(de)),100).toString(),Y=fe.includes(".")?fe.substring(0,fe.indexOf(".")+3):fe;return Dt(qe(Number(w),Number(g)),qe(Number(B),Number(Y))).toFixed(2)}return Dt(qe(Number(w),Number(g)),qe(Number(B),Number(F))).toFixed(2)},ie=z=>{if(r.value={office:R.value.office,volunteer:z==="modify"?"VOLUNTEER_MANUAL":d.volunteer,createUser:R.value.operator,prntNo:d.prntNo,formInfo:{name:R.value.ticket.name,marketAirline:R.value.ticket.marketAirline,ticketNo:R.value.ticket.ticketNo,airline:R.value.ticket.airline,tktType:R.value.ticket.tktType,etTag:R.value.ticket.etTag,currency:R.value.ticket.currency,payType:R.value.ticket.payType,segment:R.value.ticket.segment,checkSeg:[],psgType:R.value.ticket.psgType,totalTaxs:R.value.ticket.totalTaxs??"0",taxs:R.value.ticket.taxs??[],receiptPrinted:R.value.receiptPrinted,conjunction:R.value.conjunction,remark:"",creditCard:"",totalAmount:"0",commision:"0",commisionRate:"",rate:"0",otherDeduction:"0",segList:[],netRefund:"0",crsPnrNo:R.value.ticket.crsPnrNo,pnr:R.value.ticket.pnr,isCoupon:R.value.ticket.isCoupon}},v.value.amount&&N(),r.value.formInfo.netRefund=ne(r.value.formInfo),T.value==="AUTO")r.value.formInfo.checkSeg=(r.value.formInfo.segment??[]).filter(w=>w.isAble==="1").map(w=>({...w}));else{const w=d.tktNo.split("-").join("");r.value.formInfo.checkSeg=r.value.formInfo.tktType==="D"?(r.value.formInfo.segment??[]).filter(g=>g.isAble==="1"&&g.tktTag===w):(r.value.formInfo.segment??[]).filter(g=>g.isAble==="1")}},U=()=>{k.value=!0,T.value="AUTO",ie()},A=z=>{T.value="MANUAL",k.value=!0,ie(z)},le=async z=>{k.value=!1,M();try{if(R.value=await X({tktNo:z.tktNo}),await E(),x.value=!0,v.value=await ue({tktNo:z.tktNo,prntNo:z.prntNo,tktType:R.value.ticket.tktType}),x.value=!1,v.value.msg){if(v.value.msg===o("app.agentTicketRefund.noSeatWithdRawal")){C.value=!0;return}await oe(v.value.msg).then(async()=>{await j(),A("modify")}).catch(()=>Promise.reject({type:"BACK"}))}else U()}catch(w){if(x.value=!1,!y.ticketSucc||(w==null?void 0:w.type)==="BACK")return;d.tktNo.length>14&&R.value.ticket.tktType==="D"?j():await oe(o("app.agentTicketRefund.goManualRefund")).then(()=>{A("modify")})}finally{x.value=!1}},he=async z=>{k.value=!1,M();try{R.value=await X({tktNo:z.tktNo}),await j(),await E(),x.value=!0,v.value=await ue({tktNo:z.tktNo,prntNo:z.prntNo,tktType:R.value.ticket.tktType}),x.value=!1,A()}catch(w){if(x.value=!1,!y.ticketSucc||(w==null?void 0:w.type)==="BACK")return;A()}finally{x.value=!1}},ce=()=>{C.value=!1},L=async()=>{var z,w;try{x.value=!0;const g=(await Vo((w=(z=R.value)==null?void 0:z.ticket)==null?void 0:w.crsPnrNo)).data.value;x.value=!1,g?mt({message:o("app.agentTicketRefund.xeSuccess"),type:"success"}):mt({message:o("app.agentTicketRefund.xeFaild"),type:"error"})}finally{x.value=!1}};return{isDragonBoatOffice:_,fullscreenLoading:x,showRefundInfo:k,queryForm:d,volunteerFlag:T,showRefundSeatConfirm:C,packageData:r,closeRefundSeatDialog:ce,handleManualRefund:async()=>{ce(),await j(),A("modify")},handleXePnr:()=>{ce(),Xe.confirm(o("app.agentTicketRefund.confirmXePnr"),{icon:Le("em",{class:"iconfont icon-info-circle-line"}),customClass:"agent-refund-msg-box",confirmButtonText:o("app.agentTicketRefund.sure"),cancelButtonText:o("app.agentTicketRefund.cancel"),showClose:!1}).then(()=>{L()}).catch(()=>{C.value=!0})},refund:async z=>{switch(x.value=!0,z.volunteer){case"VOLUNTEER_AUTO":le(z);break;default:he(z);break}},cleanData:M,showPreview:$,openPreview:async z=>{const w={tktNo:d.tktNo},g=Mt.service({fullscreen:!0});try{const B=Pe("091T0109"),{data:F}=await Ia(w,B);P.value=F.value,$.value=z}finally{g.close()}},previewInfo:P}},Cy=a=>{const o=H({});return(()=>{o.value={office:a.previewInfo.refundTicketOrder.office,prntNo:"",volunteer:"",createUser:a.previewInfo.refundTicketOrder.operator,formInfo:{name:a.previewInfo.refundTicketOrder.ticket.name,marketAirline:a.previewInfo.refundTicketOrder.ticket.marketAirline,ticketNo:a.previewInfo.refundTicketOrder.ticket.ticketNo,airline:a.previewInfo.refundTicketOrder.ticket.airline,tktType:a.previewInfo.refundTicketOrder.ticket.tktType,etTag:a.previewInfo.refundTicketOrder.ticket.etTag,currency:a.previewInfo.refundTicketOrder.ticket.currency??a.previewInfo.refundTicketOrder.ticket.currency,payType:a.previewInfo.refundTicketOrder.ticket.payType??a.previewInfo.refundTicketOrder.ticket.payType,segment:a.previewInfo.refundTicketOrder.ticket.segment,psgType:a.previewInfo.refundTicketOrder.ticket.psgType,totalTaxs:a.previewInfo.refundCompute.amount.totalTaxs?Number(a.previewInfo.refundCompute.amount.totalTaxs).toFixed(2):"",taxs:a.previewInfo.refundCompute.amount.taxs??[],receiptPrinted:a.previewInfo.refundTicketOrder.receiptPrinted,conjunction:a.previewInfo.refundTicketOrder.conjunction,remark:a.previewInfo.refundCompute.remark,creditCard:a.previewInfo.refundCompute.creditCard,totalAmount:a.previewInfo.refundCompute.amount.totalAmount?Number(a.previewInfo.refundCompute.amount.totalAmount).toFixed(2):"",commision:a.previewInfo.refundCompute.amount.commision?Number(a.previewInfo.refundCompute.amount.commision).toFixed(2):"",commisionRate:a.previewInfo.refundCompute.amount.commisionRate?Number(a.previewInfo.refundCompute.amount.commisionRate).toFixed(2):"",rate:a.previewInfo.refundCompute.amount.commisionRate?"1":"0",otherDeduction:a.previewInfo.refundCompute.amount.otherDeduction?Number(a.previewInfo.refundCompute.amount.otherDeduction).toFixed(2):"",netRefund:a.previewInfo.refundCompute.amount.netRefund?Number(a.previewInfo.refundCompute.amount.netRefund).toFixed(2):"",crsPnrNo:"",pnr:"",isCoupon:a.previewInfo.refundTicketOrder.ticket.isCoupon}}})(),{renderData:o}},wy={class:"w-full"},Ay={class:"self-stretch justify-start items-start gap-5 inline-flex border-solid border-brand-3 w-full"},Sy={class:"flex-1 shrink basis-0 flex-col justify-start items-start inline-flex"},Dy={class:"text-gray-4 text-xs font-normal leading-tight"},Py={class:"text-gray-1 text-xs font-normal leading-tight mt-1 truncate"},Oy={class:"flex-1 shrink basis-0 flex-col justify-start items-start inline-flex"},Ey={class:"text-gray-4 text-xs font-normal leading-tight truncate"},Fy={class:"text-gray-1 text-xs font-normal leading-tight mt-1 truncate"},Vy={class:"flex-1 shrink basis-0 flex-col justify-start items-start inline-flex"},My={class:"text-gray-4 text-xs font-normal leading-tight truncate"},jy={class:"text-gray-1 text-xs font-normal leading-tight mt-1 truncate"},Ly={class:"flex-1 shrink basis-0 flex-col justify-start items-start inline-flex"},By={class:"text-gray-4 text-xs font-normal leading-tight truncate"},Iy={class:"text-gray-1 text-xs font-normal leading-tight mt-1 truncate"},Uy={class:"text-gray-1 text-xs font-normal leading-tight mt-1 truncate"},Qy={class:"flex-1 shrink basis-0 flex-col justify-start items-start inline-flex"},qy={class:"text-gray-4 text-xs font-normal leading-tight truncate"},zy={class:"text-gray-1 text-xs font-normal leading-tight mt-1 truncate"},Gy={class:"flex-1 shrink basis-0 flex-col justify-start items-start inline-flex"},Hy={class:"text-gray-3 text-xs font-normal leading-tight truncate"},Wy={class:"justify-center items-center gap-0.5 inline-flex"},Yy={class:"text-red-1 text-xs font-normal leading-tight mt-1 truncate"},Ky={class:"u-tax-cont"},Xy={class:"u-tax-title"},Jy={key:1,class:"u-tax-item"},Zy={class:"flex-1 shrink basis-0 flex-col justify-start items-start inline-flex"},eh={class:"text-gray-3 text-xs font-normal leading-tight truncate"},th={class:"justify-start items-center gap-1 inline-flex"},nh={class:"text-red-1 text-xs font-normal leading-tight mt-1 truncate"},ah={class:"px-2.5 py-1.5 bg-brand-8 rounded justify-start items-start w-full flex text-gray-3 text-xs"},sh={class:"mx-[5px]"},oh=je({__name:"PreviewDialog",props:{previewInfo:{}},setup(a){const o=a,{renderData:u}=Cy(o);return(h,f)=>{const p=yt,i=Je,_=sa,d=Sn,x=nt;return s(),ae(x,{title:h.$t("app.agentTicketRefund.preview"),"close-on-press-escape":!1,class:"preview-dialog tc-refund-preview-dialog",width:"1040px"},{default:l(()=>[t("div",wy,[c(ra,{"package-data":e(u),"title-normal":!0,"hide-refund-type":!0},null,8,["package-data"]),c(Za,{"package-data":e(u),"title-normal":!0,"hide-check":!0},null,8,["package-data"]),c(es,{"package-data":e(u),"title-normal":!0},{"refund-amount":l(()=>{var T,k,R,v,r,y,C,$,P,M,X,ue,E,j,oe,D,b,N,ne,ie,U,A,le,he,ce,L,se,Q;return[t("div",Ay,[t("div",Sy,[c(p,{effect:"dark",content:h.$t("app.agentTicketRefund.passName"),placement:"top"},{default:l(()=>[t("div",Dy,n(h.$t("app.agentTicketRefund.passName")),1)]),_:1},8,["content"]),c(p,{effect:"dark",content:(k=(T=e(u))==null?void 0:T.formInfo)==null?void 0:k.name,placement:"top"},{default:l(()=>{var S,G;return[t("div",Py,n((G=(S=e(u))==null?void 0:S.formInfo)==null?void 0:G.name),1)]}),_:1},8,["content"])]),t("div",Oy,[c(p,{effect:"dark",content:h.$t("app.agentTicketRefund.passType"),placement:"top"},{default:l(()=>[t("div",Ey,n(h.$t("app.agentTicketRefund.passType")),1)]),_:1},8,["content"]),c(p,{effect:"dark",content:e(Qn)((v=(R=e(u))==null?void 0:R.formInfo)==null?void 0:v.psgType),placement:"top"},{default:l(()=>{var S,G;return[t("div",Fy,n(e(Qn)((G=(S=e(u))==null?void 0:S.formInfo)==null?void 0:G.psgType)),1)]}),_:1},8,["content"])]),t("div",Vy,[c(p,{effect:"dark",content:h.$t("app.agentTicketRefund.totalAmount"),placement:"top"},{default:l(()=>[t("div",My,n(h.$t("app.agentTicketRefund.totalAmount")),1)]),_:1},8,["content"]),c(p,{effect:"dark",content:`${(y=(r=e(u))==null?void 0:r.formInfo)==null?void 0:y.currency} ${($=(C=e(u))==null?void 0:C.formInfo)==null?void 0:$.totalAmount}`,placement:"top"},{default:l(()=>{var S,G,J,z;return[t("div",jy,n((G=(S=e(u))==null?void 0:S.formInfo)==null?void 0:G.currency)+" "+n((z=(J=e(u))==null?void 0:J.formInfo)==null?void 0:z.totalAmount),1)]}),_:1},8,["content"])]),t("div",Ly,[c(p,{effect:"dark",content:h.$t("app.agentTicketRefund.commission"),placement:"top"},{default:l(()=>[t("div",By,n(h.$t("app.agentTicketRefund.commission")),1)]),_:1},8,["content"]),((M=(P=e(u))==null?void 0:P.formInfo)==null?void 0:M.rate)==="1"?(s(),ae(p,{key:0,effect:"dark",content:`${(ue=(X=e(u))==null?void 0:X.formInfo)==null?void 0:ue.commisionRate}%`,placement:"top"},{default:l(()=>{var S,G;return[t("div",Iy,n((G=(S=e(u))==null?void 0:S.formInfo)==null?void 0:G.commisionRate)+"%",1)]}),_:1},8,["content"])):(s(),ae(p,{key:1,effect:"dark",content:`${(j=(E=e(u))==null?void 0:E.formInfo)==null?void 0:j.currency} ${(D=(oe=e(u))==null?void 0:oe.formInfo)==null?void 0:D.commision}`,placement:"top"},{default:l(()=>{var S,G,J,z;return[t("div",Uy,n((G=(S=e(u))==null?void 0:S.formInfo)==null?void 0:G.currency)+" "+n((z=(J=e(u))==null?void 0:J.formInfo)==null?void 0:z.commision),1)]}),_:1},8,["content"]))]),t("div",Qy,[c(p,{effect:"dark",content:h.$t("app.agentTicketRefund.charge"),placement:"top"},{default:l(()=>[t("div",qy,n(h.$t("app.agentTicketRefund.charge")),1)]),_:1},8,["content"]),c(p,{effect:"dark",content:`${(N=(b=e(u))==null?void 0:b.formInfo)==null?void 0:N.currency} ${(ie=(ne=e(u))==null?void 0:ne.formInfo)==null?void 0:ie.otherDeduction}`,placement:"top"},{default:l(()=>{var S,G,J,z;return[t("div",zy,n((G=(S=e(u))==null?void 0:S.formInfo)==null?void 0:G.currency)+" "+n((z=(J=e(u))==null?void 0:J.formInfo)==null?void 0:z.otherDeduction),1)]}),_:1},8,["content"])]),t("div",Gy,[c(p,{effect:"dark",content:h.$t("app.agentTicketRefund.totalTax"),placement:"top"},{default:l(()=>[t("div",Hy,n(h.$t("app.agentTicketRefund.totalTax")),1)]),_:1},8,["content"]),t("div",Wy,[c(p,{effect:"dark",content:`${(A=(U=e(u))==null?void 0:U.formInfo)==null?void 0:A.currency} ${(he=(le=e(u))==null?void 0:le.formInfo)==null?void 0:he.totalTaxs}`,placement:"top"},{default:l(()=>{var S,G,J,z;return[t("div",Yy,n((G=(S=e(u))==null?void 0:S.formInfo)==null?void 0:G.currency)+" "+n((z=(J=e(u))==null?void 0:J.formInfo)==null?void 0:z.totalTaxs),1)]}),_:1},8,["content"]),c(d,{placement:"top",title:h.$t("app.agentTicketRefund.taxDetails"),width:176,trigger:"hover","popper-class":"u-detail-tax-popper-crs"},{reference:l(()=>[c(i,{class:"tooltip-icon"},{default:l(()=>[c(e(un))]),_:1})]),default:l(()=>{var S,G;return[t("div",Ky,[((G=(S=e(u))==null?void 0:S.formInfo)==null?void 0:G.taxs.length)>0?(s(),m(ye,{key:0},[t("div",Xy,[t("span",null,n(h.$t("app.agentTicketRefund.taxes")),1),t("span",null,n(h.$t("app.agentTicketRefund.taxAmount")),1)]),c(_,{"max-height":"120px",always:!0},{default:l(()=>{var J,z;return[(s(!0),m(ye,null,Ne((z=(J=e(u))==null?void 0:J.formInfo)==null?void 0:z.taxs,w=>{var g,B;return s(),m("div",{key:w.name,class:"u-tax-item"},[t("span",null,n(w.name),1),t("span",null,n(Number(w.value).toFixed(2))+" "+n((B=(g=e(u))==null?void 0:g.formInfo)==null?void 0:B.currency),1)])}),128))]}),_:1})],64)):(s(),m("div",Jy,[t("span",null,n(h.$t("app.agentTicketRefund.noData")),1)]))])]}),_:1},8,["title"])])]),t("div",Zy,[c(p,{effect:"dark",content:h.$t("app.agentTicketRefund.actualSettlementAmount"),placement:"top"},{default:l(()=>[t("div",eh,n(h.$t("app.agentTicketRefund.actualSettlementAmount")),1)]),_:1},8,["content"]),c(p,{effect:"dark",content:`${(L=(ce=e(u))==null?void 0:ce.formInfo)==null?void 0:L.currency} ${(Q=(se=e(u))==null?void 0:se.formInfo)==null?void 0:Q.netRefund}`,placement:"top"},{default:l(()=>{var S,G,J,z;return[t("div",th,[t("div",nh,n((G=(S=e(u))==null?void 0:S.formInfo)==null?void 0:G.currency)+" "+n((z=(J=e(u))==null?void 0:J.formInfo)==null?void 0:z.netRefund),1)])]}),_:1},8,["content"])])]),t("div",ah,[(s(!0),m(ye,null,Ne(e(u).formInfo.taxs,(S,G)=>(s(),m("div",{key:G,class:"mr-[20px]"},[t("span",null,n(h.$t("app.agentTicketRefund.taxes"))+n(S.name),1),t("span",sh,n(e(u).formInfo.currency),1),t("span",null,n(Number(S.value).toFixed(2)),1)]))),128))])]}),_:1},8,["package-data"])])]),_:1},8,["title"])}}});const ih={class:"bg-[var(--bkc-el-bg-color)] p-[10px] rounded-lg mt-[6px] min-h-[calc(100vh_-_33vh)] shadow-[0_0_8px_0_rgba(109,117,151,0.2)]"},lh=je({__name:"TicketRefund",props:{tktNo:{}},setup(a){const o=a,{isDragonBoatOffice:u,volunteerFlag:h,fullscreenLoading:f,showRefundInfo:p,queryForm:i,showRefundSeatConfirm:_,packageData:d,closeRefundSeatDialog:x,handleManualRefund:T,handleXePnr:k,refund:R,cleanData:v,showPreview:r,openPreview:y,previewInfo:C}=Ry(o);return($,P)=>{const M=kt;return Qe((s(),m("div",ih,[c(wf,{"query-form":e(i),onRefund:e(R),onOpenPreview:e(y)},null,8,["query-form","onRefund","onOpenPreview"]),e(p)&&e(h)==="AUTO"?(s(),ae(Eg,{key:0,modelValue:e(p),"onUpdate:modelValue":P[0]||(P[0]=X=>Ve(p)?p.value=X:null),volunteerFlag:e(h),"onUpdate:volunteerFlag":P[1]||(P[1]=X=>Ve(h)?h.value=X:null),"package-data":e(d),onCleanData:e(v)},null,8,["modelValue","volunteerFlag","package-data","onCleanData"])):Z("",!0),e(p)&&e(h)==="MANUAL"?(s(),ae(Ny,{key:1,modelValue:e(p),"onUpdate:modelValue":P[2]||(P[2]=X=>Ve(p)?p.value=X:null),"package-data":e(d),"is-dragon-boat-office":e(u),"query-form":e(i)},null,8,["modelValue","package-data","is-dragon-boat-office","query-form"])):Z("",!0),e(_)?(s(),ae(Ef,{key:2,modelValue:e(_),"onUpdate:modelValue":P[3]||(P[3]=X=>Ve(_)?_.value=X:null),onHandleManualRefund:e(T),onHandleCancel:e(x),onHandleXepnr:e(k)},null,8,["modelValue","onHandleManualRefund","onHandleCancel","onHandleXepnr"])):Z("",!0),e(r)?(s(),ae(oh,{key:3,modelValue:e(r),"onUpdate:modelValue":P[4]||(P[4]=X=>Ve(r)?r.value=X:null),"preview-info":e(C)},null,8,["modelValue","preview-info"])):Z("",!0)])),[[M,e(f),void 0,{fullscreen:!0,lock:!0}]])}}});const rh=(a,o)=>{var Jt;const{t:u}=We(),h=H(!1),f=(Jt=navigator==null?void 0:navigator.userAgent)==null?void 0:Jt.toLowerCase(),p=Me(()=>f==null?void 0:f.includes("electron/")),{copy:i,isSupported:_}=wa({legacy:!0}),d=H({}),x=Ae(!1),T=H(!1),k=H(""),R=H(!1),v=H(!1),r=Me(()=>M.value.tssType=="Suspend"?u("app.ticketStatus.suspendFlightConfirm"):u("app.ticketStatus.unsuspendFlightConfirm")),y=Me(()=>M.value.tssType=="Suspend"?u("app.ticketStatus.suspendFlightCheck"):u("app.ticketStatus.unsuspendFlightCheck")),C=H({pnrTss:!1,issueDate:""}),$=H(),P=H(""),M=H({tssType:"",ticketNo:"",pnrNo:""}),X=Ae({render(){return Le("em",{class:"iconfont icon-calendar"})}}),ue={issueDate:[{validator:(te,xe,Ue)=>I(!!C.value.pnrTss,te,xe,Ue),trigger:"blur"}]},E=Ae(!1),j=H(!1),oe=Ae(!1),D=Ae(!1),b=H(""),N=H(""),ne=H(""),ie=H(""),U=H({}),A={segId:0,couponStatus:"",ticketNumber:"",printNo:"",ticketManagementOrganizationCode:""},le=H({}),he=Ae(""),ce=Ae(""),L=(te,xe,Ue)=>window.electronAPI.openTicketDetailWindow(te,xe,Ue),se=te=>te==null?void 0:te.some(xe=>xe==="OPEN FOR USE"),Q=te=>te==null?void 0:te.some(xe=>Ha.includes(xe)),S=te=>te==null?void 0:te.every(xe=>xe==="OPEN FOR USE"),G=te=>te==null?void 0:te.some(xe=>["REFUNDED","USED/FLOWN"].includes(xe)),J=te=>te==null?void 0:te.every(xe=>xe==="REFUNDED"),z=te=>te==null?void 0:te.every(xe=>["EXCHANGED","FIM EXCH"].includes(xe)),w=te=>te==null?void 0:te.some(xe=>xe==="SUSPENDED"),g=te=>te==null?void 0:te.every(xe=>xe==="VOID"),B=te=>te.includes("CHECKED IN")||te.includes("LIFT/BOARDED"),F=te=>(te==null?void 0:te.some(xe=>xe==="OPEN FOR USE"))&&(te==null?void 0:te.every(xe=>!["EXCHANGED","REFUNDED"].includes(xe))),de=te=>(te==null?void 0:te.some(xe=>["OPEN FOR USE","AIRPORT CNTL"].includes(xe)))&&(te==null?void 0:te.every(xe=>!["EXCHANGED","REFUNDED"].includes(xe))),ve=te=>["OPEN FOR USE","EXCHANGED"].every(xe=>te.includes(xe))||["OPEN FOR USE","FIM EXCH"].every(xe=>te.includes(xe)),O=te=>Q(te),fe=te=>S(te)||J(te)||z(te)||se(te),Y=te=>S(te)||ve(te)||F(te),Te=te=>te==null?void 0:te.every(xe=>["VOID","REFUNDED","USED/FLOWN","EXCHANGED","SUSPENDED"].includes(xe)),V=Vs({ticketType:"",ticketNo:"",ticketManagementOrganizationCode:"",secondFactor:{secondFactorCode:"",secondFactorValue:""}}),ge=H([]),me=H([]),Re=tt({pnrNo:"",ticketTypeCode:"",etNumber:"",secondFactor:{secondFactorCode:"",secondFactorValue:""}}),Oe=Ae(!1),we=te=>(te||"").replace(/\sINF\((.+?)\)/g,""),Ee=te=>te==="I",Be=(te,xe)=>{var Ue;switch(P.value=Pe("091P0101"),Re.etNumber=te.etNumber,Re.ticketTypeCode=te.ticketTypeCode??"D",Re.secondFactor=te.secondFactor,Re.pnrNo=((Ue=te.airSeg.find(Ie=>Ie.crsPnrNo&&Ie.flightNo!=="ARNK"))==null?void 0:Ue.crsPnrNo)??"",xe){case"repealTicket":j.value=!0;break}},Ze=H(!1),I=(te,xe,Ue,Ie)=>{if(!Ue&&te){Ie(new Error(u("app.agentTicketRefund.must")));return}Ie()},_e=te=>{ie.value=te,Ze.value=!0},$e=te=>{ge.value[te].showTktPopover=!1},re=te=>{const xe={title:`${u("app.agentTicketRefund.refund")}${te}`,name:`refund?type=ticketNo&sign=${te}`,content:en(lh)};o("addNewTab",xe)},Ce=(te,xe)=>{const Ue={title:`${u("app.agentTicketRefund.newRefund")}${te}`,name:`newRefund?type=ticketNo&sign=${te}`,content:en(Nn)};o("addNewTab",Ue,"",xe)},Fe=(te,xe)=>{M.value.tssType=xe,M.value.ticketNo=te.etNumber??"",M.value.pnrNo=te.airSegCrsPnr??"",C.value.issueDate=te.issueTicketDate?et(te.issueTicketDate).format("YYYY-MM-DD"):"",v.value=!0},W=async()=>{const te=Pe("091T0202");a.queryType==="2"&&C.value.pnrTss?$.value.validate(async xe=>{xe&&(await Zt(yo({pnrNo:M.value.pnrNo,tssType:M.value.tssType,issueDate:et(C.value.issueDate).format("YYYY-MM-DD")},te)),await $t(),v.value=!1)}):(await Zt(ho({ticketNumber:M.value.ticketNo,tssType:M.value.tssType},te)),await $t(),v.value=!1)},Ye=async(te,xe)=>{var Ie,ut;const Ue=Mt.service({fullscreen:!0});try{const rt=await xo(te,xe);return V.ticketManagementOrganizationCode=((ut=(Ie=rt==null?void 0:rt.data)==null?void 0:Ie.value)==null?void 0:ut.data)??"",!0}catch{return!0}finally{Ue.close()}},lt=async(te,xe)=>{b.value="changeTicketStatus",A.ticketNumber=te,A.segId=xe.segmentIndex,A.couponStatus=xe.ticketStatus==="AIRPORT CNTL"?"OPEN FOR USE":xe.ticketStatus;const Ue=Pe("091T0204");if(!await Ye(te,Ue))return;const Ie=xe.ticketStatus==="REFUNDED"?'<span class="status-open"> OPEN FOR USE </span>':'<span class="status-refunded"> REFUNDED </span>',ut=xe.ticketStatus==="OPEN FOR USE"||xe.ticketStatus==="AIRPORT CNTL"?`${u("app.ticketStatus.generate")}`:`${u("app.ticketStatus.delete")}`;await Xe.confirm(`<div class="message-header w-[508px]">
        <em class="iconfont icon-warning-circle-fill"></em>
        <span>${u("app.ticketStatus.tip")}${ut}。</span>
      </div>
     <div class="message-cotent mb-[18px]">
      <em class="iconfont icon-info-circle-line"></em>
      <span style="color: var(--bkc-color-gray-1);font-size: 18px !important;">${u("app.ticketStatus.changeStatus")}<span/>
      <span>${Ie}${u("app.ticketStatus.status")}?</span>
     </div>`,{customClass:"ticket-status-dialog crs-btn-ui crs-btn-message-ui",confirmButtonText:u("app.ticketStatus.confirmBtn"),cancelButtonText:u("app.ticketStatus.cancelBtn"),dangerouslyUseHTMLString:!0,showClose:!1}).then(()=>{E.value=!0})},be=async(te,xe,Ue,Ie)=>{var rt,Qt;N.value=xe??"",ne.value=Ue??"",V.ticketManagementOrganizationCode=Ie;const ut=Mt.service({fullscreen:!0});if(b.value==="ticketRefundForm"){const _t={ticketNo:V.ticketNo,ticketType:Ue||V.ticketType,printerNo:xe,ticketManagementOrganizationCode:Ie,secondFactor:V.secondFactor};try{if(Oe.value){const{data:At}=await vo(_t,te);le.value=(rt=At.value)==null?void 0:rt.data,D.value=!0}else{const{data:At}=await dn(_t,te);le.value=(Qt=At.value)==null?void 0:Qt.data,le.value.ticketManagementOrganizationCode=Ie,R.value=!0}}finally{ut.close()}}else{A.printNo=N.value,A.ticketManagementOrganizationCode=Ie??"";try{const _t=Pe("091T0201");(await _o(A,_t)).data.value==="OK"&&$t()}finally{ut.close()}}},vt=(te,xe)=>{const Ue=Pe("091T0104");Tt(te,xe,Ue)},pt=(te,xe)=>{const Ue=Pe("091T0103");Tt(te,xe,Ue)},Tt=async(te,xe,Ue)=>{const Ie=te&&xe.ticketTypeCode==="I"&&xe.conjunctiveTicket?xe.conjunctiveTicket.slice(0,14):xe.etNumber;!te&&!await Ye(Ie,Ue)||(V.ticketNo=Ie,V.ticketType=xe.ticketTypeCode??"",V.secondFactor=xe.secondFactor,Oe.value=te,E.value=!te,b.value="ticketRefundForm",te&&be(Ue))},$t=()=>{o("reQueryTicket")},It=async te=>{const xe=Pe("091T0109"),{etNumber:Ue,secondFactor:Ie}=te,ut=Mt.service({fullscreen:!0});try{const{data:rt}=await Ia({tktNo:Ue,secondFactor:Ie},xe);d.value=rt.value,x.value=!0}finally{ut.close()}},Vt=te=>{const xe=`/v2/crs/pnrManagement?pnrNo=${te}`;wn.setLink(xe)},Ht=(te,xe,Ue)=>{D.value=!1,he.value=te,ce.value=xe,V.ticketManagementOrganizationCode=Ue,oe.value=!0},Xt=async(te,xe,Ue)=>{var rt;Oe.value=!1,b.value="ticketRefundForm";const Ie=Mt.service({fullscreen:!0}),ut={ticketNo:V.ticketNo,ticketType:ne.value?ne.value:V.ticketType,printerNo:xe,ticketManagementOrganizationCode:Ue,refundNo:te,secondFactor:V.secondFactor};try{const Qt=Pe("091T0104"),{data:_t}=await dn(ut,Qt);le.value=(rt=_t.value)==null?void 0:rt.data,le.value.ticketManagementOrganizationCode=V.ticketManagementOrganizationCode,R.value=!0}finally{Ie.close()}},He=async(te,xe,Ue)=>{const Ie=`<p class="text-gray-1 text-lg font-normal">${u("app.agentTicketQuery.pullControlTip")}<span class="text-green-1 text-lg font-bold pl-1">OPEN FOR USE</span></p>
      <p class="text-gray-1 text-lg font-normal">${u("app.agentTicketQuery.pullControlConfirmTip")}</p>`;await Xe.confirm(Ie,{icon:Le("em",{class:"iconfont icon-info-circle-line"}),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",confirmButtonText:u("app.ticketStatus.confirmBtn"),cancelButtonText:u("app.ticketStatus.cancelBtn"),dangerouslyUseHTMLString:!0,showClose:!1});const ut={couponNo:[te+1],remoteAirline:xe??"",ticketNo:Ue??""},rt=Pe("091T0203");await Zt(bo(ut,rt)),await $t()},ct=(te,xe,Ue)=>{if(xe==="INF"){Xe.confirm(u("app.agentTicketRefund.notAllowInfantOnlyChange"),{icon:Le("em",{class:"iconfont icon-info-circle-line"}),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",showCancelButton:!1,confirmButtonText:u("app.agentTicketRefund.sure"),showClose:!1});return}T.value=!0,k.value=te,U.value=Ue},Ut=te=>{_&&(i(te),mt({message:u("app.batchRefund.copySuccess"),type:"success"}))},ln=()=>{var te;(te=$.value)==null||te.resetFields(),M.value.tssType="",M.value.ticketNo="",M.value.pnrNo="",C.value.issueDate="",C.value.pnrTss=!1,v.value=!1};return wt(()=>a.queryTicketRes,()=>{a.queryType!=="4"?(ge.value=ft(a.queryTicketRes),(ge.value??[]).forEach((te,xe)=>{var rt,Qt;const Ue=[...new Set((rt=te.airSeg)==null?void 0:rt.filter(_t=>_t.flightNo!=="ARNK").map(_t=>{var At;return(At=_t.ticketStatus)==null?void 0:At.trim()}))],Ie=[...new Set((Qt=te.airSeg)==null?void 0:Qt.map(_t=>{var At;return(At=_t.ticketStatus)==null?void 0:At.trim()}))];te.key=bf(),te.isOpen=S(Ie),te.showTktPopover=a.queryType==="1"&&xe===0,g(Ie)||B(Ie)||(te.canRefund=O(Ie),te.canRebook=Y(Ie),te.canVoid=S(Ie)||de(Ie),te.canTktAuth=fe(Ue),te.canSuspended=S(Ie)||F(Ie),te.canUnSuspended=w(Ie),te.canQueryAndApplyRefund=G(Ie),te.canSupplementaryRefundApply=G(Ie),te.canChange=!Te(Ie))})):me.value=ft(a.queryTicketRes)},{immediate:!0,deep:!0}),st(async()=>{var Ue,Ie;const te=Pe("091Q0101"),xe=await Va(te);h.value=((Ie=(Ue=xe==null?void 0:xe.data)==null?void 0:Ue.value)==null?void 0:Ie.data)??!1}),{showSupplementRefundDialog:D,showPreview:x,previewInfo:d,openPreview:It,openChange:ct,ticketList:ge,ticketListByName:me,getPassengerName:we,ticketOperation:Be,printerType:ne,isShowRepealTicketDialog:j,ticketOperationCondition:Re,refundOperationCondition:V,closePopover:$e,goToRefund:re,goToNewRefund:Ce,reQueryTicket:$t,goToPnrManage:Vt,authTicketClick:_e,authTicketShow:Ze,stssChangeTicketStatusClick:Fe,changeStatus:lt,printNoDialog:E,openDialog:be,showTicketRefundFormDialog:R,viewRefundFormWithGid:vt,viewSupplementRefundWithGid:pt,printerNo:N,isSupplementRefund:Oe,refundTicketData:le,showSupplementSuccessDialog:oe,openSupplementSuccessDialog:Ht,invoiceNumber:he,openRefundDialog:Xt,tktNumber:ie,pullControlPower:He,changeDialogShow:T,changeTicketNo:k,copyInfo:Ut,changeFactor:U,tssForm:C,tssFormRef:$,datePrefix:X,showTssDialog:v,closeDialog:ln,TSS_FORM_RULES:ue,confirmTss:W,tssTip:r,tssCheckLabel:y,judgeInter:Ee,refundPrintNo:ce,isClient:p,openTicketDetailWindow:L,isAuthToShowWindow:h,invalidatedTicketQueryGid:P}},ch=a=>{var k;const{t:o}=We(),u=(k=navigator==null?void 0:navigator.userAgent)==null?void 0:k.toLowerCase(),h=Me(()=>u==null?void 0:u.includes("electron/")),f=(R,v,r)=>window.electronAPI.openTicketDetailWindow(R,v,r),p=H(""),i=H(!1),_=H(""),d=(R="")=>{var v,r,y;R&&(i.value=(v=R==null?void 0:R.toUpperCase())==null?void 0:v.startsWith("CC"),p.value=`${((r=R.split("/"))==null?void 0:r[1])??""}${((y=R.split("/"))==null?void 0:y[2])??""}`)},x=R=>R==="I",T=(R="")=>{var v;(v=R==null?void 0:R.toUpperCase())!=null&&v.startsWith("CC")?(_.value=o("app.agentTicketRefund.tcCredit"),d(R)):_.value=Qa(R)};return st(()=>{var R;T((R=a.previewInfo.refundTicketOrder)==null?void 0:R.ticket.payType)}),{creditCardNo:p,isCreditCard:i,payMethod:_,judgeInter:x,isClient:h,openTicketDetailWindow:f}},uh={class:"w-full p-2.5 bg-brand-4 flex-wrap flex justify-between"},dh={class:"main-title"},ph={class:"mb-1 flex items-center"},fh={key:0,class:"text-brand-2 text-base font-bold leading-normal"},mh={key:1},gh={key:2,class:"inline-flex h-[20px] px-[4px] py-[0px] rounded-[2px] text-yellow-1 bg-yellow-2 items-center text-[12px] ml-2.5"},kh={key:3,class:"iconfont icon-inf mr-[4px] text-gray-4 ml-5"},yh={key:4,class:"iconfont icon-user-fill mr-[4px] text-gray-4 ml-5"},hh={class:"text-base font-bold text-gray-1"},vh={class:"text-gray-3 py-0.5 px-1.5 rounded-sm bg-gray-7 mr-1 text-xs ml-[4px]"},_h={class:"text-sm text-gray-3"},bh={class:"text-gray-1 text-sm mr-3.5"},xh={class:"text-sm text-gray-3"},Th={class:"text-gray-1 text-sm mr-3.5"},$h={class:"text-sm text-gray-3"},Nh={class:"text-gray-1 text-sm mr-3.5"},Rh={key:0},Ch={class:"text-sm text-gray-3"},wh={class:"text-sm text-gray-1"},Ah=je({__name:"RefundInfo",props:{previewInfo:{},isAuthToShowWindow:{type:Boolean}},setup(a){const o=a,{creditCardNo:u,isCreditCard:h,payMethod:f,judgeInter:p,isClient:i,openTicketDetailWindow:_}=ch(o);return(d,x)=>{var T,k,R,v,r,y,C,$,P,M,X,ue,E;return s(),m("div",uh,[t("div",dh,[t("span",ph,[Number(d.previewInfo.refundTicketOrder.conjunction)>1?(s(),m("span",fh,n(e(ht)((T=d.previewInfo.refundTicketOrder)==null?void 0:T.ticket.ticketNo)),1)):(s(),m("div",mh,[e(i)&&d.isAuthToShowWindow?(s(),m("span",{key:0,class:"text-brand-2 text-base font-bold leading-normal cursor-pointer",onClick:x[0]||(x[0]=j=>{var oe,D,b;return e(_)((oe=d.previewInfo.refundTicketOrder)==null?void 0:oe.ticket.ticketNo,((D=d.previewInfo.secondFactor)==null?void 0:D.secondFactorCode)??"",((b=d.previewInfo.secondFactor)==null?void 0:b.secondFactorValue)??"")})},n(e(ht)(((k=d.previewInfo.refundTicketOrder)==null?void 0:k.ticket.ticketNo)??"")),1)):(s(),ae(mn,{key:1,"is-international":e(p)(((R=d.previewInfo.refundTicketOrder)==null?void 0:R.ticket.tktType)??""),"tkt-index":(v=d.previewInfo.refundTicketOrder)==null?void 0:v.ticket.ticketNo,"ticket-number":(r=d.previewInfo.refundTicketOrder)==null?void 0:r.ticket.ticketNo,"second-factor":d.previewInfo.secondFactor,"refund-class-type":"0"},null,8,["is-international","tkt-index","ticket-number","second-factor"]))])),(y=d.previewInfo.refundTicketOrder)!=null&&y.ticket.governmentPurchase?(s(),m("div",gh,"GP")):Z("",!0),(($=(C=d.previewInfo.refundTicketOrder)==null?void 0:C.ticket)==null?void 0:$.specialPassengerType)==="INF"?(s(),m("em",kh)):(s(),m("em",yh)),t("span",hh,n((P=d.previewInfo.refundTicketOrder)==null?void 0:P.ticket.passengerNameSuffix),1),t("span",vh,n(e(Rt)(((X=(M=d.previewInfo.refundTicketOrder)==null?void 0:M.ticket)==null?void 0:X.specialPassengerType)??"ADT")),1)])]),t("div",{class:Se([e(h)?"w-full":""])},[t("span",_h,n(d.$t("app.agentTicketRefund.oldTicketNo"))+"：",1),t("span",bh,n(e(ht)(((ue=d.previewInfo.refundTicketOrder.ticket)==null?void 0:ue.exchangeTktNo)??"")||"-"),1),t("span",xh,n(d.$t("app.agentTicketRefund.electronic"))+"：",1),t("span",Th,n(((E=d.previewInfo.refundTicketOrder)==null?void 0:E.ticket.etTag)==="1"?d.$t("app.agentTicketRefund.yes"):d.$t("app.agentTicketRefund.no")),1),t("span",$h,n(d.$t("app.agentTicketRefund.payment"))+"：",1),t("span",Nh,n(e(f)),1),e(h)?(s(),m("span",Rh,[t("span",Ch,n(`${d.$t("app.agentTicketRefund.creditCard")}：`),1),t("span",wh,n(e(u)),1)])):Z("",!0)],2)])}}}),as=a=>(bt("data-v-5c01c4f4"),a=a(),xt(),a),Sh=as(()=>t("i",{class:"iconfont icon-close"},null,-1)),Dh=[Sh],Ph={class:"w-full"},Oh={class:"mt-2.5 mb-1 text-sm font-bold text-gray-1 leading-[22px]"},Eh={class:"border rounded border-brand-3 mb-2.5"},Fh={key:0,class:"w-full h-5 justify-start items-center gap-2.5 inline-flex align-c"},Vh=["onClick"],Mh=as(()=>t("div",{class:"w-full border-t-[1px] border-dashed border-gray-6"},null,-1)),jh={class:"w-[22%] flex items-center"},Lh={key:0,class:"mr-2.5 text-gray-1"},Bh={key:1},Ih={class:"mr-2.5 text-gray-1"},Uh={key:2,class:"text-yellow-1 w-[42px] px-1 py-0.5 rounded-sm bg-yellow-2 mr-2.5 min-w-50 text-xs"},Qh={class:"text-gray-1"},qh={class:"w-[22%]"},zh={key:0},Gh={class:"mr-5 text-gray-1"},Hh={class:"text-gray-3"},Wh={key:1,class:"text-yellow-1 w-[42px] px-1 py-0.5 rounded-sm bg-yellow-2 mr-2.5 min-w-50 text-xs"},Yh={class:"w-[28%] text-gray-1"},Kh={class:"mr-2.5"},Xh={class:"w-[28%] items-center"},Jh={class:"p-2.5 flex justify-between h-12 items-center border-t border-dashed border-brand-6"},Zh={class:"flex items-center text-sm"},ev={class:"text-gray-3 mr-3.5"},tv={class:"text-gray-1"},nv={class:"mr-1 text-gray-3"},av={class:"text-gray-1 cursor-pointer border-b-[1.5px] border-dashed tax-detail"},sv={class:"text-gray-1 mr-2.5"},ov={class:"text-red-1"},iv={class:"text-gray-1 mr-2.5"},lv={class:"text-red-1"},rv={class:"text-gray-1 mr-2.5"},cv={class:"text-red-1"},uv={class:"text-gray-1 mr-2.5"},dv={class:"text-red-1"},pv={class:"text-sm font-bold text-gray-1"},fv={class:"text-base text-red-1 font-bold mr-2.5"},mv={class:"text-sm font-bold text-gray-1"},gv={class:"text-base font-bold text-red-1"},kv={class:"mt-5 text-center crs-btn-dialog-ui"},yv=je({__name:"PreviewDialog",props:{previewInfo:{},isAuthToShowWindow:{type:Boolean}},emits:["update:modelValue"],setup(a){var _;const o=(_=navigator==null?void 0:navigator.userAgent)==null?void 0:_.toLowerCase(),u=Me(()=>o==null?void 0:o.includes("electron/")),h=(d,x,T)=>window.electronAPI.openTicketDetailWindow(d,x,T),f=d=>{const x=[];return d.forEach(T=>{const k=x.findIndex(R=>R.find(v=>v.tktTag===T.tktTag));k>-1?x[k].push(T):x.push([T])}),x},p=d=>d==="I",i=d=>{const x=d.trim()??"";return Bt[x]&&Bt[x].color||""};return(d,x)=>{const T=Ke,k=nt;return s(),ae(k,{title:d.$t("app.agentTicketRefund.preview"),class:"preview-dialog tc-refund-preview-dialog",width:"1040px","align-center":"true","close-on-click-modal":!1,"show-close":!1,onClose:x[2]||(x[2]=R=>d.$emit("update:modelValue",!1))},{default:l(()=>{var R;return[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:x[0]||(x[0]=v=>d.$emit("update:modelValue",!1))},Dh),t("div",Ph,[t("div",Oh,n(d.$t("app.refundForm.refundPassenger")),1),t("div",Eh,[c(Ah,{"preview-info":d.previewInfo,"is-auth-to-show-window":d.isAuthToShowWindow},null,8,["preview-info","is-auth-to-show-window"]),(s(!0),m(ye,null,Ne(f(((R=d.previewInfo.refundTicketOrder)==null?void 0:R.ticket.segment)??[]),(v,r)=>{var y;return s(),m("div",{key:r+"seg",class:"px-2.5 pt-2"},[Number(d.previewInfo.refundTicketOrder.conjunction)>1?(s(),m("div",Fh,[u.value&&d.isAuthToShowWindow?(s(),m("div",{key:0,class:"text-brand-2 text-xs font-bold leading-tight cursor-pointer w-[125px]",onClick:C=>{var $,P;return h(v[0].tktTag,(($=d.previewInfo.secondFactor)==null?void 0:$.secondFactorCode)??"",((P=d.previewInfo.secondFactor)==null?void 0:P.secondFactorValue)??"")}},n(e(ht)(v[0].tktTag??"")),9,Vh)):(s(),ae(mn,{key:1,"is-international":p(((y=d.previewInfo.refundTicketOrder)==null?void 0:y.ticket.tktType)??""),"is-cds-ticket":d.previewInfo.refundTicketOrder.ticket.cdsTicket??!1,"tkt-index":v[0].tktTag,"ticket-number":v[0].tktTag,"second-factor":d.previewInfo.secondFactor,"refund-class-type":"1"},null,8,["is-international","is-cds-ticket","tkt-index","ticket-number","second-factor"])),Mh])):Z("",!0),(s(!0),m(ye,null,Ne(v,(C,$)=>(s(),m("div",{key:$,class:"flex py-1"},[t("div",jh,[C.segmentType==="3"&&C.airline?(s(),m("div",Lh,n(C.airline),1)):Z("",!0),C.segmentType==="2"?(s(),m("div",Bh,[t("span",Ih,n(C.flightNo),1)])):(s(),m("div",Uh,n(C.segmentType==="3"?"OPEN":"ARNK"),1)),t("div",Qh,n(C.cabinCode),1)]),t("div",qh,[C.segmentType==="2"?(s(),m("div",zh,[t("span",Gh,n(C.departureDate),1),t("span",Hh,n(C.departureTime),1)])):(s(),m("div",Wh,n(C.segmentType==="3"?"OPEN":"ARNK"),1))]),t("div",Yh,[t("span",Kh,n(`${C.departureCode}-${C.arriveCode}`),1)]),t("div",Xh,[t("span",{class:Se(["font-bold mr-2.5",i(C.ticketStatus??"")])},n(C.ticketStatus),3)])]))),128))])}),128)),t("div",Jh,[t("div",Zh,[t("div",ev,[K(n(d.$t("app.agentTicketRefund.totalTicketAmount")),1),t("span",tv,n(d.previewInfo.refundTicketOrder.ticket.currency)+" "+n(d.previewInfo.refundTicketOrder.ticket.totalAmount),1)]),t("div",nv,[K(n(d.$t("app.agentTicketRefund.totalTaxAmount"))+" ",1),c(qn,{class:"!mt-0",ticket:d.previewInfo.refundTicketOrder.ticket,index:0},{"tax-deatil":l(()=>[t("span",av,n(d.previewInfo.refundTicketOrder.ticket.currency)+" "+n(d.previewInfo.refundTicketOrder.ticket.totalTaxs),1)]),_:1},8,["ticket"])])]),t("div",null,[t("span",sv,[K(n(d.$t("app.agentTicketRefund.commission"))+" ",1),t("span",ov,n(d.previewInfo.refundCompute.amount.commision?`${d.previewInfo.refundTicketOrder.ticket.currency} ${d.previewInfo.refundCompute.amount.commision}`:"--"),1)]),t("span",iv,[K(n(d.$t("app.agentTicketRefund.commissionRate"))+" ",1),t("span",lv,n(d.previewInfo.refundCompute.amount.commisionRate?`${d.previewInfo.refundCompute.amount.commisionRate}%`:"--"),1)]),t("span",rv,[K(n(d.$t("app.agentTicketRefund.charge"))+" ",1),t("span",cv,n(d.previewInfo.refundTicketOrder.ticket.currency)+" "+n(d.previewInfo.refundCompute.amount.otherDeduction),1)]),t("span",uv,[K(n(d.$t("app.agentTicketRefund.totalRefund"))+" ",1),t("span",dv,n(d.previewInfo.refundTicketOrder.ticket.currency)+" "+n(d.previewInfo.refundCompute.amount.netRefund),1)])])])]),t("span",pv,n(d.$t("app.agentTicketRefund.charge")),1),t("span",fv,n(d.previewInfo.refundTicketOrder.ticket.currency)+" "+n(d.previewInfo.refundCompute.amount.otherDeduction),1),t("span",mv,n(d.$t("app.agentTicketRefund.totalRefund")),1),t("span",gv,n(d.previewInfo.refundTicketOrder.ticket.currency)+" "+n(d.previewInfo.refundCompute.amount.netRefund),1),t("div",kv,[c(T,{onClick:x[1]||(x[1]=v=>d.$emit("update:modelValue",!1))},{default:l(()=>[K(n(d.$t("app.agentTicketRefund.close")),1)]),_:1})])])]}),_:1},8,["title"])}}});const hv=at(yv,[["__scopeId","data-v-5c01c4f4"]]),vv=(a,o)=>{const u=()=>{o("update:modelValue",!1)};return{queryTkt:()=>{o("toQueryTicket"),u()},closeDialog:u,queryTSL:()=>{wn.setLink("/v2/crs/salesDaily"),u()},checkingReturnTkt:()=>{o("openRefundDialog",a.invoiceNumber,a.refundPrinterNo,a.ticketManagementOrganizationCode),u()}}},_v=t("i",{class:"iconfont icon-close"},null,-1),bv=[_v],xv={class:"flex text-[18px] items-center"},Tv={class:"text-[var(--bkc-color-gray-1)] leading-6"},$v={class:"text-[14px] text-[var(--bkc-color-gray-1)]"},Nv={class:"flex items-center pl-[30px]"},Rv={class:"flex justify-end"},Cv=je({__name:"SupplementSuccess",props:{invoiceNumber:{},refundPrinterNo:{},ticketManagementOrganizationCode:{}},emits:["openRefundDialog","toQueryTicket","update:modelValue"],setup(a,{emit:o}){const u=o,h=a,{closeDialog:f,queryTkt:p,queryTSL:i,checkingReturnTkt:_}=vv(h,u);return(d,x)=>{const T=Je,k=Ke,R=nt;return s(),ae(R,{"close-on-press-escape":!1,"close-on-click-modal":!1,width:"500px","show-close":!1,class:"supplement-success","align-center":"",onClose:e(f)},{header:l(()=>[t("div",xv,[c(T,{size:"36px",class:"success-color mr-2.5"},{default:l(()=>[c(e(Gn))]),_:1}),t("span",Tv,n(d.$t("app.refundForm.supplementaryRefundSuccess")),1)])]),default:l(()=>[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:x[0]||(x[0]=(...v)=>e(f)&&e(f)(...v))},bv),t("div",$v,[t("div",Nv,[t("span",null,n(d.$t("app.refundForm.invoiceNumber")),1),t("span",{class:"text-[var(--bkc-el-color-primary)] cursor-pointer mr-2.5",onClick:x[1]||(x[1]=(...v)=>e(_)&&e(_)(...v))},n(h.invoiceNumber),1),c(k,{onClick:e(i)},{default:l(()=>[K(n(d.$t("app.refundForm.checkTslSettlement")),1)]),_:1},8,["onClick"])]),t("div",Rv,[c(k,{type:"primary",onClick:x[2]||(x[2]=v=>e(p)())},{default:l(()=>[K(n(d.$t("app.refundForm.confirmBtn")),1)]),_:1})])])]),_:1},8,["onClose"])}}});const wv=(a,o)=>{const{t:u}=We(),h=Ae(!1),f=H(),p=H({}),i=H([]),_=H([]),d=()=>{o("update:modelValue",!1)},x=(D,b,N)=>{const ne=D.split("");return ne[b]=N,ne.join("")},T=D=>{const b={};D.forEach(ie=>{b[ie.conjunctionIndex]?b[ie.conjunctionIndex].push(ie):b[ie.conjunctionIndex]=[ie]});const N=["0000","0000","0000","0000"];let ne=0;for(const ie in b)b[ie].forEach(U=>{N[ne]=x(N[ne],Number(U.etSegIndex)-1,U.etSegIndex)}),ne++;return N},k=D=>({refund:D.etTagNew?"Y":"N",currency:D.currency,payMethod:D.payType,remark:D.remarkInfo??"",creditCard:D.creditCard?D.creditCard:"",couponNos:T(D.checkedSeg??[]),name:Ft.encode(D.name.trim()),airlineCode:D.airline}),R=D=>{const b=[];return D.forEach(N=>{if(N.taxAmount&&N.taxCode){const ne={taxCode:N.taxCode,taxAmount:Number(N.taxAmount)};b.push(ne)}}),b},v=D=>({commission:D.commision&&Number(D.commision)>0?D.commision.toString():"0",commissionRate:D.commisionRate.toString()??"",grossRefund:D.totalAmount.toString(),deduction:D.otherDeduction.toString(),netRefund:D.netRefund.toString(),taxInfos:R(D.taxs)}),r=()=>a.refundTicketData.ticketType==="I"&&a.refundTicketData.ticketNo!==a.refundTicketData.ticketNoEnd?`${a.refundTicketData.ticketNo}-${a.refundTicketData.ticketNoEnd}`:a.refundTicketData.ticketNo,y=D=>({ticketNo:r(),ticketType:D.tktType,printerNo:D.prntNo,ticketManagementOrganizationCode:D.ticketManagementOrganizationCode??"",refundFormPassengerItem:k(D),refundFormPriceItem:v(D)}),C=async D=>{var ne,ie,U;h.value=!0;const b=y(D);let N;try{N=(await To(b,"091T0108")).data.value;let A=((ne=N==null?void 0:N.data)==null?void 0:ne.refundNumber)??"";D.ticketManagementOrganizationCode!=="ARL"&&(A=(ie=N==null?void 0:N.data)!=null&&ie.refundNumber?(U=N==null?void 0:N.data)==null?void 0:U.refundNumber.substring(3):""),o("openSupplementSuccessDialog",A,D.prntNo??"",D.ticketManagementOrganizationCode??"")}finally{h.value=!1}},$=async()=>{var N,ne;const D=(N=f.value)==null?void 0:N.getFormDate();if((D.checkedSeg??[]).length<1){mt({type:"warning",message:u("app.agentTicketRefund.selSeg")});return}await((ne=f.value)==null?void 0:ne.validate())&&C(D)},P=()=>{var D;(D=f.value)==null||D.resetForm()},M=D=>{const b=[];for(const N in D)D[N].forEach(ne=>{if(ne.ticketStatus==="REFUNDED"){const ie={etSegIndex:ne.etSegIndex,deptCity:ne.deptCity,arrivalCity:ne.arrivalCity,ticketStatus:ne.ticketStatus,select:ne.select,etNo:ne.etNo,conjunctionIndex:ne.conjunctionIndex};b.push(ie)}});return b},X=(D,b)=>Nt(D)?b.toString().endsWith(".00")?b.toString().slice(0,-3):b:b.toFixed(2),ue=(D,b)=>{let N=[];return N=D.map(ne=>({taxCode:ne.taxCode,taxAmount:X(b,Number(ne.taxAmount))})),N.length<10?N.concat(new Array(10-N.length).fill({taxCode:"",taxAmount:""})).map(ne=>({...ne})):N},E=D=>{let b=D;return(D.startsWith("CC")||D.startsWith("TC"))&&(b="TC"),b},j=D=>{_.value=ue(D.taxInfos,D.currency),p.value={refundNo:"",refundType:u("app.refundForm.manualRefundType"),iata:D.iataNo,agent:D.agent,office:D.office,refundDate:et(new Date).format("DDMMMYY/HHmm").toUpperCase(),volunteer:"supplement",createUser:D.operator,prntNo:a.printerNo,name:D.passengerName,psgType:D.passengerType,remark:D.remark?D.remark.substring(2):"",remarkCode:D.remark?D.remark.substring(0,2):"",creditCard:D.creditCard,conjunction:D.conjunction,airline:D.airlineCode,tktType:D.ticketType,ticketManagementOrganizationCode:D.ticketManagementOrganizationCode,payType:E(D.payMethod),ticketNo:D.ticketNo!==D.ticketNoEnd&&D.ticketType==="I"?`${D.ticketNo}-${D.ticketNoEnd.slice(-2)}`:D.ticketNo,ticketNoView:D.ticketNoView,totalAmount:X(D.currency,Number(D.grossRefund)),commision:X(D.currency,Number(D.commission)),commisionRate:Number(D.commissionRate)>0?X(D.currency,Number(D.commissionRate)):"",otherDeduction:X(D.currency,Number(D.deduction)),netRefund:X(D.currency,Number(D.netRefund)),totalTaxs:X(D.currency,Number(D.totalTaxs)),taxs:ue(D.taxInfos,D.currency),rate:D.commission>0||D.commissionRate<=0?"0":"1",currency:D.currency,etTagNew:!1,checkedSeg:M(a.refundTicketData.segmentInfos),international:"",couponNos:[]}},oe=D=>{i.value=[];for(const b in D){const N=[];D[b].forEach(ne=>{const ie={etSegIndex:ne.etSegIndex,deptCity:ne.deptCity,arrivalCity:ne.arrivalCity,ticketStatus:ne.ticketStatus,select:ne.select,etNo:ne.etNo,conjunctionIndex:ne.conjunctionIndex};N.push(ie)}),i.value.push(N)}};return st(()=>{j(a.refundTicketData),oe(a.refundTicketData.segmentInfos)}),{taxsHistory:_,supplementRefundData:p,supplementSegmentData:i,refundFormRef:f,fullscreenLoading:h,handleCommit:$,closeSupplementDialog:d,initFormData:P}},Av=t("i",{class:"iconfont icon-close"},null,-1),Sv=[Av],Dv={class:"mt-[10px] pt-[10px] flex justify-center border-[var(--bkc-color-gray-6)] crs-btn-dialog-ui"},Pv=je({__name:"SupplementRefund",props:{printerNo:{},refundTicketData:{}},emits:["update:modelValue","openSupplementSuccessDialog"],setup(a,{emit:o}){const u=a,h=o,{taxsHistory:f,supplementRefundData:p,supplementSegmentData:i,refundFormRef:_,fullscreenLoading:d,closeSupplementDialog:x,handleCommit:T,initFormData:k}=wv(u,h);return(R,v)=>{const r=Ke,y=nt,C=kt;return s(),ae(y,{title:R.$t("app.refundForm.supplementaryRefundInfo"),"show-close":!1,width:"1040px",class:"refund-form-dialog","close-on-click-modal":!1,onClose:e(x)},{default:l(()=>[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:v[0]||(v[0]=(...$)=>e(x)&&e(x)(...$))},Sv),c(Io,{ref_key:"refundFormRef",ref:_,data:e(p),"taxs-history":e(f),"refund-tickets":e(i),"is-supplement-refund":!0},null,8,["data","taxs-history","refund-tickets"]),t("div",Dv,[Qe((s(),ae(r,{type:"primary",onClick:e(T)},{default:l(()=>[K(n(R.$t("app.agentTicketRefund.submit")),1)]),_:1},8,["onClick"])),[[C,e(d),void 0,{fullscreen:!0,lock:!0}]]),c(r,{onClick:e(k)},{default:l(()=>[K(n(R.$t("app.agentTicketRefund.reset")),1)]),_:1},8,["onClick"]),c(r,{onClick:e(x)},{default:l(()=>[K(n(R.$t("app.agentTicketRefund.cancel")),1)]),_:1},8,["onClick"])])]),_:1},8,["title","onClose"])}}}),Ov=(a,o)=>{const u=H(""),h=H(!1),f=H(!1),p=()=>{const x=[];return(a.flightList??[]).forEach(T=>{T.airSeg.forEach(k=>{k.isSelected&&x.push(k)})}),x},i=()=>{o("getChosenSegment",p()),o("queryPassenger")},_=()=>{const x=a.flightList.map(T=>T.etNumber);o("queryRtkt",x)},d=x=>x.some(T=>T.flightNo==="OPEN"&&!["VOID","REFUNDED","USED/FLOWN","EXCHANGED"].includes(T.ticketStatus));return st(()=>{var x,T;if((a.flightList??[]).length>1){const k=a.flightList.length-1,R=a.flightList[k].etNumber.slice(-2);u.value=`${a.flightList[0].etNumber}-${R}`}else u.value=((T=(x=a.flightList)==null?void 0:x[0])==null?void 0:T.etNumber)??"";h.value=a.flightList.some(k=>k.governmentPurchase),f.value=a.flightList.some(k=>d(k.airSeg))}),{ticketNo:u,isGovernmentPurchase:h,isShowRtktBtn:f,changeSelected:i,handleQueryRtkt:_}},Ev={class:"w-[1012px] pb-2 rounded border border-brand-3 flex-col justify-start items-start gap-2 inline-flex change-flight-info"},Fv={class:"self-stretch p-2.5 bg-brand-4 rounded-tl rounded-tr border border-brand-3 justify-between items-center inline-flex"},Vv={class:"justify-start items-center flex"},Mv={class:"justify-start items-start gap-2 flex mr-2.5"},jv={class:"text-brand-2 text-base font-bold leading-normal"},Lv={key:0,class:"inline-flex h-[20px] px-[4px] py-[0px] rounded-[2px] text-yellow-1 bg-yellow-2 items-center text-[12px] mr-2.5"},Bv={class:"justify-start items-center gap-2.5 flex ml-2.5"},Iv={class:"justify-start items-center gap-1 flex"},Uv={key:0,class:"iconfont icon-inf mr-[4px] text-gray-4"},Qv={key:1,class:"iconfont icon-user-fill mr-[4px] text-gray-4"},qv={class:"text-gray-1 text-base font-bold leading-normal"},zv={class:"px-1 bg-gray-7 rounded-sm justify-center items-center gap-2.5 flex h-5"},Gv={class:"text-center text-gray-3 text-xs font-normal leading-tight"},Hv={key:0,class:"self-stretch px-2.5 justify-start items-center gap-2.5 inline-flex"},Wv={class:"text-brand-2 text-xs font-bold leading-tight"},Yv=t("div",{class:"grow shrink basis-0 h-[0px] border border-dotted border-gray-6"},null,-1),Kv={class:"self-stretch px-2.5 flex-col justify-start items-start flex"},Xv={class:"w-[200px] self-stretch justify-start items-center gap-5 flex"},Jv={class:"justify-start items-center gap-1 flex"},Zv={class:"justify-start items-center gap-2.5 flex"},e_={class:"w-[110px] justify-start items-center flex"},t_={key:0,class:"text-gray-1 text-sm font-normal leading-snug mr-2.5"},n_={class:"text-gray-1 text-sm font-normal leading-snug"},a_={class:"w-[172px] justify-start items-center flex"},s_={class:"text-gray-1 text-sm font-normal leading-snug mr-2.5"},o_={class:"text-gray-3 text-sm font-normal leading-snug"},i_={key:1,class:"text-yellow-1 px-1.5 py-0.5 rounded-sm bg-yellow-2 mr-2.5 min-w-50 text-sm"},l_={class:"w-[280px] self-stretch justify-start items-center gap-2.5 flex"},r_={class:"w-24 text-gray-1 text-sm font-normal leading-snug"},c_={key:0,class:"px-1 bg-yellow-2 rounded-sm justify-start items-start gap-2.5 flex"},u_={class:"text-center text-yellow-1 text-xs font-normal leading-tight"},d_={key:1,class:"w-7 h-5 relative"},p_={class:"justify-start items-start gap-0.5 flex"},f_={key:0,class:"text-gray-1 text-sm font-normal leading-snug"},m_={key:1,class:"text-gray-1 text-sm font-normal leading-snug"},g_={key:2,class:"text-brand-2 text-sm font-normal leading-snug"},k_={class:"w-[300px] self-stretch px-1 justify-start items-center gap-3 flex"},y_=je({__name:"FlightInfo",props:{flightList:{}},emits:["queryPassenger","getChosenSegment","queryRtkt"],setup(a,{emit:o}){const u=o,h=a,{ticketNo:f,isGovernmentPurchase:p,isShowRtktBtn:i,changeSelected:_,handleQueryRtkt:d}=Ov(h,u);return(x,T)=>{const k=Ke,R=an;return s(),m("div",Ev,[t("div",Fv,[t("div",Vv,[t("div",Mv,[t("div",jv,n(e(f)),1)]),e(p)?(s(),m("div",Lv,"GP")):Z("",!0),t("div",Bv,[t("div",Iv,[x.flightList[0].specialPassengerType==="INF"?(s(),m("em",Uv)):(s(),m("em",Qv)),t("div",qv,n(x.flightList[0].passengerNameSuffix),1),t("div",zv,[t("div",Gv,n(e(Rt)(x.flightList[0].specialPassengerType)),1)])])])]),e(i)?(s(),ae(k,{key:0,"data-gid":"091V0801",onClick:T[0]||(T[0]=v=>e(d)())},{default:l(()=>[K("RTKT")]),_:1})):Z("",!0)]),(s(!0),m(ye,null,Ne(x.flightList,(v,r)=>(s(),m(ye,{key:r},[x.flightList.length>1?(s(),m("div",Hv,[t("div",Wv,n(v.etNumber),1),Yv])):Z("",!0),t("div",Kv,[(s(!0),m(ye,null,Ne(v.airSeg,y=>{var C,$;return s(),m("div",{key:y.segmentIndex,class:"self-stretch h-9 rounded justify-between items-center inline-flex"},[t("div",Xv,[t("div",Jv,[t("div",Zv,[c(R,{modelValue:y.isSelected,"onUpdate:modelValue":P=>y.isSelected=P,value:!0,disabled:["VOID","REFUNDED","USED/FLOWN","EXCHANGED"].includes(y.ticketStatus)||y.flightNo==="ARNK",size:"large",onChange:e(_)},null,8,["modelValue","onUpdate:modelValue","disabled","onChange"])])]),t("div",e_,[y.flightNo==="OPEN"&&y.airline?(s(),m("div",t_,n(y.airline),1)):Z("",!0),t("div",{class:Se(["text-gray-1 text-sm font-normal leading-snug mr-2.5",["OPEN","ARNK"].includes(y.flightNo)?"text-yellow-1 rounded-sm bg-yellow-2 px-1.5 py-0.5":""])},n(y.flightNo),3),t("div",n_,n(y.cabin),1)])]),t("div",a_,[y.depTime?(s(),m(ye,{key:0},[t("div",s_,n(y.departureDate?e(et)(y.departureDate).format("YYYY-MM-DD"):""),1),t("div",o_,n(y.depTime?e(et)(y.depTime).format("HH:mm"):""),1)],64)):(s(),m("span",i_,n(y.flightNo==="OPEN"?"OPEN":"ARNK"),1))]),t("div",l_,[t("div",r_,n(`${y.depAirportCode}-${y.arrAirportCode}`),1),y.changeReason?(s(),m("div",c_,[t("div",u_,n(y.changeReason),1)])):(s(),m("div",d_)),t("div",p_,[y.pnrNo?(s(),m("div",f_,n(y.pnrNo),1)):Z("",!0),y.pnrNo?(s(),m("div",m_,"/")):Z("",!0),y.crsPnrNo?(s(),m("div",g_,n(y.crsPnrNo),1)):Z("",!0)])]),t("div",k_,[t("div",{class:Se([e(Bt)[(C=y.ticketStatus)==null?void 0:C.trim()]?e(Bt)[($=y.ticketStatus)==null?void 0:$.trim()].color:"","text-sm font-bold leading-snug"])},n(y.ticketStatus),3)])])}),128))])],64))),128))])}}}),h_=(a,o)=>{const{t:u}=We(),h=Ms(),{orderInfo:f}=js(h),p=Cn(),i=H(!1),_=H(),d=H([]),x=H(!0),T=Ae({}),k=Ae([]),R=H([]),v=H("voluntary"),r=H("new"),y=H(!1),C=H(!1),$=H(!1),P=H(!1),M=H(!1),X=H(),ue=H(""),E=H(""),j=H(0),oe=()=>R.value.every(I=>!I.crsPnrNo&&I.flightNo!=="ANRK"),D=Me(()=>{const I=R.value.length===0,_e=r.value==="exist"&&!b.pnrNo,$e=r.value==="exist"&&b.pnrNo!==E.value&&oe();return I||_e||$e}),b=tt({pnrNo:""}),N={},ne=tt({flight:[],pnrNo:"",issueStatus:"",passengers:[],remarkArea:{remarks:[],ckins:[],clids:[],remarkOsis:[],others:[],ssrContents:[]},originalLineContents:[],historyLineContents:[],basicArea:{},pnrCanceled:!1,international:!1,key:"",rightComponentName:"",government:!1}),ie={pnrNo:[{required:!0,message:u("app.pnrManagement.validate.required")},{pattern:hn,message:u("app.change.inputCorrectPnr"),trigger:["blur"]}]},U=()=>({ticketNo:a.tktNo,detrType:"GET_ARR_TIME_TICKET",secondFactor:a.factor}),A=async I=>{try{i.value=!0;const{data:_e}=await Fa(U(),I);d.value=_e.value??[]}finally{i.value=!1}},le=async()=>{if(k.value=[],d.value.forEach(I=>{I.airSeg.forEach(_e=>{_e.isSelected&&_e.crsPnrNo&&!k.value.includes(_e.crsPnrNo)&&k.value.push(_e.crsPnrNo)})}),k.value.length>1||k.value.length===0){T.value.passengers=[];return}if(!(T.value.passengers&&T.value.passengers.length))try{i.value=!0;const I={pnrNo:k.value[0]},{data:_e}=await xa(I);T.value=(_e==null?void 0:_e.value)??{passengers:[],canceled:!1,nonExistent:!1,basicArea:{}}}catch{T.value={passengers:[],canceled:!1,nonExistent:!1,basicArea:{}}}finally{i.value=!1}},he=I=>{b.pnrNo=ue.value,x.value=!0,I==="involuntary"&&(r.value="exist"),$.value=I==="involuntary",P.value=M.value=r.value==="exist"&&oe()},ce=I=>{b.pnrNo=ue.value,x.value=!0,P.value=M.value=I==="exist"&&oe()},L=I=>R.value=I,se=I=>{var _e;return((_e=N[I])==null?void 0:_e.airportCnName)??""},Q=async()=>{const I=await Aa("searchLocalData");(I?JSON.parse(I.localData):[]).forEach($e=>{N[$e.airportCode]=$e})},S=(I,_e)=>I==="ARNK"?I:I==="VOID"?"ARNK":(I??"").includes(_e)?I.slice(2):I,G=I=>({airCN:"",airCode:I.airline,airService:[],alliance:"",aviationDepartmentGeneral:{},flightNo:S(I.flightNo,I.airline),isShared:"",planeType:"",ocFlightNumber:"",ocAirline:I.operationAirline}),J=I=>({cabinName:I.cabin,state:""}),z=(I,_e)=>({tktNum:_e,airlines:G(I),arrDays:"",arrivalAirportCN:se(I.arrAirportCode),arrivalAirportCode:I.arrAirportCode,arrivalDate:I.arrTime?et(I.arrTime).format("YYYY-MM-DD HH:mm:ss"):"",arrivalTerminal:I.arrAirportTerminal,arrivalTime:et(I.arrTime).isValid()?et(I.arrTime).format("HH:mm"):"",asr:"",cabins:[J(I)],connectLevel:"",departureAirportCN:se(I.depAirportCode),departureAirportCode:I.depAirportCode,departureDate:I.depTime?et(I.depTime).format("YYYY-MM-DD HH:mm:ss"):"",departureTerminal:I.depAirportTerminal,departureTime:et(I.depTime).isValid()?et(I.depTime).format("HH:mm"):"",flightDistance:"",flightTime:"",stopCity:"",marriedSegmentNumber:"",commonMeal:"",ticketStatus:I.ticketStatus,pnrNo:I.pnrNo,segmentType:"0",crsPnrNo:I.crsPnrNo}),w=(I,_e)=>_e==="arrive"?{code:I.arrAirportCode,name:se(I.arrAirportCode)}:{code:I.depAirportCode,name:se(I.depAirportCode)},g=I=>{const _e=[];return d.value.forEach($e=>{$e.airSeg.forEach(re=>{const Ce={arriveCity:w(re,"arrive"),departureCity:w(re,"departure"),date:{departureDate:re.departureDate},segments:[z(re,$e.etNumber)],tktType:$e.ticketTypeCode??"",openFlag:I?re.flightNo==="OPEN":!re.crsPnrNo&&re.flightNo!=="ANRK",isSelected:!!re.isSelected};Ce.key=Ls(Ce),_e.push(Ce)})}),_e},B=I=>{let _e="";return(I??"").includes("NI")?_e=I?`${I}_I`:"":_e=I?`PP_${I}`:"",_e},F=(I,_e)=>{const $e={docsName:(I==null?void 0:I.docsName)??"",holder:(I==null?void 0:I.holder)??"",documentType:B(I==null?void 0:I.documentType),ssrType:(I==null?void 0:I.ssrType)??"",idCardNumber:(I==null?void 0:I.idCardNumber)??"",visaIssueCountry:(I==null?void 0:I.visaIssueCountry)??"CN",passengerNationality:(I==null?void 0:I.passengerNationality)??"CN",visaExpiryDate:I==null?void 0:I.visaExpiryDate,birthday:(I==null?void 0:I.birthday)??"",gender:(I==null?void 0:I.gender)??"M"};return _e==="PP"?$e:{...$e,docsName:(I==null?void 0:I.docsName)??""}},de=(I,_e)=>`${et(new Date).format("YYYYMMDDHHmmssSSS")}-${I}-${_e}`,ve=(I,_e,$e)=>{var re,Ce;return{id:de(_e,$e),birthday:I.birthday??"",chineseName:I.chineseName??"",fullName:I.fullName,nameSuffix:I.nameSuffix??"",lastName:I.lastName??"",firstName:I.firstName??"",segments:[],passengerId:I.passengerId,document:F(I.document,""),documentPP:F(I.documentPP,"PP"),niForDocs:((re=I.document)==null?void 0:re.niForDocs)??"",ppForDocs:((Ce=I.documentPP)==null?void 0:Ce.ppForDocs)??"",docaInfoR:I.docaInfoR??{},docaInfoD:I.docaInfoD??{}}},O=(I,_e,$e)=>{var re,Ce;return{id:de(_e,I.passengerType??""),airlineType:$e?"I":"D",foreign:!1,chineseName:I.chineseName??"",birthday:I.birthday??"",passengerType:I.passengerType??"",specialPassengerType:I.specialPassengerType??"",fullName:I.passengerNameInPnr??"",passengerNameInPnr:I.passengerNameInPnr??"",firstName:I.firstName??"",lastName:I.lastName??"",unMinor:!!I.unMinor,unMinorAge:I.unMinor?I.unMinorAge??0:0,nameSuffix:I.nameSuffix??"",vipType:"",vipText:"",osiCtcm:(I==null?void 0:I.osiCtcm)??"",ssrCtcm:(I==null?void 0:I.ssrCtcm)??"",passengerId:I.passengerId,segments:[],document:F(I.document??{},""),documentPP:F(I.documentPP??{},"PP"),niForDocs:((re=I.document)==null?void 0:re.niForDocs)??"",ppForDocs:((Ce=I.documentPP)==null?void 0:Ce.ppForDocs)??"",infantDetail:I.infantDetail?ve(I.infantDetail,_e,I.passengerType??""):null,childSsrTypeTextInPnr:[],docaInfoR:I.docaInfoR??{},docaInfoD:I.docaInfoD??{},docsName:I.chineseName?"":I.fullName??""}},fe=(I,_e)=>I.map(($e,re)=>O($e,re,_e)),Y=()=>{var I,_e,$e,re,Ce,Fe;return{changeModel:r.value,changeType:v.value,pnrNo:b.pnrNo,ticketNumberJoin:(($e=(_e=(I=_.value)==null?void 0:I.choosePassengers)==null?void 0:_e[0])==null?void 0:$e.ticketNumberJoin)??"",ticketNumbersForTN:((Fe=(Ce=(re=_.value)==null?void 0:re.choosePassengers)==null?void 0:Ce[0])==null?void 0:Fe.ticketNumbersForTN)??[]}},Te=()=>({orderInfo:ne,...Y()}),V=()=>{var I,_e,$e,re,Ce,Fe,W,Ye;return{cts:((_e=(I=T.value)==null?void 0:I.basicArea)==null?void 0:_e.cts)??[],contact:((re=($e=T.value)==null?void 0:$e.basicArea)==null?void 0:re.contact)??[],contactorEmail:((Fe=(Ce=T.value)==null?void 0:Ce.basicArea)==null?void 0:Fe.contactorEmail)??"",responsibilityGroup:"",issueLimitCrs:"",airlinePnr:"",issueLimitIcs:"",officeName:"",officePhone:"",officeAddress:"",airline:"",contactPhones:((Ye=(W=T.value)==null?void 0:W.basicArea)==null?void 0:Ye.contactPhones)??[],authorizes:[],firstDepartureTime:"",issueWarn:!1,issueAbnormal:!1,issueLimitOffice:""}},ge=async()=>{var re;ne.basicArea=await V();const I=await g();ne.flight=I,ne.government=d.value.some(Ce=>Ce.governmentPurchase);const _e=await _a(I.filter(Ce=>Ce.isSelected));_a(I.filter(Ce=>Ce.isSelected));const $e=await fe(((re=_.value)==null?void 0:re.choosePassengers)??[],_e);ne.passengers=$e,ne.changeModelInOrder={...Y()}},me=()=>{var I;return(((I=_.value)==null?void 0:I.choosePassengers)??[]).map(_e=>({paxId:_e.passengerId,unMinor:!!_e.unMinor,unMinorAge:_e.unMinor?_e.unMinorAge??0:0,fullName:_e.passengerNameInPnr}))},Re=()=>{if(f.value.size<1)return!1;const I=Array.from(f.value.keys());return I==null?void 0:I.find($e=>{var re,Ce;return(Ce=(re=f.value.get($e))==null?void 0:re.specialContent)==null?void 0:Ce.includes(u("app.basic.occupy"))})},Oe=async()=>{var I;try{if(i.value=!0,r.value==="exist")(I=X.value)==null||I.validate(async _e=>{var re;if(!_e)return;await ge();let $e=!0;T.value.passengers.length>0&&T.value.passengers.length!==(((re=_.value)==null?void 0:re.choosePassengers)??[]).length&&await Xe.confirm(u("app.change.splitPassengerTips"),{icon:Le("em",{class:"iconfont icon-info-circle-line"}),confirmButtonText:u("app.intlPassengerForm.del.confirm"),cancelButtonText:u("app.intlPassengerForm.del.cancel"),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",showClose:!1}).then(async()=>{var Ye,lt;const Ce={travellers:me(),count:0,orderId:"",passengerRecordLocator:b.pnrNo},Fe=Pe("091V0802"),W=await Jn(Ce,Fe);b.pnrNo=((lt=(Ye=W.data.value)==null?void 0:Ye.splitedOrder)==null?void 0:lt.passengerRecordLocator)??"",$e=!0}).catch(()=>{$e=!1}),$e&&(await p.push({name:"PnrManagement",query:{pnrNo:b.pnrNo,time:et().unix(),type:v.value}}),await h.setCurrentRebookInfo(Te()),await o("update:modelValue",!1))});else{await ge(),Reflect.set(ne,"rebookInfos",ft(Te())),await h.setCurrentRebookInfo(Te());const _e=ft(ne),$e=["VOID","REFUNDED","USED/FLOWN","EXCHANGED"];_e.flight=(g(!0)??[]).filter(Ce=>{var Fe,W,Ye;return((Fe=Ce==null?void 0:Ce.segments)==null?void 0:Fe[0].airlines.flightNo)!=="ARNK"&&!$e.includes(((Ye=(W=Ce==null?void 0:Ce.segments)==null?void 0:W[0])==null?void 0:Ye.ticketStatus)??"")}),Re()||await h.pushOrderInfo("2",u("app.basic.ungeneted"),_e),await p.push({name:"PnrManagement",query:{time:et().unix(),type:v.value}}),await o("update:modelValue",!1)}}finally{i.value=!1}},we=(I,_e)=>I.some($e=>_.value.isMatchMainPassenger($e,_e)),Ee=()=>{var I;(I=X.value)==null||I.validate(async _e=>{var $e,re,Ce;if(_e)try{i.value=!0;const Fe={pnrNo:b.pnrNo};_.value.selectOnlyMainPassenger();const{data:W}=await xa(Fe);if(x.value=we((($e=W==null?void 0:W.value)==null?void 0:$e.passengers)??[],d.value),!x.value||(T.value=(W==null?void 0:W.value)??{passengers:[],canceled:!1,nonExistent:!1,basicArea:{}},(re=W==null?void 0:W.value)!=null&&re.canceled||(Ce=W==null?void 0:W.value)!=null&&Ce.nonExistent))return;E.value=b.pnrNo}catch{T.value={passengers:[],canceled:!1,nonExistent:!1,basicArea:{}},x.value=!1}finally{i.value=!1}})};wt(()=>R.value,()=>{if(j.value===R.value.length)return;E.value="";const I=R.value.every($e=>$e.changeReason.trim()==="IRR");I||(v.value="voluntary"),y.value=!I,$.value=v.value==="involuntary",P.value=oe(),M.value=oe()&&r.value==="exist";const _e=k.value.length<=1;C.value=!_e,_e||(y.value=!0,$.value=!1,v.value="voluntary",r.value="new"),ue.value=k.value[0],b.pnrNo=k.value[0],j.value=R.value.length},{deep:!0});const Be=(I,_e)=>{var re;const $e=(re=I.passenger)==null?void 0:re.segments;d.value.forEach(Ce=>{Ce.etNumber===_e&&$e.length===Ce.airSeg.length&&Ce.airSeg.forEach((Fe,W)=>{var Ye,lt,be,vt,pt;Fe.flightNo==="OPEN"&&$e[W].departureDateTime.replace(/\s/g,"")&&(Fe.departureDate=((Ye=$e==null?void 0:$e[W])==null?void 0:Ye.departureDateTime)??"",Fe.depTime=((lt=$e==null?void 0:$e[W])==null?void 0:lt.departureDateTime)??"",Fe.arrTime=((be=$e==null?void 0:$e[W])==null?void 0:be.arrivalDateTime)??"",Fe.flightNo=((vt=$e==null?void 0:$e[W])==null?void 0:vt.flightNo)??"",Fe.cabin=((pt=$e==null?void 0:$e[W])==null?void 0:pt.cabin)??"")})})},Ze=async I=>{try{const _e=Pe("091V0801");i.value=!0,I.forEach(async $e=>{var Ce;const re=((Ce=(await Ma($e,_e)).data.value)==null?void 0:Ce.data)??{};Be(re,$e)}),await fn()}finally{i.value=!1}};return st(async()=>{const I=Pe("091T0102");await A(I),await Q()}),{loading:i,flightList:d,changeType:v,changeModel:r,disableInvoluntarily:y,disableExist:C,disableNew:$,changeInfoForm:b,FORM_RULES:ie,changeModelFormRef:X,isAllowChangePnr:P,pnrNoList:k,passengerResult:T,checkSegmentList:R,passengerRef:_,orderInfoTemplate:ne,matchMainPassenger:x,isShowAccompanyPassenger:M,isDisableConfirmButton:D,searchAccompanyPassenger:Ee,buildFlightData:g,getChosenSegment:L,queryPassenger:le,handleChangeType:he,handleChangeModel:ce,confirmChange:Oe,queryRtkt:Ze}},v_=a=>{const o=H([]),u=Ae([]),h=k=>{const R=k.replace(/\s+/g,""),v=R.indexOf("("),r=R.indexOf("*");if(v>-1&&r>-1){const y=Math.min(v,r);return(R==null?void 0:R.substring(0,y))??""}else{if(v===-1&&r>-1)return(R==null?void 0:R.substring(0,r))??"";if(v>-1&&r===-1)return(R==null?void 0:R.substring(0,v))??""}return R},f=(k,R)=>{var $;const v=(R??[]).map(P=>{var M;return(M=P.etNumber)==null?void 0:M.replace(/-/g,"")}),y=k.ticketNumbersForTN.map(P=>P==null?void 0:P.replace(/-/g,"")).some(P=>v.includes(P)),C=h(k.passengerNameInPnr)===h((($=R[0])==null?void 0:$.passengerNameSuffix)??"");return y||C},p=()=>{u.value=[],o.value=[],a.passengerList.forEach(k=>{f(k,a.flightList)&&u.value.unshift(k)})},i=()=>{u.value=[],a.passengerList.forEach(k=>{f(k,a.flightList)&&u.value.unshift(k)}),o.value.forEach(k=>{f(a.passengerList[k],a.flightList)||u.value.push(a.passengerList[k])})},_=()=>{var k,R;if((a.flightList??[]).length>1){const v=a.flightList.length-1,r=a.flightList[v].etNumber.slice(-2);return`${a.flightList[0].etNumber}-${r}`}else return((R=(k=a.flightList)==null?void 0:k[0])==null?void 0:R.etNumber)??""},d=k=>{const R=k.indexOf("(");return R!==-1?k.substring(0,R).trim():k},x=()=>({birthday:"",chineseName:"",firstName:"",lastName:"",nameSuffix:a.flightList[0].nameSuffix,unMinor:!1,unMinorAge:0,passengerId:1,passengerPhone:"",fullName:a.flightList[0].fullName,passengerNameInPnr:a.flightList[0].passengerNameSuffix,passengerType:a.flightList[0].passengerType,specialPassengerType:a.flightList[0].specialPassengerType,ticketNumberJoin:_(),ticketNumbersForTN:a.flightList.map(R=>R.etNumber),segments:[]}),T=()=>{if(u.value=[],a.passengerList.length===0){u.value.push(x());return}a.passengerList.forEach((k,R)=>{f(k,a.flightList)&&k.infantDetail&&!o.value.includes(R)&&o.value.push(R),f(k,a.flightList)&&u.value.unshift(k)})};return st(()=>{T()}),wt(()=>a.passengerList,()=>{T()}),{choosePassengerIndex:o,choosePassengers:u,selectPassenger:i,selectOnlyMainPassenger:p,getInftName:d,isMatchMainPassenger:f}},__={key:0,class:"pnr-cancel text-yellow-1 bg-yellow-3 border-yellow-2"},b_={class:"cancel-tip ml-[10px]"},x_=t("em",{class:"iconfont icon-warning-circle-fill"},null,-1),T_={key:1,class:"pnr-cancel text-yellow-1 bg-yellow-3 border-yellow-2"},$_={class:"cancel-tip ml-[10px]"},N_=t("em",{class:"iconfont icon-warning-circle-fill"},null,-1),R_={key:2,class:"pnr-cancel text-yellow-1 bg-yellow-3 border-yellow-2"},C_={class:"cancel-tip ml-[10px]"},w_=t("em",{class:"iconfont icon-warning-circle-fill"},null,-1),A_={key:3,class:"texv-if=t-base text-gray-1 font-bold mt-[15px]"},S_={class:"flex"},D_={class:"bg-gray-7 ml-[5px]"},P_={class:"text-gray-3 text-xs ml-[5px] mr-[5px] mt-[2px]"},O_={key:0,class:"iconfont icon-connect ml-[5px] mr-[5px]"},E_={key:1,class:"bg-gray-7 w-[34px] ml-[5px]"},F_={class:"text-gray-3 text-xs ml-[5px] mr-[5px] mt-[2px]"},V_={class:"flex"},M_={class:"bg-gray-7 w-[34px] ml-[5px]"},j_={class:"text-gray-3 text-xs ml-[5px] mr-[5px] mt-[2px]"},L_=je({__name:"Passenger",props:{mainTicketNo:{},flightList:{},passengerList:{},pnrNoList:{},pnrNoNotPresent:{type:Boolean},matchMainPassenger:{type:Boolean}},setup(a,{expose:o}){const u=a,{choosePassengerIndex:h,choosePassengers:f,selectPassenger:p,selectOnlyMainPassenger:i,isMatchMainPassenger:_}=v_(u);return o({choosePassengers:f,isMatchMainPassenger:_,selectOnlyMainPassenger:i}),(d,x)=>{const T=yt,k=an,R=na;return s(),m(ye,null,[u.pnrNoList.length>1?(s(),m("div",__,[t("span",b_,[x_,K(n(d.$t("app.agentTicketQuery.differentTip")),1)])])):Z("",!0),u.pnrNoNotPresent?(s(),m("div",T_,[t("span",$_,[N_,K(n(d.$t("app.agentTicketQuery.changePnrTip")),1)])])):Z("",!0),u.matchMainPassenger?Z("",!0):(s(),m("div",R_,[t("span",C_,[w_,K(n(d.$t("app.agentTicketQuery.noMatchWithThePreviousPassenger")),1)])])),u.passengerList.length>1||u.passengerList.length&&u.passengerList[0].infantDetail?(s(),m("div",A_,n(d.$t("app.agentTicketQuery.peersPassenger")),1)):Z("",!0),c(R,{modelValue:e(h),"onUpdate:modelValue":x[0]||(x[0]=v=>Ve(h)?h.value=v:null),class:"flex flex-wrap"},{default:l(()=>[(s(!0),m(ye,null,Ne(u.passengerList,(v,r)=>(s(),m("div",{key:v.passengerNameInPnr},[t("label",null,[e(_)(v,d.flightList)?Z("",!0):(s(),m("div",{key:0,class:Se([{"w-[390px]":v.infantDetail&&!e(_)(v,d.flightList),"bg-brand-4 border-brand-2":e(h).includes(r),"border-inherit":!e(h).includes(r)},"pr-[4px] mr-[10px] mt-[10px] rounded border"])},[c(k,{label:r,class:"ml-[8px]",onChange:e(p)},{default:l(()=>[t("div",S_,[c(T,{placement:"top",trigger:"hover",content:(v==null?void 0:v.passengerNameInPnr)??"",teleported:!1},{default:l(()=>[t("div",{class:Se([{"w-[107px]":v.passengerNameInPnr.length>11},"whitespace-nowrap overflow-hidden text-ellipsis font-bold leading-normal"])},n(v.passengerNameInPnr),3)]),_:2},1032,["content"]),t("div",D_,[t("div",P_,n(e(Rt)(v.specialPassengerType??"ADT")),1)]),v.infantDetail?(s(),m("div",O_)):Z("",!0),c(T,{placement:"top",trigger:"hover",content:(v==null?void 0:v.passengerNameInPnr)??"",teleported:!1},{default:l(()=>{var y;return[t("div",{class:Se([{"w-[120px]":(v==null?void 0:v.infantDetail)&&v.infantDetail.passengerNameInPnr.length>11},"whitespace-nowrap overflow-hidden text-ellipsis font-bold leading-normal"])},n(((y=v.infantDetail)==null?void 0:y.passengerNameInPnr)??""),3)]}),_:2},1032,["content"]),v.infantDetail?(s(),m("div",E_,[t("div",F_,n(d.$t("app.agentTicketQuery.passengerEN.type_INF")),1)])):Z("",!0)])]),_:2},1032,["label","onChange"])],2))]),e(_)(v,d.flightList)&&v.infantDetail?(s(),m("div",{key:0,class:Se([{"w-[390px]":v.infantDetail&&!e(_)(v,d.flightList),"bg-brand-4 border-brand-2":e(h).includes(r),"border-inherit":!e(h).includes(r)},"main-baby w-[190px] mr-[10px] mt-[10px] rounded border"])},[c(k,{modelValue:e(h)[r],"onUpdate:modelValue":y=>e(h)[r]=y,label:r,class:"ml-[8px]",disabled:"",onChange:e(p)},{default:l(()=>{var y;return[c(T,{placement:"top",trigger:"hover",content:((y=v.infantDetail)==null?void 0:y.passengerNameInPnr)??"",teleported:!1},{default:l(()=>{var C;return[t("div",V_,[t("div",{class:Se([{"w-[102px]":(v==null?void 0:v.infantDetail)&&(v.infantDetail.passengerNameInPnr??"").length>11},"whitespace-nowrap overflow-hidden text-ellipsis font-bold leading-normal"])},n(((C=v.infantDetail)==null?void 0:C.passengerNameInPnr)??""),3),t("div",M_,[t("div",j_,n(d.$t("app.agentTicketQuery.passengerEN.type_INF")),1)])])]}),_:2},1032,["content"])]}),_:2},1032,["modelValue","onUpdate:modelValue","label","onChange"])],2)):Z("",!0)]))),128))]),_:1},8,["modelValue"])],64)}}});const B_=a=>(bt("data-v-5cc94d70"),a=a(),xt(),a),I_=B_(()=>t("i",{class:"iconfont icon-close"},null,-1)),U_=[I_],Q_={class:"mt-[15px]"},q_={key:1,class:"mt-[10px]"},z_={class:"flex"},G_={class:"flex justify-center crs-btn-dialog-ui"},H_={key:1,class:"h-[300px] w-full flex-col justify-center items-center gap-2.5 inline-flex"},W_={class:"flex-col justify-center items-center gap-[19px] flex"},Y_=["alt"],K_={class:"flex-col justify-center items-center gap-2.5 flex"},X_={class:"text-center text-gray-2 text-lg font-bold leading-normal"},J_=je({__name:"ChangeDialog",props:{tktNo:{},factor:{}},emits:["update:modelValue"],setup(a,{emit:o}){const u=a,h=o,{loading:f,flightList:p,changeType:i,changeModel:_,disableNew:d,disableExist:x,disableInvoluntarily:T,changeInfoForm:k,FORM_RULES:R,changeModelFormRef:v,isAllowChangePnr:r,passengerResult:y,pnrNoList:C,checkSegmentList:$,passengerRef:P,isShowAccompanyPassenger:M,isDisableConfirmButton:X,matchMainPassenger:ue,searchAccompanyPassenger:E,getChosenSegment:j,queryPassenger:oe,handleChangeType:D,handleChangeModel:b,confirmChange:N,queryRtkt:ne}=h_(u,h);return(ie,U)=>{const A=Pt,le=Ot,he=gt,ce=ot,L=Ke,se=it,Q=nt,S=kt;return s(),ae(Q,{title:ie.$t("app.change.select"),width:"1040px","close-on-click-modal":!1,"show-close":!1,"align-center":"true",tabindex:"-1","custom-class":"change-dialog",onClose:U[5]||(U[5]=G=>ie.$emit("update:modelValue",!1))},{default:l(()=>[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:U[0]||(U[0]=G=>ie.$emit("update:modelValue",!1))},U_),Qe((s(),m("div",null,[e(p).length>0?(s(),m(ye,{key:0},[e(p).length>0?(s(),ae(y_,{key:0,"flight-list":e(p),onQueryPassenger:e(oe),onGetChosenSegment:e(j),onQueryRtkt:e(ne)},null,8,["flight-list","onQueryPassenger","onGetChosenSegment","onQueryRtkt"])):Z("",!0),t("div",Q_,[e(y).passengers?(s(),ae(L_,{key:0,ref_key:"passengerRef",ref:P,"pnr-no-list":e(C),"flight-list":e(p),"match-main-passenger":e(ue),"pnr-no-not-present":e(y).canceled||e(y).nonExistent,"passenger-list":e(y).passengers,"main-ticket-no":e(p)[0].etNumber},null,8,["pnr-no-list","flight-list","match-main-passenger","pnr-no-not-present","passenger-list","main-ticket-no"])):Z("",!0)]),e($).length>0?(s(),m("div",q_,[t("div",null,[c(le,{modelValue:e(i),"onUpdate:modelValue":U[1]||(U[1]=G=>Ve(i)?i.value=G:null),onChange:e(D)},{default:l(()=>[c(A,{label:"voluntary"},{default:l(()=>[K(n(ie.$t("app.change.voluntarilyChange")),1)]),_:1}),c(A,{disabled:e(T),label:"involuntary"},{default:l(()=>[K(n(ie.$t("app.change.involuntarilyChange")),1)]),_:1},8,["disabled"])]),_:1},8,["modelValue","onChange"])]),t("div",z_,[c(le,{modelValue:e(_),"onUpdate:modelValue":U[2]||(U[2]=G=>Ve(_)?_.value=G:null),onChange:e(b)},{default:l(()=>[c(A,{disabled:e(d),label:"new"},{default:l(()=>[K(n(ie.$t("app.change.newChange")),1)]),_:1},8,["disabled"]),c(A,{disabled:e(x),label:"exist"},{default:l(()=>[K(n(ie.$t("app.change.existChange")),1)]),_:1},8,["disabled"])]),_:1},8,["modelValue","onChange"]),e(_)==="exist"?(s(),ae(se,{key:0,ref_key:"changeModelFormRef",ref:v,rules:e(R),"hide-required-asterisk":!0,model:e(k),inline:!0,class:"form-style inline-block h-[32px] w-[200px]"},{default:l(()=>[c(ce,{prop:"pnrNo",class:"w-[66px]"},{default:l(()=>[c(he,{modelValue:e(k).pnrNo,"onUpdate:modelValue":U[3]||(U[3]=G=>e(k).pnrNo=G),disabled:!e(r),onInput:U[4]||(U[4]=G=>{var J,z;e(k).pnrNo=(J=e(k).pnrNo)==null?void 0:J.trim(),e(k).pnrNo=(z=e(k).pnrNo)==null?void 0:z.toLocaleUpperCase()})},null,8,["modelValue","disabled"])]),_:1}),e(M)?(s(),ae(ce,{key:0},{default:l(()=>[c(L,{onClick:e(E)},{default:l(()=>[K(n(ie.$t("app.change.search")),1)]),_:1},8,["onClick"])]),_:1})):Z("",!0)]),_:1},8,["rules","model"])):Z("",!0)])])):Z("",!0),t("div",G_,[c(L,{type:"primary",disabled:e(X),onClick:e(N)},{default:l(()=>[K(n(ie.$t("app.button.ensure")),1)]),_:1},8,["disabled","onClick"])])],64)):(s(),m("div",H_,[t("div",W_,[t("img",{src:ia,alt:ie.$t("app.emptyTip")},null,8,Y_),t("div",K_,[t("div",X_,n(ie.$t("app.change.notRtData")),1)])])]))])),[[S,e(f)]])]),_:1},8,["title"])}}});const Z_=at(J_,[["__scopeId","data-v-5cc94d70"]]),e1=a=>(bt("data-v-1fb65eea"),a=a(),xt(),a),t1={class:"ticket-card mt-[10px] py-2.5 px-5 min-h-58 bg-gray-0 rounded-lg"},n1={class:"flex items-center justify-between relative"},a1={class:"flex items-center justify-start"},s1={key:0},o1={class:"font-bold text-base"},i1=["onClick"],l1={key:2,class:"inline-flex h-[20px] px-[4px] py-[0px] rounded-[2px] text-yellow-1 bg-yellow-2 items-center text-[12px] mr-2.5"},r1={key:3,class:"iconfont icon-inf ml-2.5 mr-[4px] text-gray-4"},c1={key:4,class:"iconfont icon-user-fill ml-2.5 mr-[4px] text-gray-4"},u1={class:"mr-[4px] text-base font-bold text-gray-1"},d1={key:6,class:"mr-[4px] text-base font-bold text-gray-1"},p1={key:7,class:"text-gray-3 py-0.5 px-1.5 rounded-sm bg-gray-7 mr-1 text-xs"},f1={key:8,class:"text-yellow-1 mr-2.5 cursor-pointer text-xs"},m1={class:"text-xs text-right"},g1={key:2,class:"ml-[12px]"},k1={class:"mt-[10px] text-sm"},y1={class:"w-[22%]"},h1={key:0,class:"mr-2.5"},v1={key:1,class:"text-yellow-1 px-1.5 py-0.5 rounded-sm bg-yellow-2 mr-2.5 min-w-50 text-xs"},_1={key:2,class:"text-gray-1 mr-2.5"},b1={class:"text-gray-1"},x1={class:"w-[22%]"},T1={class:"text-gray-1 mr-2.5"},$1={class:"text-gray-3"},N1={key:1,class:"text-yellow-1 px-1.5 py-0.5 rounded-sm bg-yellow-2 mr-2.5 min-w-50 text-xs"},R1={class:"w-[28%] text-gray-1"},C1={class:"mr-2.5"},w1={key:0,class:"text-yellow-1 px-1.5 py-0.5 rounded-sm bg-yellow-2 mr-2.5 text-xs"},A1={key:1,class:"text-gray-1"},S1={key:2,class:"text-gray-1 mx-[2px]"},D1=["onClick"],P1={class:"w-[28%] flex items-center"},O1={key:0},E1={class:"ticket-card mt-[10px] py-2.5 px-5 min-h-58 bg-gray-0 rounded-lg"},F1={class:"flex items-center justify-between relative"},V1={class:"flex items-center justify-start"},M1={class:"w-[170px] mr-2.5 font-bold text-brand-2"},j1={key:0,class:"iconfont icon-inf ml-2.5 mr-[4px] text-gray-4"},L1={key:1,class:"iconfont icon-user-fill ml-2.5 mr-[4px] text-gray-4"},B1={class:"mr-[4px] text-base font-bold text-gray-1"},I1={key:3,class:"mr-[4px] text-base font-bold text-gray-1"},U1={key:4,class:"text-gray-3 py-0.5 px-1.5 rounded-sm bg-gray-7 mr-1 text-xs"},Q1={class:"mt-[10px] text-sm"},q1={class:"w-[6%]"},z1={key:0,class:"mr-2.5"},G1={key:1,class:"text-yellow-1 px-1.5 py-0.5 rounded-sm bg-yellow-2 mr-2.5 min-w-50 text-xs"},H1={key:2,class:"text-gray-1 mr-2.5"},W1={class:"w-[11%]"},Y1={key:0,class:"text-gray-1 mr-2.5"},K1={key:1,class:"text-yellow-1 px-1.5 py-0.5 rounded-sm bg-yellow-2 mr-2.5 min-w-50 text-xs"},X1={class:"w-[15%] text-gray-1"},J1={class:"w-[24%] flex items-center"},Z1={class:"h-9 justify-start items-center gap-4 mb-2.5 inline-flex"},eb=e1(()=>t("em",{class:"iconfont icon-info-circle-line text-brand-2 !text-[36px] w-9 h-9 flex items-center"},null,-1)),tb={class:"grow shrink basis-0 text-gray-1 text-lg font-normal leading-normal"},nb=je({__name:"TicketQueryResult",props:{queryTicketRes:{},refundTicketData:{},queryType:{}},emits:["addNewTab","reQueryTicket"],setup(a,{emit:o}){const u=o,h=a,{showSupplementRefundDialog:f,authTicketShow:p,showSupplementSuccessDialog:i,ticketList:_,ticketListByName:d,isShowRepealTicketDialog:x,ticketOperationCondition:T,printerNo:k,isSupplementRefund:R,refundOperationCondition:v,refundTicketData:r,printNoDialog:y,tktNumber:C,invoiceNumber:$,showTicketRefundFormDialog:P,changeDialogShow:M,changeTicketNo:X,printerType:ue,ticketOperation:E,goToPnrManage:j,stssChangeTicketStatusClick:oe,changeStatus:D,viewRefundFormWithGid:b,viewSupplementRefundWithGid:N,authTicketClick:ne,openSupplementSuccessDialog:ie,openRefundDialog:U,openDialog:A,closePopover:le,goToNewRefund:he,reQueryTicket:ce,showPreview:L,previewInfo:se,openPreview:Q,openChange:S,pullControlPower:G,copyInfo:J,changeFactor:z,tssForm:w,tssFormRef:g,datePrefix:B,showTssDialog:F,closeDialog:de,TSS_FORM_RULES:ve,confirmTss:O,tssTip:fe,tssCheckLabel:Y,judgeInter:Te,refundPrintNo:V,isClient:ge,openTicketDetailWindow:me,isAuthToShowWindow:Re,invalidatedTicketQueryGid:Oe}=rh(h,u);return(we,Ee)=>{const Be=Ke,Ze=yt,I=an,_e=ot,$e=zo,re=it,Ce=nt,Fe=_n("permission");return s(),m(ye,null,[we.queryType!=="4"&&e(_).length>0?(s(!0),m(ye,{key:0},Ne(e(_),(W,Ye)=>{var lt;return s(),m("div",{key:W.etNumber+Ye},[t("div",t1,[t("div",n1,[t("div",a1,[e(ge)&&e(Re)?(s(),m(ye,{key:0},[W!=null&&W.etNumber?(s(),m("span",s1,[c(Be,{link:"",type:"primary",onClick:be=>e(me)(W.etNumber,W.secondFactor.secondFactorCode,W.secondFactor.secondFactorValue)},{default:l(()=>[t("span",o1,n(e(ht)(W.etNumber)),1)]),_:2},1032,["onClick"]),t("em",{class:"iconfont icon-copy text-brand-2 relative top-[2px] cursor-pointer",onClick:xn(be=>e(J)((W==null?void 0:W.etNumber)??""),["stop"])},null,8,i1)])):Z("",!0)],64)):(s(),ae(mn,{key:1,"tkt-info":W,"is-international":e(Te)(W.ticketTypeCode??""),"second-factor":W.secondFactor,"tkt-index":Ye,"query-type":we.queryType,"is-cds-ticket":W.cdsTicket??!1,onClosePopover:e(le)},null,8,["tkt-info","is-international","second-factor","tkt-index","query-type","is-cds-ticket","onClosePopover"])),W.governmentPurchase?(s(),m("div",l1,"GP")):Z("",!0),W.specialPassengerType==="INF"?(s(),m("em",r1)):(s(),m("em",c1)),W.passengerNameSuffix&&((lt=W.passengerNameSuffix)==null?void 0:lt.length)>30?(s(),ae(Ze,{key:5,class:"item",effect:"dark",content:W.passengerNameSuffix,placement:"top-start"},{default:l(()=>[t("span",u1,n(W.passengerNameSuffix),1)]),_:2},1032,["content"])):(s(),m("span",d1,n(W.passengerNameSuffix),1)),W.passengerType?(s(),m("span",p1,n(e(Rt)(W.specialPassengerType)),1)):Z("",!0),W.receiptPrinted?(s(),m("span",f1,"ReceiptPrinted")):Z("",!0)]),t("div",m1,[W!=null&&W.canQueryAndApplyRefund?(s(),ae(Be,{key:0,onClick:be=>e(b)(!1,W)},{default:l(()=>[K(n(we.$t("app.agentTicketQuery.queryRefundBtn")),1)]),_:2},1032,["onClick"])):Z("",!0),c(Ze,{class:"box-item",effect:"dark",content:we.$t("app.agentTicketQuery.supplementaryRefundApplyTip"),placement:"top-start","popper-class":"ticket-conditon-popper"},{content:l(()=>[t("div",null,n(we.$t("app.agentTicketQuery.supplementaryRefundApplyTip")),1)]),default:l(()=>[W!=null&&W.canSupplementaryRefundApply?(s(),m("span",{key:0,class:Se({"button-spacing":W==null?void 0:W.canSupplementaryRefundApply})},[Qe((s(),ae(Be,{onClick:be=>e(N)(!0,W)},{default:l(()=>[K(n(we.$t("app.agentTicketQuery.supplementaryRefundApplyBtn")),1)]),_:2},1032,["onClick"])),[[Fe,"crs-tc-ticketOperation-ticketQuery-supplement-button"]])],2)):Z("",!0)]),_:2},1032,["content"]),W!=null&&W.canChange?(s(),m("span",{key:1,class:Se({"button-spacing":W==null?void 0:W.canChange})},[Qe((s(),ae(Be,{"data-gid":"091T0102",onClick:be=>e(S)(W.etNumber,W.specialPassengerType,W.secondFactor)},{default:l(()=>[K(n(we.$t("app.agentTicketRefund.change")),1)]),_:2},1032,["onClick"])),[[Fe,"crs-tc-ticketOperation-ticketQuery-ticketChange-button"]])],2)):Z("",!0),W!=null&&W.canRefund?(s(),m("span",g1,[c(Be,{onClick:be=>e(Q)(W)},{default:l(()=>[K(n(we.$t("app.agentTicketRefund.preview")),1)]),_:2},1032,["onClick"])])):Z("",!0),W!=null&&W.canRefund?(s(),m("span",{key:3,class:Se({"button-spacing":W==null?void 0:W.canRefund})},[Qe((s(),ae(Be,{"data-gid":"091Q0101",onClick:be=>e(he)(W.etNumber,W.secondFactor)},{default:l(()=>[K(n(we.$t("app.agentTicketQuery.refundBtn")),1)]),_:2},1032,["onClick"])),[[Fe,"crs-tc-ticketOperation-ticketQuery-refundTicket-button"]])],2)):Z("",!0),W!=null&&W.canVoid?(s(),m("span",{key:4,class:Se({"button-spacing":W==null?void 0:W.canVoid})},[Qe((s(),ae(Be,{onClick:be=>e(E)(W,"repealTicket")},{default:l(()=>[K(n(we.$t("app.agentTicketQuery.invalidBtn")),1)]),_:2},1032,["onClick"])),[[Fe,"crs-tc-ticketOperation-ticketQuery-repealTicket-button"]])],2)):Z("",!0),W!=null&&W.canSuspended?(s(),m("span",{key:5,class:Se({"button-spacing":W==null?void 0:W.canSuspended})},[Qe((s(),ae(Be,{onClick:be=>e(oe)(W,"Suspend")},{default:l(()=>[K(n(we.$t("app.agentTicketQuery.suspendBtn")),1)]),_:2},1032,["onClick"])),[[Fe,"crs-tc-ticketOperation-ticketQuery-suspendOrUnSuspend-button"]])],2)):Z("",!0),W!=null&&W.canUnSuspended?(s(),m("span",{key:6,class:Se({"button-spacing":W==null?void 0:W.canUnSuspended})},[Qe((s(),ae(Be,{onClick:be=>e(oe)(W,"Resume")},{default:l(()=>[K(n(we.$t("app.agentTicketQuery.unsuspendBtn")),1)]),_:2},1032,["onClick"])),[[Fe,"crs-tc-ticketOperation-ticketQuery-suspendOrUnSuspend-button"]])],2)):Z("",!0),W!=null&&W.canTktAuth?(s(),m("span",{key:7,class:Se({"button-spacing":W==null?void 0:W.canTktAuth})},[Qe((s(),ae(Be,{"data-gid":"091N0203",onClick:be=>e(ne)(W.etNumber)},{default:l(()=>[K(n(we.$t("app.agentTicketQuery.ticketAuthBtn")),1)]),_:2},1032,["onClick"])),[[Fe,"crs-tc-ticketOperation-ticketQuery-ticketAuth-button"]])],2)):Z("",!0)])]),t("div",k1,[(s(!0),m(ye,null,Ne(W.airSeg??[],(be,vt)=>{var pt,Tt,$t,It;return s(),m("div",{key:`${W.etNumber}${be.depAirportCode}`,class:"flex justify-between seg-info"},[t("div",y1,[be.flightNo==="OPEN"&&be.airline?(s(),m("span",h1,n(be.airline),1)):Z("",!0),["OPEN","ARNK"].includes(be.flightNo)||(pt=be.flightNo)!=null&&pt.includes("VOID")?(s(),m("span",v1,n(be.flightNo==="OPEN"?"OPEN":"ARNK"),1)):(s(),m("span",_1,n(e(ka)((be==null?void 0:be.airline)??"",(be==null?void 0:be.operationAirline)??"")?`*${be.flightNo}`:be.flightNo),1)),t("span",b1,n(be.cabin),1)]),t("div",x1,[be.depTime?(s(),m(ye,{key:0},[t("span",T1,n(e(et)(be.depTime).format("YYYY-MM-DD")??""),1),t("span",$1,n(e(et)(be.depTime).format("HH:mm")??""),1)],64)):(s(),m("span",N1,n(be.flightNo==="OPEN"?"OPEN":"ARNK"),1))]),t("div",R1,[t("span",C1,n(be.depAirportCode)+"-"+n(be.arrAirportCode),1),be.changeReason?(s(),m("span",w1,n(be.changeReason),1)):Z("",!0),be.pnrNo?(s(),m("span",A1,n(be.pnrNo),1)):Z("",!0),be.pnrNo?(s(),m("span",S1,"/")):Z("",!0),be.crsPnrNo?(s(),ae(Be,{key:3,link:"",type:"primary",onClick:Vt=>e(j)(be.crsPnrNo)},{default:l(()=>[K(n(be.crsPnrNo),1)]),_:2},1032,["onClick"])):Z("",!0),be.crsPnrNo?(s(),m("em",{key:4,class:"iconfont icon-copy text-brand-2 relative top-[2px] cursor-pointer",onClick:Vt=>e(J)(be.crsPnrNo)},null,8,D1)):Z("",!0)]),t("div",P1,[t("span",{class:Se([e(Bt)[(Tt=be.ticketStatus)==null?void 0:Tt.trim()]?e(Bt)[($t=be.ticketStatus)==null?void 0:$t.trim()].color:"","font-bold mr-2.5"])},n(be.ticketStatus),3),["REFUNDED","OPEN FOR USE","AIRPORT CNTL"].includes((It=be.ticketStatus)==null?void 0:It.trim())?(s(),ae(Ze,{key:0,class:"box-item",effect:"dark",content:we.$t("app.agentTicketQuery.changeStatusTips"),placement:"bottom-start"},{default:l(()=>[be.airline!=="CZ"?(s(),m("span",O1,[Qe((s(),ae(Be,{link:"",type:"primary",size:"small",onClick:Vt=>e(D)(W.etNumber,be)},{default:l(()=>[K(n(we.$t("app.agentTicketQuery.changeStatusBtn")),1)]),_:2},1032,["onClick"])),[[Fe,"crs-tc-ticketOperation-ticketQuery-updateStatus-button"]])])):Z("",!0)]),_:2},1032,["content"])):Z("",!0),be.ticketStatus.includes("AIRPORT CNTL")?Qe((s(),ae(Be,{key:1,link:"",type:"primary",size:"small",onClick:Vt=>e(G)(vt,be.airline,W.etNumber)},{default:l(()=>[K(n(we.$t("app.agentTicketQuery.pullControlPower")),1)]),_:2},1032,["onClick"])),[[Fe,"crs-tc-ticketOperation-ticketQuery-pullControlPower-button"]]):Z("",!0)])])}),128))])])])}),128)):Z("",!0),we.queryType==="4"&&e(d).length>0?(s(!0),m(ye,{key:1},Ne(e(d),(W,Ye)=>{var lt;return s(),m("div",{key:W.etNumber+Ye},[t("div",E1,[t("div",F1,[t("div",V1,[t("span",M1,n(e(ht)(W.etNumber)),1),W.passengerType&&W.passengerType==="INF"?(s(),m("em",j1)):Z("",!0),W.passengerType&&W.passengerType!=="INF"?(s(),m("em",L1)):Z("",!0),W.passengerName&&((lt=W.passengerName)==null?void 0:lt.length)>30?(s(),ae(Ze,{key:2,class:"item",effect:"dark",content:W.passengerName,placement:"top-start"},{default:l(()=>[t("span",B1,n(W.passengerName),1)]),_:2},1032,["content"])):(s(),m("span",I1,n(W.passengerName),1)),W.passengerType?(s(),m("span",U1,n(e(Rt)(W.passengerType)),1)):Z("",!0)])]),t("div",Q1,[(s(!0),m(ye,null,Ne(W.airSeg??[],be=>{var vt,pt,Tt;return s(),m("div",{key:`${W.etNumber}${be.depAirportCode}`,class:"flex justify-between seg-info"},[t("div",q1,[be.fltNo==="OPEN"&&be.airlineCode?(s(),m("span",z1,n(be.airlineCode),1)):Z("",!0),["OPEN","ARNK"].includes(be.fltNo)||(vt=be.fltNo)!=null&&vt.includes("VOID")?(s(),m("span",G1,n(be.fltNo==="OPEN"?"OPEN":"ARNK"),1)):(s(),m("span",H1,n(e(ka)((be==null?void 0:be.airlineCode)??"",(be==null?void 0:be.operateAirline)??"")?`*${be.airlineCode}${be.fltNo}`:`${be.airlineCode}${be.fltNo}`),1))]),t("div",W1,[be.depDate?(s(),m("span",Y1,n(e(et)(be.depDate).format("YYYY-MM-DD")??""),1)):(s(),m("span",K1,n(be.fltNo==="OPEN"?"OPEN":"ARNK"),1))]),t("div",X1,[t("span",null,n(be.depAirportCode)+"-"+n(be.arrAirportCode),1)]),t("div",J1,[t("span",{class:Se([e(Bt)[(pt=be.status)==null?void 0:pt.trim()]?e(Bt)[(Tt=be.status)==null?void 0:Tt.trim()].color:"","font-bold mr-2.5"])},n(be.status),3)])])}),128))])])])}),128)):Z("",!0),e(x)?(s(),ae(Op,{key:2,modelValue:e(x),"onUpdate:modelValue":Ee[0]||(Ee[0]=W=>Ve(x)?x.value=W:null),"invalidated-ticket-query-gid":e(Oe),"ticket-operation-condition":e(T),onReQueryTicket:e(ce)},null,8,["modelValue","invalidated-ticket-query-gid","ticket-operation-condition","onReQueryTicket"])):Z("",!0),e(p)?(s(),ae(vf,{key:3,modelValue:e(p),"onUpdate:modelValue":Ee[1]||(Ee[1]=W=>Ve(p)?p.value=W:null),"ticket-no":e(C)},null,8,["modelValue","ticket-no"])):Z("",!0),e(y)?(s(),ae(Xa,{key:4,modelValue:e(y),"onUpdate:modelValue":Ee[2]||(Ee[2]=W=>Ve(y)?y.value=W:null),"ticket-management-organization-code":e(v).ticketManagementOrganizationCode??"",onOpenDialog:e(A)},null,8,["modelValue","ticket-management-organization-code","onOpenDialog"])):Z("",!0),e(P)?(s(),ae(ta,{key:5,modelValue:e(P),"onUpdate:modelValue":Ee[3]||(Ee[3]=W=>Ve(P)?P.value=W:null),"printer-no":e(k),"printer-type":e(ue),"is-supplement-refund":e(R),"refund-operation-condition":e(v),"refund-ticket-data":e(r),onReQueryTicket:e(ce)},null,8,["modelValue","printer-no","printer-type","is-supplement-refund","refund-operation-condition","refund-ticket-data","onReQueryTicket"])):Z("",!0),e(f)?(s(),ae(Pv,{key:6,modelValue:e(f),"onUpdate:modelValue":Ee[4]||(Ee[4]=W=>Ve(f)?f.value=W:null),"printer-no":e(k),"refund-ticket-data":e(r),onOpenSupplementSuccessDialog:e(ie)},null,8,["modelValue","printer-no","refund-ticket-data","onOpenSupplementSuccessDialog"])):Z("",!0),e(i)?(s(),ae(Cv,{key:7,modelValue:e(i),"onUpdate:modelValue":Ee[5]||(Ee[5]=W=>Ve(i)?i.value=W:null),"ticket-management-organization-code":e(v).ticketManagementOrganizationCode??"","invoice-number":e($),"refund-printer-no":e(V),onOpenRefundDialog:e(U),onToQueryTicket:e(ce)},null,8,["modelValue","ticket-management-organization-code","invoice-number","refund-printer-no","onOpenRefundDialog","onToQueryTicket"])):Z("",!0),e(L)?(s(),ae(hv,{key:8,modelValue:e(L),"onUpdate:modelValue":Ee[6]||(Ee[6]=W=>Ve(L)?L.value=W:null),"preview-info":e(se),"is-auth-to-show-window":e(Re)},null,8,["modelValue","preview-info","is-auth-to-show-window"])):Z("",!0),e(M)?(s(),ae(Z_,{key:9,modelValue:e(M),"onUpdate:modelValue":Ee[7]||(Ee[7]=W=>Ve(M)?M.value=W:null),"tkt-no":e(X),factor:e(z)},null,8,["modelValue","tkt-no","factor"])):Z("",!0),c(Ce,{modelValue:e(F),"onUpdate:modelValue":Ee[10]||(Ee[10]=W=>Ve(F)?F.value=W:null),width:"500","close-on-click-modal":!1,"show-close":!1,class:"ticket-pnr-dialog",onClose:e(de)},{footer:l(()=>[c(Be,{type:"primary",onClick:e(O)},{default:l(()=>[K(n(we.$t("app.agentTicketRefund.sure")),1)]),_:1},8,["onClick"]),c(Be,{onClick:e(de)},{default:l(()=>[K(n(we.$t("app.agentTicketRefund.cancel")),1)]),_:1},8,["onClick"])]),default:l(()=>[t("div",Z1,[eb,t("div",tb,n(e(fe)),1)]),we.queryType==="2"?(s(),ae(re,{key:0,ref_key:"tssFormRef",ref:g,model:e(w),rules:e(ve),"require-asterisk-position":"right",inline:!0,class:"form"},{default:l(()=>[c(_e,{prop:"pnrTss",class:"ml-[52px]"},{default:l(()=>[c(I,{modelValue:e(w).pnrTss,"onUpdate:modelValue":Ee[8]||(Ee[8]=W=>e(w).pnrTss=W),label:e(Y)},null,8,["modelValue","label"])]),_:1}),e(w).pnrTss?(s(),ae(_e,{key:0,label:we.$t("app.ticketStatus.issueDate"),prop:"issueDate"},{default:l(()=>[c($e,{modelValue:e(w).issueDate,"onUpdate:modelValue":Ee[9]||(Ee[9]=W=>e(w).issueDate=W),class:"date-picker","prefix-icon":e(B),type:"date",format:"YYYY-MM-DD",placeholder:we.$t("app.ticketStatus.issueDate")},null,8,["modelValue","prefix-icon","placeholder"])]),_:1},8,["label"])):Z("",!0)]),_:1},8,["model","rules"])):Z("",!0)]),_:1},8,["modelValue","onClose"])],64)}}});const ab=at(nb,[["__scopeId","data-v-1fb65eea"]]),sb=(a,o)=>{const{t:u}=We(),{copy:h,isSupported:f}=wa({legacy:!0});return{ticketList:Me(()=>ft(a.batchRefundRes.refundResult)??[]),goTicketQuery:d=>{o("addNewTab",{title:u("app.agentTicketQuery.ticketList"),name:"ticketQuery",content:null},d)},doCopy:d=>{f&&(h(d),mt({message:u("app.batchRefund.copySuccess"),type:"success",duration:2*1e3,grouping:!0,repeatNum:1}))}}},ob={class:"batch-refund-result mt-[10px] py-2.5 px-2.5 min-h-58 bg-gray-0 rounded-lg"},ib={key:0,class:"text-xs"},lb={key:1,class:"text-xs"},rb={key:2,class:"text-xs"},cb={class:"flex flex-wrap justify-start items-center"},ub={class:"max-w-[400px]"},db={key:0},pb=["onClick"],fb=["onClick"],mb=je({__name:"BatchRefundResult",props:{batchRefundRes:{}},emits:["addNewTab"],setup(a,{emit:o}){const u=o,h=a,{ticketList:f,doCopy:p,goTicketQuery:i}=sb(h,u);return(_,d)=>{const x=Je,T=yt;return s(),m("div",ob,[t("div",{class:Se(["leading-tight justify-start items-center gap-1 inline-flex border rounded p-2.5 min-w-full",_.batchRefundRes.allSuccess?"text-green-2 bg-green-4 border-green-3":"text-red-1 bg-red-3 border-red-2"])},[c(x,null,{default:l(()=>[_.batchRefundRes.allSuccess?(s(),ae(e(Mn),{key:0,class:"text-base leading-tight text-green-2"})):(s(),ae(e(jn),{key:1,class:"text-base leading-tight text-red-1"}))]),_:1}),_.batchRefundRes.allSuccess?(s(),m("span",ib,n(_.$t("app.batchRefund.successTip")),1)):_.batchRefundRes.allFail?(s(),m("span",lb,n(_.$t("app.batchRefund.failedTip")),1)):(s(),m("span",rb,n(_.$t("app.batchRefund.partialFailedTip")),1))],2),t("div",cb,[(s(!0),m(ye,null,Ne(e(f),(k,R)=>(s(),m("div",{key:R,class:"w-[1/10] flex justify-between items-center mt-2.5 mr-2.5 py-3.5 px-2.5 border rounded border-brand-3"},[k.success?(s(),ae(x,{key:0,class:"mr-1.5"},{default:l(()=>[c(e(Mn),{class:"text-base leading-tight text-green-2"})]),_:1})):Z("",!0),k.success?Z("",!0):(s(),ae(T,{key:1,placement:"top"},{content:l(()=>[t("div",ub,[t("div",null,n(_.$t("app.batchRefund.errorCode"))+n(k.errorCode||"--"),1),t("div",null,n(_.$t("app.batchRefund.description"))+n(k.description||"--"),1),t("div",null,n(_.$t("app.batchRefund.transactionNo"))+n(k.transactionNo||"--"),1),k.satTransactionNo?(s(),m("div",db,n(_.$t("app.batchRefund.satTransactionNo"))+n(k.satTransactionNo||"--"),1)):Z("",!0)])]),default:l(()=>[k.success?Z("",!0):(s(),ae(x,{key:0,class:"mr-1.5"},{default:l(()=>[c(e(jn),{class:"text-base leading-tight text-red-1 cursor-pointer"})]),_:1}))]),_:2},1024)),t("span",{class:"text-brand-2 text-xs leading-tight mr-2.5 pt-[1px] cursor-pointer",onClick:v=>e(i)(k.ticketNo)},n(k.ticketNo),9,pb),t("em",{class:"iconfont icon-copy cursor-pointer leading-none text-brand-2 w-[14px] h-[14px]",onClick:v=>e(p)(k.ticketNo)},null,8,fb)]))),128))])])}}});const gb=at(mb,[["__scopeId","data-v-215dd92e"]]),kb=()=>{const{t:a}=We(),o=Ae(!1),u=La(),h=Bs(),f=Cn(),p=H("ticketQuery"),i=H([]),_=H(),d=Ae(""),x=H(),T=Ae(!1),k=H({}),R=Ae(!1),v=Ae(""),r=Ae(!1),y=Ae(!1),C=Ae(!1),$=Ae(!1),P=Ae(!1),M=H({}),X=Ae(""),ue=Ae(""),E=H({ticketType:"",ticketNo:""}),j=H({}),oe=H([{title:a("app.agentTicketQuery.ticketList"),name:"ticketQuery",content:en(ab)}]),D=(g,B,F)=>{var de;F&&(M.value=F),B?((de=_.value)==null||de.queryTktByRoute(B,"1"),p.value="ticketQuery"):(oe.value.findIndex(O=>O.name===g.name)===-1&&oe.value.push(g),p.value=g.name,g.name.indexOf("?type=ticketNo&sign=")!==-1&&(d.value=g.name.split("?type=ticketNo&sign=")[1]))},b=(g,B)=>{const F=oe.value;let de;g===-1&&(de=oe.value.findIndex(ve=>ve.name===B)),p.value===B&&oe.value.forEach((ve,O)=>{ve.name===B&&(p.value=F[O-1].name)}),oe.value.splice(de||g,1)},N=g=>{p.value=oe.value[g].name},ne=()=>{var g;(g=_.value)==null||g.queryTkt()},ie=(g,B)=>{p.value="ticketQuery",i.value=g,v.value=B,_.value.closeLoading()},U=()=>{T.value=!0},A=g=>{var F;T.value=!1;const B={title:`${a("app.batchRefund.batchRefundResult")}`,name:`${a("app.batchRefund.batchRefundResult")}`,content:en(gb)};D(B),k.value=g,(F=x.value)==null||F.closeLoading()},le=()=>{R.value=!0},he=()=>{$.value=!0},ce=()=>{P.value=!0},L=()=>{R.value=!1,Xe.alert(a("app.agentTicketQuery.bopSuccessTips"),{icon:Le("em",{class:"iconfont icon-check-circle"}),customClass:"u-msg-box-icon",confirmButtonText:a("app.ticketStatus.confirmBtn"),showClose:!1})},se=g=>({ticketNo:E.value.ticketNo,ticketType:ue.value||E.value.ticketType,ticketManagementOrganizationCode:E.value.ticketManagementOrganizationCode,printerNo:X.value,refundNo:g??""}),Q=async(g,B)=>{var de;ue.value=B,X.value=g;const F=se();try{o.value=!0;const ve=Pe("091T0104"),{data:O}=await dn(F,ve);j.value=(de=O.value)==null?void 0:de.data,C.value=!0}finally{o.value=!1}},S=async g=>{var ve;const{ticketNo:B,ticketType:F,printerNo:de}=g;E.value={ticketNo:B,ticketType:F},X.value=de;try{o.value=!0;const O=Pe("091T0104"),{data:fe}=await dn(g,O);j.value=(ve=fe.value)==null?void 0:ve.data,j.value.ticketManagementOrganizationCode=g.ticketManagementOrganizationCode??"",C.value=!0}finally{o.value=!1}},G=(g,B,F,de,ve)=>({ticketNo:g,refundNo:B,ticketType:F?"D":"I",printerNo:de,ticketManagementOrganizationCode:ve}),J=async(g,B,F,de,ve)=>{var O,fe,Y;try{o.value=!0;const Te=Pe("091T0107"),{data:V}=await dn(G(g,B,F,de,ve),Te),ge=F?"D":"I",me=((O=V.value)==null?void 0:O.data.ticketNo)??"";E.value={ticketNo:me,ticketType:ge,ticketManagementOrganizationCode:ve},X.value=de,(fe=_.value)==null||fe.closeRefundView(),j.value=(Y=V.value)==null?void 0:Y.data,j.value.ticketManagementOrganizationCode=ve??"",C.value=!0,o.value=!1}finally{o.value=!1}},z=()=>{r.value=!0},w=()=>{y.value=!0};return wt(()=>r.value,g=>{u.setShowManualRefund(g)}),Ra(()=>{var g;if(h.query.ticketNo&&h.query.type){const B={title:`${a("app.agentTicketRefund.refund")}${h.query.ticketNo}`,name:`refund?type=ticketNo&sign=${h.query.ticketNo}`,content:en(Nn)};D(B,"",{secondFactorCode:"CN",secondFactorValue:(g=h==null?void 0:h.query)==null?void 0:g.refundPnrNo}),f.replace("/v2/crs/ticketOperation")}}),{loading:o,printNo:X,printType:ue,showRefundFormDialog:C,refundOperationCondition:E,refundFormData:j,editableTabs:oe,currentTab:p,refundEtNumber:d,queryType:v,queryTicketRes:i,ticketQueryConditionRef:_,showBatchRefund:T,showAuthOffice:y,showManualRefund:r,batchRefundRes:k,batchRefundRef:x,factor:M,showBopRefund:R,showRtktDialog:$,showCccfDialog:P,addTab:D,changeTab:N,removeTab:b,handleQueryTicket:ie,reQueryTicket:ne,openBatchRefund:U,handleBatchRefund:A,openBopRefund:le,bopRefundSuccess:L,openManualRefund:z,openAuthOffice:w,openRefundFormDialog:Q,deliverRefundData:S,openRefundDialog:J,openRtkt:he,openCccf:ce}},yb=kb,hb={class:"h-[27px] p-[4px] mr-2.5"},vb=["href","download"],_b=t("i",{class:"iconfont icon-download text-[16px] text-[var(--bkc-el-color-primary)]"},null,-1),bb=je({__name:"DownloadTemplate",setup(a){const{t:o}=We(),u=H(Is()),h=Ae(""),f=Ae(""),p=()=>{h.value=`${Qo}/SGUI-Batch-Refund-Template-CRS${u.value==="en"?"-EN":""}.xlsx`,f.value=o("app.batchRefund.batchRefundTemplate")};return(i,_)=>(s(),m("div",hb,[t("a",{href:h.value,download:f.value,class:"no-underline text-[var(--bkc-el-color-primary)]"},[_b,t("span",{class:"text-[12px]",onClick:p},n(i.$t("app.batchRefund.downloadTemplate")),1)],8,vb)]))}}),xb=()=>{const a=H(""),o=H([]),u=H([]),h=p=>new Promise(i=>{const _=new FileReader;_.readAsBinaryString(p),_.onload=d=>{var x;i((x=d==null?void 0:d.target)==null?void 0:x.result)}});return{fileName:a,resolveFile:async p=>{const i=p.raw;if(i){a.value=i.name;const _=await h(i),T=(await new Go.Workbook().xlsx.load(_)).getWorksheet(1),k=[],R=[];T==null||T.eachRow({includeEmpty:!0},function(v,r){if(r===1)v.eachCell({includeEmpty:!0},function(y){R.push(y.value)});else{const y={};v.eachCell({includeEmpty:!0},function(C,$){(C.value||C.value===0)&&(y[R[$-1]]=C.value)}),Lo(y)||k.push(y)}}),u.value=R,o.value=k}},jsonData:o,fileContentTitle:u}},Tb=a=>{const{t:o}=We(),{fileName:u,jsonData:h,resolveFile:f,fileContentTitle:p}=xb(),{postToAIGPersonalization:i}=oa(ca.salesTicketManagementRefundedAig),_=Ae(!0),d=Ae(!1),x=Ae(!1),T=Ae(!1),k=H("part"),R=H(),v=H({batchTkt:[{ticketNo:"",domestic:!0,voteCounterNo:"",commissionAmount:"",commissionRate:"",netRefund:"",currency:"",otherDeduction:"",failure:!1,errorCode:"",description:"",satTransactionNo:"",transactionNo:""}]}),r={ticketNo:[{required:!0,message:o("app.batchRefund.ticketNoNotEmpty"),trigger:"blur"},{pattern:Rn,message:o("app.batchRefund.formatError"),trigger:"blur"}],voteCounter:[{required:!0,message:o("app.batchRefund.printNoNotEmpty"),trigger:"blur"},{pattern:Gt,message:o("app.batchRefund.formatError"),trigger:"blur"}]},y=Me(()=>v.value.batchTkt.every(A=>!A.ticketNo)),C=Me(()=>v.value.batchTkt.every(A=>!A.netRefund||A.failure)),$=()=>{_.value=!0,d.value=!1,a("update:modelValue",!1)},P=async A=>{var le;v.value.batchTkt.splice(A,1),(le=R.value)==null||le.validate()},M=()=>{const A={ticketNo:"",domestic:!0,voteCounterNo:"",commissionAmount:"",commissionRate:"",netRefund:"",currency:"",otherDeduction:"",failure:!1,errorCode:"",description:"",satTransactionNo:"",transactionNo:""};v.value.batchTkt.length>0&&(A.voteCounterNo=v.value.batchTkt[0].voteCounterNo),v.value.batchTkt.push(A)},X=()=>{const A=u.value.substring(u.value.lastIndexOf("."));return[".xls",".xlsx"].includes(A)},ue=()=>{var A,le,he,ce;return((A=p.value)==null?void 0:A.length)===3&&((le=p.value)==null?void 0:le[0])===o("app.batchRefund.ticketNo")&&((he=p.value)==null?void 0:he[1])===o("app.batchRefund.printNo")&&((ce=p.value)==null?void 0:ce[2])===o("app.batchRefund.ticketType")},E=(A,le)=>A[le],j=()=>h.value.map(A=>{const[le,he,ce]=[E(A,o("app.batchRefund.ticketNo")),E(A,o("app.batchRefund.printNo")),E(A,o("app.batchRefund.ticketType"))];if(le||ce||he||he===0)return{ticketNo:le??"",domestic:ce?ce.trim()===o("app.batchRefund.domestic"):!0,voteCounterNo:he||he===0?`${he}`:""}}),oe=()=>{var le;if([_.value,d.value]=[!0,!1],(le=R.value)==null||le.clearValidate(),!X()){_.value=!1;return}if(!ue()){d.value=!0;return}let A=j();if(A.length<=0){d.value=!0;return}A=A.length<=20?A:A.slice(0,20),v.value.batchTkt=[...A]},D=()=>{x.value=!1},b=()=>v.value.batchTkt.map(A=>({ticketNo:A.ticketNo,domestic:A.domestic,printNo:A.voteCounterNo})),N=async()=>{var A;(A=R.value)==null||A.validate(async le=>{if(le){let he;try{x.value=!0;const ce=Pe("091U0104");he=(await $o({ticketList:b()},ce)).data.value,a("handleBatchRefund",he)}catch{x.value=!1}try{if(he!=null&&he.refundOrder){const ce=he==null?void 0:he.refundOrder;await i(ce)}}catch(ce){console.error("批量退票入库异常:",ce.message)}}else{await Xe.alert(o("app.batchRefund.formatErrorTips"),{icon:Le("em",{class:"iconfont icon-close-circle-line text-[var(--bkc-color-special-red-1)]"}),customClass:"u-msg-box-icon",confirmButtonText:o("app.batchRefund.confirm"),showClose:!1,draggable:!0});return}})},ne=A=>A==="all"?v.value.batchTkt.map(he=>({ticketNo:he.ticketNo,domestic:he.domestic,printNo:he.voteCounterNo})):v.value.batchTkt.filter(he=>!he.netRefund||he.failure).map(he=>({ticketNo:he.ticketNo,domestic:he.domestic,printNo:he.voteCounterNo})),ie=async A=>{var le;T.value=!1,(le=R.value)==null||le.validate(async he=>{if(!he){await Xe.alert(o("app.batchRefund.formatErrorTips"),{icon:Le("em",{class:"iconfont icon-close-circle-line text-[var(--bkc-color-special-red-1)]"}),customClass:"u-msg-box-icon",confirmButtonText:o("app.batchRefund.confirm"),showClose:!1,draggable:!0});return}if(A&&U()){T.value=!0;return}try{x.value=!0;const ce=A?"all":k.value,L=Pe("091U0101"),se=(await No({ticketList:ne(ce)},L)).data.value;x.value=!1,(v.value.batchTkt??[]).forEach(Q=>{(se??[]).forEach(S=>{var G,J,z,w,g;ht(Q.ticketNo)===ht(S.ticketNo)&&(Q.commissionAmount=((G=S.refundFeeDTO)==null?void 0:G.commissionAmount)??"",Q.commissionRate=((J=S.refundFeeDTO)==null?void 0:J.commissionRate)??"",Q.otherDeduction=((z=S.refundFeeDTO)==null?void 0:z.otherDeduction)??"",Q.netRefund=((w=S.refundFeeDTO)==null?void 0:w.netRefund)??"",Q.currency=((g=S.refundFeeDTO)==null?void 0:g.currency)??"",Q.failure=S.failure,Q.errorCode=S.errorCode??"",Q.description=S.description??"",Q.satTransactionNo=S.satTransactionNo??"",Q.transactionNo=S.transactionNo??"")})})}catch{x.value=!1}})},U=()=>{const A=v.value.batchTkt.filter(le=>!le.netRefund||le.failure);return A.length!==0&&A.length!==v.value.batchTkt.length};return{scopeType:k,showQueryScope:T,disabledBatchRefund:C,batchDataRule:r,confirmBatchRefund:N,closeBatchRefund:$,deleteBatchTktInfo:P,addBatchTktInfo:M,disabledBatch:y,fileName:u,resolveFile:f,jsonData:h,importBatchTktInfo:oe,isFailure:d,isFileType:_,loading:x,closeLoading:D,formRef:R,batchRefundFormData:v,queryRefundFee:ie}},$b=Tb,Nb=t("i",{class:"iconfont icon-close"},null,-1),Rb=[Nb],Cb={class:"text-[18px] font-[700] text-[var(--bkc-color-gray-1)]"},wb={class:"flex item-center batch-tips h-[36px] p-2 mb-[10px] border border-solid border-yellow-2 rounded-[4px] bg-yellow-3 text-yellow-1"},Ab=t("em",{class:"iconfont icon-warning-circle-fill mr-[8px] leading-5"},null,-1),Sb={class:"text-xs leading-5"},Db={class:"flex justify-between"},Pb=t("i",{class:"icon-plus-square iconfont text-[var(--bkc-el-color-primary)]"},null,-1),Ob={key:0,class:"px-[5px] py-[10px] text-[var(--bkc-color-special-red-1)]"},Eb={key:1,class:"px-[5px] py-[10px] text-[var(--bkc-color-special-red-1)]"},Fb={class:"p-[10px] pl-[10px] border border-solid border-[var(--bkc-theme-2)] rounded-[8px] mt-[20px] mb-[10px]"},Vb={class:"max-h-[450px] overflow-y-scroll batch-refund-form"},Mb={class:"flex text-[12px] mb-[5px]"},jb={class:"w-[140px] mr-[40px] text-[var(--bkc-color-gray-1)]"},Lb=t("span",{class:"text-[var(--bkc-color-special-red-1)]"},"*",-1),Bb={class:"w-[60px] mr-[40px] text-[var(--bkc-color-gray-1)]"},Ib=t("span",{class:"text-[var(--bkc-color-special-red-1)]"},"*",-1),Ub={class:"w-[120px] mr-[40px] text-[var(--bkc-color-gray-1)]"},Qb=t("span",{class:"text-[var(--bkc-color-special-red-1)]"},"*",-1),qb={class:"w-[100px] mr-[40px] text-[var(--bkc-color-gray-1)]"},zb={class:"w-[100px] mr-[40px] text-[var(--bkc-color-gray-1)]"},Gb={class:"w-[192px] text-[var(--bkc-color-gray-1)]"},Hb={key:0,class:"w-5"},Wb={class:"w-[100px] h-8 mr-[40px] justify-start items-center inline-flex"},Yb={key:0,class:"text-center text-gray-2 text-xs font-normal leading-tight"},Kb={key:1,class:"text-center text-gray-2 text-xs font-normal leading-tight"},Xb={key:2,class:"text-center text-gray-2 text-xs font-normal leading-tight"},Jb={class:"w-[100px] h-8 mr-[40px] justify-start items-center inline-flex"},Zb={key:0,class:"text-center text-gray-2 text-xs font-normal leading-tight"},ex={key:1,class:"text-center text-gray-2 text-xs font-normal leading-tight"},tx={class:"w-[192px] h-8 justify-start items-center inline-flex"},nx={key:0,class:"text-red-1 text-xs font-bold leading-tight"},ax={class:"max-w-[400px] max-h-[200px] overflow-y-auto"},sx={key:0},ox={class:"flex align-center cursor-pointer"},ix={class:"text-red-1 text-xs font-bold leading-tight"},lx={key:2,class:"text-red-1 text-xs font-bold leading-tight"},rx=["onClick"],cx=t("em",{class:"iconfont icon-delete text-[20px] relative text-brand-2"},null,-1),ux=[cx],dx={key:0,class:"w-full text-center text-[var(--bkc-el-color-primary)] cursor-pointer"},px={class:"crs-btn-dialog-ui"},fx={class:"flex items-center"},mx=t("div",{class:"iconfont icon-info-circle-line text-brand-2 mr-[16px]"},null,-1),gx={class:"text-lg text-gray-1"},kx=je({__name:"BatchRefundDialog",emits:["handleBatchRefund","update:modelValue"],setup(a,{expose:o,emit:u}){const h=u,{scopeType:f,showQueryScope:p,disabledBatchRefund:i,batchDataRule:_,confirmBatchRefund:d,closeBatchRefund:x,deleteBatchTktInfo:T,addBatchTktInfo:k,disabledBatch:R,fileName:v,isFailure:r,isFileType:y,resolveFile:C,importBatchTktInfo:$,closeLoading:P,loading:M,formRef:X,batchRefundFormData:ue,queryRefundFee:E}=$b(h);return o({closeLoading:P}),(j,oe)=>{const D=gt,b=Ho,N=Ke,ne=aa,ie=ot,U=An,A=Je,le=yt,he=it,ce=Pt,L=Ot,se=nt,Q=kt;return s(),ae(se,{"close-on-press-escape":!1,"close-on-click-modal":!1,"show-close":!1,class:"batch-refund-dialog tc-input-pad-init","align-center":!0,width:"1040px",onClose:e(x)},{header:l(()=>[t("span",Cb,n(j.$t("app.batchRefund.batchRefund")),1)]),footer:l(()=>[t("span",px,[Qe((s(),ae(N,{disabled:e(R),type:"primary","data-gid":"091U0101",onClick:oe[2]||(oe[2]=S=>e(E)(!0))},{default:l(()=>[K(n(j.$t("app.batchRefund.checkTheRefundFee")),1)]),_:1},8,["disabled"])),[[Q,e(M),void 0,{fullscreen:!0,lock:!0}]]),Qe((s(),ae(N,{"data-gid":"091U0104",disabled:e(i),type:"primary",onClick:e(d)},{default:l(()=>[K(n(j.$t("app.batchRefund.batchRefundBtn")),1)]),_:1},8,["disabled","onClick"])),[[Q,e(M),void 0,{fullscreen:!0,lock:!0}]]),c(N,{onClick:e(x)},{default:l(()=>[K(n(j.$t("app.batchRefund.cancel")),1)]),_:1},8,["onClick"])])]),default:l(()=>[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:oe[0]||(oe[0]=(...S)=>e(x)&&e(x)(...S))},Rb),t("div",wb,[Ab,t("span",Sb,n(j.$t("app.batchRefund.batchRefundTips")),1)]),t("div",Db,[c(b,{accept:".xlsx, .xls",action:"","show-file-list":!1,"auto-upload":!1,"on-change":e(C)},{default:l(()=>[c(D,{modelValue:e(v),"onUpdate:modelValue":oe[1]||(oe[1]=S=>Ve(v)?v.value=S:null),placeholder:j.$t("app.batchRefund.pleaseSelectImportFile"),class:Se(["mr-[10px]","cursor-pointer",e(r)||!e(y)?"upload-box-error":""])},{suffix:l(()=>[Pb]),_:1},8,["modelValue","placeholder","class"])]),_:1},8,["on-change"]),c(bb),c(N,{disabled:!e(v),type:"primary",onClick:e($)},{default:l(()=>[K(n(j.$t("app.batchRefund.importBtn")),1)]),_:1},8,["disabled","onClick"])]),e(r)?(s(),m("span",Ob,n(j.$t("app.batchRefund.importFailureTips")),1)):Z("",!0),e(y)?Z("",!0):(s(),m("span",Eb,n(j.$t("app.batchRefund.fileTypeTips")),1)),c(ne,{"border-style":"dashed"}),t("div",Fb,[t("div",Vb,[t("div",Mb,[t("p",jb,[K(n(j.$t("app.batchRefund.ticketNo")),1),Lb]),t("p",Bb,[K(n(j.$t("app.refundForm.ticketType")),1),Ib]),t("p",Ub,[K(n(j.$t("app.batchRefund.printNo")),1),Qb]),t("p",qb,n(j.$t("app.batchRefund.agencyFeeRate")),1),t("p",zb,n(j.$t("app.batchRefund.commission")),1),t("p",Gb,n(j.$t("app.batchRefund.refundTotal")),1),e(ue).batchTkt.length>1?(s(),m("p",Hb)):Z("",!0)]),c(he,{ref_key:"formRef",ref:X,model:e(ue)},{default:l(()=>[(s(!0),m(ye,null,Ne(e(ue).batchTkt,(S,G)=>(s(),m("div",{key:G,class:"flex"},[c(ie,{class:"inline-flex mr-[40px] mb-[10px] w-[140px]",prop:"batchTkt."+G+".ticketNo",rules:e(_).ticketNo},{default:l(()=>[c(D,{modelValue:S.ticketNo,"onUpdate:modelValue":J=>S.ticketNo=J,modelModifiers:{trim:!0},clearable:""},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"]),c(ie,{class:"inline-flex mr-[40px] mb-[10px] w-[60px]",prop:"batchTkt."+G+".domestic"},{default:l(()=>[c(U,{modelValue:S.domestic,"onUpdate:modelValue":J=>S.domestic=J,"inline-prompt":"","active-text":j.$t("app.issue.dom"),"inactive-text":j.$t("app.issue.intr")},null,8,["modelValue","onUpdate:modelValue","active-text","inactive-text"])]),_:2},1032,["prop"]),c(ie,{class:"inline-flex mr-[40px] w-[120px] mb-[10px]",prop:"batchTkt."+G+".voteCounterNo",rules:e(_).voteCounter},{default:l(()=>[(s(),ae(Kt,{key:S.voteCounterNo+G,modelValue:S.voteCounterNo,"onUpdate:modelValue":[J=>S.voteCounterNo=J,J=>{var z;return(z=e(X))==null?void 0:z.validateField(`batchTkt.${G}.voteCounterNo`)}],modelModifiers:{trim:!0},"select-class":"w-[140px]"},null,8,["modelValue","onUpdate:modelValue"]))]),_:2},1032,["prop","rules"]),t("div",Wb,[!S.commissionAmount&&!S.commissionRate?(s(),m("div",Yb,"-")):Number(S.commissionAmount)>0||Number(S.commissionRate)===0?(s(),m("div",Kb,n(S.currency)+" "+n(Number(S.commissionAmount).toFixed(2)),1)):(s(),m("div",Xb,n(S.commissionRate)+"%",1))]),t("div",Jb,[S.otherDeduction?(s(),m("div",ex,n(S.currency)+" "+n(Number(S.otherDeduction).toFixed(2)),1)):(s(),m("div",Zb,"-"))]),t("div",tx,[!S.netRefund&&!S.failure?(s(),m("div",nx,"-")):S.failure?(s(),ae(le,{key:1,placement:"top"},{content:l(()=>[t("div",ax,[t("div",null,n(j.$t("app.batchRefund.errorCode"))+n(S.errorCode||"--"),1),t("div",null,n(j.$t("app.batchRefund.description"))+n(S.description||"--"),1),t("div",null,n(j.$t("app.batchRefund.transactionNo"))+n(S.transactionNo||"--"),1),S.satTransactionNo?(s(),m("div",sx,n(j.$t("app.batchRefund.satTransactionNo"))+n(S.satTransactionNo||"--"),1)):Z("",!0)])]),default:l(()=>[t("span",ox,[c(A,{class:"mr-1"},{default:l(()=>[c(e(jn),{class:"text-base leading-tight text-red-1 cursor-pointer"})]),_:1}),t("span",ix,n(j.$t("app.batchRefund.countFailed")),1)])]),_:2},1024)):(s(),m("div",lx,n(S.currency)+" "+n(Number(S.netRefund).toFixed(2)),1))]),e(ue).batchTkt.length>1?(s(),m("div",{key:0,class:"cursor-pointer w-5 h-8 justify-start items-center gap-2.5 inline-flex",onClick:J=>e(T)(G)},ux,8,rx)):Z("",!0)]))),128))]),_:1},8,["model"]),e(ue).batchTkt.length<20?(s(),m("div",dx,[c(N,{link:"",type:"primary",onClick:e(k)},{default:l(()=>[K(n(j.$t("app.batchRefund.addTicketNo")),1)]),_:1},8,["onClick"])])):Z("",!0)])]),c(se,{modelValue:e(p),"onUpdate:modelValue":oe[5]||(oe[5]=S=>Ve(p)?p.value=S:null),class:"scope-dialog","show-close":!1,width:"500px",top:"40vh","close-on-click-modal":!0},{footer:l(()=>[c(L,{modelValue:e(f),"onUpdate:modelValue":oe[3]||(oe[3]=S=>Ve(f)?f.value=S:null)},{default:l(()=>[c(ce,{label:"part",size:"large"},{default:l(()=>[K(n(j.$t("app.batchRefund.partialTicketsd")),1)]),_:1}),c(ce,{label:"all",size:"large",class:"mr-[20px]"},{default:l(()=>[K(n(j.$t("app.batchRefund.allTicket")),1)]),_:1})]),_:1},8,["modelValue"]),Qe((s(),ae(N,{type:"primary",onClick:oe[4]||(oe[4]=S=>e(E)(!1))},{default:l(()=>[K(n(j.$t("app.batchRefund.checkTheRefundFee")),1)]),_:1})),[[Q,e(M),void 0,{fullscreen:!0,lock:!0}]])]),default:l(()=>[t("div",fx,[mx,t("div",gx,n(j.$t("app.batchRefund.queryScope")),1)])]),_:1},8,["modelValue"])]),_:1},8,["onClose"])}}});const yx=a=>{const{t:o}=We(),u=tt({refundFormNumber:"",deviceNum:""}),h=H(),f={refundFormNumber:[{required:!0,message:o("app.agentTicketQuery.rtNumEmpty"),trigger:"change"},{pattern:Xs,message:o("app.agentTicketQuery.rtNumError"),trigger:"change"}],deviceNum:[{required:!0,message:o("app.agentTicketRefund.prntNoNotEmpty"),trigger:"change"},{pattern:Gt,message:o("app.agentTicketRefund.onlySupportDigits"),trigger:"change"}]};return{bopFromData:u,rules:f,bopFrom:h,reFundBop:async()=>{h.value&&await h.value.validate(async _=>{var d;if(_){const x=Mt.service({fullscreen:!0});try{const T=Pe("091U0103");((d=(await Ro(u,T)).data.value)==null?void 0:d.resCode)==="SUCCESS"&&a("bopRefundSuccess")}finally{x.close()}}})},cancel:()=>{a("update:modelValue",!1)}}},hx=yx,vx=t("i",{class:"iconfont icon-close"},null,-1),_x=[vx],bx={class:"flex item-center justify-start h-[36px] px-2.5 py-2 mt-[20px] mb-[10px] bg-yellow-3 rounded border border-solid border-yellow-2 text-yellow-1"},xx=t("em",{class:"iconfont icon-warning-circle-fill leading-5"},null,-1),Tx={class:"ml-[5px] text-xs leading-5"},$x={class:"mt-[10px] px-[100px]"},Nx={class:"flex justify-center crs-btn-dialog-ui"},Rx=je({__name:"BopRefundDialog",emits:["bopRefundSuccess","update:modelValue"],setup(a,{emit:o}){const u=o,{bopFromData:h,rules:f,bopFrom:p,reFundBop:i,cancel:_}=hx(u);return(d,x)=>{const T=gt,k=ot,R=Ke,v=it,r=nt;return s(),ae(r,{title:d.$t("app.agentTicketQuery.bopRefund"),"close-on-press-escape":!1,"close-on-click-modal":!1,"show-close":!1,"align-center":"true",class:"preview-dialog bop-preview-dialog tc-input-pad-init",width:"680px",onClose:e(_)},{default:l(()=>[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:x[0]||(x[0]=(...y)=>e(_)&&e(_)(...y))},_x),t("div",null,[t("div",bx,[xx,t("span",Tx,n(d.$t("app.agentTicketQuery.bopRefundTips")),1)]),t("div",$x,[c(v,{ref_key:"bopFrom",ref:p,rules:e(f),"require-asterisk-position":"right",model:e(h)},{default:l(()=>[c(k,{label:d.$t("app.agentTicketQuery.rtNum"),prop:"refundFormNumber"},{default:l(()=>[c(T,{modelValue:e(h).refundFormNumber,"onUpdate:modelValue":x[1]||(x[1]=y=>e(h).refundFormNumber=y),modelModifiers:{trim:!0},onInput:x[2]||(x[2]=y=>{var C;return e(h).refundFormNumber=((C=e(h).refundFormNumber)==null?void 0:C.toUpperCase())??""})},null,8,["modelValue"])]),_:1},8,["label"]),c(k,{label:d.$t("app.agentTicketQuery.repelTicket.ticketMachineNumber"),prop:"deviceNum"},{default:l(()=>[c(Kt,{modelValue:e(h).deviceNum,"onUpdate:modelValue":[x[3]||(x[3]=y=>e(h).deviceNum=y),x[4]||(x[4]=y=>{var C;return(C=e(p))==null?void 0:C.validateField("deviceNum")})],modelModifiers:{trim:!0},"select-class":"w-[382.54px]"},null,8,["modelValue"])]),_:1},8,["label"]),t("div",Nx,[c(R,{"data-gid":"091U0103",type:"primary",onClick:x[5]||(x[5]=y=>e(i)())},{default:l(()=>[K(n(d.$t("app.ticketStatus.confirmBtn")),1)]),_:1}),c(R,{onClick:e(_)},{default:l(()=>[K(n(d.$t("app.ticketStatus.cancelBtn")),1)]),_:1},8,["onClick"])])]),_:1},8,["rules","model"])])])]),_:1},8,["title","onClose"])}}});const Cx=a=>{const{t:o}=We(),u=Ct(),h=H(!0),f=H(),p=H(""),i=H(""),_=H(!1),d=H(),x=H(),T=H("1"),k=H(!1),R=H(!0),v=H(),r=H(!1),y=H({authLevel:"oneLevel",office:"",ticketNo:"",ticketNoEnd:""}),C=Me(()=>{var se;return((se=u.state.user)==null?void 0:se.agent)??""}),$=async()=>{var se,Q,S,G,J,z,w,g,B,F;try{const de=Pe("091N0206");r.value=!0;const ve={ticketNumber:y.value.ticketNo},O=await ja(ve,de);((se=O.data.value)==null?void 0:se.code)==="200"?(p.value=((G=(S=(Q=O==null?void 0:O.data)==null?void 0:Q.value)==null?void 0:S.data.ticketDisplayAuthInfo)==null?void 0:G.bookOffice)??"",i.value=((w=(z=(J=O==null?void 0:O.data)==null?void 0:J.value)==null?void 0:z.data.ticketDisplayAuthInfo)==null?void 0:w.ticketNumber)??"",f.value=(F=(B=(g=O==null?void 0:O.data)==null?void 0:g.value)==null?void 0:B.data.ticketDisplayAuthInfo)==null?void 0:F.authInfoList.filter(fe=>fe.authTo!=="")):k.value=!1}finally{r.value=!1}},P=async se=>{var Q;try{r.value=!0;const S={removeOffice:se,ticketNumber:y.value.ticketNo},G=Pe("091N0102");((Q=(await Ln(S,G)).data.value)==null?void 0:Q.code)==="200"&&(await mt({message:o("app.intlPassengerForm.del.delSuccess"),type:"success"}),await $())}finally{r.value=!1}},M=()=>{_.value=!0},X=async()=>{var se,Q;try{r.value=!0;let S=!0;y.value.authLevel==="oneLevel"?S=!1:S=!0;const G={accreditOffice:y.value.office,reAuth:S,ticketNumber:y.value.ticketNo},J=Pe("091N0201"),z=await Bn(G,J);((Q=(se=z==null?void 0:z.data)==null?void 0:se.value)==null?void 0:Q.code)==="200"&&(await mt({message:o("app.agentTicketQuery.ticketAuth.addAuthSuccess"),type:"success"}),await $())}finally{r.value=!1}},ue=async se=>{se&&await se.validate(async Q=>{Q&&X()})},E=(se,Q,S)=>{var w;const G=(w=y.value.ticketNo)==null?void 0:w.replace("-",""),J=G==null?void 0:G.substring(G.length-3),z=parseInt(y.value.ticketNoEnd,10)-parseInt(J,10);if(z<0||z>29){S(o("app.agentTicketQuery.ticketAuth.lastThreeDigitsTips"));return}S()},j={office:[{required:!0,message:o("app.agentTicketQuery.ticketAuth.officeTipOne"),trigger:"blur"},{pattern:Tn,message:o("app.agentTicketQuery.ticketAuth.officeTipTwo")}],ticketNo:[{required:!0,message:o("app.agentTicketQuery.ticketAuth.ticketTipOne"),trigger:"blur"},{pattern:/(^\d{3}(-)\d{10}$)|(^\d{13}$)/,message:o("app.agentTicketQuery.ticketAuth.ticketTipTwo"),trigger:"blur"}],ticketNoEnd:[{pattern:/^[0-9]{3}$/,message:o("app.agentTicketQuery.ticketAuth.ticketNoEndTipOne")},{validator:E,trigger:"blur"}]},oe=()=>{var se;k.value=!1,(se=v.value)==null||se.resetFields(),y.value.office="",y.value.authLevel="oneLevel",y.value.ticketNoEnd=""},D=async()=>{try{r.value=!0,k.value=!0,await $()}finally{r.value=!1}},b=async()=>{k.value=!1},N=()=>{const se=[],Q=y.value.ticketNo.replace("-","");if(se.push(Q),!y.value.ticketNoEnd)return se;const S=parseInt(Q.substring(10),10),J=parseInt(y.value.ticketNoEnd,10)-S+1;for(let z=1;z<J;z++){const w=(Number(Q)+z).toString();se.push(w)}return se},ne=async()=>{try{const se=Pe("091N0204");r.value=!0;const Q=[];(N()??[]).forEach(J=>{const z={accreditOffice:y.value.office,reAuth:y.value.authLevel==="twoLevel",ticketNumber:J};Q.push(Bn(z,se))}),(await Promise.allSettled(Q)).filter(J=>J.value.data.value.code!=="200").length<1&&(await mt({message:o("app.agentTicketQuery.ticketAuth.addAuthSuccess"),type:"success"}),await $())}finally{r.value=!1}},ie=async()=>{try{const se=Pe("091N0205");r.value=!0;const Q=[];(N()??[]).forEach(J=>{const z={removeOffice:y.value.office,ticketNumber:J};Q.push(Ln(z,se))}),(await Promise.allSettled(Q)).filter(J=>J.value.data.value.code!=="200").length<1&&mt({message:o("app.intlPassengerForm.del.delSuccess"),type:"success"})}finally{r.value=!1}},U=()=>{var se;(se=v.value)==null||se.validate(Q=>{if(Q)switch(T.value){case"1":ne();break;case"2":ie();break;case"3":D();break}})},A=()=>{a("update:modelValue",!1)},le=()=>`TICKET_AUTH_OFFICE_${C.value}_`??"",he=()=>{try{const se=T.value==="2"?"delete":"add";return(JSON.parse(localStorage.getItem(`${le()}${se}`)??"")||[]).map(S=>({value:S}))}catch{return[]}},ce=()=>{if(Tn.test(y.value.office)){const se=T.value==="2"?"delete":"add",Q=he().map(G=>G.value).filter(G=>G!==y.value.office);Q.unshift(y.value.office);const S=Q.slice(0,5);localStorage.setItem(`${le()}${se}`,JSON.stringify(S))}},L=se=>{var Q;y.value.office=se.value,(Q=x.value)==null||Q.blur()};return wt(()=>y.value.office,se=>{y.value.office=(se==null?void 0:se.toUpperCase())??""}),{authType:T,authForm:y,authFormRules:j,authTicketShow:k,handleConfirm:U,handleCancel:A,ticketAuthFormRef:v,authChange:oe,dialogTableVisible:h,tableData:f,authDelete:P,authSubmit:ue,authTicketFormRef:d,showAdd:M,addAuthVisible:_,office:p,showAuthOffice:R,closeShowAuth:b,ticketNum:i,showLoading:r,officeHistoryRef:x,loadOfficeHistory:he,saveOfficeHistory:ce,selectOfficeHistory:L}},wx=Cx,ua=a=>(bt("data-v-3adb8bea"),a=a(),xt(),a),Ax={class:"pb-[20px]"},Sx=ua(()=>t("i",{class:"iconfont icon-close"},null,-1)),Dx=[Sx],Px={class:"w-full px-0 pt-0.5 pb-[5px] bg-gray-0 rounded-md flex-col justify-start items-center gap-2.5 inline-flex mt-[-12px] query-auth-ticket"},Ox={class:"self-stretch justify-between items-center inline-flex"},Ex={class:"text-gray-1 text-lg font-bold leading-normal"},Fx={class:"self-stretch h-full flex-col justify-center items-center flex"},Vx={class:"self-stretch h-full flex-col justify-center items-center gap-2.5 flex"},Mx={class:"h-full flex-col justify-center items-start gap-5 flex"},jx={class:"self-stretch flex-col justify-center items-start gap-2.5 flex"},Lx={class:"justify-start items-center gap-2.5 inline-flex h-[20px] w-[420px]"},Bx={class:"text-gray-2 text-xs font-normal leading-tight"},Ix={class:"text-gray-2 text-xs font-normal leading-tight"},Ux={class:"text-gray-2 text-xs font-normal leading-tight"},Qx={key:0,class:"justify-start items-center gap-2.5 inline-flex h-[20px] authType"},qx={class:"text-gray-2 text-xs font-normal leading-tight"},zx={class:"text-gray-2 text-xs font-normal leading-tight"},Gx={class:"self-stretch justify-start items-center gap-1.5 inline-flex w-full"},Hx={class:"self-stretch justify-start items-center gap-1.5 inline-flex w-full"},Wx={class:"justify-start items-center flex w-full"},Yx={class:"self-stretch justify-start items-center gap-1.5 inline-flex w-full"},Kx=ua(()=>t("div",{class:"text-gray-2 text-xs font-normal leading-tight"},"-",-1)),Xx={class:"self-stretch justify-center items-center gap-2.5 inline-flex w-full mt-5 crs-btn-dialog-ui"},Jx={class:"justify-start items-start flex"},Zx={key:0,class:"w-full auth-data"},eT={class:"self-stretch h-full flex-col justify-center items-center flex w-full"},tT={class:"flex-col justify-center items-center gap-2.5 inline-flex w-full"},nT={class:"self-stretch justify-between items-center inline-flex"},aT={class:"justify-start items-center gap-[68px] flex"},sT={class:"text-gray-1 text-sm font-normal leading-snug"},oT={class:"text-gray-1 text-sm font-normal leading-snug"},iT={class:"flex-col justify-start items-start inline-flex"},lT={class:"self-stretch p-2.5 rounded border border-brand-3 justify-start items-start inline-flex"},rT={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},cT={class:"self-stretch h-[30px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},uT={class:"grow shrink basis-0 h-[22px] text-gray-4 text-sm font-normal leading-snug"},dT={class:"grow shrink basis-0 h-[22px] text-gray-1 text-sm font-normal leading-snug"},pT={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},fT={class:"self-stretch h-[30px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},mT={class:"grow shrink basis-0 h-[22px] text-gray-4 text-sm font-normal leading-snug"},gT={class:"grow shrink basis-0 h-[22px] text-gray-1 text-sm font-normal leading-snug"},kT={key:0},yT={key:1},hT={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},vT={class:"self-stretch h-[30px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},_T={class:"grow shrink basis-0 h-[22px] text-gray-4 text-sm font-normal leading-snug"},bT=ua(()=>t("i",{class:"cursor-pointer primary-color iconfont icon-delete text-brand-2"},null,-1)),xT={key:0,class:"self-stretch h-[82px] flex-col justify-start items-start gap-3.5 flex add-auth-tkt"},TT={class:"self-stretch h-9 px-2.5 py-2 bg-yellow-3 rounded border border-yellow-2 flex-col justify-start items-start gap-2.5 flex"},$T={class:"h-5 justify-start items-center inline-flex text-yellow-1"},NT={class:"text-xs font-normal leading-tight"},RT={class:"w-[632px] justify-start items-center gap-2.5 inline-flex"},CT={class:"justify-start items-center gap-2.5 flex"},wT={class:"justify-start items-center flex"},AT={class:"text-gray-2 text-xs font-normal leading-tight"},ST={class:"text-gray-2 text-xs font-normal leading-tight"},DT={class:"justify-start items-center flex crs-btn-dialog-ui"},PT=je({__name:"TicketAuth",emits:["update:modelValue"],setup(a,{emit:o}){const u=o,{authType:h,authForm:f,authFormRules:p,handleConfirm:i,ticketAuthFormRef:_,authChange:d,authTicketShow:x,handleCancel:T,tableData:k,authDelete:R,authSubmit:v,authTicketFormRef:r,showAdd:y,addAuthVisible:C,office:$,closeShowAuth:P,ticketNum:M,showAuthOffice:X,showLoading:ue,officeHistoryRef:E,loadOfficeHistory:j,saveOfficeHistory:oe,selectOfficeHistory:D}=wx(u);return(b,N)=>{const ne=Pt,ie=Ot,U=ot,A=Ya,le=Je,he=yt,ce=gt,L=Ke,se=Ea,Q=it,S=aa,G=nt,J=kt;return s(),ae(G,{modelValue:e(X),"onUpdate:modelValue":N[11]||(N[11]=z=>Ve(X)?X.value=z:null),"close-on-click-modal":!1,"align-center":"true",width:"680px",class:"preview-dialog","close-on-press-escape":!1,"show-close":!1,onClose:e(T)},{default:l(()=>[Qe((s(),m("div",Ax,[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:N[0]||(N[0]=(...z)=>e(T)&&e(T)(...z))},Dx),t("div",Px,[t("div",Ox,[t("div",Ex,n(b.$t("app.agentTicketQuery.ticketAuth.title")),1)]),t("div",Fx,[t("div",Vx,[t("div",Mx,[c(Q,{ref_key:"ticketAuthFormRef",ref:_,model:e(f),rules:e(p),"require-asterisk-position":"right",class:"w-[400px] auth-form"},{default:l(()=>[t("div",jx,[t("div",Lx,[c(ie,{modelValue:e(h),"onUpdate:modelValue":N[1]||(N[1]=z=>Ve(h)?h.value=z:null),class:"h-[20px]",onChange:e(d)},{default:l(()=>[c(ne,{label:"1"},{default:l(()=>[t("span",Bx,n(b.$t("app.agentTicketQuery.ticketAuth.addingEntitlement")),1)]),_:1}),c(ne,{label:"2"},{default:l(()=>[t("span",Ix,n(b.$t("app.agentTicketQuery.ticketAuth.deletingEntitlement")),1)]),_:1}),c(ne,{label:"3"},{default:l(()=>[t("span",Ux,n(b.$t("app.agentTicketQuery.ticketAuth.viewingAuthorization")),1)]),_:1})]),_:1},8,["modelValue","onChange"])]),e(h)==="1"?(s(),m("div",Qx,[c(U,null,{default:l(()=>[c(ie,{modelValue:e(f).authLevel,"onUpdate:modelValue":N[2]||(N[2]=z=>e(f).authLevel=z)},{default:l(()=>[c(ne,{label:"oneLevel",size:"large"},{default:l(()=>[t("span",qx,n(b.$t("app.agentTicketQuery.ticketAuth.oneAuthorization")),1)]),_:1}),c(ne,{label:"twoLevel",size:"large"},{default:l(()=>[t("span",zx,n(b.$t("app.agentTicketQuery.ticketAuth.secondaryAuthorization")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1})])):Z("",!0),t("div",{class:Se(["flex-col justify-center items-start gap-2.5 inline-flex w-full",{"mt-[-14px] authType-3":e(h)==="3"}])},[t("div",Gx,[e(h)==="1"||e(h)==="2"?(s(),ae(U,{key:0,class:"w-full",label:b.$t("app.agentTicketQuery.ticketAuth.officeNum"),prop:"office"},{default:l(()=>[c(A,{ref_key:"officeHistoryRef",ref:E,modelValue:e(f).office,"onUpdate:modelValue":N[3]||(N[3]=z=>e(f).office=z),modelModifiers:{trim:!0},"fetch-suggestions":e(j),placeholder:b.$t("app.agentTicketQuery.ticketAuth.officeTipOne"),class:"w-full",debounce:"",onSelect:e(D),onBlur:e(oe)},null,8,["modelValue","fetch-suggestions","placeholder","onSelect","onBlur"])]),_:1},8,["label"])):Z("",!0)]),t("div",Hx,[t("div",Wx,[c(U,{label:b.$t("app.agentTicketQuery.ticketAuth.ticketNo"),class:"ticket-no w-full ticketNo-div",prop:"ticketNo"},{default:l(()=>[t("div",Yx,[e(h)!=="3"?(s(),ae(he,{key:0,content:e(h)==="1"?b.$t("app.agentTicketQuery.ticketAuth.addTips"):b.$t("app.agentTicketQuery.ticketAuth.addTipsThree"),placement:"top-start","popper-class":"ticket-auth-popper"},{default:l(()=>[c(le,{class:"tooltip-icon ml-[-4px] mr-[13px] rotate-180"},{default:l(()=>[c(e(un))]),_:1})]),_:1},8,["content"])):Z("",!0),c(ce,{modelValue:e(f).ticketNo,"onUpdate:modelValue":N[4]||(N[4]=z=>e(f).ticketNo=z),modelModifiers:{trim:!0},class:"w-full",placeholder:b.$t("app.agentTicketQuery.ticketAuth.ticketTipOne")},null,8,["modelValue","placeholder"])])]),_:1},8,["label"])]),e(h)==="1"||e(h)==="2"?(s(),m(ye,{key:0},[Kx,c(U,{label:"",class:"ticket-no-end flex-nowrap",prop:"ticketNoEnd"},{default:l(()=>[c(ce,{modelValue:e(f).ticketNoEnd,"onUpdate:modelValue":N[5]||(N[5]=z=>e(f).ticketNoEnd=z),modelModifiers:{trim:!0},placeholder:b.$t("app.agentTicketQuery.ticketAuth.ticketNoEnd"),maxlength:"3"},null,8,["modelValue","placeholder"])]),_:1})],64)):Z("",!0)])],2)]),t("div",Xx,[t("div",Jx,[e(h)==="2"?(s(),ae(se,{key:0,title:b.$t("app.agentTicketQuery.ticketAuth.removeConfirm"),teleported:!1,width:"160",icon:e(un),"icon-color":"var(--bkc-el-color-primary)",placement:"top","cancel-button-type":"button","data-gid":"091N0205",onConfirm:e(i)},{reference:l(()=>[c(L,{type:"primary"},{default:l(()=>[K(n(b.$t("app.agentTicketQuery.ticketAuth.confirm")),1)]),_:1})]),_:1},8,["title","icon","onConfirm"])):(s(),ae(L,{key:1,type:"primary",onClick:e(i)},{default:l(()=>[K(n(b.$t("app.agentTicketQuery.ticketAuth.confirm")),1)]),_:1},8,["onClick"])),c(L,{class:Se([e(h)==="2"?"ml-[12px]":""]),onClick:e(T)},{default:l(()=>[K(n(b.$t("app.agentTicketQuery.ticketAuth.cancel")),1)]),_:1},8,["class","onClick"])])])]),_:1},8,["model","rules"])])]),e(x)&&e(k)?(s(),m("div",Zx,[t("div",eT,[c(S,{"border-style":"dashed"}),t("div",tT,[t("div",nT,[t("div",aT,[t("div",sT,n(b.$t("app.agentTicketQuery.ticketAuth.ticketNo"))+"："+n(e(M)),1),t("div",oT,n(b.$t("app.agentTicketQuery.ticketAuth.ticketingOfficeNumber"))+"："+n(e($)),1)]),t("div",iT,[t("div",{class:"justify-center items-center gap-1 inline-flex",onClick:N[6]||(N[6]=(...z)=>e(y)&&e(y)(...z))},[c(L,{link:"",type:"primary",size:"small"},{default:l(()=>[K(n(b.$t("app.agentTicketQuery.ticketAuth.addingEntitlement")),1)]),_:1})])])]),t("div",lT,[t("div",rT,[t("div",cT,[t("div",uT,n(b.$t("app.agentTicketQuery.ticketAuth.officeNum")),1)]),(s(!0),m(ye,null,Ne(e(k),(z,w)=>(s(),m("div",{key:w,class:"self-stretch h-[50px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},[t("div",dT,n(z.authTo),1)]))),128))]),t("div",pT,[t("div",fT,[t("div",mT,n(b.$t("app.agentTicketQuery.ticketAuth.authAuthority")),1)]),(s(!0),m(ye,null,Ne(e(k),(z,w)=>(s(),m("div",{key:w,class:"self-stretch h-[50px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},[t("div",gT,[z.reAuth?(s(),m("span",kT,[c(le,null,{default:l(()=>[c(e(Wn))]),_:1})])):(s(),m("span",yT,n(b.$t("app.agentTicketQuery.ticketAuth.noAuth")),1))])]))),128))]),t("div",hT,[t("div",vT,[t("div",_T,n(b.$t("app.agentTicketQuery.ticketAuth.operation")),1)]),(s(!0),m(ye,null,Ne(e(k),(z,w)=>(s(),m("div",{key:w,class:"self-stretch h-[50px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-5 inline-flex"},[c(se,{title:b.$t("app.agentTicketQuery.ticketAuth.removeConfirm"),icon:e(un),teleported:!1,width:"160","icon-color":"var(--bkc-el-color-primary)","cancel-button-type":"button",onConfirm:g=>e(R)(z.authTo)},{reference:l(()=>[bT]),_:2},1032,["title","icon","onConfirm"])]))),128))])]),e(C)?(s(),m("div",xT,[t("div",TT,[t("div",$T,[c(le,{class:"tooltip-icon mr-[8px]"},{default:l(()=>[c(e(Hn))]),_:1}),t("div",NT,n(b.$t("app.agentTicketQuery.ticketAuth.addTipsTwo")),1)])]),t("div",RT,[t("div",CT,[c(Q,{ref_key:"authTicketFormRef",ref:r,model:e(f),inline:"",size:"small",rules:e(p),"require-asterisk-position":"right"},{default:l(()=>[c(U,null,{default:l(()=>[c(ie,{modelValue:e(f).authLevel,"onUpdate:modelValue":N[7]||(N[7]=z=>e(f).authLevel=z)},{default:l(()=>[t("div",wT,[c(ne,{label:"oneLevel",size:"large"},{default:l(()=>[t("span",AT,n(b.$t("app.agentTicketQuery.ticketAuth.oneAuthorization")),1)]),_:1}),c(ne,{label:"twoLevel",size:"large"},{default:l(()=>[t("span",ST,n(b.$t("app.agentTicketQuery.ticketAuth.secondaryAuthorization")),1)]),_:1})])]),_:1},8,["modelValue"])]),_:1}),c(U,{label:b.$t("app.agentTicketQuery.ticketAuth.officeNum"),prop:"office"},{default:l(()=>[c(A,{ref_key:"officeHistoryRef",ref:E,modelValue:e(f).office,"onUpdate:modelValue":N[8]||(N[8]=z=>e(f).office=z),modelModifiers:{trim:!0},"fetch-suggestions":e(j),placeholder:b.$t("app.agentTicketQuery.ticketAuth.officeTipOne"),class:"w-full",debounce:"",onSelect:e(D),onBlur:e(oe)},null,8,["modelValue","fetch-suggestions","placeholder","onSelect","onBlur"])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),t("div",DT,[e(C)?(s(),ae(L,{key:0,type:"primary","data-gid":"091N0101",onClick:N[9]||(N[9]=z=>e(v)(e(r)))},{default:l(()=>[K(n(b.$t("app.agentTicketQuery.ticketAuth.confirm")),1)]),_:1})):Z("",!0),e(C)?(s(),ae(L,{key:1,onClick:N[10]||(N[10]=z=>e(P)())},{default:l(()=>[K(n(b.$t("app.agentTicketQuery.ticketAuth.cancel")),1)]),_:1})):Z("",!0)])])])):Z("",!0)])])])):Z("",!0)])])])),[[J,e(ue)]])]),_:1},8,["modelValue","onClose"])}}});const OT=at(PT,[["__scopeId","data-v-3adb8bea"]]),ET={class:"flex justify-between"},FT={class:"mt-[20px] h-[476px]"},VT={key:1,class:"flex justify-center items-center flex-col pt-[156px]"},MT=["alt"],jT={class:"mt-[19px] text-lg leading-[24px] font-bold text-gray-2"},LT={class:"text-base text-gray-4"},BT=je({__name:"RtktDialog",emits:["update:modelValue"],setup(a,{emit:o}){const u=o,{t:h}=We(),f=H(!1),p=H(),i=Ae({});let _=[];const d={ticketNo:[{required:!0,message:h("app.agentTicketQuery.validate.tktNoNull"),trigger:"blur"},{pattern:Rn,message:h("app.agentTicketQuery.validate.tktNoError"),trigger:"blur"}]},x=tt({ticketNo:""}),T=async()=>{const y=await Aa("searchLocalData"),C=[];(JSON.parse(y.localData??"")??[]).forEach($=>{const P={code:$.airportCode,cnName:$.cityArray[1],enName:$.cityArray[0]};C.push(P)}),_=C},k=y=>((y??[]).forEach((C,$)=>{_==null||_.forEach(P=>{P.code===C.departureCity&&(y[$]=Object.assign(y[$],{departureCityName:P.cnName})),P.code===C.arrivalCity&&(y[$]=Object.assign(y[$],{arrivalCityName:P.cnName}))})}),y),R=()=>{const{passenger:y}=i.value;y&&(y.segments=k(y==null?void 0:y.segments))},v=()=>{var y;(y=p.value)==null||y.validate(async C=>{if(C)try{f.value=!0;const $=Pe("091U0105"),{data:P}=await Co(x.ticketNo,$);i.value=P.value??{},await R()}catch{i.value={}}finally{f.value=!1}})},r=()=>{var y;(y=p.value)==null||y.resetFields()};return st(async()=>{await T()}),(y,C)=>{const $=gt,P=ot,M=Ke,X=it,ue=nt,E=kt;return s(),ae(ue,{title:"RTKT",width:"890px","close-on-click-modal":!1,"align-center":!0,class:"repel-tikect-dialog tc-input-pad-init crs-new-ui-init-cls",onClose:C[2]||(C[2]=()=>u("update:modelValue",!1))},{default:l(()=>{var j,oe;return[t("div",ET,[c(X,{ref_key:"formRef",ref:p,model:x,rules:d,inline:!0,"require-asterisk-position":"right"},{default:l(()=>[c(P,{prop:"ticketNo",label:e(h)("app.agentTicketQuery.ticketAuth.ticketNo")},{default:l(()=>[c($,{modelValue:x.ticketNo,"onUpdate:modelValue":C[0]||(C[0]=D=>x.ticketNo=D),modelModifiers:{trim:!0},clearable:!0},null,8,["modelValue"])]),_:1},8,["label"]),c(P,null,{default:l(()=>[c(M,{type:"primary","data-gid":"091U0105",onClick:v},{default:l(()=>[K(n(y.$t("app.agentTicketQuery.queryBtn")),1)]),_:1})]),_:1}),c(P,null,{default:l(()=>[c(M,{onClick:r},{default:l(()=>[K(n(y.$t("app.agentTicketQuery.reset")),1)]),_:1})]),_:1})]),_:1},8,["model"]),(j=i.value)!=null&&j.ticket?(s(),ae(Ao,{key:0,"rtkt-detailed-info":i.value,onClick:C[1]||(C[1]=D=>u("update:modelValue",!1))},null,8,["rtkt-detailed-info"])):Z("",!0)]),Qe((s(),m("div",FT,[(oe=i.value)!=null&&oe.ticket?(s(),ae(So,{key:0,"rtkt-detailed-info":i.value},null,8,["rtkt-detailed-info"])):(s(),m("div",VT,[t("img",{src:ia,alt:y.$t("app.fastQuery.netPrice.nodata")},null,8,MT),t("p",jT,n(y.$t("app.fastQuery.netPrice.noOrderInfo")),1),t("p",LT,n(y.$t("app.agentTicketQuery.plsInputTicketToSearch")),1)]))])),[[E,f.value]])]}),_:1})}}});const St=Ca.global.t,ss=[{label:St("app.agentTicketRefund.pay.cash"),value:"CASH"},{label:St("app.agentTicketRefund.pay.tc"),value:"TC"},{label:St("app.agentTicketRefund.pay.check"),value:"CHECK"},{label:St("app.agentTicketRefund.pay.gr"),value:"GR"},{label:St("app.agentTicketRefund.pay.ef"),value:"EF"}];St("app.agentTicketRefund.passenger.adt"),St("app.agentTicketRefund.passenger.chd"),St("app.agentTicketRefund.passenger.chd"),St("app.agentTicketRefund.passenger.yth"),St("app.agentTicketRefund.passenger.inf");const IT=[{label:St("app.pnrManagement.paymentMethod.domestic"),value:"D"},{label:St("app.pnrManagement.paymentMethod.international"),value:"I"}],UT="TYN202",QT="UP6225819300614137",qT=a=>{var Te;const{t:o}=We(),u=Ct(),h=27,f=H("ONLY_REFUND"),p=H(""),i=H(!1),_=H(),{defaultOffice:d,office:x,defaultRoleWithPid:T,agent:k,defaultOfficeIataNum:R}=u.state.user,v=T?d:((Te=x==null?void 0:x.split(";"))==null?void 0:Te[0])??"",r=H([]),y=H([]),C={BSP:{label:o("app.agentTicketQuery.BSPTicket"),value:"BSP"},GPBSP:{label:o("app.agentTicketQuery.GPTicket"),value:"GPBSP"},BOPBSP:{label:o("app.agentTicketQuery.BOPTicket"),value:"BOPBSP"},CDS:{label:o("app.agentTicketQuery.CDSTicket"),value:"CDS"},GPCDS:{label:o("app.agentTicketQuery.GP_CDSTicket"),value:"GPCDS"},ARL:{label:o("app.agentTicketQuery.OWNTicket"),value:"ARL"}},$=tt({iata:R,agent:k,office:v,volunteer:"",createUser:"",printNo:"",check:"",marketAirline:"",currency:"",name:"",psgType:"",etTag:"1",remark:"",remarkCode:"",remarkInfo:"",creditCard:"",conjunction:"",airline:"",tktType:"D",payType:"CASH",ticketNo:"",totalAmount:"",commision:"",commisionRate:"",otherDeduction:"",otherDeductionRate:"",netRefund:"0.00",totalTaxs:"",taxs:[],rate:"",receiptPrinted:"",segment:[],crsPnrNo:"",pnr:"",isCoupon:"",isDragonBoatOffice:d===UT,refundDate:et().format("DDMMMYY/HHmm").toUpperCase(),conjunctionTicketNos:[],ticketManagementOrganizationCode:""}),P=Me(()=>{const V=`${$.airline}${$.ticketNo}`;return!Us.test(V)}),M=Me(()=>{var V;return(V=u.state.user)==null?void 0:V.entityType}),X=Me(()=>!["CDS","GPCDS"].includes($.ticketManagementOrganizationCode)),ue=()=>{X.value||($.printNo="")},E=(V,ge,me)=>{var Oe;const Re=Number(V.field.split(".")[1]);$.taxs[Re].name&&!ge?me(o("app.agentTicketRefund.taxAmount")):!$.taxs[Re].name&&!ge&&((Oe=_.value)==null||Oe.clearValidate(`taxs.${Re}.name`),me()),me()},j=(V,ge,me)=>{var Oe;const Re=Number(V.field.split(".")[1]);$.taxs[Re].value&&!ge?me(o("app.agentTicketRefund.taxes")):!$.taxs[Re].value&&!ge&&((Oe=_.value)==null||Oe.clearValidate(`taxs.${Re}.value`),me()),me()},oe=(V,ge,me)=>{$.payType==="TC"&&(ge?!$.isDragonBoatOffice&&!Kn.test(ge)?me(o("app.agentTicketRefund.creditCardInput")):$.isDragonBoatOffice&&!Xn.test(ge)&&me(o("app.agentTicketRefund.dragonBoatOfficeInput")):me(o("app.agentTicketRefund.creditCardNotEmpty"))),me()},D=()=>(r.value=new Array(4).fill(""),$.segment.some(V=>V)?$.segment.forEach((V,ge)=>{V&&!no.test(V)&&(r.value[ge]=o("app.agentTicketRefund.onlySupportFixedQuantityDigits",{num:4}))}):r.value[0]=o("app.pnrManagement.validate.required"),r.value.every(V=>!V)),b=Me(()=>({tktType:[{required:!0,message:o("app.agentTicketRefund.ticketTypeNotEmpty"),trigger:["change","blur"]}],printNo:[{required:!0,message:o("app.agentTicketRefund.prntNoNotEmpty"),trigger:"blur"},{pattern:Gt,message:o("app.agentTicketRefund.onlySupportDigits"),trigger:"blur"}],airline:[{required:!0,message:o("app.agentTicketRefund.refundAirlineSettlementCodeNotEmpty"),trigger:"blur"},{pattern:Oa,message:o("app.agentTicketRefund.onlySupportFixedQuantityDigits",{num:3}),trigger:"blur"}],ticketNo:[{required:!0,message:o("app.agentTicketRefund.refundTicketNoNotEmpty"),trigger:"blur"},{pattern:Js,message:o("app.agentTicketRefund.supportDigitsAndHorizontalBar"),trigger:"blur"}],conjunction:[{required:!0,message:o("app.agentTicketRefund.numberOfCombinedTicketsNotEmpty"),trigger:"blur"},{pattern:Zs,message:o("app.agentTicketRefund.onlySupportFixedQuantityDigits",{num:1}),trigger:"blur"}],currency:[{required:!0,message:o("app.agentTicketRefund.currencyNotEmpty"),trigger:"blur"},{pattern:eo,message:o("app.agentTicketRefund.onlySupportLetters"),trigger:"blur"}],payType:[{required:!0,message:o("app.agentTicketRefund.paymentSel"),trigger:["change","blur"]},{pattern:Yn,message:o("app.agentTicketRefund.paymentInput"),trigger:["change","blur"]}],totalAmount:[{pattern:dt,message:o("app.agentTicketRefund.onlySupportDigits"),trigger:"blur"}],taxValue:[{pattern:Sa,message:o("app.agentTicketRefund.onlySupportDigits"),trigger:"blur"},{validator:E,trigger:"blur"}],taxName:[{pattern:Da,message:o("app.agentTicketRefund.taxes"),trigger:"blur"},{validator:j,trigger:"blur"}],commisionRate:[{pattern:dt,message:o("app.agentTicketRefund.onlySupportDigits"),trigger:"blur"}],otherDeductionRate:[{pattern:dt,message:o("app.agentTicketRefund.onlySupportDigits"),trigger:"blur"}],otherDeduction:[{pattern:dt,message:o("app.agentTicketRefund.onlySupportDigits"),trigger:"blur"}],commision:[{pattern:dt,message:o("app.agentTicketRefund.onlySupportDigits"),trigger:"blur"}],psdName:[{pattern:to,message:o("app.agentTicketRefund.psgNameError"),trigger:"blur"}],creditCard:[{validator:oe,trigger:"blur"}],remarkInfo:[{pattern:$n,message:o("app.agentTicketRefund.remarkHint"),trigger:"blur"}],netRefund:[{required:!0,message:o("app.agentTicketRefund.prntNoNotEmpty"),trigger:"blur"},{pattern:dt,message:o("app.agentTicketRefund.onlySupportDigits"),trigger:"blur"}],ticketManagementOrganizationCode:[{required:!0,message:o("app.agentTicketRefund.prntNoNotEmpty"),trigger:"blur"}]})),N=()=>{const{totalAmount:V,totalTaxs:ge,otherDeduction:me,commision:Re,commisionRate:Oe}=$;if(!L())if(Oe){const we=jt(Lt(Number(V),Number(Oe)),100).toString(),Ee=pn(Number(we),2).toString();$.commision=Ee;const Be=`${Dt(qe(Number(V),Number(ge)),qe(Number(me),Number(Ee)))}`;$.netRefund=Number(Be).toFixed(2)}else{const we=`${Dt(qe(Number(V),Number(ge)),qe(Number(me),Number(Re)))}`;$.netRefund=Number(we).toFixed(2)}},ne=()=>{if($.taxs.length===h)return;const V=$.taxs.length+5>h?h-$.taxs.length:5,ge=new Array(V).fill({name:"",value:""});$.taxs=$.taxs.concat(ge).map(me=>({...me}))},ie=()=>{let V=new gn(0);$.taxs.forEach((ge,me)=>{var Re;(Re=_.value)==null||Re.validateField(`taxs.${me}.value`).then(Oe=>{Oe&&(V=V.add(new gn(ge.value?ge.value:0)),$.totalTaxs=Nt($.currency)?V.toString():Number(V).toFixed(2),Nt($.currency)?se(""):N())})})},U=V=>V.length<10?V.concat(new Array(10-V.length).fill({name:"",value:""})).map(ge=>({...ge})):V,A=async()=>{var V,ge;try{i.value=!0;let me=`${$.airline}${$.ticketNo}`;const Re=me.indexOf("-");me=Re!==-1?me.substring(0,Re):me;const Oe=Pe("091Q0203"),we=(ge=(V=await Ba({ticketNo:me},Oe))==null?void 0:V.data)==null?void 0:ge.value;$.taxs=[],((we==null?void 0:we.rtKTTaxes)??[]).forEach(Ee=>{$.taxs.push({name:Ee.taxType,value:Ee.taxAmount})}),$.taxs=U($.taxs),$.totalAmount=(we==null?void 0:we.ticketAmount)??"",$.currency=(we==null?void 0:we.currency)??"",ie()}finally{i.value=!1}},le=async()=>{const V=[];$.taxs.forEach((ge,me)=>{var Re,Oe;V.push((Re=_.value)==null?void 0:Re.validateField(`taxs.${me}.name`)),V.push((Oe=_.value)==null?void 0:Oe.validateField(`taxs.${me}.value`))}),await Promise.all(V),$.taxs.forEach((ge,me)=>{$.taxs[me].value&&($.taxs[me].value=Nt($.currency)?$.taxs[me].value??0:Number($.taxs[me].value??0).toFixed(2))}),ie()},he=async()=>{var V;$.segment=new Array(4).fill(""),r.value=new Array(4).fill(""),await fn(),$.ticketNo&&((V=_.value)==null||V.validateField("ticketNo"))},ce=V=>V&&!dt.test(V),L=()=>{const{totalAmount:V,otherDeductionRate:ge,otherDeduction:me,commision:Re,commisionRate:Oe}=$;return ce(V??"")||ce(ge??"")||ce(me??"")||ce(Re??"")||ce(Oe??"")},se=V=>{if(V==="otherDeductionRate"&&$.otherDeductionRate){const Ee=jt(Lt(Number($.totalAmount),Number($.otherDeductionRate)),100).toString();$.otherDeduction=pn(Number(Ee),2).toString()}const{totalAmount:ge,totalTaxs:me,otherDeduction:Re,commision:Oe,commisionRate:we}=$;if(!L()){if(we){const Ee=jt(Lt(Number(ge),Number(we)),100).toString(),Be=pn(Number(Ee),2).toString();$.commision=Be.endsWith(".00")?Be.slice(0,-3):Be;const Ze=`${Dt(qe(Number(ge),Number(me)),qe(Number(Re),Number(Be)))}`;$.netRefund=Number(Ze).toFixed(2)}else $.netRefund=`${Dt(qe(Number(ge),Number(me)),qe(Number(Re),Number(Oe)))}`;$.netRefund.endsWith(".00")&&($.netRefund=$.netRefund.slice(0,-3))}},Q=V=>{if(!L()){if(Nt($.currency)){se(V);return}if(V==="otherDeductionRate"&&$.otherDeductionRate){$.otherDeductionRate=Number($.otherDeductionRate).toFixed(2);const ge=jt(Lt(Number($.totalAmount),Number($.otherDeductionRate)),100).toString();$.otherDeduction=Number(ge).toFixed(2)}$.totalAmount&&($.totalAmount=Number($.totalAmount).toFixed(2)),$.commision&&($.commision=Number($.commision).toFixed(2)),$.commisionRate&&($.commisionRate=Number($.commisionRate).toFixed(2)),$.otherDeduction&&($.otherDeduction=Number($.otherDeduction).toFixed(2)),N()}},S=()=>{$.commisionRate||($.commision="")},G=()=>{_.value.clearValidate("creditCard"),$.payType.toUpperCase()==="TC"&&($.creditCard=$.isDragonBoatOffice?QT:"")},J=V=>{V.target.value&&!ss.some(ge=>ge.label===V.target.value)&&($.payType=V.target.value)},z=V=>({commision:V.commision&&Number(V.commision)>0?V.commision:"0",commisionRate:V.commisionRate??"",netRefund:V.netRefund,otherDeduction:V.otherDeduction||"0",taxs:V.taxs.filter(ge=>!!ge.value),totalAmount:V.totalAmount||"0",totalTaxs:V.totalTaxs}),w=V=>{const ge=V.segment.filter(Re=>!!Re),me=new Array(4-(ge==null?void 0:ge.length)).fill("0000");return[...ge,...me]},g=V=>({airline:V.airline,crsPnrNo:"",currency:V.currency,etTag:V.etTag,marketAirline:"",name:Ft.encode(V.name.trim()),payType:V.payType.toUpperCase(),pnr:"",psgType:"",segment:[],couponNos:w(V),ticketNo:`${V.airline}-${V.ticketNo}`,tktType:V.tktType}),B=V=>({modificationType:"ONLY_REFUND",prntNo:V.printNo,ticketManagementOrganizationCode:V.ticketManagementOrganizationCode,resultpre:{amount:z(V),conjunction:V.conjunction,creditCard:V.creditCard,isCoupon:V.isCoupon,office:V.office,operator:V.createUser,remark:V.remarkInfo??"",segList:[],ticket:g(V),volunteer:"NON_VOLUNTEER_MANUAL"}}),F=()=>{a("update:modelValue",!1)},de=V=>({ticketNo:V,ticketType:$.tktType,printerNo:$.printNo??"",refundNo:p.value??"",ticketManagementOrganizationCode:$.ticketManagementOrganizationCode??""}),ve=async()=>{const V=`${$.airline}${$.ticketNo.includes("-")?$.ticketNo.split("-")[0]:$.ticketNo}`;await a("deliverRefundData",de(V)),await F(),Xe.close()},O=async()=>{var V;(V=_.value)==null||V.validate(async ge=>{var Re,Oe,we,Ee;const me=D();if(!(!ge||!me)){i.value=!0;try{if(f.value==="ONLY_REFUND"){const Be=B($),Ze=Pe("091Q0202"),I=(Oe=(Re=await jo(Be,Ze))==null?void 0:Re.data)==null?void 0:Oe.value,_e=(we=I==null?void 0:I.data)==null?void 0:we.refundNumber.replace("-","");p.value=_e??"",$.ticketManagementOrganizationCode!=="ARL"&&(p.value=_e?_e.substring(3):"");const $e=Le("p",{className:"flex",style:{"margin-top":"10px"}},[Le("span",{className:"text-[14px]"},`${o("app.agentTicketRefund.refundTicketNumber")}：`),Le("span",{className:"text-[14px] text-brand-2 cursor-pointer",onClick:ve},p.value)]),re=(Ee=I==null?void 0:I.data)!=null&&Ee.refundStatus.includes("success")?o("app.agentTicketRefund.refundSuccess"):o("app.agentTicketRefund.refundSuccessButStatusFailed");lo(re,$e).then(F).catch(F)}}finally{i.value=!1}}})},fe=()=>{var V,ge,me,Re,Oe,we,Ee,Be,Ze,I,_e,$e;((V=M.value)!=null&&V.includes("$$$")||(ge=M.value)!=null&&ge.includes("BSP"))&&(y.value.push(C.BSP),y.value.push(C.GPBSP)),!((me=M.value)!=null&&me.includes("BSP"))&&((Re=M.value)!=null&&Re.includes("GP"))&&y.value.push(C.GPBSP),((Oe=M.value)!=null&&Oe.includes("$$$")||(we=M.value)!=null&&we.includes("BOP"))&&y.value.push(C.BOPBSP),((Ee=M.value)!=null&&Ee.includes("$$$")||(Be=M.value)!=null&&Be.includes("CDS"))&&(y.value.push(C.CDS),y.value.push(C.GPCDS)),((Ze=M.value)!=null&&Ze.includes("$$$")||(I=M.value)!=null&&I.includes("本票"))&&y.value.push(C.ARL),$.ticketManagementOrganizationCode=(($e=(_e=y.value)==null?void 0:_e[0])==null?void 0:$e.value)??""};return(()=>{$.taxs=U([]),$.segment=new Array(4).fill(""),r.value=new Array(4).fill(""),fe()})(),{refundType:f,fullscreenLoading:i,refundFormData:$,FORM_RULES:b,refundFormRef:_,MAX_TAX_NUM:h,segmentErrorMessage:r,queryRTKTDisabled:P,getTaxAll:A,validSegment:D,addTax:ne,checkTax:le,calcAmount:Q,changePayType:G,bindPaymentValue:J,submitRefund:O,closeDialog:F,changeTicketType:he,commisionRateChange:S,ticketOrganizationList:y,isShowPrintNo:X,changeTicketManagementOrganizationCode:ue}},zT=qT,GT=t("i",{class:"iconfont icon-close"},null,-1),HT=[GT],WT={class:"ticket-refund-form"},YT={class:"h-[24px] my-[10px] flex justify-center items-center text-gray-2 text-[16px] font-bold"},KT={class:"w-full flex-col justify-start items-start inline-flex border-b border-dashed border-gray-6"},XT={class:"self-stretch justify-start items-start gap-5 inline-flex"},JT={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},ZT={class:"w-[90px] text-gray-3 text-xs shrink-0"},e0=t("div",{class:"justify-start items-start flex text-gray-2 text-xs font-bold"},"-",-1),t0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},n0={key:1,class:"inline-block w-[12px]"},a0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},s0={key:1},o0={class:"inline-block w-[84px] text-[12px] text-gray-2"},i0=t("span",null,"-",-1),l0={class:"self-stretch justify-start items-start gap-5 inline-flex"},r0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},c0={class:"w-[90px] text-gray-3 text-xs shrink-0"},u0={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},d0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},p0={class:"w-[84px] text-gray-3 text-xs shrink-0"},f0={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},m0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},g0={class:"w-[84px] text-gray-3 text-xs shrink-0"},k0={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},y0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},h0={class:"w-[84px] text-gray-3 text-xs shrink-0"},v0={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},_0={class:"w-full flex-col justify-start items-start inline-flex border-b border-dashed border-gray-6 mt-[10px]"},b0={class:"self-stretch justify-start items-start gap-5 inline-flex"},x0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] conjunction-num"},T0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] relative"},$0=t("span",{class:"iconfont icon-info-circle-line absolute left-[58px]"},null,-1),N0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] conjunction-num"},R0=t("div",{class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},null,-1),C0={class:"self-stretch justify-start items-start gap-5 inline-flex"},w0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] passenger-name"},A0={class:"grow shrink basis-0 min-h-[32px] justify-start flex mb-[10px]"},S0={class:"w-[84px] text-gray-3 text-xs h-[32px] flex items-center shrink-0 refund-segment"},D0={class:"self-stretch justify-start items-start gap-5 inline-flex"},P0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] require amount"},O0={key:0,class:"not-required-tip"},E0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] require"},F0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] require refund-type"},V0=t("div",{class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},null,-1),M0={class:"w-full flex-col justify-start items-start inline-flex border-b border-dashed border-gray-6 mt-[10px]"},j0={class:"self-stretch justify-start items-start gap-5 inline-flex"},L0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},B0={class:"w-full mb-[10px]"},I0={class:"flex justify-between text-gray-3 text-xs leading-[20px] mb-[6px]"},U0={class:"ml-[20px]"},Q0={class:"text-gray-2 font-[700]"},q0={class:"w-full grow self-stretch justify-start items-start gap-[10px] gap-x-[20px] flex flex-wrap"},z0={class:"w-[20px] text-gray-3 text-xs shrink-0 leading-8"},G0={class:"w-[40px] mr-[6px] shrink-0"},H0={class:"w-full flex-col justify-start items-start inline-flex mt-[10px]"},W0={class:"self-stretch justify-start items-start gap-5 inline-flex"},Y0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},K0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] rate"},X0=t("div",{class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight ml-[4px]"},"%",-1),J0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] rate"},Z0=t("div",{class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight ml-[4px]"},"%",-1),e$={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},t$={class:"self-stretch justify-start items-start gap-5 inline-flex"},n$={class:"grow shrink basis-0 h-[32px] justify-start items-center flex mb-[10px]"},a$={class:"w-[90px] text-gray-3 text-xs shrink-0"},s$={class:"justify-start items-center flex text-gray-2 text-xs relative"},o$=t("span",{class:"iconfont icon-info-circle-line absolute left-[-40px]"},null,-1),i$={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] rate"},l$={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},r$=t("div",{class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},null,-1),c$={class:"ml-[260px] text-yellow-1"},u$={class:"flex justify-center w-full mt-[10px] footer items-center"},d$=je({__name:"ManualRefund",emits:["update:modelValue","deliverRefundData"],setup(a,{emit:o}){const u=o,{refundType:h,fullscreenLoading:f,refundFormRef:p,refundFormData:i,FORM_RULES:_,MAX_TAX_NUM:d,segmentErrorMessage:x,queryRTKTDisabled:T,getTaxAll:k,validSegment:R,addTax:v,checkTax:r,calcAmount:y,changePayType:C,bindPaymentValue:$,submitRefund:P,closeDialog:M,changeTicketType:X,commisionRateChange:ue,ticketOrganizationList:E,isShowPrintNo:j,changeTicketManagementOrganizationCode:oe}=zT(u);return(D,b)=>{const N=tn,ne=nn,ie=ot,U=Je,A=gt,le=yt,he=An,ce=Ke,L=it,se=Pt,Q=Ot,S=nt,G=kt;return s(),ae(S,{width:"1040",title:D.$t("app.agentTicketRefund.manualRefundBtn"),"show-close":!1,"close-on-click-modal":!1,class:"crs-new-ui-init-cls ticket-manual-refund-dialog","align-center":!0,onClose:e(M)},{default:l(()=>[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:b[0]||(b[0]=(...J)=>e(M)&&e(M)(...J))},HT),Qe((s(),m("div",null,[t("div",WT,[t("div",null,[t("div",YT,n(D.$t("app.agentTicketRefund.refundInformationForm")),1)]),c(L,{ref_key:"refundFormRef",ref:p,model:e(i),"require-asterisk-position":"right"},{default:l(()=>{var J,z,w,g,B;return[t("div",KT,[t("div",XT,[t("div",JT,[t("div",ZT,n(D.$t("app.agentTicketRefund.refundTicketNumber")),1),e0]),t("div",t0,[c(ie,{label:D.$t("app.refundForm.ticketType"),prop:"tktType",rules:e(_).tktType},{default:l(()=>[c(ne,{modelValue:e(i).tktType,"onUpdate:modelValue":b[1]||(b[1]=F=>e(i).tktType=F),placeholder:D.$t("app.agentTicketRefund.choose"),clearable:"",onChange:e(X)},{default:l(()=>[(s(!0),m(ye,null,Ne(e(IT),F=>(s(),ae(N,{key:F.value,label:F.label,value:F.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder","onChange"])]),_:1},8,["label","rules"])]),t("div",{class:Se(["grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] ticketManagementOrganizationCode",e(Qs)()==="en"?"ticket-organization-en":""])},[c(ie,{label:D.$t("app.refundForm.ticketManagementOrganizationCode"),prop:"ticketManagementOrganizationCode",rules:e(_).ticketManagementOrganizationCode},{default:l(()=>[c(ne,{modelValue:e(i).ticketManagementOrganizationCode,"onUpdate:modelValue":b[2]||(b[2]=F=>e(i).ticketManagementOrganizationCode=F),disabled:!e(i).ticketManagementOrganizationCode,placeholder:e(i).ticketManagementOrganizationCode?"":D.$t("app.agentTicketQuery.noData"),onChange:e(oe)},{default:l(()=>[(s(!0),m(ye,null,Ne(e(E),F=>(s(),ae(N,{key:F.value,label:F.label,value:F.value},{default:l(()=>[t("span",null,[e(i).ticketManagementOrganizationCode===F.value?(s(),ae(U,{key:0,size:12,class:"iconfont icon-right-line"})):(s(),m("span",n0))]),K(" "+n(F.label),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled","placeholder","onChange"])]),_:1},8,["label","rules"])],2),t("div",a0,[e(j)?(s(),ae(ie,{key:0,label:D.$t("app.agentTicketRefund.prntNo"),prop:"printNo",rules:e(_).printNo},{default:l(()=>[c(Kt,{modelValue:e(i).printNo,"onUpdate:modelValue":[b[3]||(b[3]=F=>e(i).printNo=F),b[4]||(b[4]=F=>e(p).validateField("printNo"))],"need-distinguish":!1,"select-class":"w-[150px]"},null,8,["modelValue"])]),_:1},8,["label","rules"])):(s(),m("span",s0,[t("span",o0,n(D.$t("app.agentTicketRefund.prntNo")),1),i0]))])]),t("div",l0,[t("div",r0,[t("div",c0,n(D.$t("app.agentTicketRefund.refundAgent")),1),t("div",u0,n(((J=e(i))==null?void 0:J.agent)??"-"),1)]),t("div",d0,[t("div",p0,n(D.$t("app.agentTicketRefund.refundIataNo")),1),t("div",f0,n(((z=e(i))==null?void 0:z.iata)??"-"),1)]),t("div",m0,[t("div",g0,n(D.$t("app.agentTicketRefund.refundOffice")),1),t("div",k0,n(((w=e(i))==null?void 0:w.office)??"-"),1)]),t("div",y0,[t("div",h0,n(D.$t("app.agentTicketRefund.refundDate")),1),t("div",v0,n(((g=e(i))==null?void 0:g.refundDate)??"-"),1)])])]),t("div",_0,[t("div",b0,[t("div",x0,[c(ie,{label:D.$t("app.agentTicketRefund.refundAirlineSettlementCode"),class:"w90",prop:"airline",rules:e(_).airline},{default:l(()=>[c(A,{modelValue:e(i).airline,"onUpdate:modelValue":b[5]||(b[5]=F=>e(i).airline=F),modelModifiers:{trim:!0},clearable:""},null,8,["modelValue"])]),_:1},8,["label","rules"])]),t("div",T0,[c(ie,{label:D.$t("app.agentTicketRefund.refundTicketNo"),prop:"ticketNo",rules:e(_).ticketNo},{default:l(()=>[c(A,{modelValue:e(i).ticketNo,"onUpdate:modelValue":b[6]||(b[6]=F=>e(i).ticketNo=F),modelModifiers:{trim:!0},clearable:""},null,8,["modelValue"])]),_:1},8,["label","rules"]),c(le,{placement:"top",content:D.$t("app.agentTicketRefund.internationalTicketNoTips"),"popper-class":"w-[255px]"},{default:l(()=>[$0]),_:1},8,["content"])]),t("div",N0,[c(ie,{label:D.$t("app.agentTicketRefund.numberOfCombinedTickets"),prop:"conjunction",rules:e(_).conjunction},{default:l(()=>[c(A,{modelValue:e(i).conjunction,"onUpdate:modelValue":b[7]||(b[7]=F=>e(i).conjunction=F),modelModifiers:{trim:!0},clearable:""},null,8,["modelValue"])]),_:1},8,["label","rules"])]),R0]),t("div",C0,[t("div",w0,[c(ie,{label:D.$t("app.agentTicketRefund.passName"),prop:"name",class:"w90",rules:e(_).psdName},{default:l(()=>[c(A,{modelValue:e(i).name,"onUpdate:modelValue":b[8]||(b[8]=F=>e(i).name=F),clearable:"",onInput:b[9]||(b[9]=F=>e(i).name=e(i).name.toUpperCase())},null,8,["modelValue"])]),_:1},8,["label","rules"])]),t("div",A0,[t("div",S0,n(D.$t("app.agentTicketRefund.refundSeg")),1),(s(!0),m(ye,null,Ne(e(i).segment,(F,de)=>(s(),ae(ie,{key:de,prop:"segment."+de,error:e(x)[de],class:"mr10"},{default:l(()=>[c(A,{modelValue:e(i).segment[de],"onUpdate:modelValue":ve=>e(i).segment[de]=ve,modelModifiers:{trim:!0},clearable:"",onBlur:e(R)},null,8,["modelValue","onUpdate:modelValue","onBlur"])]),_:2},1032,["prop","error"]))),128))])]),t("div",D0,[t("div",P0,[c(ie,{label:D.$t("app.agentTicketRefund.totalTicketAmount"),prop:"totalAmount",class:Se(["w90",{"not-required-container":!e(i).totalAmount}]),rules:e(_).totalAmount},{default:l(()=>[c(A,{modelValue:e(i).totalAmount,"onUpdate:modelValue":b[10]||(b[10]=F=>e(i).totalAmount=F),modelModifiers:{trim:!0},clearable:"",onBlur:b[11]||(b[11]=F=>e(y)("totalAmount"))},null,8,["modelValue"]),e(i).totalAmount?Z("",!0):(s(),m("div",O0,n(D.$t("app.agentTicketRefund.totalAmountNotRequired")),1))]),_:1},8,["label","rules","class"])]),t("div",E0,[c(ie,{label:D.$t("app.agentTicketRefund.refundPayType"),prop:"payType",rules:e(_).payType},{default:l(()=>[c(ne,{modelValue:e(i).payType,"onUpdate:modelValue":b[12]||(b[12]=F=>e(i).payType=F),modelModifiers:{trim:!0},filterable:"","allow-create":"","default-first-option":"","automatic-dropdown":"",placeholder:D.$t("app.agentTicketRefund.choose"),clearable:"",onChange:e(C),onBlur:e($)},{default:l(()=>[(s(!0),m(ye,null,Ne(e(ss),(F,de)=>(s(),ae(N,{key:de,label:F.label,value:F.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder","onChange","onBlur"])]),_:1},8,["label","rules"])]),t("div",F0,[c(ie,{label:D.$t("app.agentTicketRefund.refundCurrency"),prop:"currency",rules:e(_).currency},{default:l(()=>[c(A,{modelValue:e(i).currency,"onUpdate:modelValue":b[13]||(b[13]=F=>e(i).currency=F),modelModifiers:{trim:!0},clearable:"",onInput:b[14]||(b[14]=F=>e(i).currency=e(i).currency.toUpperCase())},null,8,["modelValue"])]),_:1},8,["label","rules"])]),V0])]),t("div",M0,[t("div",j0,[t("div",L0,[c(ie,{label:D.$t("app.agentTicketRefund.etTag")},{default:l(()=>[c(he,{modelValue:e(i).etTag,"onUpdate:modelValue":b[15]||(b[15]=F=>e(i).etTag=F),"inline-prompt":"","active-text":"Y","inactive-text":"N","active-value":"1","inactive-value":"0"},null,8,["modelValue"])]),_:1},8,["label"])])]),t("div",B0,[t("div",I0,[t("div",null,[t("span",null,n(D.$t("app.agentTicketRefund.refundTax")),1),t("span",U0,n(D.$t("app.fare.singleFare.totalTax")),1),t("span",Q0," "+n(e(i).currency)+" "+n(e(i).totalTaxs),1)]),t("div",null,[c(ce,{link:"",type:"primary",size:"small",disabled:e(T),onClick:e(k)},{default:l(()=>[K(n(D.$t("app.agentTicketRefund.rtktTax")),1)]),_:1},8,["disabled","onClick"]),c(ce,{link:"",type:"primary",size:"small",disabled:((B=e(i).taxs)==null?void 0:B.length)===e(d),onClick:e(v)},{default:l(()=>[K(n(D.$t("app.agentTicketRefund.addTaxs")),1)]),_:1},8,["disabled","onClick"])])]),t("div",q0,[(s(!0),m(ye,null,Ne(e(i).taxs,(F,de)=>(s(),m("div",{key:de,class:"grow shrink-0 basis-0 h-[32px] justify-start flex w-[calc((100%_-_80px)_/_5)] min-w-[calc((100%_-_80px)_/_5)] max-w-[calc((100%_-_80px)_/_5)]"},[t("div",z0,n(de+1),1),t("div",G0,[c(ie,{prop:"taxs."+de+".name",rules:e(_).taxName},{default:l(()=>[c(A,{modelValue:F.name,"onUpdate:modelValue":ve=>F.name=ve,modelModifiers:{trim:!0},onInput:ve=>F.name=F.name.toUpperCase(),onBlur:e(r)},null,8,["modelValue","onUpdate:modelValue","onInput","onBlur"])]),_:2},1032,["prop","rules"])]),c(ie,{prop:"taxs."+de+".value",rules:e(_).taxValue},{default:l(()=>[c(A,{modelValue:F.value,"onUpdate:modelValue":ve=>F.value=ve,modelModifiers:{trim:!0},clearable:"",onBlur:e(r)},null,8,["modelValue","onUpdate:modelValue","onBlur"])]),_:2},1032,["prop","rules"])]))),128))])])]),t("div",H0,[t("div",W0,[t("div",Y0,[c(ie,{label:D.$t("app.agentTicketRefund.commision"),prop:"commision",class:"w90",rules:e(_).commision},{default:l(()=>[c(A,{modelValue:e(i).commision,"onUpdate:modelValue":b[16]||(b[16]=F=>e(i).commision=F),modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",onBlur:b[17]||(b[17]=F=>e(y)("commision"))},null,8,["modelValue"])]),_:1},8,["label","rules"])]),t("div",K0,[c(ie,{label:D.$t("app.agentTicketRefund.commissionRate"),prop:"commisionRate",rules:e(_).commisionRate},{default:l(()=>[c(A,{modelValue:e(i).commisionRate,"onUpdate:modelValue":b[18]||(b[18]=F=>e(i).commisionRate=F),modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",onBlur:b[19]||(b[19]=F=>e(y)("commisionRate")),onInput:e(ue)},null,8,["modelValue","onInput"]),X0]),_:1},8,["label","rules"])]),t("div",J0,[c(ie,{label:D.$t("app.agentTicketRefund.inputOtherDeductionRate"),prop:"otherDeductionRate",class:"w170",rules:e(_).otherDeductionRate},{default:l(()=>[c(A,{modelValue:e(i).otherDeductionRate,"onUpdate:modelValue":b[20]||(b[20]=F=>e(i).otherDeductionRate=F),modelModifiers:{trim:!0},placeholder:"1-100",clearable:"",onBlur:b[21]||(b[21]=F=>e(y)("otherDeductionRate"))},null,8,["modelValue"]),Z0]),_:1},8,["label","rules"])]),t("div",e$,[c(ie,{label:D.$t("app.agentTicketRefund.otherDeduction"),prop:"otherDeduction",class:"w110",rules:e(_).otherDeduction},{default:l(()=>[c(A,{modelValue:e(i).otherDeduction,"onUpdate:modelValue":b[22]||(b[22]=F=>e(i).otherDeduction=F),modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",onBlur:b[23]||(b[23]=F=>e(y)("otherDeduction"))},null,8,["modelValue"])]),_:1},8,["label","rules"])])]),t("div",t$,[t("div",n$,[t("div",a$,n(D.$t("app.agentTicketRefund.remark")),1),t("div",s$,[c(ie,{prop:"remarkInfo",rules:e(_).remarkInfo},{default:l(()=>[c(A,{modelValue:e(i).remarkInfo,"onUpdate:modelValue":b[24]||(b[24]=F=>e(i).remarkInfo=F),clearable:"",placeholder:D.$t("app.agentTicketRefund.remarkPleaceHolder"),onInput:b[25]||(b[25]=F=>e(i).remarkInfo=e(i).remarkInfo.toUpperCase())},null,8,["modelValue","placeholder"])]),_:1},8,["rules"]),c(le,{placement:"top",content:D.$t("app.agentTicketRefund.remarkTips")},{default:l(()=>[o$]),_:1},8,["content"])])]),t("div",i$,[c(ie,{label:D.$t("app.agentTicketRefund.totalRefund"),prop:"netRefund",rules:e(_).netRefund},{default:l(()=>[c(A,{modelValue:e(i).netRefund,"onUpdate:modelValue":b[26]||(b[26]=F=>e(i).netRefund=F),modelModifiers:{trim:!0},clearable:""},null,8,["modelValue"])]),_:1},8,["label","rules"])]),t("div",l$,[c(ie,{label:D.$t("app.agentTicketRefund.creditCardInfo"),prop:"creditCard",rules:e(_).creditCard},{default:l(()=>[c(A,{modelValue:e(i).creditCard,"onUpdate:modelValue":b[27]||(b[27]=F=>e(i).creditCard=F),modelModifiers:{trim:!0},clearable:"",onInput:b[28]||(b[28]=F=>e(i).creditCard=e(i).creditCard.toUpperCase())},null,8,["modelValue"])]),_:1},8,["label","rules"])]),r$])])]}),_:1},8,["model"])]),t("div",c$,n(D.$t("app.agentTicketRefund.netRefundTip")),1),t("div",u$,[c(Q,{modelValue:e(h),"onUpdate:modelValue":b[29]||(b[29]=J=>Ve(h)?h.value=J:null),class:"mr-[10px]"},{default:l(()=>[c(se,{disabled:"",label:"ONLY_REFUND"},{default:l(()=>[K(n(D.$t("app.agentTicketRefund.onlyRt")),1)]),_:1})]),_:1},8,["modelValue"]),c(ce,{type:"primary",onClick:e(P)},{default:l(()=>[K(n(D.$t("app.agentTicketRefund.refund")),1)]),_:1},8,["onClick"])])])),[[G,e(f)]])]),_:1},8,["title","onClose"])}}});const p$=async(a,o)=>{const u=a.cloneNode(!0),h=document.createElement("div");h.style.cssText=`
    position: fixed;
    top: 0;
    left: 0;
    width: ${a.offsetWidth}px;
    height: ${a.offsetHeight+100}px;
    overflow: visible;
    z-index: -9999;
  `,h.appendChild(u),document.body.appendChild(h);try{const f=await Bo(u,{useCORS:!0,scale:3,windowWidth:u.scrollWidth,windowHeight:u.scrollHeight,backgroundColor:"#FFFFFF",allowTaint:!0,logging:!0}),p=new ro("p","pt",[1500,1500]);p.addImage(f,"PNG",0,0,1500,1500),p.save(o)}finally{document.body.removeChild(h)}},f$=a=>{const{t:o}=We(),u=H(),h=tt({printNo:"",airlineSettlementCode:"",startTicketNo:"",endTicketNo:""}),f=H(),p=H(!1),i=H(!1),_=H({}),d={printNo:[{required:!0,message:o("app.cccf.required"),trigger:"blur"},{pattern:Gt,message:o("app.cccf.printNoTip"),trigger:"blur"}],airlineSettlementCode:[{required:!0,message:o("app.cccf.required"),trigger:"blur"},{pattern:Oa,message:o("app.cccf.airlineSettlementCodeTip"),trigger:"blur"}],startTicketNo:[{required:!0,message:o("app.cccf.required"),trigger:"blur"},{pattern:ao,message:o("app.cccf.startTicketNoTip"),trigger:"blur"}],endTicketNo:[{pattern:so,message:o("app.cccf.endTicketNoTip"),trigger:"blur"}]},x=y=>({beginNumber:y.startTicketNo,endNumber:y.endTicketNo,airlineNumber:y.airlineSettlementCode,deviceNumber:y.printNo});return{cccfFromRef:u,cccfFromData:h,rules:d,creditCardReceiptData:_,printRef:f,printStyleControl:p,downloadPdfStyleControl:i,handleQuery:async()=>{var y;(y=u==null?void 0:u.value)==null||y.validate(async C=>{var P;if(!C)return;const $=Mt.service({fullscreen:!0});try{const M=Pe("091U0106"),X=await wo(x(h),M);_.value=((P=X==null?void 0:X.data)==null?void 0:P.value)??{}}finally{$.close()}})},handleReset:()=>{h.printNo="",h.airlineSettlementCode="",h.startTicketNo="",h.endTicketNo=""},handlePrint:async()=>{p.value=!0,await fn(),await Uo(f.value,{paging:!0,style:`<style>
        .print-refund-form-panel {
          width: 1050px
        }
      </style>`}),p.value=!1},handlePdf:async()=>{i.value=!0,await fn(),await p$(f.value,`${h.airlineSettlementCode}-${h.startTicketNo}-${h.endTicketNo}`),i.value=!1},cancel:()=>{a("update:modelValue",!1)}}},m$=f$,g$=t("i",{class:"iconfont icon-close"},null,-1),k$=[g$],y$={class:"border-separate border-spacing-0 border-gray-2 w-full"},h$={class:"w-1/2 border-t border-l border-gray-2 p-1.5"},v$=t("span",{class:"font-bold"},"PASSENGER NAME : ",-1),_$=t("td",{class:"w-1/2 border-t border-l border-r border-gray-2 p-1.5"},[t("span",null,"-")],-1),b$={class:"w-1/2 border-t border-l border-gray-2 p-1.5"},x$=t("span",{class:"font-bold"},"COUNTRY OF SELL : ",-1),T$={class:"w-1/2 border-t border-l border-r border-gray-2 p-1.5"},$$=t("span",{class:"font-bold"},"ORIGIN/DESTINATION : ",-1),N$={class:"w-1/2 border-t border-l border-gray-2 p-1.5"},R$=t("span",{class:"font-bold"},"BOOKING : ",-1),C$=t("span",null,"-",-1),w$={class:"w-1/2 border-t border-l border-r border-gray-2 p-1.5"},A$=t("span",null,"TIME DATE AND PLACE OF ISSUE ",-1),S$={class:"w-1/2 border-t border-l border-gray-2 p-1.5"},D$=t("span",{class:"font-bold"},"TICKET # : ",-1),P$={class:"w-1/2 border-t border-l border-r border-gray-2 p-1.5"},O$={class:"flex"},E$={class:"w-[80px]"},F$={class:"w-1/2 border-t border-l border-gray-2 p-1.5"},V$=t("span",{class:"font-bold"},"IATA # : ",-1),M$={class:"w-1/2 p-0 border-t border-l border-r border-gray-2"},j$={class:"flex h-full"},L$={class:"w-1/2 border-r border-gray-2 p-1.5"},B$={class:"w-1/2 p-1.5"},I$=t("span",{class:"font-bold"},"AGENT : ",-1),U$={class:"h-full"},Q$={class:"w-full h-full p-0 border border-gray-2",colspan:"2"},q$={class:"flex h-full"},z$={class:"w-[26%] h-full p-1.5 border-r border-gray-2 flex flex-col"},G$=t("div",{class:"font-bold"},"ITINERARY",-1),H$={key:0,class:"flex"},W$=t("div",{class:"w-[50px]"},"FORM",-1),Y$={class:"flex"},K$=t("div",{class:"w-[50px]"},"TO",-1),X$={class:"w-[19%] p-1.5 border-r border-gray-2 flex flex-col"},J$=t("div",{class:"font-bold"},"CARRIER",-1),Z$={class:"w-[19%] p-1.5 border-r border-gray-2 flex flex-col"},eN=t("div",{class:"font-bold"},"FLIGHT",-1),tN=t("div",null,"VOID",-1),nN={class:"w-[19%] p-1.5 border-r border-gray-2 flex flex-col"},aN=t("div",{class:"font-bold"},"CLASS",-1),sN={class:"w-[19%] p-1.5 border-r border-gray-2 flex flex-col"},oN=t("div",{class:"font-bold"},"DATE",-1),iN={class:"w-1/5 p-1.5 border-r border-gray-2 flex flex-col"},lN=t("div",{class:"font-bold"},"STATUS",-1),rN={class:"w-1/5 p-1.5 border-r border-gray-2 flex flex-col"},cN=t("div",{class:"font-bold"},"FARE",-1),uN=t("div",null,"-",-1),dN=[uN],pN={class:"w-1/5 p-1.5 border-r border-gray-2 flex flex-col"},fN=t("div",{class:"font-bold"},"BASIS/TKT",-1),mN={class:"w-1/5 p-1.5 border-r border-gray-2 flex flex-col"},gN=t("div",{class:"font-bold"},"DESIGNATOR",-1),kN=t("div",null,"-",-1),yN=[kN],hN={class:"w-1/5 p-1.5 flex flex-col"},vN=t("div",{class:"font-bold"},"STOPOVER",-1),_N={class:"border-separate border-spacing-0 border-gray-2 w-full mt-2.5"},bN={class:"w-1/3 border-t border-l border-gray-2 p-1.5"},xN=t("div",{class:"font-bold"},"CARD HOLDER",-1),TN={class:"w-1/3 border-t border-l border-gray-2 p-1.5"},$N=t("div",{class:"font-bold"},"CREDIT CARD CODE",-1),NN=t("td",{class:"w-1/3 border-t border-l border-r border-gray-2 p-1.5"},[t("div",{class:"font-bold"},"BANK SEQUENCE"),t("div",null,"-")],-1),RN={class:"w-1/3 border-t border-l border-gray-2 p-1.5"},CN=t("div",{class:"font-bold"},"EXPIRE DATE",-1),wN={class:"w-1/3 border-t border-l border-gray-2 p-1.5"},AN=t("div",{class:"font-bold"},"APPROVAL CODE",-1),SN={class:"w-1/3 border-t border-l border-r border-gray-2 p-1.5"},DN=t("div",{class:"font-bold"},"AMOUNT",-1),PN=t("tr",null,[t("td",{class:"w-full border border-gray-2 p-1.5",colspan:"3"},[t("div",{class:"font-bold"},"CARDHOLDER SIGNATURE : ")])],-1),ON={key:1,class:"flex justify-center flex-col items-center min-h-[400px]"},EN=["alt"],FN={class:"mt-[20px] text-lg leading-[24px] font-bold text-gray-2"},VN={class:"text-base text-gray-4"},MN={key:2,class:"flex justify-center crs-btn-dialog-ui"},jN=je({__name:"CreditCardReceiptPrintDialog",emits:["update:modelValue"],setup(a,{emit:o}){const u=o,{cccfFromRef:h,cccfFromData:f,rules:p,creditCardReceiptData:i,printRef:_,printStyleControl:d,downloadPdfStyleControl:x,handleQuery:T,handleReset:k,handlePrint:R,handlePdf:v,cancel:r}=m$(u);return(y,C)=>{const $=ot,P=gt,M=Ke,X=it,ue=nt;return s(),ae(ue,{title:`${y.$t("app.cccf.creditCardReceiptPrint")}CCCF`,"close-on-press-escape":!1,"close-on-click-modal":!1,"show-close":!1,"align-center":"true",class:"cccf-dialog crs-new-ui-init-cls",width:"1040px",onClose:e(r)},{default:l(()=>{var E,j,oe,D,b,N,ne,ie;return[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:C[0]||(C[0]=(...U)=>e(r)&&e(r)(...U))},k$),t("div",null,[t("div",null,[c(X,{ref_key:"cccfFromRef",ref:h,rules:e(p),model:e(f),"require-asterisk-position":"right",class:"flex cccf-from"},{default:l(()=>[c($,{label:y.$t("app.cccf.printNo"),prop:"printNo",class:"mr-2.5"},{default:l(()=>[c(Kt,{modelValue:e(f).printNo,"onUpdate:modelValue":[C[1]||(C[1]=U=>e(f).printNo=U),C[2]||(C[2]=U=>{var A;return(A=e(h))==null?void 0:A.validateField("printNo")})],modelModifiers:{trim:!0},"select-class":"w-[100px]"},null,8,["modelValue"])]),_:1},8,["label"]),c($,{label:y.$t("app.cccf.airlineSettlementCode"),prop:"airlineSettlementCode",class:"mr-2.5"},{default:l(()=>[c(P,{modelValue:e(f).airlineSettlementCode,"onUpdate:modelValue":C[3]||(C[3]=U=>e(f).airlineSettlementCode=U),modelModifiers:{trim:!0},class:"airline-settlement-code"},null,8,["modelValue"])]),_:1},8,["label"]),c($,{label:y.$t("app.cccf.startTicketNo"),prop:"startTicketNo",class:"mr-2.5"},{default:l(()=>[c(P,{modelValue:e(f).startTicketNo,"onUpdate:modelValue":C[4]||(C[4]=U=>e(f).startTicketNo=U),modelModifiers:{trim:!0},class:"start-ticket-no"},null,8,["modelValue"])]),_:1},8,["label"]),c($,{label:y.$t("app.cccf.endTicketNo"),prop:"endTicketNo",class:"mr-2.5"},{default:l(()=>[c(P,{modelValue:e(f).endTicketNo,"onUpdate:modelValue":C[5]||(C[5]=U=>e(f).endTicketNo=U),modelModifiers:{trim:!0},class:"end-ticket-no"},null,8,["modelValue"])]),_:1},8,["label"]),c(M,{type:"primary","data-gid":"091U0106",onClick:C[6]||(C[6]=U=>e(T)())},{default:l(()=>[K(n(y.$t("app.cccf.query")),1)]),_:1}),c(M,{onClick:C[7]||(C[7]=U=>e(k)())},{default:l(()=>[K(n(y.$t("app.cccf.reset")),1)]),_:1})]),_:1},8,["rules","model"])]),(E=e(i))!=null&&E.pnr?(s(),m("div",{key:0,ref_key:"printRef",ref:_,class:Se(["text-gray-2 min-h-[400px] w-[1008px]",e(x)?"p-[20px]":""])},[t("table",y$,[t("tbody",null,[t("tr",null,[t("td",h$,[v$,t("span",null,n(e(i).fullName||"-"),1)]),_$]),t("tr",null,[t("td",b$,[x$,t("span",null,n(e(i).countryCode),1)]),t("td",T$,[$$,t("span",null,n(e(i).orgArrivalCityCode),1)])]),t("tr",null,[t("td",N$,[R$,t("span",null,n(e(i).pnr||"-"),1),C$]),t("td",w$,[A$,t("span",null,n(e(i).ticketIssueTime||"-"),1)])]),t("tr",null,[t("td",S$,[D$,t("span",null,n(e(i).bspCurrencyCode||"-"),1)]),t("td",P$,[t("div",O$,[t("div",E$,n(e(i).ticketIssueDate||"-"),1),K(n(e(i).locationSubTypeCode||"-"),1)])])]),t("tr",null,[t("td",F$,[V$,t("span",null,n(e(i).agentIataNumber||"-"),1)]),t("td",M$,[t("div",j$,[t("div",L$,[t("span",null,n(e(i).issueOfficeId||"-"),1),K(" DEV-"),t("span",null,n(e(i).deviceNumber||"-"),1)]),t("div",B$,[I$,t("span",null,n(e(i).issueAgentId||"-"),1)])])])]),t("tr",U$,[t("td",Q$,[t("div",q$,[t("div",z$,[G$,(s(!0),m(ye,null,Ne(e(i).itineraryInfoList,(U,A)=>(s(),m("div",{key:A},[A===0?(s(),m("div",H$,[W$,K(n(U.originCityNumericCode||"-"),1)])):Z("",!0),t("div",Y$,[K$,K(n(U.destinationCityCode||"-"),1)])]))),128))]),t("div",X$,[J$,(s(!0),m(ye,null,Ne(e(i).itineraryInfoList,(U,A)=>(s(),m("div",{key:A},[t("div",null,n(U.ocAirlineCode||"-"),1)]))),128))]),t("div",Z$,[eN,(s(!0),m(ye,null,Ne(e(i).itineraryInfoList,(U,A)=>(s(),m("div",{key:A},[t("div",null,n(U.ocFlightNumber||"-"),1)]))),128)),tN]),t("div",nN,[aN,(s(!0),m(ye,null,Ne(e(i).itineraryInfoList,(U,A)=>(s(),m("div",{key:A},[t("div",null,n(U.ocClassId||"-"),1)]))),128))]),t("div",sN,[oN,(s(!0),m(ye,null,Ne(e(i).itineraryInfoList,(U,A)=>(s(),m("div",{key:A},[t("div",null,n(U.departureDate||"-"),1)]))),128))]),t("div",iN,[lN,(s(!0),m(ye,null,Ne(e(i).itineraryInfoList,(U,A)=>(s(),m("div",{key:A},[t("div",null,n(U.reservationStatusCode||"-"),1)]))),128))]),t("div",rN,[cN,(s(!0),m(ye,null,Ne(e(i).itineraryInfoList,(U,A)=>(s(),m("div",{key:A},dN))),128))]),t("div",pN,[fN,(s(!0),m(ye,null,Ne(e(i).itineraryInfoList,(U,A)=>(s(),m("div",{key:A},[t("div",null,n(U.seatValueLevel||"-"),1)]))),128))]),t("div",mN,[gN,(s(!0),m(ye,null,Ne(e(i).itineraryInfoList,(U,A)=>(s(),m("div",{key:A},yN))),128))]),t("div",hN,[vN,(s(!0),m(ye,null,Ne(e(i).itineraryInfoList,(U,A)=>(s(),m("div",{key:A},[t("div",null,n(U.stopoverCode||"-"),1)]))),128))])])])])])]),t("table",_N,[t("tbody",null,[t("tr",null,[t("td",bN,[xN,t("div",null,n(((j=e(i).paymentInfo)==null?void 0:j.creditCardNumber)||"-"),1)]),t("td",TN,[$N,t("div",null,n(((oe=e(i).paymentInfo)==null?void 0:oe.operatingCompany)||"-"),1)]),NN]),t("tr",null,[t("td",RN,[CN,t("div",null,n(((D=e(i).paymentInfo)==null?void 0:D.endDate)||"-"),1)]),t("td",wN,[AN,t("div",null,n(((b=e(i).paymentInfo)==null?void 0:b.checkCode)||"-"),1)]),t("td",SN,[DN,t("div",null,[K(n(((N=e(i).paymentInfo)==null?void 0:N.paymentAmount)||"-")+" ",1),t("span",null,n(((ne=e(i).paymentInfo)==null?void 0:ne.currencyCode)||"-"),1)])])]),PN])]),t("div",{class:Se(["mt-2.5",e(d)?"":"text-xs"])},"CARDHOLDER ACKNOWLEDGES RECEIPT OF GOODS AND/OR SERVICE IN THE AMOUNT OF THE TOTAL SHOWN HEREON AND AGRESS TO PERFORM THE OBLIGATIONS SET FORTH IN THE CARDHOLDER' S AGREEMENT WITH THE ISSUER.",2)],2)):(s(),m("div",ON,[t("img",{src:ia,alt:y.$t("app.fastQuery.skQuerys.nodata")},null,8,EN),t("div",FN,n(y.$t("app.commonProblems.noQueryData")),1),t("div",VN,n(y.$t("app.commonProblems.enterCriteriaQuery")),1)])),(ie=e(i))!=null&&ie.pnr?(s(),m("div",MN,[c(M,{type:"primary",onClick:C[8]||(C[8]=U=>e(v)())},{default:l(()=>[K(n(y.$t("app.cccf.download")),1)]),_:1}),c(M,{type:"primary",onClick:C[9]||(C[9]=U=>e(R)())},{default:l(()=>[K(n(y.$t("app.cccf.print")),1)]),_:1}),c(M,{onClick:e(r)},{default:l(()=>[K(n(y.$t("app.cccf.close")),1)]),_:1},8,["onClick"])])):Z("",!0)])]}),_:1},8,["title","onClose"])}}});const LN={class:"ticket-operation-container crs-new-ui-init-cls"},BN={class:"bg-gray-0 rounded-lg shadow-[0_0_8px_0_rgba(109,117,151,0.2)]"},IN={class:"bg-gray-8 h-[50px] flex"},UN=["onClick"],LR=je({__name:"TicketOperationContainer",setup(a){const{loading:o,printNo:u,printType:h,showRefundFormDialog:f,refundOperationCondition:p,refundFormData:i,currentTab:_,editableTabs:d,refundEtNumber:x,queryTicketRes:T,ticketQueryConditionRef:k,showBatchRefund:R,showManualRefund:v,showAuthOffice:r,queryType:y,batchRefundRes:C,batchRefundRef:$,factor:P,showBopRefund:M,showRtktDialog:X,showCccfDialog:ue,addTab:E,changeTab:j,removeTab:oe,handleQueryTicket:D,reQueryTicket:b,openBatchRefund:N,handleBatchRefund:ne,openBopRefund:ie,bopRefundSuccess:U,openManualRefund:A,openAuthOffice:le,deliverRefundData:he,openRefundDialog:ce,openRtkt:L,openCccf:se}=yb();return(Q,S)=>{var z,w;const G=Je,J=kt;return Qe((s(),m("div",null,[t("div",LN,[t("div",BN,[t("div",IN,[(s(!0),m(ye,null,Ne(e(d),(g,B)=>{var F,de;return s(),m("div",{key:g.name,class:Se([B===0?"p-[14px]":"p-[10px] pr-[0px]",e(_)===(((de=(F=e(d))==null?void 0:F[0])==null?void 0:de.name)??"ticketQuery")&&B===0?"bg-gray-0":"","h-full cursor-pointer text-[14px]"]),onClick:ve=>e(j)(B)},[t("span",{class:Se([B===0?"":"rounded-[4px] px-[10px] h-[32px] border-[1px] border-solid",B!==0?e(_)===g.name?"border-brand-2 bg-brand-4":"border-gray-6 bg-gray-0":"","flex items-center text-gray-2"])},[t("span",{class:Se([e(_)===g.name?"text-brand-2 font-[700]":""])},n(g.title),3),B!==0?(s(),ae(G,{key:0,class:"ml-[6px]",onClick:xn(ve=>e(oe)(B,g.name),["stop"])},{default:l(()=>[c(e(qs))]),_:2},1032,["onClick"])):Z("",!0)],2)],10,UN)}),128))]),Qe(t("div",null,[c(Di,{ref_key:"ticketQueryConditionRef",ref:k,onHandleQueryTicket:e(D),onAddNewTab:e(E),onOpenAuthOffice:e(le),onOpenBatchRefund:e(N),onOpenBopRefund:e(ie),onOpenRefundDialog:e(ce),onOpenManualRefund:e(A),onOpenRtkt:e(L),onOpenCccf:e(se)},null,8,["onHandleQueryTicket","onAddNewTab","onOpenAuthOffice","onOpenBatchRefund","onOpenBopRefund","onOpenRefundDialog","onOpenManualRefund","onOpenRtkt","onOpenCccf"])],512),[[vn,e(_)===(((w=(z=e(d))==null?void 0:z[0])==null?void 0:w.name)??"ticketQuery")]])]),(s(!0),m(ye,null,Ne(e(d),g=>Qe((s(),m("div",{key:g.name},[(s(),ae(zs(g.content),{key:g.name,"query-ticket-res":e(T),"tkt-no":e(x),"batch-refund-res":e(C),"query-type":e(y),factor:e(P),onAddNewTab:e(E),onRemoveTab:e(oe),onReQueryTicket:e(b)},null,40,["query-ticket-res","tkt-no","batch-refund-res","query-type","factor","onAddNewTab","onRemoveTab","onReQueryTicket"]))])),[[vn,e(_)===g.name]])),128))]),e(M)?(s(),ae(Rx,{key:0,modelValue:e(M),"onUpdate:modelValue":S[0]||(S[0]=g=>Ve(M)?M.value=g:null),onBopRefundSuccess:e(U)},null,8,["modelValue","onBopRefundSuccess"])):Z("",!0),e(R)?(s(),ae(kx,{key:1,ref_key:"batchRefundRef",ref:$,modelValue:e(R),"onUpdate:modelValue":S[1]||(S[1]=g=>Ve(R)?R.value=g:null),onHandleBatchRefund:e(ne)},null,8,["modelValue","onHandleBatchRefund"])):Z("",!0),e(r)?(s(),ae(OT,{key:2,modelValue:e(r),"onUpdate:modelValue":S[2]||(S[2]=g=>Ve(r)?r.value=g:null)},null,8,["modelValue"])):Z("",!0),e(v)?(s(),ae(d$,{key:3,modelValue:e(v),"onUpdate:modelValue":S[3]||(S[3]=g=>Ve(v)?v.value=g:null),onDeliverRefundData:e(he)},null,8,["modelValue","onDeliverRefundData"])):Z("",!0),e(f)?(s(),ae(ta,{key:4,modelValue:e(f),"onUpdate:modelValue":S[4]||(S[4]=g=>Ve(f)?f.value=g:null),"printer-no":e(u),"printer-type":e(h),"is-supplement-refund":!1,"refund-operation-condition":e(p),"refund-ticket-data":e(i),onReQueryTicket:e(b)},null,8,["modelValue","printer-no","printer-type","refund-operation-condition","refund-ticket-data","onReQueryTicket"])):Z("",!0),e(X)?(s(),ae(BT,{key:5,modelValue:e(X),"onUpdate:modelValue":S[5]||(S[5]=g=>Ve(X)?X.value=g:null)},null,8,["modelValue"])):Z("",!0),e(ue)?(s(),ae(jN,{key:6,modelValue:e(ue),"onUpdate:modelValue":S[6]||(S[6]=g=>Ve(ue)?ue.value=g:null)},null,8,["modelValue"])):Z("",!0)])),[[J,e(o),void 0,{fullscreen:!0,lock:!0}]])}}});export{LR as default};
