import{L as Z,dO as ae,q as N,N as te,O as j,hz as ne,w as E,hS as re,x as S,B as _,P as O,F as D,D as y,A as e,Q as ie,G as R,z as k,y as q,b9 as de,H as ue,J as K,C as H,_ as Q,hT as ce,M as J,em as x,hr as fe,dJ as me,hN as pe,hl as Y,r as T,hU as ve,a3 as ye,hV as ge,ac as G,aF as Ce,o as be,hW as he,hX as W,e8 as ke,Y as De,a2 as X,v as Te,W as Ee,a5 as Ie,$ as Se,a0 as Ae,X as Be,dz as Fe,a6 as $e,T as we,a7 as Pe,hY as Le,K as Oe}from"./index-a2fbd71b.js";import{c as Re}from"./refs-b36b7130.js";import{i as Ne}from"./isUndefined-aa0326a0.js";const ee=Symbol("dialogInjectionKey"),oe=Z({center:Boolean,alignCenter:Boolean,closeIcon:{type:ae},customClass:{type:String,default:""},draggable:Boolean,fullscreen:Boolean,showClose:{type:Boolean,default:!0},title:{type:String,default:""},ariaLevel:{type:String,default:"2"}}),Me={close:()=>!0},ze=["aria-level"],Ve=["aria-label"],Ue=["id"],_e=N({name:"ElDialogContent"}),qe=N({..._e,props:oe,emits:Me,setup(o){const t=o,{t:u}=te(),{Close:F}=ce,{dialogRef:n,headerRef:c,bodyId:I,ns:a,style:g}=j(ee),{focusTrapRef:r}=j(ne),f=E(()=>[a.b(),a.is("fullscreen",t.fullscreen),a.is("draggable",t.draggable),a.is("align-center",t.alignCenter),{[a.m("center")]:t.center},t.customClass]),m=Re(r,n),C=E(()=>t.draggable);return re(n,c,C),(s,d)=>(S(),_("div",{ref:e(m),class:y(e(f)),style:H(e(g)),tabindex:"-1"},[O("header",{ref_key:"headerRef",ref:c,class:y(e(a).e("header"))},[D(s.$slots,"header",{},()=>[O("span",{role:"heading","aria-level":s.ariaLevel,class:y(e(a).e("title"))},ie(s.title),11,ze)]),s.showClose?(S(),_("button",{key:0,"aria-label":e(u)("el.dialog.close"),class:y(e(a).e("headerbtn")),type:"button",onClick:d[0]||(d[0]=$=>s.$emit("close"))},[R(e(ue),{class:y(e(a).e("close"))},{default:k(()=>[(S(),q(de(s.closeIcon||e(F))))]),_:1},8,["class"])],10,Ve)):K("v-if",!0)],2),O("div",{id:e(I),class:y(e(a).e("body"))},[D(s.$slots,"default")],10,Ue),s.$slots.footer?(S(),_("footer",{key:0,class:y(e(a).e("footer"))},[D(s.$slots,"footer")],2)):K("v-if",!0)],6))}});var Ke=Q(qe,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/dialog/src/dialog-content.vue"]]);const je=Z({...oe,appendToBody:Boolean,appendTo:{type:J(String),default:"body"},beforeClose:{type:J(Function)},destroyOnClose:Boolean,closeOnClickModal:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},lockScroll:{type:Boolean,default:!0},modal:{type:Boolean,default:!0},openDelay:{type:Number,default:0},closeDelay:{type:Number,default:0},top:{type:String},modelValue:Boolean,modalClass:String,width:{type:[String,Number]},zIndex:{type:Number},trapFocus:{type:Boolean,default:!1},headerAriaLevel:{type:String,default:"2"}}),Je={open:()=>!0,opened:()=>!0,close:()=>!0,closed:()=>!0,[x]:o=>fe(o),openAutoFocus:()=>!0,closeAutoFocus:()=>!0},Ye=(o,t)=>{var u;const n=me().emit,{nextZIndex:c}=pe();let I="";const a=Y(),g=Y(),r=T(!1),f=T(!1),m=T(!1),C=T((u=o.zIndex)!=null?u:c());let s,d;const $=ve("namespace",he),M=E(()=>{const i={},h=`--${$.value}-dialog`;return o.fullscreen||(o.top&&(i[`${h}-margin-top`]=o.top),o.width&&(i[`${h}-width`]=ye(o.width))),i}),z=E(()=>o.alignCenter?{display:"flex"}:{});function w(){n("opened")}function V(){n("closed"),n(x,!1),o.destroyOnClose&&(m.value=!1)}function U(){n("close")}function P(){d==null||d(),s==null||s(),o.openDelay&&o.openDelay>0?{stop:s}=W(()=>L(),o.openDelay):L()}function A(){s==null||s(),d==null||d(),o.closeDelay&&o.closeDelay>0?{stop:d}=W(()=>l(),o.closeDelay):l()}function B(){function i(h){h||(f.value=!0,r.value=!1)}o.beforeClose?o.beforeClose(i):A()}function p(){o.closeOnClickModal&&B()}function L(){ke&&(r.value=!0)}function l(){r.value=!1}function v(){n("openAutoFocus")}function b(){n("closeAutoFocus")}function le(i){var h;((h=i.detail)==null?void 0:h.focusReason)==="pointer"&&i.preventDefault()}o.lockScroll&&ge(r);function se(){o.closeOnPressEscape&&B()}return G(()=>o.modelValue,i=>{i?(f.value=!1,P(),m.value=!0,C.value=Ne(o.zIndex)?c():C.value++,Ce(()=>{n("open"),t.value&&(t.value.scrollTop=0)})):r.value&&A()}),G(()=>o.fullscreen,i=>{t.value&&(i?(I=t.value.style.transform,t.value.style.transform=""):t.value.style.transform=I)}),be(()=>{o.modelValue&&(r.value=!0,m.value=!0,P())}),{afterEnter:w,afterLeave:V,beforeLeave:U,handleClose:B,onModalClick:p,close:A,doClose:l,onOpenAutoFocus:v,onCloseAutoFocus:b,onCloseRequested:se,onFocusoutPrevented:le,titleId:a,bodyId:g,closed:f,style:M,overlayDialogStyle:z,rendered:m,visible:r,zIndex:C}},Ge=["aria-label","aria-labelledby","aria-describedby"],We=N({name:"ElDialog",inheritAttrs:!1}),Xe=N({...We,props:je,emits:Je,setup(o,{expose:t}){const u=o,F=De();X({scope:"el-dialog",from:"the title slot",replacement:"the header slot",version:"3.0.0",ref:"https://element-plus.org/en-US/component/dialog.html#slots"},E(()=>!!F.title)),X({scope:"el-dialog",from:"custom-class",replacement:"class",version:"2.3.0",ref:"https://element-plus.org/en-US/component/dialog.html#attributes",type:"Attribute"},E(()=>!!u.customClass));const n=Te("dialog"),c=T(),I=T(),a=T(),{visible:g,titleId:r,bodyId:f,style:m,overlayDialogStyle:C,rendered:s,zIndex:d,afterEnter:$,afterLeave:M,beforeLeave:z,handleClose:w,onModalClick:V,onOpenAutoFocus:U,onCloseAutoFocus:P,onCloseRequested:A,onFocusoutPrevented:B}=Ye(u,c);Ee(ee,{dialogRef:c,headerRef:I,bodyId:f,ns:n,rendered:s,style:m});const p=Le(V),L=E(()=>u.draggable&&!u.fullscreen);return t({visible:g,dialogContentRef:a}),(l,v)=>(S(),q(Pe,{to:l.appendTo,disabled:l.appendTo!=="body"?!1:!l.appendToBody},[R(we,{name:"dialog-fade",onAfterEnter:e($),onAfterLeave:e(M),onBeforeLeave:e(z),persisted:""},{default:k(()=>[Ie(R(e(Se),{"custom-mask-event":"",mask:l.modal,"overlay-class":l.modalClass,"z-index":e(d)},{default:k(()=>[O("div",{role:"dialog","aria-modal":"true","aria-label":l.title||void 0,"aria-labelledby":l.title?void 0:e(r),"aria-describedby":e(f),class:y(`${e(n).namespace.value}-overlay-dialog`),style:H(e(C)),onClick:v[0]||(v[0]=(...b)=>e(p).onClick&&e(p).onClick(...b)),onMousedown:v[1]||(v[1]=(...b)=>e(p).onMousedown&&e(p).onMousedown(...b)),onMouseup:v[2]||(v[2]=(...b)=>e(p).onMouseup&&e(p).onMouseup(...b))},[R(e(Ae),{loop:"",trapped:e(g),"focus-start-el":"container",onFocusAfterTrapped:e(U),onFocusAfterReleased:e(P),onFocusoutPrevented:e(B),onReleaseRequested:e(A)},{default:k(()=>[e(s)?(S(),q(Ke,Be({key:0,ref_key:"dialogContentRef",ref:a},l.$attrs,{"custom-class":l.customClass,center:l.center,"align-center":l.alignCenter,"close-icon":l.closeIcon,draggable:e(L),fullscreen:l.fullscreen,"show-close":l.showClose,title:l.title,"aria-level":l.headerAriaLevel,onClose:e(w)}),Fe({header:k(()=>[l.$slots.title?D(l.$slots,"title",{key:1}):D(l.$slots,"header",{key:0,close:e(w),titleId:e(r),titleClass:e(n).e("title")})]),default:k(()=>[D(l.$slots,"default")]),_:2},[l.$slots.footer?{name:"footer",fn:k(()=>[D(l.$slots,"footer")])}:void 0]),1040,["custom-class","center","align-center","close-icon","draggable","fullscreen","show-close","title","aria-level","onClose"])):K("v-if",!0)]),_:3},8,["trapped","onFocusAfterTrapped","onFocusAfterReleased","onFocusoutPrevented","onReleaseRequested"])],46,Ge)]),_:3},8,["mask","overlay-class","z-index"]),[[$e,e(g)]])]),_:3},8,["onAfterEnter","onAfterLeave","onBeforeLeave"])],8,["to","disabled"]))}});var Ze=Q(Xe,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/dialog/src/dialog.vue"]]);const eo=Oe(Ze);export{eo as E,Je as a,je as d,Ye as u};
