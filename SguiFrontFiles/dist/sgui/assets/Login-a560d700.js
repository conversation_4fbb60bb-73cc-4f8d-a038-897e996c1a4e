import{b0 as Z,r as y,ab as j,o as ee,iQ as Re,iR as Lt,iS as Nt,q as G,al as ie,ah as ve,w as se,x as R,B as D,G as w,z as _,a5 as we,a6 as Ve,P as m,Q as $,am as le,an as ue,iT as Mt,A as p,H as et,iU as ze,D as Oe,au as tt,ai as rt,aj as At,ak as X,aG as J,dN as it,ac as He,J as H,y as B,aZ as lt,a8 as Me,aw as Ge,c0 as St,ar as Dt,at as Qe,aR as ot,bZ as at,iV as Ft,bA as Tt,a$ as Ut,aX as Bt,iW as Vt,iX as zt,iY as Ot,b1 as Ie,b3 as Te,E as qe,C as Qt,iZ as qt,i_ as xt,i$ as Ht,bq as Gt,j0 as Kt,j1 as Yt,j2 as Wt,a4 as ge}from"./index-a2fbd71b.js";import{u as ut,a as Xt,l as st,b as ct,i as xe,c as $e,e as Jt}from"./common-859eeea5.js";import{a as Ce,E as ne}from"./index-96be5bee.js";import{E as ye}from"./index-4cb0667b.js";import{E as _e}from"./index-a5188d18.js";import{_ as ce}from"./_plugin-vue_export-helper-c27b6911.js";import{g as Le}from"./encrypt-facb275d.js";import{U as Zt}from"./passwordValid-45ecf3f8.js";import{e as jt}from"./browserEvent-e8caf045.js";import{I as eo}from"./I18n-092596e7.js";import{V as to}from"./index.min-09ff5f99.js";import{u as oo,f as nt}from"./config-6a4e2d9f.js";import{E as ao}from"./index-d88b3135.js";import{E as so}from"./index-e149ca58.js";import{ae as no,af as ro}from"./regular-crs-531dcd3f.js";import"./castArray-2ae6cabb.js";import"./index-b5333773.js";import"./index-fe3402a7.js";import"./isUndefined-aa0326a0.js";import"./index-350461d4.js";import"./dropdown-7f4180b8.js";import"./refs-b36b7130.js";import"./isEqual-c950930c.js";import"./flatten-b73d2287.js";const Ue={userNameInput:"",checkCodeInput:""},ke=Z({userName:"",verificationCode:"",captcha:""}),dt=y(`${Re}?rdm=${Math.random()}`),Be=()=>{dt.value=`${Re}?rdm=${Math.random()}`},pt=a=>{const{t:r}=j(),d=y(!1),u=y(!1),c=Z({findPasswordContent:r("app.retrievePassword.getVerifyCode"),resetLoading:!1,nextLoading:!1,nextCanClick:!0,findPasswordCanClick:!0}),{checkCodeInput:f,startCount:n,isDisabled:e}=ut("{0}s",c.findPasswordContent),t=y(),o=async v=>{c.resetLoading=!0;try{u.value=!1;const P=window.location.pathname.includes("crs")?"C":"";await Nt({username:ke.userName,verificationCode:v,system:P}),n(),d.value=!0,a.emit("changeBtn",!1),setTimeout(()=>{d.value=!1,a.emit("changeBtn",!0)},3e3),c.resetLoading=!1,c.nextCanClick=!1}catch{Be(),u.value=!0}},s=v=>{a.emit("reset"),a.emit("chagepagestatus",v)},l=v=>{t.value&&t.value.validateField(["userName","verificationCode"],P=>{P?(o(v),u.value=!0):c.nextLoading=!1})},i=async v=>{if(!c.nextLoading){Ue.userNameInput=ke.userName,Ue.checkCodeInput=ke.captcha,c.nextLoading=!0;try{await Lt(Ue),c.nextLoading=!1,a.emit("chagepagestatus",v)}catch{c.nextLoading=!1}}},g=v=>{t.value&&t.value.validate(P=>{P?i(v):c.nextLoading=!1})};return ee(()=>{Be()}),{findPassword:ke,findPasswordPageInfo:c,goBack:s,checkCodeInput:f,isDisabled:e,countDownFindPassword:l,nextFormFindPass:g,nextFormFindPassHandle:i,findPasswordForm:t,showMessage:d,captchaUrl:dt,captchaChange:Be,isError:u}},io=()=>{const{t:a}=j(),r=a("app.retrievePassword.pleaseInputUserName"),d=a("app.retrievePassword.usernameLength"),u=a("app.retrievePassword.pleaseInputVerifyCode"),c=a("app.retrievePassword.verifyFormat");return{findPasswordRules:Z({userName:[{required:!0,message:r,trigger:"blur"},{max:50,message:d,trigger:"blur"}],captcha:[{required:!0,message:a("app.retrievePassword.pleaseInputVerifyMegCode"),trigger:"blur"},{pattern:/^[0-9a-zA-Z]{6}$/,message:c,trigger:"blur"}],verificationCode:[{required:!0,message:u,trigger:"blur"},{min:4,max:4,message:a("app.retrievePassword.length"),trigger:"blur"}]})}},lo=G({name:"PasswordRetrieval",components:{ElButton:ie,ElForm:Ce,ElFormItem:ne,ElRow:ye,ElCol:_e,ElInput:ve},emits:["changeBtn","chagepagestatus"],setup(a,r){const d=Z({username:y(""),certificate:y("")}),u=se(()=>window.location.pathname.includes("crs")??!1),{findPassword:c,findPasswordPageInfo:f,goBack:n,checkCodeInput:e,isDisabled:t,countDownFindPassword:o,nextFormFindPass:s,findPasswordForm:l,showMessage:i,captchaUrl:g,captchaChange:v,isError:P}=pt(r),{findPasswordRules:C}=io();return{formInline:d,findPassword:c,findPasswordPageInfo:f,goBack:n,countDownFindPassword:o,nextFormFindPass:s,findPasswordRules:C,findPasswordForm:l,checkCodeInput:e,isDisabled:t,showMessage:i,captchaUrl:g,captchaChange:v,isError:P,isCrs:u}}});const Ae=a=>(le("data-v-32001fe3"),a=a(),ue(),a),uo={class:"retrieve-password"},co={class:"showTip"},po=Ae(()=>m("em",{class:"iconfont icon-info-circle-fill"},null,-1)),fo={class:"tip"},go={class:"anchor-link"},mo={class:"titleCapcha"},vo=Ae(()=>m("em",{class:"iconfont icon-loginuser"},null,-1)),ho={class:"flex-row"},wo=Ae(()=>m("em",{class:"iconfont icon-loginverify"},null,-1)),_o=["src"],Co={class:"flexStyle"},yo=Ae(()=>m("em",{class:"iconfont icon-loginverify"},null,-1)),Po={key:0},bo={key:1},Eo={key:0},ko={key:1};function Ro(a,r,d,u,c,f){const n=_e,e=ye,t=ve,o=ne,s=ie,l=Ce;return R(),D("div",uo,[w(l,{key:"formFindPassword",ref:"findPasswordForm",class:"form",model:a.findPassword,rules:a.findPasswordRules},{default:_(()=>[we(m("div",co,[po,m("span",fo,$(a.$t("app.retrievePassword.sentCode")),1)],512),[[Ve,a.showMessage]]),m("div",go,[w(e,null,{default:_(()=>[w(n,{span:9},{default:_(()=>[m("div",{class:"linkBack",onClick:r[0]||(r[0]=i=>a.goBack("pageLogin"))},"< "+$(a.$t("app.retrievePassword.back")),1)]),_:1}),w(n,{span:15},{default:_(()=>[m("span",mo,$(a.$t("app.retrievePassword.findPassword")),1)]),_:1})]),_:1})]),w(o,{prop:"userName"},{default:_(()=>[w(t,{modelValue:a.findPassword.userName,"onUpdate:modelValue":r[1]||(r[1]=i=>a.findPassword.userName=i),size:"large",type:"text",clearable:"",placeholder:a.isCrs?a.$t("app.retrievePassword.userNameCRS"):a.$t("app.retrievePassword.userName")},{prefix:_(()=>[vo]),_:1},8,["modelValue","placeholder"])]),_:1}),w(o,{prop:"verificationCode"},{default:_(()=>[m("div",ho,[w(t,{modelValue:a.findPassword.verificationCode,"onUpdate:modelValue":r[2]||(r[2]=i=>a.findPassword.verificationCode=i),clearable:"",placeholder:a.$t("app.login.verifyCode"),size:"large",class:"findPasswordCaptcha"},{prefix:_(()=>[wo]),_:1},8,["modelValue","placeholder"]),m("img",{class:"captcha-img",src:a.captchaUrl,alt:"",onClick:r[3]||(r[3]=i=>a.captchaChange())},null,8,_o)])]),_:1}),w(o,{prop:"captcha"},{default:_(()=>[m("div",Co,[w(t,{modelValue:a.findPassword.captcha,"onUpdate:modelValue":r[4]||(r[4]=i=>a.findPassword.captcha=i),type:"text",size:"large",class:"findPasswordCaptcha",placeholder:a.$t("app.retrievePassword.verification")},{prefix:_(()=>[yo]),_:1},8,["modelValue","placeholder"]),w(s,{type:"primary",class:"btn-verify",disabled:a.isDisabled,"data-gid":"081V0103",onClick:r[5]||(r[5]=i=>a.countDownFindPassword(a.findPassword.verificationCode))},{default:_(()=>[a.isDisabled?(R(),D("span",bo,$(a.checkCodeInput),1)):(R(),D("span",Po,$(a.$t("app.retrievePassword.getVerifyCode")),1))]),_:1},8,["disabled"])])]),_:1}),w(o,null,{default:_(()=>[w(s,{class:"next-btn",type:"primary",size:"large",style:{width:"100%",height:"40px","font-size":"14px"},disabled:a.findPasswordPageInfo.nextCanClick,onClick:r[6]||(r[6]=i=>a.nextFormFindPass("pageResetPassword"))},{default:_(()=>[a.findPasswordPageInfo.nextLoading?(R(),D("span",ko,"Loading...")):(R(),D("span",Eo,$(a.$t("app.retrievePassword.next")),1))]),_:1},8,["disabled"])]),_:1})]),_:1},8,["model","rules"])])}const Io=ce(lo,[["render",Ro],["__scopeId","data-v-32001fe3"]]),$o=a=>{const{t:r}=j(),{validVaried:d,validContinuity:u,validKeyboard:c,validRepeat:f}=Zt(),n=Z({certificate:"",recertificate:""}),e=Z({resetConfLoading:!1}),t=async C=>{if(!e.resetConfLoading){e.resetConfLoading=!0;try{await Mt({certificate:Le(n.certificate),recertificate:Le(n.recertificate)}),e.resetConfLoading=!1,a("chagepagestatus",C)}catch(h){e.resetConfLoading=!1,["SGUI-0137-14","SGUI-0138-15"].includes(h.code)&&a("chagepagestatus",C)}}},o=(C,h,k)=>{(d(h)||u(h)||c(h)||f(h))&&k(new Error(r("app.ResetPassword.strongPwd"))),k()},s=(C,h,k)=>{n.certificate!==n.recertificate?k(new Error(r("app.ResetPassword.notSame"))):k()},l=Z({certificate:[{required:!0,message:r("app.ResetPassword.newPwdTip"),trigger:"blur"},{min:8,max:16,message:r("app.ResetPassword.strongPwd"),trigger:"blur"},{validator:o,trigger:"blur"}],recertificate:[{required:!0,message:r("app.ResetPassword.confirmNewPwd"),trigger:"blur"},{min:8,max:16,message:r("app.ResetPassword.confirmNewPwdIsStrong"),trigger:"blur"},{validator:s,trigger:"blur"},{validator:o,trigger:"blur"}]}),i=y(),g=[r("app.personal.oauthTokenRule1"),r("app.personal.oauthTokenRule2"),r("app.personal.oauthTokenRule3"),r("app.personal.oauthTokenRule4"),r("app.personal.oauthTokenRule5")];return{resetPasswordRules:l,resetPasswordReq:n,resetPasswordInfo:e,goBack:C=>{a("reset"),a("chagepagestatus",C)},resetPasswordSubmit:C=>{i.value&&i.value.validate(h=>{h&&t(C)})},resetPasswordForm:i,validArray:g}},ft=$o,gt=a=>(le("data-v-fe1fa4d2"),a=a(),ue(),a),Lo={class:"retrieve-password"},No={class:"anchor-link"},Mo={class:"titleCapcha"},Ao=gt(()=>m("div",{class:"message-left"},null,-1)),So={class:"message-ul"},Do=gt(()=>m("span",null,null,-1)),Fo={key:0},To={key:1},Uo={name:"ResetPassword"},Bo=G({...Uo,emits:["chagepagestatus","reset"],setup(a,{emit:r}){const d=r,{resetPasswordRules:u,resetPasswordReq:c,resetPasswordInfo:f,goBack:n,resetPasswordSubmit:e,resetPasswordForm:t,validArray:o}=ft(d);return(s,l)=>(R(),D("div",Lo,[w(p(Ce),{key:"formResetPassword",ref_key:"resetPasswordForm",ref:t,class:"form",rules:p(u),model:p(c)},{default:_(()=>[m("div",No,[w(p(ye),null,{default:_(()=>[w(p(_e),{span:9},{default:_(()=>[m("div",{class:"linkBack",onClick:l[0]||(l[0]=i=>p(n)("pagePasswordRetrieval"))},"< "+$(s.$t("app.ResetPassword.back")),1)]),_:1}),w(p(_e),{span:15},{default:_(()=>[m("span",Mo,$(s.$t("app.ResetPassword.findPassword")),1)]),_:1})]),_:1})]),w(p(ne),{prop:"certificate",class:"certificate-item"},{default:_(()=>[w(p(ve),{modelValue:p(c).certificate,"onUpdate:modelValue":l[1]||(l[1]=i=>p(c).certificate=i),size:"large",type:"password",clearable:"",placeholder:s.$t("app.ResetPassword.newPassword"),"show-password":""},{default:_(()=>[w(p(et),null,{default:_(()=>[w(p(ze))]),_:1})]),_:1},8,["modelValue","placeholder"])]),_:1}),w(p(ne),{prop:"recertificate"},{default:_(()=>[w(p(ve),{modelValue:p(c).recertificate,"onUpdate:modelValue":l[2]||(l[2]=i=>p(c).recertificate=i),size:"large",type:"password",placeholder:s.$t("app.ResetPassword.newPasswordAgain"),"show-password":""},{default:_(()=>[w(p(et),null,{default:_(()=>[w(p(ze))]),_:1})]),_:1},8,["modelValue","placeholder"])]),_:1}),w(p(ne),null,{default:_(()=>[m("div",{class:Oe(["valid",p(tt)()==="en"?"en-valid-h":"valid-h"])},[m("div",{class:Oe(["message",p(tt)()==="en"?"en-message":""])},[Ao,m("div",So,[m("ul",null,[(R(!0),D(rt,null,At(p(o),(i,g)=>(R(),D("li",{key:g,class:"default"},[Do,X(" "+$(i),1)]))),128))])])],2)],2),w(p(ie),{type:"primary",size:"large",style:{width:"100%",height:"40px","font-size":"14px"},onClick:l[3]||(l[3]=i=>p(e)("pageLogin"))},{default:_(()=>[p(f).resetConfLoading?(R(),D("span",To,"Loading...")):(R(),D("span",Fo,$(s.$t("app.ResetPassword.sure")),1))]),_:1})]),_:1})]),_:1},8,["rules","model"])]))}});const Vo=ce(Bo,[["__scopeId","data-v-fe1fa4d2"]]);/*!
 * qrcode.vue v3.4.1
 * A Vue.js component to generate QRCode.
 * © 2017-2023 @scopewu(https://github.com/scopewu)
 * MIT License.
 */var Ne=function(){return Ne=Object.assign||function(r){for(var d,u=1,c=arguments.length;u<c;u++){d=arguments[u];for(var f in d)Object.prototype.hasOwnProperty.call(d,f)&&(r[f]=d[f])}return r},Ne.apply(this,arguments)};var re;(function(a){var r=function(){function n(e,t,o,s){if(this.version=e,this.errorCorrectionLevel=t,this.modules=[],this.isFunction=[],e<n.MIN_VERSION||e>n.MAX_VERSION)throw new RangeError("Version value out of range");if(s<-1||s>7)throw new RangeError("Mask value out of range");this.size=e*4+17;for(var l=[],i=0;i<this.size;i++)l.push(!1);for(var i=0;i<this.size;i++)this.modules.push(l.slice()),this.isFunction.push(l.slice());this.drawFunctionPatterns();var g=this.addEccAndInterleave(o);if(this.drawCodewords(g),s==-1)for(var v=1e9,i=0;i<8;i++){this.applyMask(i),this.drawFormatBits(i);var P=this.getPenaltyScore();P<v&&(s=i,v=P),this.applyMask(i)}c(0<=s&&s<=7),this.mask=s,this.applyMask(s),this.drawFormatBits(s),this.isFunction=[]}return n.encodeText=function(e,t){var o=a.QrSegment.makeSegments(e);return n.encodeSegments(o,t)},n.encodeBinary=function(e,t){var o=a.QrSegment.makeBytes(e);return n.encodeSegments([o],t)},n.encodeSegments=function(e,t,o,s,l,i){if(o===void 0&&(o=1),s===void 0&&(s=40),l===void 0&&(l=-1),i===void 0&&(i=!0),!(n.MIN_VERSION<=o&&o<=s&&s<=n.MAX_VERSION)||l<-1||l>7)throw new RangeError("Invalid value");var g,v;for(g=o;;g++){var P=n.getNumDataCodewords(g,t)*8,C=f.getTotalBits(e,g);if(C<=P){v=C;break}if(g>=s)throw new RangeError("Data too long")}for(var h=0,k=[n.Ecc.MEDIUM,n.Ecc.QUARTILE,n.Ecc.HIGH];h<k.length;h++){var A=k[h];i&&v<=n.getNumDataCodewords(g,A)*8&&(t=A)}for(var L=[],M=0,F=e;M<F.length;M++){var z=F[M];d(z.mode.modeBits,4,L),d(z.numChars,z.mode.numCharCountBits(g),L);for(var V=0,q=z.getData();V<q.length;V++){var de=q[V];L.push(de)}}c(L.length==v);var x=n.getNumDataCodewords(g,t)*8;c(L.length<=x),d(0,Math.min(4,x-L.length),L),d(0,(8-L.length%8)%8,L),c(L.length%8==0);for(var K=236;L.length<x;K^=253)d(K,8,L);for(var Y=[];Y.length*8<L.length;)Y.push(0);return L.forEach(function(W,U){return Y[U>>>3]|=W<<7-(U&7)}),new n(g,t,Y,l)},n.prototype.getModule=function(e,t){return 0<=e&&e<this.size&&0<=t&&t<this.size&&this.modules[t][e]},n.prototype.getModules=function(){return this.modules},n.prototype.drawFunctionPatterns=function(){for(var e=0;e<this.size;e++)this.setFunctionModule(6,e,e%2==0),this.setFunctionModule(e,6,e%2==0);this.drawFinderPattern(3,3),this.drawFinderPattern(this.size-4,3),this.drawFinderPattern(3,this.size-4);for(var t=this.getAlignmentPatternPositions(),o=t.length,e=0;e<o;e++)for(var s=0;s<o;s++)e==0&&s==0||e==0&&s==o-1||e==o-1&&s==0||this.drawAlignmentPattern(t[e],t[s]);this.drawFormatBits(0),this.drawVersion()},n.prototype.drawFormatBits=function(e){for(var t=this.errorCorrectionLevel.formatBits<<3|e,o=t,s=0;s<10;s++)o=o<<1^(o>>>9)*1335;var l=(t<<10|o)^21522;c(l>>>15==0);for(var s=0;s<=5;s++)this.setFunctionModule(8,s,u(l,s));this.setFunctionModule(8,7,u(l,6)),this.setFunctionModule(8,8,u(l,7)),this.setFunctionModule(7,8,u(l,8));for(var s=9;s<15;s++)this.setFunctionModule(14-s,8,u(l,s));for(var s=0;s<8;s++)this.setFunctionModule(this.size-1-s,8,u(l,s));for(var s=8;s<15;s++)this.setFunctionModule(8,this.size-15+s,u(l,s));this.setFunctionModule(8,this.size-8,!0)},n.prototype.drawVersion=function(){if(!(this.version<7)){for(var e=this.version,t=0;t<12;t++)e=e<<1^(e>>>11)*7973;var o=this.version<<12|e;c(o>>>18==0);for(var t=0;t<18;t++){var s=u(o,t),l=this.size-11+t%3,i=Math.floor(t/3);this.setFunctionModule(l,i,s),this.setFunctionModule(i,l,s)}}},n.prototype.drawFinderPattern=function(e,t){for(var o=-4;o<=4;o++)for(var s=-4;s<=4;s++){var l=Math.max(Math.abs(s),Math.abs(o)),i=e+s,g=t+o;0<=i&&i<this.size&&0<=g&&g<this.size&&this.setFunctionModule(i,g,l!=2&&l!=4)}},n.prototype.drawAlignmentPattern=function(e,t){for(var o=-2;o<=2;o++)for(var s=-2;s<=2;s++)this.setFunctionModule(e+s,t+o,Math.max(Math.abs(s),Math.abs(o))!=1)},n.prototype.setFunctionModule=function(e,t,o){this.modules[t][e]=o,this.isFunction[t][e]=!0},n.prototype.addEccAndInterleave=function(e){var t=this.version,o=this.errorCorrectionLevel;if(e.length!=n.getNumDataCodewords(t,o))throw new RangeError("Invalid argument");for(var s=n.NUM_ERROR_CORRECTION_BLOCKS[o.ordinal][t],l=n.ECC_CODEWORDS_PER_BLOCK[o.ordinal][t],i=Math.floor(n.getNumRawDataModules(t)/8),g=s-i%s,v=Math.floor(i/s),P=[],C=n.reedSolomonComputeDivisor(l),h=0,k=0;h<s;h++){var A=e.slice(k,k+v-l+(h<g?0:1));k+=A.length;var L=n.reedSolomonComputeRemainder(A,C);h<g&&A.push(0),P.push(A.concat(L))}for(var M=[],F=function(z){P.forEach(function(V,q){(z!=v-l||q>=g)&&M.push(V[z])})},h=0;h<P[0].length;h++)F(h);return c(M.length==i),M},n.prototype.drawCodewords=function(e){if(e.length!=Math.floor(n.getNumRawDataModules(this.version)/8))throw new RangeError("Invalid argument");for(var t=0,o=this.size-1;o>=1;o-=2){o==6&&(o=5);for(var s=0;s<this.size;s++)for(var l=0;l<2;l++){var i=o-l,g=(o+1&2)==0,v=g?this.size-1-s:s;!this.isFunction[v][i]&&t<e.length*8&&(this.modules[v][i]=u(e[t>>>3],7-(t&7)),t++)}}c(t==e.length*8)},n.prototype.applyMask=function(e){if(e<0||e>7)throw new RangeError("Mask value out of range");for(var t=0;t<this.size;t++)for(var o=0;o<this.size;o++){var s=void 0;switch(e){case 0:s=(o+t)%2==0;break;case 1:s=t%2==0;break;case 2:s=o%3==0;break;case 3:s=(o+t)%3==0;break;case 4:s=(Math.floor(o/3)+Math.floor(t/2))%2==0;break;case 5:s=o*t%2+o*t%3==0;break;case 6:s=(o*t%2+o*t%3)%2==0;break;case 7:s=((o+t)%2+o*t%3)%2==0;break;default:throw new Error("Unreachable")}!this.isFunction[t][o]&&s&&(this.modules[t][o]=!this.modules[t][o])}},n.prototype.getPenaltyScore=function(){for(var e=0,t=0;t<this.size;t++){for(var o=!1,s=0,l=[0,0,0,0,0,0,0],i=0;i<this.size;i++)this.modules[t][i]==o?(s++,s==5?e+=n.PENALTY_N1:s>5&&e++):(this.finderPenaltyAddHistory(s,l),o||(e+=this.finderPenaltyCountPatterns(l)*n.PENALTY_N3),o=this.modules[t][i],s=1);e+=this.finderPenaltyTerminateAndCount(o,s,l)*n.PENALTY_N3}for(var i=0;i<this.size;i++){for(var o=!1,g=0,l=[0,0,0,0,0,0,0],t=0;t<this.size;t++)this.modules[t][i]==o?(g++,g==5?e+=n.PENALTY_N1:g>5&&e++):(this.finderPenaltyAddHistory(g,l),o||(e+=this.finderPenaltyCountPatterns(l)*n.PENALTY_N3),o=this.modules[t][i],g=1);e+=this.finderPenaltyTerminateAndCount(o,g,l)*n.PENALTY_N3}for(var t=0;t<this.size-1;t++)for(var i=0;i<this.size-1;i++){var v=this.modules[t][i];v==this.modules[t][i+1]&&v==this.modules[t+1][i]&&v==this.modules[t+1][i+1]&&(e+=n.PENALTY_N2)}for(var P=0,C=0,h=this.modules;C<h.length;C++){var k=h[C];P=k.reduce(function(M,F){return M+(F?1:0)},P)}var A=this.size*this.size,L=Math.ceil(Math.abs(P*20-A*10)/A)-1;return c(0<=L&&L<=9),e+=L*n.PENALTY_N4,c(0<=e&&e<=2568888),e},n.prototype.getAlignmentPatternPositions=function(){if(this.version==1)return[];for(var e=Math.floor(this.version/7)+2,t=this.version==32?26:Math.ceil((this.version*4+4)/(e*2-2))*2,o=[6],s=this.size-7;o.length<e;s-=t)o.splice(1,0,s);return o},n.getNumRawDataModules=function(e){if(e<n.MIN_VERSION||e>n.MAX_VERSION)throw new RangeError("Version number out of range");var t=(16*e+128)*e+64;if(e>=2){var o=Math.floor(e/7)+2;t-=(25*o-10)*o-55,e>=7&&(t-=36)}return c(208<=t&&t<=29648),t},n.getNumDataCodewords=function(e,t){return Math.floor(n.getNumRawDataModules(e)/8)-n.ECC_CODEWORDS_PER_BLOCK[t.ordinal][e]*n.NUM_ERROR_CORRECTION_BLOCKS[t.ordinal][e]},n.reedSolomonComputeDivisor=function(e){if(e<1||e>255)throw new RangeError("Degree out of range");for(var t=[],o=0;o<e-1;o++)t.push(0);t.push(1);for(var s=1,o=0;o<e;o++){for(var l=0;l<t.length;l++)t[l]=n.reedSolomonMultiply(t[l],s),l+1<t.length&&(t[l]^=t[l+1]);s=n.reedSolomonMultiply(s,2)}return t},n.reedSolomonComputeRemainder=function(e,t){for(var o=t.map(function(v){return 0}),s=function(v){var P=v^o.shift();o.push(0),t.forEach(function(C,h){return o[h]^=n.reedSolomonMultiply(C,P)})},l=0,i=e;l<i.length;l++){var g=i[l];s(g)}return o},n.reedSolomonMultiply=function(e,t){if(e>>>8||t>>>8)throw new RangeError("Byte out of range");for(var o=0,s=7;s>=0;s--)o=o<<1^(o>>>7)*285,o^=(t>>>s&1)*e;return c(o>>>8==0),o},n.prototype.finderPenaltyCountPatterns=function(e){var t=e[1];c(t<=this.size*3);var o=t>0&&e[2]==t&&e[3]==t*3&&e[4]==t&&e[5]==t;return(o&&e[0]>=t*4&&e[6]>=t?1:0)+(o&&e[6]>=t*4&&e[0]>=t?1:0)},n.prototype.finderPenaltyTerminateAndCount=function(e,t,o){return e&&(this.finderPenaltyAddHistory(t,o),t=0),t+=this.size,this.finderPenaltyAddHistory(t,o),this.finderPenaltyCountPatterns(o)},n.prototype.finderPenaltyAddHistory=function(e,t){t[0]==0&&(e+=this.size),t.pop(),t.unshift(e)},n.MIN_VERSION=1,n.MAX_VERSION=40,n.PENALTY_N1=3,n.PENALTY_N2=3,n.PENALTY_N3=40,n.PENALTY_N4=10,n.ECC_CODEWORDS_PER_BLOCK=[[-1,7,10,15,20,26,18,20,24,30,18,20,24,26,30,22,24,28,30,28,28,28,28,30,30,26,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,10,16,26,18,24,16,18,22,22,26,30,22,22,24,24,28,28,26,26,26,26,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28],[-1,13,22,18,26,18,24,18,22,20,24,28,26,24,20,30,24,28,28,26,30,28,30,30,30,30,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,17,28,22,16,22,28,26,26,24,28,24,28,22,24,24,30,28,28,26,28,30,24,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30]],n.NUM_ERROR_CORRECTION_BLOCKS=[[-1,1,1,1,1,1,2,2,2,2,4,4,4,4,4,6,6,6,6,7,8,8,9,9,10,12,12,12,13,14,15,16,17,18,19,19,20,21,22,24,25],[-1,1,1,1,2,2,4,4,4,5,5,5,8,9,9,10,10,11,13,14,16,17,17,18,20,21,23,25,26,28,29,31,33,35,37,38,40,43,45,47,49],[-1,1,1,2,2,4,4,6,6,8,8,8,10,12,16,12,17,16,18,21,20,23,23,25,27,29,34,34,35,38,40,43,45,48,51,53,56,59,62,65,68],[-1,1,1,2,4,4,4,5,6,8,8,11,11,16,16,18,16,19,21,25,25,25,34,30,32,35,37,40,42,45,48,51,54,57,60,63,66,70,74,77,81]],n}();a.QrCode=r;function d(n,e,t){if(e<0||e>31||n>>>e)throw new RangeError("Value out of range");for(var o=e-1;o>=0;o--)t.push(n>>>o&1)}function u(n,e){return(n>>>e&1)!=0}function c(n){if(!n)throw new Error("Assertion error")}var f=function(){function n(e,t,o){if(this.mode=e,this.numChars=t,this.bitData=o,t<0)throw new RangeError("Invalid argument");this.bitData=o.slice()}return n.makeBytes=function(e){for(var t=[],o=0,s=e;o<s.length;o++){var l=s[o];d(l,8,t)}return new n(n.Mode.BYTE,e.length,t)},n.makeNumeric=function(e){if(!n.isNumeric(e))throw new RangeError("String contains non-numeric characters");for(var t=[],o=0;o<e.length;){var s=Math.min(e.length-o,3);d(parseInt(e.substring(o,o+s),10),s*3+1,t),o+=s}return new n(n.Mode.NUMERIC,e.length,t)},n.makeAlphanumeric=function(e){if(!n.isAlphanumeric(e))throw new RangeError("String contains unencodable characters in alphanumeric mode");var t=[],o;for(o=0;o+2<=e.length;o+=2){var s=n.ALPHANUMERIC_CHARSET.indexOf(e.charAt(o))*45;s+=n.ALPHANUMERIC_CHARSET.indexOf(e.charAt(o+1)),d(s,11,t)}return o<e.length&&d(n.ALPHANUMERIC_CHARSET.indexOf(e.charAt(o)),6,t),new n(n.Mode.ALPHANUMERIC,e.length,t)},n.makeSegments=function(e){return e==""?[]:n.isNumeric(e)?[n.makeNumeric(e)]:n.isAlphanumeric(e)?[n.makeAlphanumeric(e)]:[n.makeBytes(n.toUtf8ByteArray(e))]},n.makeEci=function(e){var t=[];if(e<0)throw new RangeError("ECI assignment value out of range");if(e<128)d(e,8,t);else if(e<16384)d(2,2,t),d(e,14,t);else if(e<1e6)d(6,3,t),d(e,21,t);else throw new RangeError("ECI assignment value out of range");return new n(n.Mode.ECI,0,t)},n.isNumeric=function(e){return n.NUMERIC_REGEX.test(e)},n.isAlphanumeric=function(e){return n.ALPHANUMERIC_REGEX.test(e)},n.prototype.getData=function(){return this.bitData.slice()},n.getTotalBits=function(e,t){for(var o=0,s=0,l=e;s<l.length;s++){var i=l[s],g=i.mode.numCharCountBits(t);if(i.numChars>=1<<g)return 1/0;o+=4+g+i.bitData.length}return o},n.toUtf8ByteArray=function(e){e=encodeURI(e);for(var t=[],o=0;o<e.length;o++)e.charAt(o)!="%"?t.push(e.charCodeAt(o)):(t.push(parseInt(e.substring(o+1,o+3),16)),o+=2);return t},n.NUMERIC_REGEX=/^[0-9]*$/,n.ALPHANUMERIC_REGEX=/^[A-Z0-9 $%*+.\/:-]*$/,n.ALPHANUMERIC_CHARSET="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:",n}();a.QrSegment=f})(re||(re={}));(function(a){(function(r){var d=function(){function u(c,f){this.ordinal=c,this.formatBits=f}return u.LOW=new u(0,1),u.MEDIUM=new u(1,0),u.QUARTILE=new u(2,3),u.HIGH=new u(3,2),u}();r.Ecc=d})(a.QrCode||(a.QrCode={}))})(re||(re={}));(function(a){(function(r){var d=function(){function u(c,f){this.modeBits=c,this.numBitsCharCount=f}return u.prototype.numCharCountBits=function(c){return this.numBitsCharCount[Math.floor((c+7)/17)]},u.NUMERIC=new u(1,[10,12,14]),u.ALPHANUMERIC=new u(2,[9,11,13]),u.BYTE=new u(4,[8,16,16]),u.KANJI=new u(8,[8,10,12]),u.ECI=new u(7,[0,0,0]),u}();r.Mode=d})(a.QrSegment||(a.QrSegment={}))})(re||(re={}));var me=re,mt="H",Ke={L:me.QrCode.Ecc.LOW,M:me.QrCode.Ecc.MEDIUM,Q:me.QrCode.Ecc.QUARTILE,H:me.QrCode.Ecc.HIGH},zo=function(){try{new Path2D().addPath(new Path2D)}catch{return!1}return!0}();function vt(a){return a in Ke}function ht(a,r){r===void 0&&(r=0);var d=[];return a.forEach(function(u,c){var f=null;u.forEach(function(n,e){if(!n&&f!==null){d.push("M".concat(f+r," ").concat(c+r,"h").concat(e-f,"v1H").concat(f+r,"z")),f=null;return}if(e===u.length-1){if(!n)return;f===null?d.push("M".concat(e+r,",").concat(c+r," h1v1H").concat(e+r,"z")):d.push("M".concat(f+r,",").concat(c+r," h").concat(e+1-f,"v1H").concat(f+r,"z"));return}n&&f===null&&(f=e)})}),d.join("")}var Ye={value:{type:String,required:!0,default:""},size:{type:Number,default:100},level:{type:String,default:mt,validator:function(a){return vt(a)}},background:{type:String,default:"#fff"},foreground:{type:String,default:"#000"},margin:{type:Number,required:!1,default:0}},Oo=Ne(Ne({},Ye),{renderAs:{type:String,required:!1,default:"canvas",validator:function(a){return["canvas","svg"].indexOf(a)>-1}}}),Qo=G({name:"QRCodeSvg",props:Ye,setup:function(a){var r=y(0),d=y(""),u=function(){var c=a.value,f=a.level,n=a.margin,e=me.QrCode.encodeText(c,Ke[f]).getModules();r.value=e.length+n*2,d.value=ht(e,n)};return u(),it(u),function(){return J("svg",{width:a.size,height:a.size,"shape-rendering":"crispEdges",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 ".concat(r.value," ").concat(r.value)},[J("path",{fill:a.background,d:"M0,0 h".concat(r.value,"v").concat(r.value,"H0z")}),J("path",{fill:a.foreground,d:d.value})])}}}),qo=G({name:"QRCodeCanvas",props:Ye,setup:function(a){var r=y(null),d=function(){var u=a.value,c=a.level,f=a.size,n=a.margin,e=a.background,t=a.foreground,o=r.value;if(o){var s=o.getContext("2d");if(s){var l=me.QrCode.encodeText(u,Ke[c]).getModules(),i=l.length+n*2,g=window.devicePixelRatio||1,v=f/i*g;o.height=o.width=f*g,s.scale(v,v),s.fillStyle=e,s.fillRect(0,0,i,i),s.fillStyle=t,zo?s.fill(new Path2D(ht(l,n))):l.forEach(function(P,C){P.forEach(function(h,k){h&&s.fillRect(k+n,C+n,1,1)})})}}};return ee(d),it(d),function(){return J("canvas",{ref:r,style:{width:"".concat(a.size,"px"),height:"".concat(a.size,"px")}})}}}),xo=G({name:"Qrcode",render:function(){var a=this.$props,r=a.renderAs,d=a.value,u=a.size,c=a.margin,f=a.level,n=a.background,e=a.foreground,t=u>>>0,o=c>>>0,s=vt(f)?f:mt;return J(r==="svg"?Qo:qo,{value:d,size:t,margin:o,level:s,background:n,foreground:e})},props:Oo});const Ho={key:0},Go=G({__name:"QrCodeComponent",props:{identifyQrCode:{},customSize:{default:100},customLevel:{default:"M"},customFg:{default:"#000000"},customBg:{default:"#FFFFFF"}},setup(a){const r=a,d=y(!1),u=y(""),c=()=>{r.identifyQrCode&&r.identifyQrCode.length>0&&(d.value=!0,u.value=r.identifyQrCode)};return ee(()=>{c()}),He(()=>r.identifyQrCode,()=>{c()}),(f,n)=>d.value?(R(),D("div",Ho,[w(xo,{value:u.value,size:f.customSize,level:f.customLevel,foreground:f.customFg,background:f.customBg},null,8,["value","size","level","foreground","background"])])):H("",!0)}}),Ko=a=>(le("data-v-2a268acf"),a=a(),ue(),a),Yo={class:"h-[524px] min-h-[400px] protocol-box"},Wo={class:"states"},Xo=Ko(()=>m("div",{id:"protocolMdId",class:"markdown-license"},null,-1)),Jo={class:"footer"},Zo=G({__name:"ProtocolDialog",emits:["update:modelValue","changeState"],setup(a,{emit:r}){const d=r,{t:u}=j(),c=y(!1),f=o=>{n(),d("changeState",o)},n=()=>{d("update:modelValue",!1)},e=o=>{to.preview(document.getElementById("protocolMdId"),o,{mode:"light",anchor:0,theme:{current:"light",path:`${nt}/dist/css/content-theme`},cdn:nt})},t=async()=>{c.value=!0;try{const s=await(await fetch(`${oo}/SguiProtocol.md`)).text();!s||s!=null&&s.startsWith("<!DOCTYPE html>")?e(u("app.emptyTip")):e(s)}catch{return}finally{c.value=!1}};return ee(async()=>{await t()}),(o,s)=>{const l=ie,i=ao,g=lt;return R(),B(i,{width:"1040","close-on-click-modal":!1,"show-close":!1,"close-on-press-escape":!1,class:"protocol-view-dialog",onClose:n},{default:_(()=>[m("div",Yo,[we((R(),D("div",Wo,[Xo,m("div",Jo,[w(l,{type:"primary",onClick:s[0]||(s[0]=v=>f(!0))},{default:_(()=>[X($(o.$t("app.login.agree")),1)]),_:1}),w(l,{plain:"",onClick:s[1]||(s[1]=v=>f(!1))},{default:_(()=>[X($(o.$t("app.login.cancel")),1)]),_:1})])])),[[g,c.value]])])]),_:1})}}});const jo=ce(Zo,[["__scopeId","data-v-2a268acf"]]),ea=(a,r)=>{const d=Me(),u=Ge(),c=d.query.authValue;let f=!1;const n=y(0),e=y(!1),t=y(""),o=["IAM-7101-15","IAM-7102-16","IAM-7103-17","IAM-7104-18","IAM-7106-10","IAM-7199-78"];let s=null;const l=y(),i=y(""),g=y(!1),v=y(!1),{startCountdown:P,timeOut:C,countdownText:h,stopCountDownInterval:k}=Xt(),A=y(""),L=se(()=>A.value?"#D9D9D9":"#000000"),M=Z({username:"",certificate:"",verificationCode:""}),F=se(()=>{var E;return window.location.hostname.includes("intl")||((E=window.location.pathname)==null?void 0:E.includes("intl"))}),z=y(St()),V=y(!1),q=y(!1),de=se(()=>M.certificate!==i.value),x=y(null),K=y(!1),Y=y(),W=y(),{t:U}=j(),De=U("app.login.pleaseInputUserName"),T=U("app.login.pleaseInputWorkNo"),b=U("app.login.pleaseInputPwd"),te=U("app.login.pleaseInputVerifyCode"),O=U("app.login.length"),be={username:[{required:!0,message:De,trigger:"blur"}],workNo:[{required:!0,message:T,trigger:"blur"}],certificate:[{required:!0,message:b,trigger:"blur"}],verificationCode:[{required:!0,message:te,trigger:"blur"},{min:4,max:4,message:O,trigger:"blur"}]},oe=y(`${Re}?rdm=${Math.random()}`);let pe=!0;const he=()=>{oe.value=`${Re}?rdm=${Math.random()}`},ae=380,I=y(0),wt=se(()=>({transform:`translate3d(${-I.value}px, 0px, 0px)`})),_t=async()=>{const E=de.value?Le(M.certificate):M.certificate,N=Tt(),S={certificate:E,username:M.username,verificationCode:M.verificationCode};return N.includes("icrspsssell")&&(S.username=M.workNo??"",S.system="C"),S},We=()=>{s!==null&&(clearTimeout(s),s=null)},Ee=()=>{We(),k()},Xe=async()=>{if(C.value)return;const{data:E}=await Vt(t.value),N=E.value;if(!C.value)if(N.code==="200"){const S=await xe(f,N.data.token);if(Ee(),S){r("update:currentToken",N.data.token),r("update:agentDomesticEpid",!0);return}await st(N.data.token,u)}else if(o.includes(N.code))Ee(),N.code==="IAM-7103-17"?A.value=U("app.login.bioQrCodeTimeout"):A.value=U("app.login.bioQrCodeInvalid");else{if(I.value===0||C.value)return;s=setTimeout(Xe,l.value)}},Ct=(E,N)=>{A.value="",l.value=N*1e3,P(E*60),Xe()},Je=()=>at.config({driver:at.INDEXEDDB,name:"index_db_util",storeName:"airport"}),yt=async E=>{if(!v.value)return;await Je();const N={username:E.username,certificate:E.certificate,operationTime:new Date().getTime()},S=await ot("LOGIN_INFO_ACCOUNT"),fe=S?JSON.parse(S.localData??""):[];fe.length=0,fe.push(N),await Ut("LOGIN_INFO_ACCOUNT",JSON.stringify(fe),new Date().getTime())},Pt=E=>navigator.userAgent.toLowerCase().includes("electron/")&&(E??"")===""?(Bt.error(U("app.login.macNull")),!1):!0,Fe=async(E,N)=>{try{pe=!1,t.value="";const S=await _t(),fe=await ct(c);if(!Pt(fe))return;const Q=(await Ft(S,fe)).data;if(r("update:currentToken",Q.token),n.value=Q.securityLevel,f=!Q.agentAbroad,yt(S),Q.bioExpireTime&&Q.bioExpireTime>0&&Q.iamToken){r("update:showLanguage",!1),I.value+=ae,t.value=Q.iamToken,Ct(Q.bioExpireTime,Q.bioIntervalTime??5);return}if(n.value===0){if(await xe(f,Q.token)){r("update:agentDomesticEpid",!0);return}await $e(Q.token,u);return}r("update:showLanguage",!1),I.value+=ae,a.value=Q.iamToken,N(),E.resetFields()}catch{he()}finally{pe=!0}},bt=(E,N)=>{W.value.validate(S=>{S&&pe&&Fe(E,N)})},Et=()=>{x.value&&(Qe.close(),x.value=null),je(!0)},kt=(E,N)=>{W.value&&W.value.validate(S=>{if(S&&pe){if(Y.value={form:E,submitMultCaptcha:N},!F.value&&!V.value){x.value=Qe.confirm(J("div",{class:"protocol-info-tip-box"},[J("div",{class:"text-gray-1 text-lg font-normal leading-normal"},U("app.login.pleaseReadAndAgree")),J("div",{class:"pl-1 text-brand-2 text-lg font-normal leading-normal cursor-pointer",onClick:Et},U("app.login.sguiProtocol"))]),{icon:J("em",{class:"iconfont icon-info-circle-line"}),customClass:"protocol-msg crs-btn-ui",dangerouslyUseHTMLString:!0,closeOnClickModal:!1,showClose:!1,cancelButtonClass:z.value==="en"?"ptotocol-cancel-en":"ptotocol-cancel",confirmButtonClass:z.value==="en"?"ptotocol-sure-en":"ptotocol-sure",confirmButtonText:U("app.login.agree"),cancelButtonText:U("app.login.cancel")}).then(()=>{V.value=!0,Fe(E,N)});return}Fe(E,N)}})},Rt=()=>{r("update:showLanguage",!0),Ee(),I.value-=ae,n.value=0,he(),W.value&&W.value.resetFields(),Ze()},It=async()=>{const{token:E,iamToken:N}=d.query;E?await st(E,u):N&&(I.value=ae)},Ze=async()=>{if(g.value){await Je();const E=await ot("LOGIN_INFO_ACCOUNT"),N=E?JSON.parse(E.localData??""):[];if(!E||N.length===0)return;v.value=!0;const S=N[0];M.workNo=S.username,M.username=S.username,i.value=S.certificate,M.certificate=S.certificate}},$t=E=>{var N,S;E&&(V.value=E),E&&K.value&&bt((N=Y.value)==null?void 0:N.form,(S=Y.value)==null?void 0:S.submitMultCaptcha)},je=E=>{K.value=E,q.value=!0};return He(()=>C.value,E=>{E&&(We(),A.value=U("app.login.bioQrCodeTimeout"))}),ee(async()=>{It();const E=u.currentRoute.value.fullPath;e.value=E.includes("icrspsssell"),navigator.userAgent.toLowerCase().includes("electron/")&&(g.value=!0),await Ze()}),Dt(()=>{Ee()}),{submitLogin:kt,loginUser:M,captchaChange:he,loginRules:be,captchaUrl:oe,loginForm:W,backLoginForm:Rt,trackStyles:wt,securityLevel:n,isCrsLogin:e,identifyQrCode:t,countdownText:h,qrCodeCustomFg:L,qrErrorTip:A,isChangeAutoPwd:de,isClientAndCheck:v,isClient:g,isAgreementConsent:V,showProtocol:q,changeState:$t,viewProtocol:je,isIntl:F}},ta=ea,oa=a=>{const r=Me(),d=Ge(),{t:u}=j(),c=Z({captcha:""}),f={captcha:[{required:!0,message:u("app.login.pleaseInputVerifyCode"),trigger:"blur"},{min:6,max:6,message:u("app.login.sixLength"),trigger:"blur"}]},{checkCodeInput:n,startCount:e,isDisabled:t}=ut(u("app.login.verifyCodeSending"),u("app.login.resendVerifycode"));let o=!0;const s=y(""),l=async()=>{e(),await zt(s.value)},i=async()=>{try{o=!1;const C=(await Ot(c.captcha,s.value)).data;if(a("update:currentToken",C.token),await xe(!C.agentAbroad,C.token)){a("update:agentDomesticEpid",!0);return}await $e(C.token,d)}finally{o=!0}},g=y(),v=()=>{g.value&&g.value.validate(P=>{P&&o&&i()})};return ee(()=>{const{iamToken:P}=r.query;P&&(s.value=P)}),{multUser:c,submitMultLogin:v,submitMultCaptcha:l,multLoginRules:f,multLoginForm:g,checkCodeInput:n,isDisabled:t,currentIamToken:s}},aa=oa,Se=a=>(le("data-v-0d1aae07"),a=a(),ue(),a),sa=Se(()=>m("em",{class:"iconfont icon-loginuser"},null,-1)),na=Se(()=>m("em",{class:"iconfont icon-loginuser"},null,-1)),ra=Se(()=>m("em",{class:"iconfont icon-loginpassword"},null,-1)),ia={class:"flex-row"},la=Se(()=>m("em",{class:"iconfont icon-loginverify"},null,-1)),ua=["src"],ca={class:"flex h-[22px] items-center protocol-box"},da={class:"flex items-center"},pa={class:"leading-[22px] rounded-sm flex-col justify-start items-start inline-flex overflow-hidden"},fa={class:"tips"},ga={key:0,class:"form"},ma={class:"mult-top bio-identify-title"},va={class:"mult-title"},ha={class:"flex items-center flex-col"},wa={class:"text-[14px] text-gray-4 mt-[10px] text-center"},_a={key:0},Ca={key:1},ya={class:"mult-top"},Pa={class:"mult-title"},ba=G({__name:"LoginForm",emits:["chagepagestatus","update:currentToken","update:agentDomesticEpid","update:showLanguage"],setup(a,{emit:r}){const d=r,{multUser:u,submitMultLogin:c,multLoginRules:f,multLoginForm:n,checkCodeInput:e,submitMultCaptcha:t,isDisabled:o,currentIamToken:s}=aa(d),{loginUser:l,captchaChange:i,loginRules:g,captchaUrl:v,loginForm:P,backLoginForm:C,trackStyles:h,submitLogin:k,securityLevel:A,isCrsLogin:L,identifyQrCode:M,countdownText:F,qrCodeCustomFg:z,qrErrorTip:V,isClientAndCheck:q,isClient:de,isAgreementConsent:x,showProtocol:K,changeState:Y,viewProtocol:W,isIntl:U}=ta(s,d),De=T=>{d("chagepagestatus",T)};return(T,b)=>{const te=ve,O=ne,be=so,oe=ie,pe=_e,he=ye,ae=Ce;return R(),D(rt,null,[m("div",{class:"login-form-wipper",style:Qt(p(h))},[w(ae,{ref_key:"loginForm",ref:P,rules:p(g),model:p(l),class:"form",onSubmit:b[11]||(b[11]=qe(()=>{},["prevent"]))},{default:_(()=>[p(L)?(R(),B(O,{key:0,prop:"workNo"},{default:_(()=>[w(te,{modelValue:p(l).workNo,"onUpdate:modelValue":b[0]||(b[0]=I=>p(l).workNo=I),placeholder:T.$t("app.login.workNo"),clearable:"",size:"large"},{prefix:_(()=>[sa]),_:1},8,["modelValue","placeholder"])]),_:1})):(R(),B(O,{key:1,prop:"username"},{default:_(()=>[w(te,{modelValue:p(l).username,"onUpdate:modelValue":b[1]||(b[1]=I=>p(l).username=I),placeholder:T.$t("app.login.userName"),clearable:"",size:"large"},{prefix:_(()=>[na]),_:1},8,["modelValue","placeholder"])]),_:1})),w(O,{prop:"certificate"},{default:_(()=>[w(te,{modelValue:p(l).certificate,"onUpdate:modelValue":b[2]||(b[2]=I=>p(l).certificate=I),type:"password",clearable:"",placeholder:T.$t("app.login.oauth"),size:"large","show-password":""},{prefix:_(()=>[ra]),_:1},8,["modelValue","placeholder"])]),_:1}),w(O,{prop:"verificationCode"},{default:_(()=>[m("div",ia,[w(te,{modelValue:p(l).verificationCode,"onUpdate:modelValue":b[3]||(b[3]=I=>p(l).verificationCode=I),clearable:"",placeholder:T.$t("app.login.verifyCode"),size:"large",onKeyup:b[4]||(b[4]=Ie(I=>p(k)(p(n),p(t)),["enter"]))},{prefix:_(()=>[la]),_:1},8,["modelValue","placeholder"]),m("img",{class:"captcha-img z-10",src:p(v),alt:"","data-gid":"081V0102",onClick:b[5]||(b[5]=I=>p(i)())},null,8,ua)])]),_:1}),p(de)?(R(),B(O,{key:2,class:"flight-time","label-width":"56spx"},{default:_(()=>[w(be,{modelValue:p(q),"onUpdate:modelValue":b[6]||(b[6]=I=>Te(q)?q.value=I:null),label:T.$t("app.personal.saveLoginInfoTip")},null,8,["modelValue","label"])]),_:1})):H("",!0),p(U)?H("",!0):(R(),B(O,{key:3},{default:_(()=>[m("div",ca,[m("div",da,[w(be,{modelValue:p(x),"onUpdate:modelValue":b[7]||(b[7]=I=>Te(x)?x.value=I:null),size:"large"},{default:_(()=>[X($(T.$t("app.login.readAndAgree")),1)]),_:1},8,["modelValue"])]),m("div",pa,[w(oe,{type:"primary",link:"",class:"pro-btn",onClick:b[8]||(b[8]=I=>p(W)(!1))},{default:_(()=>[X($(T.$t("app.login.sguiProtocol")),1)]),_:1})])])]),_:1})),w(O,null,{default:_(()=>[w(oe,{type:"primary",class:"submit-button","data-gid":"081V0104",onClick:b[9]||(b[9]=I=>p(k)(p(n),p(t)))},{default:_(()=>[X($(T.$t("app.login.loginText")),1)]),_:1})]),_:1}),w(he,{class:"retrieve-password retrieve-password-center"},{default:_(()=>[w(pe,null,{default:_(()=>[m("span",fa,$(T.$t("app.login.tipsText")),1),m("a",{class:"linkPwd",onClick:b[10]||(b[10]=I=>De("pagePasswordRetrieval"))},$(T.$t("app.login.retrievePassword")),1)]),_:1})]),_:1})]),_:1},8,["rules","model"]),p(M)?(R(),D("div",ga,[m("div",ma,[m("a",{href:"#",class:"linkBack",onClick:b[12]||(b[12]=I=>p(C)())},$(T.$t("app.login.back")),1),m("span",va,$(T.$t("app.login.verifyCode")),1)]),m("div",ha,[w(Go,{"identify-qr-code":p(M),"custom-size":220,"custom-fg":p(z)},null,8,["identify-qr-code","custom-fg"]),m("div",wa,[p(V)?(R(),D("span",_a,$(p(V)),1)):(R(),D("span",Ca,$(T.$t("app.login.bioIdentificationTip",{countdown:p(F)})),1))])])])):p(A)!==0?(R(),B(ae,{key:1,ref_key:"multLoginForm",ref:n,rules:p(f),model:p(u),class:"form",onSubmit:b[17]||(b[17]=qe(()=>{},["prevent"]))},{default:_(()=>[m("div",ya,[m("a",{href:"#",class:"linkBack",onClick:b[13]||(b[13]=I=>p(C)())},$(T.$t("app.login.back")),1),m("span",Pa,$(T.$t("app.login.verifyCode")),1)]),w(O,{prop:"captcha"},{default:_(()=>[w(te,{modelValue:p(u).captcha,"onUpdate:modelValue":b[14]||(b[14]=I=>p(u).captcha=I),placeholder:T.$t("app.login.verifyCode"),clearable:"","prefix-icon":p(ze),size:"large",onKeyup:b[15]||(b[15]=Ie(I=>p(c)(),["enter"]))},null,8,["modelValue","placeholder","prefix-icon"])]),_:1}),w(O,null,{default:_(()=>[w(oe,{type:"primary",class:"submit-button",onClick:b[16]||(b[16]=I=>p(c)())},{default:_(()=>[X($(T.$t("app.login.sure")),1)]),_:1})]),_:1}),w(O,null,{default:_(()=>[w(oe,{type:"primary",class:"submit-button",disabled:p(o),onClick:p(t)},{default:_(()=>[X($(p(e)),1)]),_:1},8,["disabled","onClick"])]),_:1})]),_:1},8,["rules","model"])):H("",!0)],4),p(K)?(R(),B(jo,{key:0,modelValue:p(K),"onUpdate:modelValue":b[18]||(b[18]=I=>Te(K)?K.value=I:null),class:"max-h-[560px]",onChangeState:p(Y)},null,8,["modelValue","onChangeState"])):H("",!0)],64)}}});const Ea=ce(ba,[["__scopeId","data-v-0d1aae07"]]),ka="/sgui/assets/epid-a0f6d60a.png",Ra=a=>(le("data-v-ffce7b58"),a=a(),ue(),a),Ia={key:0,class:"epid-form"},$a={class:"form-header"},La=Ra(()=>m("img",{class:"logo",src:ka},null,-1)),Na=G({__name:"DomesticRepresentatives",props:{currentToken:{}},emits:["update:agentDomesticEpid"],setup(a,{emit:r}){const d=a,u=r,{t:c}=j(),f=Ge(),n=Me();let e="";const t=y(),o=y({epidNumber:"",activationCode:""}),s=y(!0),l={epidNumber:[{required:!0,message:c("app.domesticRepresentatives.pleaseInputChannelNumber"),trigger:"blur"},{pattern:no,message:c("app.domesticRepresentatives.illeigalAccess"),trigger:"blur"}],activationCode:[{required:!0,message:c("app.domesticRepresentatives.pleaseInputActiveCode"),trigger:"blur"},{pattern:ro,message:c("app.domesticRepresentatives.illeigalAccess"),trigger:"blur"}]},i=C=>{const h=c("app.domesticRepresentatives.expiryDateNotSufficient",{num:C});Qe.confirm(h,{icon:J("em",{class:"iconfont icon-info-circle-line text-brand-2 text-[32px]"}),customClass:"alert-message-common crs-btn-ui unbind-epid-expire-time-box",showClose:!1,showCancelButton:!1,closeOnClickModal:!1}).then(async()=>{await $e(d.currentToken,f)})},g=async(C,h)=>{try{await Ht(C,e);const k=Jt(h);if(k){await i(k);return}await $e(d.currentToken,f)}catch{u("update:agentDomesticEpid",!1)}},v=async()=>{var C;s.value=!1;try{const h=await qt(e),k=(C=h==null?void 0:h.data)==null?void 0:C[0];if(k&&k.activationStatus===0){const{epidNumber:A,v:L,nextOccupyStatus:M,expireTime:F}=k;await g({epidNumber:A,version:L,occupyStatus:M},F);return}s.value=!0}catch{u("update:agentDomesticEpid",!1)}},P=()=>{t.value.validate(async C=>{if(C){const h=await xt({epidNumber:o.value.epidNumber,activationCode:Le(o.value.activationCode)},e),k=h==null?void 0:h.data;if(!k)return;const{epidNumber:A,v:L,occupyStatus:M,expireTime:F}=k;await g({epidNumber:A,version:L,occupyStatus:M},F)}})};return ee(async()=>{const C=n.query.authValue;e=await ct(C),v()}),(C,h)=>{const k=ve,A=ne,L=ie,M=Ce;return s.value?(R(),D("div",Ia,[w(M,{ref_key:"representativesFormRef",ref:t,rules:l,model:o.value,onSubmit:h[4]||(h[4]=qe(()=>{},["prevent"]))},{default:_(()=>[m("div",$a,[m("div",null,$(C.$t("app.domesticRepresentatives.connectionChannelActivation")),1),La]),w(A,{prop:"epidNumber",label:C.$t("app.domesticRepresentatives.ChannelNumber")},{default:_(()=>[w(k,{modelValue:o.value.epidNumber,"onUpdate:modelValue":h[0]||(h[0]=F=>o.value.epidNumber=F),modelModifiers:{trim:!0},clearable:"",onKeyup:Ie(P,["enter"]),onInput:h[1]||(h[1]=F=>o.value.epidNumber=o.value.epidNumber.toUpperCase())},null,8,["modelValue"])]),_:1},8,["label"]),w(A,{prop:"activationCode",label:C.$t("app.domesticRepresentatives.activeCode")},{default:_(()=>[w(k,{modelValue:o.value.activationCode,"onUpdate:modelValue":h[2]||(h[2]=F=>o.value.activationCode=F),modelModifiers:{trim:!0},clearable:"",onKeyup:Ie(P,["enter"]),onInput:h[3]||(h[3]=F=>o.value.activationCode=o.value.activationCode.toUpperCase())},null,8,["modelValue"])]),_:1},8,["label"]),w(A,null,{default:_(()=>[w(L,{type:"primary",class:"submit-button",onClick:P},{default:_(()=>[X($(C.$t("app.domesticRepresentatives.active")),1)]),_:1})]),_:1})]),_:1},8,["model"])])):H("",!0)}}});const Ma=ce(Na,[["__scopeId","data-v-ffce7b58"]]),Aa=a=>{const r=y("pageLogin"),{resetPasswordReq:d,resetPasswordInfo:u}=ft(a),{findPassword:c,findPasswordPageInfo:f}=pt(a),n=()=>{c.userName="",c.captcha="",c.verificationCode="",f.resetLoading=!1,f.nextLoading=!1,f.nextCanClick=!0,f.findPasswordCanClick=!0,d.certificate="",d.recertificate="",u.resetConfLoading=!1};return{pageName:r,changePageStatus:t=>{n(),r.value=t},reset:n}},Sa=Aa,Da="/sgui/assets/forbidLogo-17609a7c.png",Fa="/sgui/assets/Vector-0248234d.png",Ta="/sgui/assets/forbidDown-94867865.png",Ua=a=>(le("data-v-92c5a629"),a=a(),ue(),a),Ba={class:"w-full h-[100vh] bg-gradient-to-b from-undefined to-undefined flex-col justify-center items-center gap-[170px] inline-flex bg-color"},Va={class:"w-[900px] justify-between items-center inline-flex"},za={class:"flex-col justify-start items-start gap-20 inline-flex"},Oa=Gt('<div class="justify-start items-center gap-6 inline-flex" data-v-92c5a629><div class="pt-1.5 bg-undefined bg-opacity-60 rounded-[128px] shadow shadow-inner justify-end items-center flex" data-v-92c5a629><img class="w-[76px]" src="'+Da+'" data-v-92c5a629></div><div class="flex-col justify-start items-start inline-flex" data-v-92c5a629><img class="w-[226px]" src="'+Fa+'" data-v-92c5a629></div></div>',1),Qa={class:"justify-start items-center gap-10 inline-flex"},qa={class:"text-justify text-brand-1 text-2xl font-normal"},xa={class:"h-[46px] py-[11px] bg-undefined rounded justify-center items-center gap-2.5 flex"},Ha=Ua(()=>m("img",{class:"w-[216px] h-[260px] relative",src:Ta},null,-1)),Ga=G({__name:"ForbidCrs",setup(a){const r=()=>{window.open("https://eterm.travelsky.cn/tosp/#/SGUI")};return(d,u)=>{const c=ie;return R(),D("div",Ba,[m("div",Va,[m("div",za,[Oa,m("div",Qa,[m("div",qa,$(d.$t("app.login.clientLogin")),1),m("div",xa,[w(c,{type:"primary",class:"download-button",size:"large",onClick:r},{default:_(()=>[X($(d.$t("app.login.downLoad")),1)]),_:1})])])]),Ha])])}}});const Ka=ce(Ga,[["__scopeId","data-v-92c5a629"]]),Ya=a=>a.length===3?{airline:a.slice(1,3),isIntranet:"0"}:a.length===2?{airline:a,isIntranet:"1"}:{airline:"travelsky",isIntranet:a==="i"?"0":"1"},Wa=()=>{const a=y(""),r=y(!1),d=y(""),u=se(()=>window.location.pathname.includes("crs")??!1),c=se(()=>window.location.href.includes("uatsgui")??!1),f=Me(),n=y(!0),e=y(!1),t=async()=>{const i=f.path.match(Kt)??["sgui"];Yt(f.path);const g=Ya(i[0]);d.value=g.airline},o=async()=>{try{if(e.value=!0,navigator.userAgent.toLowerCase().includes("electron/"))n.value=!1;else{const{data:i}=await Wt();i.value!==null&&(n.value=i.value)}}finally{e.value=!1}},s=()=>{window.open("https://uatsgui.travelsky.com.cn:38443/client-app/index.html")};return ee(async()=>{t(),u.value&&await o()}),{airlineRefer:d,isCrs:u,isUat:c,goUATClientDownload:s,currentToken:a,agentDomesticEpid:r,isForbidCrs:n,loading:e}},Xa=Wa,Ja=G({name:"Login",components:{PasswordRetrieval:Io,ResetPassword:Vo,I18n:eo,DomesticRepresentatives:Ma,LoginForm:Ea,ForbidCrs:Ka},setup(a,r){const d=y(!0),u=y(1),{locale:c}=j({useScope:"global"}),f=k=>{d.value=k},n=y(!0);jt(),sessionStorage.removeItem("bugTipsPermission"),He(c,()=>{u.value+=1});const{pageName:e,changePageStatus:t,reset:o}=Sa(r),{airlineRefer:s,isCrs:l,isUat:i,goUATClientDownload:g,currentToken:v,agentDomesticEpid:P,isForbidCrs:C,loading:h}=Xa();return{pageName:e,changePageStatus:t,reset:o,languageNumber:u,airlineRefer:s,showLineAndInfo:d,changeBtn:f,currentToken:v,agentDomesticEpid:P,isCrs:l,isUat:i,goUATClientDownload:g,isForbidCrs:C,loading:h,showLanguage:n}}}),Za="/sgui/assets/logo-crs-earth-4561e049.png",ja="/sgui/assets/logo-ics-earth-4e132c76.png";const Pe=a=>(le("data-v-4b97ebb2"),a=a(),ue(),a),es={key:0,class:"icon svg-icon","aria-hidden":"true"},ts=["xlink:href"],os={class:"wipper-container"},as={key:0,class:"logo logo-title"},ss=Pe(()=>m("img",{class:"m-auto h-[48px]",src:Za},null,-1)),ns=[ss],rs={key:1,class:"logo logo-title"},is=Pe(()=>m("img",{class:"m-auto h-[50px]",src:ja},null,-1)),ls=[is],us={class:"top"},cs=Pe(()=>m("span",{class:"vector-left vector"},null,-1)),ds=Pe(()=>m("span",{class:"ellipse"},null,-1)),ps=Pe(()=>m("span",{class:"vector-right vector"},null,-1)),fs=[cs,ds,ps],gs={class:"copyright-span text-gray-4 text-xs font-normal leading-tight"};function ms(a,r,d,u,c,f){const n=ge("i18n"),e=ge("ForbidCrs"),t=ge("DomesticRepresentatives"),o=ge("login-form"),s=ge("PasswordRetrieval"),l=ge("ResetPassword"),i=ye,g=lt;return we((R(),D("div",null,[a.showLanguage?(R(),B(n,{key:0})):H("",!0),a.isCrs&&a.isForbidCrs?(R(),B(e,{key:1})):(R(),B(i,{key:a.languageNumber,class:Oe(["login",a.isCrs?"login-new":""])},{default:_(()=>[a.airlineRefer?(R(),D("svg",es,[m("use",{"xlink:href":"#icon-"+a.airlineRefer+"-login"},null,8,ts)])):H("",!0),a.agentDomesticEpid?(R(),B(t,{key:1,"agent-domestic-epid":a.agentDomesticEpid,"onUpdate:agentDomesticEpid":r[0]||(r[0]=v=>a.agentDomesticEpid=v),class:"wipper-container","current-token":a.currentToken},null,8,["agent-domestic-epid","current-token"])):H("",!0),we(m("div",os,[a.isCrs?(R(),D("div",as,ns)):(R(),D("div",rs,ls)),we(m("div",us,fs,512),[[Ve,a.showLineAndInfo]]),a.pageName==="pageLogin"?(R(),B(o,{key:2,"current-token":a.currentToken,"onUpdate:currentToken":r[1]||(r[1]=v=>a.currentToken=v),"agent-domestic-epid":a.agentDomesticEpid,"onUpdate:agentDomesticEpid":r[2]||(r[2]=v=>a.agentDomesticEpid=v),"show-language":a.showLanguage,"onUpdate:showLanguage":r[3]||(r[3]=v=>a.showLanguage=v),onChagepagestatus:a.changePageStatus},null,8,["current-token","agent-domestic-epid","show-language","onChagepagestatus"])):a.pageName==="pagePasswordRetrieval"?(R(),B(s,{key:3,onChangeBtn:a.changeBtn,onChagepagestatus:a.changePageStatus,onReset:a.reset},null,8,["onChangeBtn","onChagepagestatus","onReset"])):a.pageName==="pageResetPassword"?(R(),B(l,{key:4,onChagepagestatus:a.changePageStatus,onReset:a.reset},null,8,["onChagepagestatus","onReset"])):H("",!0)],512),[[Ve,!a.agentDomesticEpid]]),m("span",gs,$(a.$t("app.login.companyName")),1),a.isUat?(R(),D("span",{key:2,class:"text-[14px] text-gray-4 cursor-pointer absolute right-[30px] bottom-[16px]",onClick:r[4]||(r[4]=(...v)=>a.goUATClientDownload&&a.goUATClientDownload(...v))},$(a.$t("app.login.downloadClient")),1)):H("",!0)]),_:1},8,["class"]))])),[[g,a.loading]])}const Qs=ce(Ja,[["render",ms],["__scopeId","data-v-4b97ebb2"]]);export{Qs as default};
