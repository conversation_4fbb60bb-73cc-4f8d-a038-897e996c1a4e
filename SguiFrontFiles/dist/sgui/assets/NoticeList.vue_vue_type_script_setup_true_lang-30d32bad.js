import{L as ye,e7 as Re,dJ as Ie,Y as je,r as b,w as j,A as e,dM as ze,ac as W,s as Fe,o as ie,el as qe,e6 as Ue,W as We,eq as Ke,er as Qe,q as G,v as ge,x as f,B as h,P as B,y as Q,z as V,a5 as X,D as R,E as oe,G as O,H as ne,es as Ge,a6 as se,T as pe,J as F,et as Je,F as be,C as Te,ai as Y,aj as K,Q as P,_ as ke,O as Ze,b0 as Xe,ar as et,ec as tt,K as ot,Z as st,ao as Ce,bT as Ne,aY as we,am as nt,an as at,R as xe,eu as lt,ak as le,c9 as Se,ev as it,ew as me,aZ as ct}from"./index-a2fbd71b.js";import{E as rt}from"./index-350461d4.js";import{E as ut,a as ft}from"./index-ff93035e.js";import{_ as ue}from"./_plugin-vue_export-helper-c27b6911.js";import{u as dt}from"./index-a42e5d8f.js";import{t as _e}from"./throttle-d22b7fb0.js";import{E as vt}from"./index-ac58700d.js";const pt=ye({initialIndex:{type:Number,default:0},height:{type:String,default:""},trigger:{type:String,values:["hover","click"],default:"hover"},autoplay:{type:Boolean,default:!0},interval:{type:Number,default:3e3},indicatorPosition:{type:String,values:["","none","outside"],default:""},arrow:{type:String,values:["always","hover","never"],default:"hover"},type:{type:String,values:["","card"],default:""},loop:{type:Boolean,default:!0},direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},pauseOnHover:{type:Boolean,default:!0}}),mt={change:(o,w)=>[o,w].every(Re)},$e=Symbol("carouselContextKey"),he=300,_t=(o,w,a)=>{const{children:n,addChild:M,removeChild:v}=dt(Ie(),"ElCarouselItem"),T=je(),l=b(-1),p=b(null),x=b(!1),C=b(),N=b(0),S=b(!0),E=j(()=>o.arrow!=="never"&&!e(s)),_=j(()=>n.value.some(u=>u.props.label.toString().length>0)),t=j(()=>o.type==="card"),s=j(()=>o.direction==="vertical"),m=j(()=>o.height!=="auto"?{height:o.height}:{height:`${N.value}px`,overflow:"hidden"}),y=_e(u=>{i(u)},he,{trailing:!0}),g=_e(u=>{ce(u)},he),d=u=>S.value?l.value<=1?u<=1:u>1:!0;function r(){p.value&&(clearInterval(p.value),p.value=null)}function I(){o.interval<=0||!o.autoplay||p.value||(p.value=setInterval(()=>$(),o.interval))}const $=()=>{l.value<n.value.length-1?l.value=l.value+1:o.loop&&(l.value=0)};function i(u){if(ze(u)){const Z=n.value.filter(U=>U.props.name===u);Z.length>0&&(u=n.value.indexOf(Z[0]))}if(u=Number(u),Number.isNaN(u)||u!==Math.floor(u))return;const L=n.value.length,z=l.value;u<0?l.value=o.loop?L-1:0:u>=L?l.value=o.loop?0:L-1:l.value=u,z===l.value&&c(z),fe()}function c(u){n.value.forEach((L,z)=>{L.translateItem(z,l.value,u)})}function k(u,L){var z,Z,U,ee;const te=e(n),de=te.length;if(de===0||!u.states.inStage)return!1;const Ae=L+1,Oe=L-1,ve=de-1,Pe=te[ve].states.active,He=te[0].states.active,Ve=(Z=(z=te[Ae])==null?void 0:z.states)==null?void 0:Z.active,Ye=(ee=(U=te[Oe])==null?void 0:U.states)==null?void 0:ee.active;return L===ve&&He||Ve?"left":L===0&&Pe||Ye?"right":!1}function q(){x.value=!0,o.pauseOnHover&&r()}function A(){x.value=!1,I()}function D(u){e(s)||n.value.forEach((L,z)=>{u===k(L,z)&&(L.states.hover=!0)})}function H(){e(s)||n.value.forEach(u=>{u.states.hover=!1})}function J(u){l.value=u}function ce(u){o.trigger==="hover"&&u!==l.value&&(l.value=u)}function Ee(){i(l.value-1)}function Le(){i(l.value+1)}function fe(){r(),o.pauseOnHover||I()}function De(u){o.height==="auto"&&(N.value=u)}function Be(){var u;const L=(u=T.default)==null?void 0:u.call(T);if(!L)return null;const z=Ke(L),Z="ElCarouselItem",U=z.filter(ee=>Qe(ee)&&ee.type.name===Z);return(U==null?void 0:U.length)===2&&o.loop&&!t.value?(S.value=!0,U):(S.value=!1,null)}W(()=>l.value,(u,L)=>{c(L),S.value&&(u=u%2,L=L%2),L>-1&&w("change",u,L)}),W(()=>o.autoplay,u=>{u?I():r()}),W(()=>o.loop,()=>{i(l.value)}),W(()=>o.interval,()=>{fe()});const re=Fe();return ie(()=>{W(()=>n.value,()=>{n.value.length>0&&i(o.initialIndex)},{immediate:!0}),re.value=qe(C.value,()=>{c()}),I()}),Ue(()=>{r(),C.value&&re.value&&re.value.stop()}),We($e,{root:C,isCardType:t,isVertical:s,items:n,loop:o.loop,addItem:M,removeItem:v,setActiveItem:i,setContainerHeight:De}),{root:C,activeIndex:l,arrowDisplay:E,hasLabel:_,hover:x,isCardType:t,items:n,isVertical:s,containerStyle:m,isItemsTwoLength:S,handleButtonEnter:D,handleButtonLeave:H,handleIndicatorClick:J,handleMouseEnter:q,handleMouseLeave:A,setActiveItem:i,prev:Ee,next:Le,PlaceholderItem:Be,isTwoLengthShow:d,throttledArrowClick:y,throttledIndicatorHover:g}},ht=["onMouseenter","onClick"],yt={key:0},It="ElCarousel",gt=G({name:It}),bt=G({...gt,props:pt,emits:mt,setup(o,{expose:w,emit:a}){const n=o,{root:M,activeIndex:v,arrowDisplay:T,hasLabel:l,hover:p,isCardType:x,items:C,isVertical:N,containerStyle:S,handleButtonEnter:E,handleButtonLeave:_,handleIndicatorClick:t,handleMouseEnter:s,handleMouseLeave:m,setActiveItem:y,prev:g,next:d,PlaceholderItem:r,isTwoLengthShow:I,throttledArrowClick:$,throttledIndicatorHover:i}=_t(n,a),c=ge("carousel"),k=j(()=>{const A=[c.b(),c.m(n.direction)];return e(x)&&A.push(c.m("card")),A}),q=j(()=>{const A=[c.e("indicators"),c.em("indicators",n.direction)];return e(l)&&A.push(c.em("indicators","labels")),n.indicatorPosition==="outside"&&A.push(c.em("indicators","outside")),e(N)&&A.push(c.em("indicators","right")),A});return w({setActiveItem:y,prev:g,next:d}),(A,D)=>(f(),h("div",{ref_key:"root",ref:M,class:R(e(k)),onMouseenter:D[6]||(D[6]=oe((...H)=>e(s)&&e(s)(...H),["stop"])),onMouseleave:D[7]||(D[7]=oe((...H)=>e(m)&&e(m)(...H),["stop"]))},[B("div",{class:R(e(c).e("container")),style:Te(e(S))},[e(T)?(f(),Q(pe,{key:0,name:"carousel-arrow-left",persisted:""},{default:V(()=>[X(B("button",{type:"button",class:R([e(c).e("arrow"),e(c).em("arrow","left")]),onMouseenter:D[0]||(D[0]=H=>e(E)("left")),onMouseleave:D[1]||(D[1]=(...H)=>e(_)&&e(_)(...H)),onClick:D[2]||(D[2]=oe(H=>e($)(e(v)-1),["stop"]))},[O(e(ne),null,{default:V(()=>[O(e(Ge))]),_:1})],34),[[se,(A.arrow==="always"||e(p))&&(n.loop||e(v)>0)]])]),_:1})):F("v-if",!0),e(T)?(f(),Q(pe,{key:1,name:"carousel-arrow-right",persisted:""},{default:V(()=>[X(B("button",{type:"button",class:R([e(c).e("arrow"),e(c).em("arrow","right")]),onMouseenter:D[3]||(D[3]=H=>e(E)("right")),onMouseleave:D[4]||(D[4]=(...H)=>e(_)&&e(_)(...H)),onClick:D[5]||(D[5]=oe(H=>e($)(e(v)+1),["stop"]))},[O(e(ne),null,{default:V(()=>[O(e(Je))]),_:1})],34),[[se,(A.arrow==="always"||e(p))&&(n.loop||e(v)<e(C).length-1)]])]),_:1})):F("v-if",!0),O(e(r)),be(A.$slots,"default")],6),A.indicatorPosition!=="none"?(f(),h("ul",{key:0,class:R(e(q))},[(f(!0),h(Y,null,K(e(C),(H,J)=>X((f(),h("li",{key:J,class:R([e(c).e("indicator"),e(c).em("indicator",A.direction),e(c).is("active",J===e(v))]),onMouseenter:ce=>e(i)(J),onClick:oe(ce=>e(t)(J),["stop"])},[B("button",{class:R(e(c).e("button"))},[e(l)?(f(),h("span",yt,P(H.props.label),1)):F("v-if",!0)],2)],42,ht)),[[se,e(I)(J)]])),128))],2)):F("v-if",!0)],34))}});var Tt=ke(bt,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/carousel/src/carousel.vue"]]);const kt=ye({name:{type:String,default:""},label:{type:[String,Number],default:""}}),Ct=(o,w)=>{const a=Ze($e),n=Ie(),M=.83,v=b(),T=b(!1),l=b(0),p=b(1),x=b(!1),C=b(!1),N=b(!1),S=b(!1),{isCardType:E,isVertical:_}=a;function t(d,r,I){const $=I-1,i=r-1,c=r+1,k=I/2;return r===0&&d===$?-1:r===$&&d===0?I:d<i&&r-d>=k?I+1:d>c&&d-r>=k?-2:d}function s(d,r){var I,$;const i=e(_)?((I=a.root.value)==null?void 0:I.offsetHeight)||0:(($=a.root.value)==null?void 0:$.offsetWidth)||0;return N.value?i*((2-M)*(d-r)+1)/4:d<r?-(1+M)*i/4:(3+M)*i/4}function m(d,r,I){const $=a.root.value;return $?((I?$.offsetHeight:$.offsetWidth)||0)*(d-r):0}const y=(d,r,I)=>{var $;const i=e(E),c=($=a.items.value.length)!=null?$:Number.NaN,k=d===r;!i&&!tt(I)&&(S.value=k||d===I),!k&&c>2&&a.loop&&(d=t(d,r,c));const q=e(_);x.value=k,i?(N.value=Math.round(Math.abs(d-r))<=1,l.value=s(d,r),p.value=e(x)?1:M):l.value=m(d,r,q),C.value=!0,k&&v.value&&a.setContainerHeight(v.value.offsetHeight)};function g(){if(a&&e(E)){const d=a.items.value.findIndex(({uid:r})=>r===n.uid);a.setActiveItem(d)}}return ie(()=>{a.addItem({props:o,states:Xe({hover:T,translate:l,scale:p,active:x,ready:C,inStage:N,animating:S}),uid:n.uid,translateItem:y})}),et(()=>{a.removeItem(n.uid)}),{carouselItemRef:v,active:x,animating:S,hover:T,inStage:N,isVertical:_,translate:l,isCardType:E,scale:p,ready:C,handleItemClick:g}},Nt=G({name:"ElCarouselItem"}),wt=G({...Nt,props:kt,setup(o){const w=o,a=ge("carousel"),{carouselItemRef:n,active:M,animating:v,hover:T,inStage:l,isVertical:p,translate:x,isCardType:C,scale:N,ready:S,handleItemClick:E}=Ct(w),_=j(()=>{const s=`${`translate${e(p)?"Y":"X"}`}(${e(x)}px)`,m=`scale(${e(N)})`;return{transform:[s,m].join(" ")}});return(t,s)=>X((f(),h("div",{ref_key:"carouselItemRef",ref:n,class:R([e(a).e("item"),e(a).is("active",e(M)),e(a).is("in-stage",e(l)),e(a).is("hover",e(T)),e(a).is("animating",e(v)),{[e(a).em("item","card")]:e(C),[e(a).em("item","card-vertical")]:e(C)&&e(p)}]),style:Te(e(_)),onClick:s[0]||(s[0]=(...m)=>e(E)&&e(E)(...m))},[e(C)?X((f(),h("div",{key:0,class:R(e(a).e("mask"))},null,2)),[[se,!e(M)]]):F("v-if",!0),be(t.$slots,"default")],6)),[[se,e(S)]])}});var Me=ke(wt,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/carousel/src/carousel-item.vue"]]);const xt=ot(Tt,{CarouselItem:Me}),St=st(Me),$t=(o,w)=>Ce(`${Ne}/announcement/queryTopicList`,{headers:{gid:w}}).post(o).json(),Mt=(o,w)=>Ce(`${Ne}/announcement/queryTopicDetail`,{headers:{gid:w}}).post(o).json(),Et=o=>(nt("data-v-abec693f"),o=o(),at(),o),Lt=Et(()=>B("div",{class:"h-0 border-t border-dashed border-gray-6 w-[90%] my-[10px]"},null,-1)),Dt=["onClick"],Bt=["id","title"],At=G({__name:"NoticeMain",props:{isModal:{type:Boolean}},emits:["get-topic-name"],setup(o,{expose:w,emit:a}){const n=b(),M=o,v=a,T=b("1"),l=b([]),p=b([]),x=b(0),C=b(0),N=(_,t)=>{x.value=_.topicNumber,C.value=_.topicNumber,v("get-topic-name",_.topicName,p.value[0]??[]),t&&(T.value="1")},S=(_,t)=>{var s;_==="crs"?(T.value="1",N((s=p.value)==null?void 0:s[0])):(T.value="2",setTimeout(()=>{var y;let m=((y=document.getElementById(`${_}-${t.topicName}`))==null?void 0:y.offsetTop)??0;n.value.scrollTo({top:m}),N(t)},100))},E=_=>{const t=(p.value??[]).find(s=>s.topicName===_);if(t)S("crs",t);else{const s=(l.value??[]).find(m=>m.topicName===_);s&&S("ics",s)}};return ie(async()=>{var s,m,y,g,d,r;const _=we("141L0125"),{data:t}=await $t({sysId:"C"},_);l.value=((s=t==null?void 0:t.value)==null?void 0:s.icsTopics)??[],p.value=((m=t==null?void 0:t.value)==null?void 0:m.crsTopics)??[],C.value=(g=(y=p.value)==null?void 0:y[0])==null?void 0:g.topicNumber,v("get-topic-name",(r=(d=p.value)==null?void 0:d[0])==null?void 0:r.topicName)}),w({focusName:E,onClick:N}),(_,t)=>{const s=ut,m=ft;return f(),Q(m,{modelValue:T.value,"onUpdate:modelValue":t[1]||(t[1]=y=>T.value=y),accordion:"",class:"notice"},{default:V(()=>{var y;return[O(s,{id:`crs-${((y=p.value[0])==null?void 0:y.topicName)??"MISSIC"}`,title:_.$t("app.systemNotice.sysNoticeTitle"),name:"1",class:R(["notice system-notice",T.value==="1"?"text-color":""]),"data-gid":"01060201",onClick:t[0]||(t[0]=()=>{N(p.value[0])})},null,8,["id","title","class"]),Lt,O(s,{title:_.$t("app.systemNotice.Airline"),name:"2",class:"notice second"},{default:V(()=>[O(e(rt),{ref_key:"icsScrollbarRef",ref:n,"max-height":"430px",always:""},{default:V(()=>[B("ul",{class:R(["notice_ul",M.isModal?"modal_notice_ul":""])},[(f(!0),h(Y,null,K(l.value,g=>(f(),h("li",{key:g.msgId,"data-gid":"01060201",class:R(["notice-item",x.value===g.topicNumber?"select":""]),onClick:()=>{N(g)}},[B("div",{id:`ics-${g.topicName}`,title:g.topicName},P(g.topicName==="MISSIC"?"代理人公告":g.topicName),9,Bt)],10,Dt))),128))],2)]),_:1},512)]),_:1},8,["title"])]}),_:1},8,["modelValue"])}}});const Io=ue(At,[["__scopeId","data-v-abec693f"]]),Ot={class:"topic-info"},Pt={class:"topic"},Ht={class:"author-info"},Vt=G({__name:"noticeTitle",props:{title:{},time:{}},setup(o){const w=o,a=j(()=>w.time?xe(w.time).format("YYYY-MM-DD hh:mm:ss"):"");return(n,M)=>{const v=ne;return f(),h("div",Ot,[B("div",Pt,P(n.title??""),1),B("div",Ht,[B("span",null,[O(v,null,{default:V(()=>[O(e(lt))]),_:1}),le(P(n.$t("app.systemNotice.publisher")),1)]),B("span",null,[O(v,null,{default:V(()=>[O(e(Se))]),_:1}),le(P(n.$t("app.systemNotice.date"))+" ",1),B("span",null,P(a.value??""),1)])])])}}});const ae=ue(Vt,[["__scopeId","data-v-6ad65fd9"]]),Yt=(o,w)=>{const a=b({}),n=b(o.currentIndex??-1),M=b(),v=b(!0),T=b(!1),l=b(!1),p=j(()=>{var s;const t=(s=a.value)==null?void 0:s.contentLinkText;return t&&t.length>0}),x=j(()=>{var s;const t=(s=a.value)==null?void 0:s.subTopics;return t&&t.length>0}),C=j(()=>{var s,m;const t=(m=(s=a.value)==null?void 0:s.subTopics[n.value])==null?void 0:m.contentLinkText;return t&&(t==null?void 0:t.length)>0}),N=j(()=>{const t=n.value===-1,s=p.value||x.value;return t&&p.value?{show:"text",showMainContent:!0}:s?{show:"subtext",showMainContent:!1}:{show:"notext",showMainContent:!0}}),S=t=>{n.value=t},E=t=>{w("focusTopicName",t)},_=()=>{w("back",n.value)};return W(()=>o.topicInfos,()=>{o.topicInfos&&(a.value=o.topicInfos??{})},{immediate:!0,deep:!0}),W(()=>o.currentIndex,()=>{o.currentIndex&&(n.value=o.currentIndex??-1)},{immediate:!0,deep:!0}),{currentTopicInfos:a,currentSubTopicIndex:n,noticeCarousel:M,changePage:S,isSpillout:T,showMainContent:v,isShowMore:l,focusTopicName:E,back:_,isShowContentOrSubInfo:N,hasContent:p,hasSubsContent:C}},Rt=Yt,jt={class:"notice-detail-info"},zt={key:0},Ft={key:1},qt={key:2},Ut={key:0},Wt={key:1},Kt={class:"messages"},Qt={key:0},Gt={key:0,class:"original-text-font text-gray-2"},Jt=["onClick"],Zt={key:0,class:"original-text-font text-gray-2"},Xt=["onClick"],eo=G({__name:"NoticeDetailInfo",props:{topicInfos:{},currentIndex:{}},emits:["focusTopicName","back"],setup(o,{expose:w,emit:a}){const n=o,M=a,{currentTopicInfos:v,noticeCarousel:T,changePage:l,isShowContentOrSubInfo:p,currentSubTopicIndex:x,hasSubsContent:C,focusTopicName:N,back:S}=Rt(n,M);return w({focusTopicName:N}),(E,_)=>{var y,g,d,r,I,$;const t=ne,s=St,m=xt;return f(),h("div",jt,[e(p).show==="subtext"?(f(),h("div",{key:0,ref:"backRef",class:"mb-2.5 text-brand-2 text-xs cursor-pointer",onClick:_[0]||(_[0]=(...i)=>e(S)&&e(S)(...i))},[O(t,{class:"text-brand-2"},{default:V(()=>[O(e(it))]),_:1}),le(P(E.$t("app.systemNotice.back")),1)],512)):F("",!0),B("div",null,[e(p).show==="text"?(f(),h("div",zt,[(f(!0),h(Y,null,K((y=e(v))==null?void 0:y.contentLinkText,(i,c)=>(f(),h(Y,{key:c},[c===0?(f(),Q(ae,{key:c,title:i==null?void 0:i.title,time:i==null?void 0:i.time},null,8,["title","time"])):F("",!0)],64))),128))])):e(p).show==="notext"?(f(),h("div",Ft,[O(ae,{title:(g=e(v))==null?void 0:g.topicName,time:(d=e(v))==null?void 0:d.updateTime},null,8,["title","time"])])):e(p).show==="subtext"?(f(),h("div",qt,[e(C)?(f(),h("div",Ut,[(f(!0),h(Y,null,K((I=(r=e(v))==null?void 0:r.subTopics[e(x)])==null?void 0:I.contentLinkText,(i,c)=>(f(),h(Y,{key:c},[c===0?(f(),Q(ae,{key:0,title:i==null?void 0:i.title,time:i==null?void 0:i.time},null,8,["title","time"])):F("",!0)],64))),128))])):(f(),h("div",Wt,[O(ae,{title:"",time:""})]))])):F("",!0)]),B("div",Kt,[e(p).showMainContent?(f(),h("div",Qt,[(f(!0),h(Y,null,K(($=e(v))==null?void 0:$.contentLinkText,(i,c)=>(f(),h(Y,{key:c},[i.type&&i.type==="text"?(f(),h("span",Gt,P(i.text),1)):(f(),h("span",{key:1,class:"cursor-pointer text-brand-2 original-text-font",onClick:k=>e(N)(i.text)},P(i.text),9,Jt))],64))),128))])):(f(),Q(m,{key:1,ref_key:"noticeCarousel",ref:T,height:"420",trigger:"click",autoplay:!1,"indicator-position":"none","initial-index":e(x),onChange:e(l)},{default:V(()=>{var i;return[(f(!0),h(Y,null,K((i=e(v))==null?void 0:i.subTopics,c=>(f(),Q(s,{key:c.subTopicIndex},{default:V(()=>[(f(!0),h(Y,null,K(c.contentLinkText,(k,q)=>(f(),h(Y,{key:q},[k.type==="text"?(f(),h("span",Zt,P(k.text),1)):(f(),h("span",{key:1,class:"cursor-pointer text-brand-2 original-text-font",onClick:A=>e(N)(k.text)},P(k.text),9,Xt))],64))),128))]),_:2},1024))),128))]}),_:1},8,["initial-index","onChange"]))])])}}});const to=ue(eo,[["__scopeId","data-v-defceff9"]]),oo=(o,w)=>{const a=b({}),n=b(-1);let M="";const v=b(""),T=b(!1),l=b(!1),p=(t,s,m)=>{if(!t)return[];const y=[],g=t.match(me),d=(t==null?void 0:t.split(me))??[];let r=0;for(let I=0;I<d.length;I++)d[I]===void 0?(y.push({type:"action",text:(g==null?void 0:g[r])??"",title:s,time:m}),r++):y.push({type:"text",text:d[I],title:s,time:m});return y},x=t=>{n.value=t,l.value=!0,v.value=""},C=t=>{var m,y,g;const s=((m=t==null?void 0:t.replace("YI:",""))==null?void 0:m.split("/"))??[];v.value=(y=s[1])==null?void 0:y.trim(),w("focusTopicName",(g=s[0])==null?void 0:g.trim())},N=t=>{n.value=t,l.value=!1},S=async t=>{var m,y,g,d,r,I,$;const s={sysId:"C",topicName:t};try{T.value=!0;const i=we("141L0126"),{data:c}=await Mt(s,i);a.value=(c==null?void 0:c.value)??{},(((m=c==null?void 0:c.value)==null?void 0:m.subTopics)??[]).length||(l.value=!0),M=t,a.value.contentLinkText=p((g=(y=a.value)==null?void 0:y.msgInfo)==null?void 0:g.msgContent,(r=(d=a.value)==null?void 0:d.msgInfo)==null?void 0:r.msgTitle,($=(I=a.value)==null?void 0:I.msgInfo)==null?void 0:$.updateTime),(a.value.subTopics??[]).forEach(k=>{var q,A,D;k.contentLinkText=p((q=k==null?void 0:k.msgInfo)==null?void 0:q.msgContent,(A=k==null?void 0:k.msgInfo)==null?void 0:A.msgTitle,(D=k==null?void 0:k.msgInfo)==null?void 0:D.updateTime)})}finally{T.value=!1}},E=async t=>{l.value=!1,n.value=-1,a.value={},await S(t)},_=async()=>{var t;if(M!==o.topicName&&await E(o.topicName),v.value){const s=(t=a.value)==null?void 0:t.subTopics.findIndex(m=>m.subTopicName===v.value);if(s>-1){x(s);return}}};return W(()=>o.topicName,()=>{o.topicName&&_()}),ie(async()=>{o.topicName&&o.topicName!==""&&await E(o.topicName)}),{topicInfos:a,currentSubTopicIndex:n,goSubTopic:x,repeatShowMainTopicInfo:_,loading:T,isShowDetail:l,focusTopicName:C,goBack:N}},so=oo,no={class:"h-full notice-list"},ao={class:"font-['Inconsolata'] text-gray-1 text-[16px] leading-[22px] font-bold"},lo=["onClick"],io={class:"flex justify-between items-center font-['Inconsolata']"},co={class:"flex justify-between items-center text-xs"},ro={class:"mx-[3px] text-gray-4"},uo={class:"text-gray-2"},go=G({__name:"NoticeList",props:{topicName:{}},emits:["focusTopicName"],setup(o,{expose:w,emit:a}){const n=o,M=a,{topicInfos:v,currentSubTopicIndex:T,goSubTopic:l,repeatShowMainTopicInfo:p,loading:x,isShowDetail:C,focusTopicName:N,goBack:S}=so(n,M);return w({repeatShowMainTopicInfo:p,goSubTopic:l}),(E,_)=>{var y,g,d;const t=vt,s=ne,m=ct;return X((f(),h("div",no,[(((y=e(v))==null?void 0:y.subTopics)??[]).length>0&&!e(C)?(f(),h(Y,{key:0},[B("div",ao,P((g=e(v))==null?void 0:g.topicName),1),(f(!0),h(Y,null,K((d=e(v))==null?void 0:d.subTopics,(r,I)=>(f(),h("div",{key:r.subTopicIndex,class:"flex justify-between items-center py-2.5 border-b border-dashed border-b-gray-6",onClick:$=>e(l)(I)},[B("div",io,[O(t,{class:"!h-[20px] bg-brand-3 text-brand-1 !rounded-sm !px-[15px]"},{default:V(()=>[le(P(r.subTopicName),1)]),_:2},1024),B("span",{class:R(["hover:text-brand-2 max-w-[500px] ml-2.5 text-[16px] leading-[22px] cursor-pointer font-bold truncate ...",e(T)===I?"text-brand-2":"text-gray-1"])},P(r.msgTitle),3)]),B("div",co,[O(s,null,{default:V(()=>[O(e(Se))]),_:1}),B("span",ro,P(E.$t("app.systemNotice.date")),1),B("span",uo,P(r.updateTime?e(xe)(r.updateTime).format("YYYY-MM-DD hh:mm:ss"):""),1)])],8,lo))),128))],64)):F("",!0),e(C)&&e(v)?(f(),Q(to,{key:1,"topic-infos":e(v),"current-index":e(T),onFocusTopicName:e(N),onBack:e(S)},null,8,["topic-infos","current-index","onFocusTopicName","onBack"])):F("",!0)])),[[m,e(x)]])}}});export{Io as N,go as _};
