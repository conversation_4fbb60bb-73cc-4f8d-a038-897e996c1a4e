import{dW as Fo,gD as Za,gE as Lo,f1 as wo,dV as No,gF as Io,gG as Eo,gH as Po,L as Mo,gI as Qo,gJ as Xa,q as Re,Y as Oo,v as Ro,r as O,w as Ie,x as p,y as Ae,z as $,a5 as mt,P as n,D as be,A as t,H as He,b9 as Vo,J as ge,B as D,F as lt,ak as me,Q as b,ai as _e,G as _,a6 as fa,T as Bo,_ as Yo,gK as Uo,K as Ho,dL as qo,m as jo,aF as gt,gL as Go,gM as eo,aS as Je,aR as nt,aA as Ko,a$ as ma,f6 as it,gN as zo,ao as Ye,a_ as We,dl as to,R as te,ab as rt,ac as Ge,dz as ao,aj as ke,X as oo,gO as Jo,gP as Wo,gQ as Zo,c0 as ta,b0 as ha,d1 as Gt,o as pt,fo as Xo,C as sa,b3 as Ze,aE as en,am as $t,an as xt,ah as Tt,E as Et,b1 as Wt,c3 as s,aB as St,aC as Ot,aH as no,af as Pt,ag as qt,fe as tn,aY as an,a9 as Kt,s as et,aG as ft,aO as io,gR as Ia,gS as Ea,aq as jt,f9 as Be,dD as on,ar as kt,gT as nn,aN as Ct,aT as Ca,aU as rn,al as aa,b7 as Pa,b8 as Ma,aW as sn,aX as Da,aZ as oa,fr as ln,au as cn,eW as ro,gU as la,aw as dn,a8 as un,fE as pn,fs as ca,c1 as gn,g8 as fn,ez as so,fb as mn,bo as hn,gV as vn,gW as yn,fd as Qa,at as lo,ey as co,gX as Ue,gY as bn,d0 as Aa,bT as Rt}from"./index-a2fbd71b.js";import{U as _n,m as Cn}from"./config-6a4e2d9f.js";import{E as Dn}from"./index-a5188d18.js";import{E as An}from"./index-4cb0667b.js";import{_ as je}from"./_plugin-vue_export-helper-c27b6911.js";import{E as $a}from"./index-e246ba26.js";import{E as bt}from"./index-fe3402a7.js";import{_ as $n}from"./theme-light_empty-0081a108.js";import{E as xa}from"./index-3fe919b5.js";import{u as na}from"./TicketPopItem.vue_vue_type_script_setup_true_lang-eb783de8.js";import{E as Zt,a as uo}from"./index-8ff7f67e.js";import{c as xn}from"./_createAggregator-542037ee.js";import{C as po}from"./index-4e9a4449.js";import{E as _t}from"./index-35a05a15.js";import{e as Tn}from"./index-7a3e9562.js";import{f as Sn,E as Xt,a as go}from"./index-e149ca58.js";import{g as Ta,a as fo}from"./time-04300430.js";import{a7 as kn,a2 as Fn,a5 as Ln,a8 as Oa,a9 as wn}from"./regular-crs-531dcd3f.js";import{E as Vt,a as Bt}from"./index-96be5bee.js";import{E as Nn,a as In,b as En}from"./index-b5333773.js";import{t as va}from"./throttle-d22b7fb0.js";import{E as mo}from"./index-350461d4.js";import{E as Pn}from"./index-0d3e7ae5.js";import{u as Mn}from"./usePersonalization-e583570d.js";import{i as Qn}from"./TicketInfoPopover-9ac1ad15.js";import{_ as On}from"./Personalization.vue_vue_type_script_setup_true_lang-c9263e2a.js";import{E as Rn}from"./index-fe047024.js";import{u as Vn}from"./useTemporaryOrderUtils-37a380df.js";import{E as Bn}from"./index-61b49c38.js";const Ra=e=>{let a=0,o=e;for(;o;)a+=o.offsetTop,o=o.offsetParent;return a},Yn=(e,a)=>Math.abs(Ra(e)-Ra(a));function Un(e,a,o){var r=-1,d=e.length;a<0&&(a=-a>d?0:d+a),o=o>d?d:o,o<0&&(o+=d),d=a>o?0:o-a>>>0,a>>>=0;for(var i=Array(d);++r<d;)i[r]=e[r+a];return i}function Hn(e,a){return a.length<2?e:Fo(e,Un(a,0,-1))}function qn(e,a){return a=Za(a,e),e=Hn(e,a),e==null||delete e[Lo(wo(a))]}function jn(e){return Tn(e)?void 0:e}var Gn=1,Kn=2,zn=4,Jn=Sn(function(e,a){var o={};if(e==null)return o;var r=!1;a=No(a,function(i){return i=Za(i,e),r||(r=i.length>1),i}),Io(e,Eo(e),o),r&&(o=Po(o,Gn|Kn|zn,jn));for(var d=a.length;d--;)qn(o,a[d]);return o});const Wn=Jn;var Zn=xn(function(e,a,o){e[o?0:1].push(a)},function(){return[[],[]]});const Lt=Zn,Xn=["light","dark"],ei=Mo({title:{type:String,default:""},description:{type:String,default:""},type:{type:String,values:Qo(Xa),default:"info"},closable:{type:Boolean,default:!0},closeText:{type:String,default:""},showIcon:Boolean,center:Boolean,effect:{type:String,values:Xn,default:"light"}}),ti={close:e=>e instanceof MouseEvent},ai=Re({name:"ElAlert"}),oi=Re({...ai,props:ei,emits:ti,setup(e,{emit:a}){const o=e,{Close:r}=Uo,d=Oo(),i=Ro("alert"),l=O(!0),A=Ie(()=>Xa[o.type]),g=Ie(()=>[i.e("icon"),{[i.is("big")]:!!o.description||!!d.default}]),C=Ie(()=>({[i.is("bold")]:o.description||d.default})),v=m=>{l.value=!1,a("close",m)};return(m,T)=>(p(),Ae(Bo,{name:t(i).b("fade"),persisted:""},{default:$(()=>[mt(n("div",{class:be([t(i).b(),t(i).m(m.type),t(i).is("center",m.center),t(i).is(m.effect)]),role:"alert"},[m.showIcon&&t(A)?(p(),Ae(t(He),{key:0,class:be(t(g))},{default:$(()=>[(p(),Ae(Vo(t(A))))]),_:1},8,["class"])):ge("v-if",!0),n("div",{class:be(t(i).e("content"))},[m.title||m.$slots.title?(p(),D("span",{key:0,class:be([t(i).e("title"),t(C)])},[lt(m.$slots,"title",{},()=>[me(b(m.title),1)])],2)):ge("v-if",!0),m.$slots.default||m.description?(p(),D("p",{key:1,class:be(t(i).e("description"))},[lt(m.$slots,"default",{},()=>[me(b(m.description),1)])],2)):ge("v-if",!0),m.closable?(p(),D(_e,{key:2},[m.closeText?(p(),D("div",{key:0,class:be([t(i).e("close-btn"),t(i).is("customed")]),onClick:v},b(m.closeText),3)):(p(),Ae(t(He),{key:1,class:be(t(i).e("close-btn")),onClick:v},{default:$(()=>[_(t(r))]),_:1},8,["class"]))],64)):ge("v-if",!0)],2)],2),[[fa,l.value]])]),_:3},8,["name"]))}});var ni=Yo(oi,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/alert/src/alert.vue"]]);const ii=Ho(ni),ut="ElInfiniteScroll",ri=50,si=200,li=0,ci={delay:{type:Number,default:si},distance:{type:Number,default:li},disabled:{type:Boolean,default:!1},immediate:{type:Boolean,default:!0}},Sa=(e,a)=>Object.entries(ci).reduce((o,[r,d])=>{var i,l;const{type:A,default:g}=d,C=e.getAttribute(`infinite-scroll-${r}`);let v=(l=(i=a[C])!=null?i:C)!=null?l:g;return v=v==="false"?!1:v,v=A(v),o[r]=Number.isNaN(v)?g:v,o},{}),ho=e=>{const{observer:a}=e[ut];a&&(a.disconnect(),delete e[ut].observer)},di=(e,a)=>{const{container:o,containerEl:r,instance:d,observer:i,lastScrollTop:l}=e[ut],{disabled:A,distance:g}=Sa(e,d),{clientHeight:C,scrollHeight:v,scrollTop:m}=r,T=m-l;if(e[ut].lastScrollTop=m,i||A||T<0)return;let E=!1;if(o===e)E=v-(C+m)<=g;else{const{clientTop:N,scrollHeight:P}=e,f=Yn(e,r);E=m+C>=f+N+P-g}E&&a.call(d)};function da(e,a){const{containerEl:o,instance:r}=e[ut],{disabled:d}=Sa(e,r);d||o.clientHeight===0||(o.scrollHeight<=o.clientHeight?a.call(r):ho(e))}const ui={async mounted(e,a){const{instance:o,value:r}=a;qo(r)||jo(ut,"'v-infinite-scroll' binding value must be a function"),await gt();const{delay:d,immediate:i}=Sa(e,o),l=Go(e,!0),A=l===window?document.documentElement:l,g=va(di.bind(null,e,r),d);if(l){if(e[ut]={instance:o,container:l,containerEl:A,delay:d,cb:r,onScroll:g,lastScrollTop:A.scrollTop},i){const C=new MutationObserver(va(da.bind(null,e,r),ri));e[ut].observer=C,C.observe(e,{childList:!0,subtree:!0}),da(e,r)}l.addEventListener("scroll",g)}},unmounted(e){const{container:a,onScroll:o}=e[ut];a==null||a.removeEventListener("scroll",o),ho(e)},async updated(e){if(!e[ut])await gt();else{const{containerEl:a,cb:o,observer:r}=e[ut];a.clientHeight&&r&&da(e,o)}}},ya=ui;ya.install=e=>{e.directive("InfiniteScroll",ya)};const pi=ya,gi=["ContactHeaderInfo","TableList","TabBars"],fi=["TagBar","Menu"],{createData:mi}=zo(),Yt=async()=>{const{agent:e}=await eo.getters.user;return`${_n}${e}`},Mt=async(e,a)=>{const o=await Yt();await ma(o,JSON.stringify(e),a)},hi=async()=>{const e=await Yt();return Je(await nt(e))},Dt=async()=>{const e=await hi();return e?{localData:JSON.parse(e.localData),updateTime:e.updateTime}:{localData:{orderList:[],orderDetails:[]},updateTime:new Date().getTime()}},Mg=async()=>{const e=await Yt();await Ko(e)},vi=async()=>{const{localData:e}=await Dt(),{orderList:a=[]}=e;return a},yi=async()=>(await vi()).some(a=>{var o;return((o=a.messages)==null?void 0:o.length)>0}),ka=async()=>{const e=await Yt();gi.forEach(a=>{it(`${e}_${a}`,"CHANGE")})},vo=async()=>{const e=await Yt(),a=await yi();fi.forEach(o=>{it(`${e}_NOTIFY_${o}`,a)})},Qg=async(e,a)=>{const{localData:o}=await Dt();o.orderList=e??[],await Mt(o,a),await ka(),await vo()},Og=async(e,a)=>{const{localData:o,updateTime:r}=await Dt();o.orderList??(o.orderList=[]),o.orderList.findIndex(i=>i.pnrNo===e)===-1&&(o.orderList.push({pnrNo:e,time:a,messages:[]}),await Mt(o,r),await ka())},Rg=async e=>{const{localData:a,updateTime:o}=await Dt();a.orderList??(a.orderList=[]);const r=a.orderList.findIndex(d=>d.pnrNo===e);r>-1&&(a.orderList[r].messages=[],await Mt(a,o),await vo())},Vg=async e=>{const{localData:a,updateTime:o}=await Dt();a.orderList??(a.orderList=[]);const r=a.orderList.findIndex(d=>d.pnrNo===e);r>-1&&(a.orderList.splice(r,1),await Mt(a,o),await ka())},Bg=async e=>{const{localData:a,updateTime:o}=await Dt();a.orderDetails??(a.orderDetails=[]);const r=a.orderDetails.findIndex(d=>d.pnrNo===e.pnrNo);r===-1?(a.orderDetails.push(e),await Mt(a,o)):(a.orderDetails[r]=e,await Mt(a,o))},Yg=async(e,a)=>{const o=await Yt();mi({id:o,localData:e,updateTime:a})},Ug=async()=>{const{localData:e}=await Dt(),{orderDetails:a=[]}=e;return a},Hg=async()=>{const{localData:e}=await Dt(),{orderList:a=[],orderDetails:o=[]}=e;return a.filter(r=>o.findIndex(d=>d.pnrNo===r.pnrNo)===-1).map(r=>r.pnrNo)},yo=(e,a)=>Ye(`${We}/flight/queryFlights`,{headers:{gid:a}}).post(e).json(),bi=(e,a)=>Ye(`${We}/flight/queryFlights`,{headers:{gid:a}},{ignoreError:!0}).post(e).json(),_i=(e,a)=>Ye(`${We}/flight/queryMultiInfos`,{headers:{gid:a}}).post(e).json(),Ci=()=>Ye(`${to}/commonConfiguration/terminalSelectInfo`).get().json(),Di=e=>Ye(`${to}/commonConfiguration/querySeamlessOrDa`,{headers:{gid:e}}).get().json(),qg=(e,a)=>Ye(`${We}/apiAvSearch/airRetrievePor`,{headers:{gid:e}},{onFetchError:a}).post().json(),jg=(e,a)=>Ye(`${We}/apiAvSearch/airPreOccupy`,{headers:{gid:a}}).post(e).json(),Gg=(e,a,o)=>Ye(`${We}/apiAvSearch/airCancelPor`,{headers:{gid:a}},{onFetchError:o}).post(e).json(),Kg=e=>Ye(`${We}/apiAvSearch/airIgnorePor`,{headers:{gid:e}}).post().json(),zg=(e,a)=>Ye(`${We}/apiAvSearch/airIgnorePor`,{headers:{gid:e}},{onFetchError:a}).post().json(),Jg=e=>Ye(`${We}/flight/segment/deleteSegment`).post(e).json(),Wg=e=>Ye(`${We}/flight/segment/modifyStatus`).post(e).json(),Zg=(e,a)=>Ye(`${We}/flight/segment/updateSegmentOrder`,{headers:{gid:a}}).post(e).json(),Xg=(e,a)=>Ye(`${We}/flight/segment/selectSegment`,{headers:{gid:a}}).post(e).json(),bo=e=>Ye(`${We}/flight/segment/addSegment`).post(e).json(),ef=e=>Ye(`${We}/flight/segment/unmark`).post(e).json(),Ai=(e,a)=>Ye(`${We}/flight/quota`,{headers:{gid:a}}).post(e).json(),tf=e=>Ye(`${We}/flight/segment/qtbRebook`).post(e).json(),zt=e=>{var r,d;if(!e)return"";const a=parseInt(((r=e==null?void 0:e.split(":"))==null?void 0:r[0])??0,10),o=parseInt(((d=e==null?void 0:e.split(":"))==null?void 0:d[1])??0,10);return`${a!==0?`${a}h`:""}${o!==0?`${o}m`:""}`},$i=()=>{const e=new Date;return e.setHours(0),e.setMinutes(0),e.setSeconds(0),e.setMilliseconds(0),te(e)},xi=(e,a)=>{const o=te(a).diff(e,"minute"),r=o%60,d=Math.floor(o/60);return d>0?r>0?`${d}h${r}m`:`${d}h`:`${r}m`},yt=(e,a)=>{let o=e&&te(e).isValid()?te(e).format("YYYY-MM-DD"):"";if(!o)return o;const r=new Date;return o=a&&te(o).isBefore(te(r),"day")?te(o).add(1,"year").format("YYYY-MM-DD"):te(o).format("YYYY-MM-DD"),o},Ti=["id"],ua="year-disabled-status",pa="month-disabled-status",wt="disabled-status",Va="custom-checkbox-container",Ba="custom-checkbox",Si=Re({__name:"CustomDatePicker",props:{modelValue:{default:""},disabledDate:{},disabled:{type:Boolean},disableEndDate:{},autoComputeDate:{type:Boolean},disabledDatePickerPreEl:{type:Boolean,default:!0},showSwitch:{type:Boolean},roundTripSwitch:{type:Boolean}},emits:["update:modelValue","update:roundTripSwitch","change"],setup(e,{emit:a}){const o=e,r=a,{t:d}=rt(),i=new Date,l=Ie({get:()=>o.roundTripSwitch?o.modelValue.length?[yt(o.modelValue[0],o.autoComputeDate),yt(o.modelValue[1],o.autoComputeDate)]:[]:yt(o.modelValue,o.autoComputeDate),set:x=>{if(o.roundTripSwitch){x&&r("update:modelValue",[yt(x[0],o.autoComputeDate),yt(x[1],o.autoComputeDate)]),!x&&r("update:modelValue","");return}r("update:modelValue",yt(x,o.autoComputeDate))}}),A=Ie(()=>o.disabled),g=O(),C=O();let v=[];const m=`custom-date-picker-panel-${new Date().getTime().toString()}`,T=O(`datePickerId-${new Date().getTime().toString()}`),E=O(`${m} custom-popover-picker-panel`),N=x=>{const S={valid:!1,date:""};if(Zo.test(x)){S.valid=!0;const U=x.split("-"),z=i.getFullYear().toString();let V="",K="";U.length>1?(V=U[0],K=U[1]):(V=x.slice(0,2),K=x.slice(2));const W=`${z}-${V}-${K}`;S.date=yt(W,o.autoComputeDate)}return S},P=x=>{if(!x)return{valid:!1,date:""};let S=!1,U="";if(Jo.test(x)){const z=x.split("-"),V=z[0],K=z[1].length>1?z[1]:`0${z[1]}`,W=z[2].length>1?z[2]:`0${z[2]}`;U=`${V}-${K}-${W}`,S=!0}else if(Wo.test(x))S=!0,U=`${x.slice(0,4)}-${x.slice(4,6)}-${x.slice(6,8)}`;else{const{valid:z,date:V}=N(x);S=z,U=V}if(te(U).isValid()){const z=new Date(te(U).format("YYYY-MM-DD"));U=o.disabledDate&&o.disabledDate(z)?"":te(U).format("YYYY-MM-DD"),S=!!U}return{valid:S,date:U}},f=x=>x?x.replace(/\s+/g,""):"",F=()=>{const x=document.getElementById(T.value),S=x==null?void 0:x.getElementsByTagName("input"),z=[f(S==null?void 0:S[0].value),f(S==null?void 0:S[1].value)].map(K=>P(K));if(z.some(K=>!K.valid)){r("update:modelValue",[]);return}r("update:modelValue",z.map(K=>K.date))},B=()=>{var V;const x=document.getElementById(T.value),S=((V=x==null?void 0:x.getElementsByTagName("input")[0].value)==null?void 0:V.trim())??"",{valid:U,date:z}=P(S);if(U){r("update:modelValue",z);return}r("update:modelValue","")},Y=()=>{if(o.roundTripSwitch){F();return}B()},h=x=>{v=x,r("change",x)},c=x=>{var W,I,L,w,M,k,ae,he,fe,de;const S=te(o.disableEndDate).isValid()?te(o.disableEndDate):te(new Date),U=(W=g.value)==null?void 0:W.children[0],z=(I=g.value)==null?void 0:I.children[1],V=(L=C.value)==null?void 0:L.children[0],K=(w=C.value)==null?void 0:w.children[1];S.isBefore(te(x),"year")?((M=g.value)==null||M.classList.remove(ua),(k=C.value)==null||k.classList.remove(ua),U==null||U.classList.remove(wt),V==null||V.classList.remove(wt)):((ae=g.value)==null||ae.classList.add(ua),U==null||U.classList.add(wt)),S.isBefore(te(x),"month")?((he=g.value)==null||he.classList.remove(pa),(fe=C.value)==null||fe.classList.remove(pa),z==null||z.classList.remove(wt),K==null||K.classList.remove(wt)):((de=g.value)==null||de.classList.add(pa),z==null||z.classList.add(wt))},u=()=>{const x=te(new Date).format("YYYY-MM-DD"),S=v.length?v:[x,te(x).add(1,"month").format("YYYY-MM-DD")];o.disableEndDate&&c(S[0])},J=async()=>{await gt();const x=document.querySelector(`.${m}`);if(!x)return;const S=x.querySelector(`.${Ba}`);if(S&&(S.checked=!!o.roundTripSwitch),o.roundTripSwitch){if(o.disabledDatePickerPreEl){const z=x.querySelector(".is-left");z&&(g.value=z.querySelector(".bkc-el-date-range-picker__header"))}const U=x.querySelector(".is-right");U&&(C.value=U.querySelector(".bkc-el-date-range-picker__header")),u();return}o.disabledDatePickerPreEl&&(g.value=x.querySelector(".bkc-el-date-picker__prev-btn")),C.value=x.querySelector(".bkc-el-date-picker__next-btn"),u()},y=()=>{if(!o.showSwitch)return;gt();const x=document.querySelector(`.${m}`);if(x&&!x.querySelector(`.${Va}`)){const S=document.createElement("div");S.className=Va;const U=document.createElement("input");U.type="checkbox",U.id="myCheckbox",U.className=Ba,U.checked=o.roundTripSwitch;const z=document.createElement("span");z.textContent=d("app.fastQuery.headerQuery.selectDate"),z.className="main-label";const V=document.createElement("span");V.textContent=d("app.fastQuery.headerQuery.Return"),V.className="check-label",S.insertBefore(V,S.firstChild),S.insertBefore(U,S.firstChild),S.insertBefore(z,S.firstChild),x.insertBefore(S,x.firstChild),U.addEventListener("change",async()=>{r("update:roundTripSwitch",!o.roundTripSwitch),r("update:modelValue",o.roundTripSwitch?"":[]),U.checked=!o.roundTripSwitch})}},G=x=>{if(o.roundTripSwitch)v=l.value;else{const S=te(l.value).format("YYYY-MM-DD");v=[S,te(S).add(1,"month").format("YYYY-MM-DD")]}y(),!(!x||!o.disableEndDate)&&J()},Q=x=>{if(o.roundTripSwitch)v=[te(x[0]).format("YYYY-MM-DD"),te(x[1]).format("YYYY-MM-DD")];else{const S=te(x).format("YYYY-MM-DD");v=[S,te(S).add(1,"month").format("YYYY-MM-DD")]}u()};return Ge(()=>o.roundTripSwitch,()=>{J()}),(x,S)=>{const U=xa;return p(),D("div",{id:T.value,class:"custom-date-picker"},[_(U,oo({modelValue:l.value,"onUpdate:modelValue":S[0]||(S[0]=z=>l.value=z),modelModifiers:{trim:!0}},x.$attrs,{"popper-class":E.value,"disabled-date":x.disabledDate,disabled:A.value,clearable:!0,onVisibleChange:G,onPanelChange:Q,onBlur:Y,onChange:h}),ao({_:2},[ke(x.$slots,(z,V)=>({name:V,fn:$(()=>[lt(x.$slots,V,{},void 0,!0)])}))]),1040,["modelValue","popper-class","disabled-date","disabled"])],8,Ti)}}});const Fa=je(Si,[["__scopeId","data-v-d8c88513"]]);var Jt=(e=>(e.A="A",e.B="B",e.H="H",e))(Jt||{});const ki=(e,a)=>{const o=na(),r="国内",d="国际",i=O(""),l=O(o.historyAirline),A=O({}),g=O({}),C=O(),v=O(!1),m=O(!1),T=O(!1),E=O(ta()),N=ha({}),P=ha({}),f=O([]);let F=!1;const B=O(""),Y=O(),h=O(),c=(j,Z)=>{const X=[];if(Z[0]){const ne=Z[0].airportCode;Z[0].airportCode=`${j}#${ne}`}for(let ne=0;ne<Z.length;)X.push(Z.slice(ne,ne+=4));return X},u=j=>{const Z=[];return(j??[]).forEach(X=>{const ne=[];X.alphabeticalOrderValue.forEach(ie=>{const ee={};ee.type=ie.alphabet,ee.airPort=c(ie.alphabet,ie.alphabetValue),ne.push(ee)});const ve={};ve.group=X.alphabeticalOrder,ve.groupValue=ne,Z.push(ve)}),Z},J=j=>{const Z=[Jt.A,Jt.B,Jt.H];f.value=j.map(X=>{const ne={...X,type:X.firstLevel===r,airportEnName:X.airportEnName||X.airportCode,airportFullName:"",airPortFormatName:"",cityName:"",cityEnName:X.cityEnName||X.cityCode,searchName:[...X.nameArray,...X.namePYArray].toString(),searchCity:[...X.cityArray,...X.cityPYArray].toString()};return E.value==="en"?(ne.cityName=ne.cityEnName,ne.airportFullName=ne.airportEnName===ne.airportCode?ne.airportCode:`${ne.airportEnName}${ne.airportCode}`,ne.airPortFormatName=ne.airportEnName):(ne.cityName=ne.secondLevel,ne.airportFullName=`${ne.airportCnName}${ne.airportCode}`,ne.airPortFormatName=ne.airportCnName),ne}).sort((X,ne)=>{const ve=X.firstLevel===r,ie=ne.firstLevel===r;if(ve&&!ie)return-1;if(!ve&&ie)return 1;const ee=X.codeType,ue=ne.codeType,$e=Z.indexOf(ee)===-1?Z.length:Z.indexOf(ee),Fe=Z.indexOf(ue)===-1?Z.length:Z.indexOf(ue);return $e-Fe})},y=()=>{var Z;const j={};j.data=JSON.parse((Z=Y.value)==null?void 0:Z.localData),N.aggregate=u(j.data.domestic),P.aggregate=u(j.data.international)},G=()=>{var X;const j={};j.data=JSON.parse((X=h.value)==null?void 0:X.localData);const Z=Je(j.data);J(Z)},Q=j=>{if(j==="local"){const Z=setInterval(async()=>{Y.value=await nt("diLocalData"),Y.value&&(y(),clearInterval(Z))},5e3)}else{const Z=setInterval(async()=>{h.value=await nt("searchLocalData"),h.value&&(G(),clearInterval(Z))},5e3)}},x=async()=>{if(Y.value=await nt("diLocalData"),!Y.value){Q("local");return}y()},S=async()=>{if(h.value=await nt("searchLocalData"),!h.value){Q("all");return}G()},U=(j,Z)=>((j??[]).some(ne=>{const ve=E.value==="en"?ne.airportEnName===Z.airportEnName:ne.airportCnName===Z.airportCnName;return ne.airportCode===Z.airportCode&&ve})||((j??[]).length===8&&j.shift(),(j??[]).push(Z)),j),z=(j,Z,X,ne)=>{var ie,ee;const ve={airportCode:j,airportCnName:Z,airportEnName:X,airPortFullName:"",airPortFormatName:""};ne?l.value.domesticHistory=U((ie=l.value)==null?void 0:ie.domesticHistory,ve):l.value.internationalHistory=U((ee=l.value)==null?void 0:ee.internationalHistory,ve),o.setHistoryAirLine(l.value)},V=(j,Z,X,ne,ve)=>{i.value=j,a("update:modelValue",j),a("update:name",E.value==="en"?X:Z),a("change",E.value==="en"?X:Z,j,e.segIndex),v.value=!1,C.value=ve??""},K=Gt(async j=>{j.length!==0?(v.value=!1,m.value=!1,T.value=!0,await gt(),F=!1,B.value=j,f.value.some(X=>`${X.airportCnName}${X.airportCode}`===j||`${X.cityName}${X.cityCode}`===j)&&(T.value=!1),v.value=!0):T.value=!1},200),W=Ie({get:()=>(i.value=e.modelValue.toLocaleUpperCase(),e.modelValue===e.name?e.modelValue:E.value==="en"&&e.name?`${e.modelValue} ${e.name??""}`:`${e.modelValue}${e.name??""}`),set:j=>{i.value=j.toLocaleUpperCase(),a("update:modelValue",j.toLocaleUpperCase()),a("update:name",""),a("change",j,e.segIndex),K(j)}}),I=()=>{v.value=!0,m.value=!0,T.value=!1},L=O({}),w=(j,Z)=>!j||(Xo.test(j)?j.length<3:j.length<2)?!1:Z.some(ne=>ne.airportCode===j||ne.cityCode===j||ne.airportCnName===j||ne.cityName===j?(C.value=ne.cityCode===j||ne.cityName.includes(j)?"city":"airport",L.value=ne,!0):!1),M=(j,Z,X,ne,ve)=>{F=!0,z(j,Z,X,ne),V(j,Z,X,ne,ve)},k=(j,Z,X,ne,ve)=>{F=!0,z(j,Z,X,ne),V(j,Z,X,ne,ve)},ae=()=>{if(F)return;const j=e.modelValue.toUpperCase(),Z=w(j,f.value);let X="",ne=j,ve="";Z&&(C.value==="airport"?(X=L.value.airportCnName,ve=L.value.airportEnName,ne=L.value.airportCode):(X=L.value.cityName,ve=L.value.cityEnName,ne=L.value.cityCode),z(ne,X,ve,L.value.type)),i.value=ne,a("update:name",E.value==="en"?ve:X),a("update:modelValue",ne),a("change",ne,e.segIndex)},he=j=>{j.keyCode===9&&ae()},fe=()=>{v.value&&(v.value=!1),T.value=!1,m.value=!1},de=async()=>{var Z,X;const j=await nt("HOT_CITIES");if(j){const ne=JSON.parse(j==null?void 0:j.localData);A.value=((Z=ne.filter(ve=>ve.field3===d))==null?void 0:Z[0])??{},g.value=((X=ne.filter(ve=>ve.field3===r))==null?void 0:X[0])??{}}},oe=()=>{a("triggerAirBlur")};return Ge(()=>{var j;return(j=eo.state.user)==null?void 0:j.switchAirline},async()=>{Y.value=await nt("diLocalData"),y()}),Ge(()=>e.isAshingTransitTerminal,()=>{e.isAshingTransitTerminal&&(m.value=!1)}),pt(async()=>{await x(),await S(),await de()}),{visible:v,hotCitiesOfDomestic:g,hotCitiesOfInter:A,tabShow:m,showInputModel:W,searchShow:T,domesticData:N,internationalData:P,searchAllData:f,inputValue:B,inputClick:I,keyChange:K,getPortValue:M,getSearchValue:k,clickOutside:fe,popoverHide:ae,updateCityCnName:he,airBlurClick:oe,locale:E,threeCharacterCodeInput:i}},Fi=ki,Li=(e,a)=>{const o=na(),r=O(""),d=O(""),i=O([]),l=O([]),A=Ie(()=>{const u=o.historyAirline,J=e.difference?u.domesticHistory:u.internationalHistory;return Je(J).map(G=>{var Q;return{...G,airPortFullName:e.locale==="en"?G.airportEnName||G.airportCode:G.airportCnName||G.airportCode,airPortFormatName:m(G),tooltipDisabled:e.locale==="en"?((Q=G.airportEnName)==null?void 0:Q.length)<14:G.airportCnName.length<7}})}),g=async u=>{d.value=u.airportCode;const J=u.airportCode,y=J.includes("#")?J.slice(2,J.length):J;a("sendPortValue",y,u.airportCnName,u.airportEnName,e.difference)},C=u=>{d.value=u.code,a("sendPortValue",u.code,u.city,u.city,e.difference,"city")},v=u=>u.includes("#")?u.slice(2,u.length):u,m=u=>e.locale==="en"?u.airportEnName:u.airportCnName,T=u=>u[0].airportCode.includes("#"),E=O(32),N=O(0),P=O(0),f=O(10),F=O(0),B=O([]),Y=u=>{N.value=u.target.scrollTop,P.value=Math.floor(u.target.scrollTop/E.value),f.value=P.value+10},h=async u=>{if(u.paneName==="hotHistory")return;N.value=0,P.value=0,f.value=10;const J=i.value.filter(G=>G.group===u.paneName),y=[];J[0].groupValue.forEach(G=>{G.airPort.forEach(Q=>{y.push(Q)})}),B.value=y,F.value=B.value.length};Ge(()=>e.modelValue,()=>{d.value=e.modelValue.slice(0,3)},{immediate:!0});const c=Ie(()=>B.value.slice(P.value,f.value));return pt(async()=>{i.value=e.resData.aggregate,r.value="hotHistory"}),{tabActive:r,isChecked:d,paneData:i,history:A,getRealCode:v,checkContent:g,transformText:m,includeMark:T,submitHotCities:C,itemHeight:E,scrollTop:N,curryDataLength:F,scroll:Y,tabClick:h,visibleList:c,scrollRef:l}},wi=Li,Ni={class:"main flex flex-row flex-wrap w-[537px] pt-3"},Ii={key:0,class:"more flex w-full"},Ei={class:"h-[22px] leading-[22px] text-red-1 whitespace-nowrap text-xs pr-5"},Pi={class:"map flex flex-row flex-wrap"},Mi=["onClick"],Qi={class:"airport-info-box"},Oi={class:"airport-code-box"},Ri={class:"airport-code-text"},Vi={class:"focus:outline-none"},Bi={class:"more flex w-full"},Yi={key:0,class:"h-[22px] leading-[22px] text-xs text-red-1 pr-5 whitespace-nowrap"},Ui={class:"map flex flex-row flex-wrap"},Hi=["onClick"],qi={class:"airport-info-box"},ji={class:"airport-code-box"},Gi={class:"airport-code-text"},Ki={class:"airport-name-text"},zi={key:0,class:"h-[22px] leading-[22px] text-xs text-red-1 w-2.5 pr-6"},Ji={key:1,class:"h-[22px] mr-6 leading-4"},Wi=["onClick"],Zi={class:"airport-info-box"},Xi={class:"airport-code-box"},er={class:"airport-code-text"},tr={class:"airport-name-text"},ar=Re({__name:"AgentPort",props:{resData:{},difference:{type:Boolean},hotCities:{},locale:{},modelValue:{}},emits:["sendPortValue"],setup(e,{emit:a}){const o=e,r=a,{tabActive:d,isChecked:i,paneData:l,history:A,checkContent:g,transformText:C,includeMark:v,submitHotCities:m,getRealCode:T,itemHeight:E,scrollTop:N,curryDataLength:P,scroll:f,tabClick:F,visibleList:B,scrollRef:Y}=wi(o,r);return(h,c)=>(p(),Ae(t(uo),{modelValue:t(d),"onUpdate:modelValue":c[1]||(c[1]=u=>Ze(d)?d.value=u:null),type:"border-card",class:"portTabs w-[536px] px-5 box-border",onTabClick:t(F)},{default:$(()=>[_(t(Zt),{label:h.$t("app.fastQuery.headerQuery.hotHistory"),name:"hotHistory",lazy:!0},{default:$(()=>{var u,J;return[n("div",Ni,[(u=h.hotCities)!=null&&u.field4?(p(),D("div",Ii,[n("span",Ei,b(h.$t("app.fastQuery.headerQuery.hot")),1),n("div",Pi,[(p(!0),D(_e,null,ke(JSON.parse(h.hotCities.field4.toString()),y=>(p(),D("div",{key:y.code,class:be(["content mr-1 text-ellipsis hover:text-[var(--bkc-el-color-primary)] hover:bg-brand-7 hover:rounded-sm",{active:t(i)===y.code}]),onClick:G=>t(m)(y)},[_(t(bt),{effect:"dark",content:y.city,placement:"top"},{default:$(()=>[n("div",Qi,[n("div",Oi,[n("span",Ri,b(t(T)(y.code)),1)]),n("div",Vi,b(y.city),1)])]),_:2},1032,["content"])],10,Mi))),128))])])):ge("",!0),n("div",Bi,[((J=t(A))==null?void 0:J.length)!==0?(p(),D("span",Yi,b(h.$t("app.fastQuery.headerQuery.history")),1)):ge("",!0),n("div",Ui,[(p(!0),D(_e,null,ke(t(A),y=>(p(),D("div",{key:y.airportCode,class:be(["content mr-1 text-ellipsis hover:text-[var(--bkc-el-color-primary)] hover:bg-brand-7 hover:rounded-sm",{active:t(i)===y.airportCode}]),onClick:G=>t(g)(y)},[_(t(bt),{effect:"dark",content:y.airPortFormatName,placement:"top"},{default:$(()=>[n("div",qi,[n("div",ji,[n("span",Gi,b(t(T)(y.airportCode)),1)]),n("div",Ki,b(y.airPortFormatName),1)])]),_:2},1032,["content"])],10,Hi))),128))])])])]}),_:1},8,["label"]),(p(!0),D(_e,null,ke(t(l),(u,J)=>(p(),Ae(t(Zt),{key:u.group,label:u.group,name:u.group,lazy:!0},{default:$(()=>[n("div",{ref_for:!0,ref:y=>{y&&(t(Y)[J]=y)},class:"scroll overflow-auto w-full h-[350px]",onScroll:c[0]||(c[0]=(...y)=>t(f)&&t(f)(...y))},[n("div",{class:"overflow-hidden",style:sa({height:(t(P)+1)*t(E)+"px"})},[n("ul",{class:"list-none m-0",style:sa({"margin-top":`${t(N)}px`})},[(p(!0),D(_e,null,ke(t(B),(y,G)=>{var Q;return p(),D("li",{key:G,class:"flex flex-nowrap",style:sa({height:t(E)+"px","margin-top":t(v)(y)?"10px":"0px"})},[t(v)(y)?(p(),D("span",zi,b((Q=y[0])==null?void 0:Q.airportCode.slice(0,1)),1)):(p(),D("span",Ji)),(p(!0),D(_e,null,ke(y,x=>(p(),D("div",{key:x.airportCode,class:be(["content mr-1 text-ellipsis hover:text-[var(--bkc-el-color-primary)] hover:bg-brand-7 hover:rounded-sm",{active:t(i)===x.airportCode}]),onClick:S=>t(g)(x)},[_(t(bt),{effect:"dark",content:t(C)(x),placement:"top"},{default:$(()=>[n("div",Zi,[n("div",Xi,[n("span",er,b(t(T)(x.airportCode)),1)]),n("div",tr,b(t(C)(x)),1)])]),_:2},1032,["content"])],10,Wi))),128))],4)}),128))],4)],4)],544)]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue","onTabClick"]))}});const Ya=je(ar,[["__scopeId","data-v-887a84dd"]]),or=(e,a)=>{const{t:o}=rt(),r=Ie(()=>e.searchData),d=O([]),i=O([]),l=O(),A=O(""),g=O(0),C=O(),v=O(),m=350,T=3,E=24,N=m/E+T,P=O(),f=x=>{const S={};return x.forEach(U=>{const{firstLevel:z,secondLevel:V,cityCode:K,cityName:W,country:I}=U,L=z+V;S[L]||(S[L]={firstLevel:z,secondLevel:W,secondLevelFullName:W===K?K:`${W}${K}`,country:I,cityCode:K,list:[]}),S[L].list.push(U)}),Object.values(S)},F=(x,S,U,z)=>{const V=e.locale==="en"?(x==null?void 0:x.split(" "))??"":S,K=V==null?void 0:V.map(W=>W[0]).join("").toUpperCase().startsWith(U);return z?K:(x==null?void 0:x.toUpperCase().startsWith(U))||K},B=(x,S)=>{if(!S)return[];const U=S.toUpperCase(),[z,V]=Lt(x,de=>de.airportCode.toUpperCase().startsWith(U)),[K,W]=Lt(V,de=>de.cityCode.toUpperCase().startsWith(U)),[I,L]=Lt(W,de=>F(de.cityName,de.cityPYArray,U,!0)),[w,M]=Lt(L,de=>de.cityName.toUpperCase().startsWith(U)),[k,ae]=Lt(M,de=>F(de.airPortFormatName,de.namePYArray,U,!0)),[he,fe]=Lt(ae,de=>de.airPortFormatName.toUpperCase().startsWith(U));return[...new Set([...z,...K,...I,...w,...k,...he,...fe])]},Y=x=>{const S=new Set,U=new Set,z=new Set,V=x.toUpperCase(),K=r.value;for(let I=K.length-1;I>=0;I--){const L=K[I].airportCode,w=K[I].cityCode;if(L.startsWith(V)||e.type!=="1"&&w.startsWith(V))S.add(K[I]);else{const M=F(K[I].airPortFormatName,K[I].namePYArray,V),k=x.length>3&&(K[I].airportCnName+K[I].airportCode).includes(x);M||k?U.add(K[I]):F(K[I].cityName,K[I].cityPYArray,V)&&z.add(K[I])}}return f(B([...S,...U,...z],x))},h=x=>{const S=[],U=[],z=[];return x.forEach(V=>V.firstLevel==="国际"?U.push(V):z.push(V)),x.length&&(x[0].firstLevel==="国际"?(U.length&&S.push({code:"I",list:U}),z.length&&S.push({code:"D",list:z})):(z.length&&S.push({code:"D",list:z}),U.length&&S.push({code:"I",list:U}))),S},c=x=>new Promise(U=>{const z=h(Y(x));U(z)}),u=x=>{l.value=x.airportCode;const S=x.firstLevel==="国内";a("sendSearchValue",x.airportCode,x.airportCnName,x.airportEnName,S,"airport",x.airportEnName)},J=x=>{l.value=x.cityCode;const S=x.firstLevel==="国内";a("sendSearchValue",x.cityCode,x.secondLevel,x.secondLevel,S,"city")},y=(x,S)=>x.length+S.length>9?`${x.substr(0,6)}...${S}`:x+S,G=x=>{const z=Math.floor((x||0)/E)+N;let V=0,K=-1;const W=i.value.findIndex((L,w)=>(K=L.list.findIndex((M,k)=>{const ae=V+M.list.length;return z<ae&&!(z<V)||w===i.value.length-1&&k===L.list.length-1&&!(ae>z)?!0:(V=ae,!1)}),K>-1)),I=[];if(W===-1){d.value=I;return}i.value.slice(0,W+1).forEach((L,w)=>{if(w>W)return;if(w<W){I.push(L);return}const M=L.list.slice(0,K+1);I.push({...L,list:M})}),(I??[]).forEach(L=>{((L==null?void 0:L.list)??[]).forEach(w=>{((w==null?void 0:w.list)??[]).sort((M,k)=>{const ae=["A","B","H"],he=ae.indexOf(M.codeType),fe=ae.indexOf(k.codeType);return he===fe?0:he-fe})})}),d.value=I},Q=x=>{C.value||(C.value=!0,requestAnimationFrame(()=>{C.value=!1}),G(x.target.scrollTop))};return en(async()=>{A.value=o("app.fastQuery.headerQuery.searching"),P.value=!0,i.value=await c(e.input),await G(),P.value=!1,d.value.length<=0&&(A.value=o("app.fastQuery.headerQuery.noAirportsFound"))}),{allData:r,currySearchRes:d,isChecked:l,text:A,checkMemory:u,transformText:y,containerRef:v,handleScroll:Q,totalHeight:g,searching:P,checkCity:J}},nr=or,ir=e=>($t("data-v-70c79d4c"),e=e(),xt(),e),rr={class:"type-text-box"},sr={class:"text-name"},lr=ir(()=>n("div",{class:"name-line"},null,-1)),cr={class:"flex cursor-pointer"},dr=["textContent"],ur={key:0,class:"list"},pr={class:"airport-info-box"},gr={class:"airport-code-box"},fr={class:"airport-code-text"},mr={class:"airport-name-text"},hr={key:1,class:"list"},vr={class:"airport-info-box hover:bg-brand-7 hover:text-[var(--bkc-el-color-primary)]"},yr={class:"airport-code-box"},br={class:"airport-code-text"},_r=["onClick"],Cr={key:2,class:"list"},Dr=["onClick"],Ar={class:"airport-info-box hover:bg-brand-7 hover:text-[var(--bkc-el-color-primary)]"},$r={class:"airport-code-box"},xr={class:"airport-code-text"},Tr={class:"w-[248px] shadow bg-gray-0 rounded-sm border border-gray-6 text-gray-6"},Sr={class:"h-[22px] leading-[22px] inline-block my-2 pl-2 w-[240px] text-xs bg-gray-7 text-gray-2"},kr=Re({__name:"AgentSearch",props:{type:{},input:{},searchData:{},locale:{}},emits:["sendSearchValue"],setup(e,{emit:a}){const o=e,r=a,{currySearchRes:d,isChecked:i,text:l,checkMemory:A,handleScroll:g,containerRef:C,searching:v,checkCity:m}=nr(o,r);return(T,E)=>(p(),D(_e,null,[mt(n("div",{class:be(["agent-search",T.locale==="en"?"en-agent-search":""]),onScroll:E[0]||(E[0]=(...N)=>t(g)&&t(g)(...N))},[n("div",null,[n("div",{ref_key:"containerRef",ref:C},[(p(!0),D(_e,null,ke(t(d),(N,P)=>(p(),D("div",{key:P,class:"panel"},[n("div",rr,[n("div",sr,b(N.code==="I"?T.$t("app.avSearch.Intl"):T.$t("app.avSearch.Dom")),1),lr]),(p(!0),D(_e,null,ke(N.list,(f,F)=>(p(),D("div",{key:F},[n("div",cr,[n("span",{class:"country-code-text",textContent:b(f.country)},null,8,dr),T.type===0?(p(),D("div",ur,[_(t(bt),{effect:"dark",content:f.secondLevel,placement:"top"},{default:$(()=>[n("div",pr,[n("div",gr,[n("div",fr,b(f.cityCode.includes("#")?f.cityCode.slice(2,f.cityCode.length):f.cityCode),1)]),n("div",mr,b(f.secondLevel),1)])]),_:2},1032,["content"])])):(p(),D("div",hr,[_(t(bt),{effect:"dark",content:f.secondLevel,placement:"top"},{default:$(()=>[n("div",vr,[n("div",yr,[n("div",br,b(f.cityCode.includes("#")?f.cityCode.slice(2,f.cityCode.length):f.cityCode),1)]),n("div",{class:be(["airport-name-text",t(i)===f.cityCode?"bg-brand-7 text-[var(--bkc-el-color-primary)]":""]),onClick:B=>t(m)(f)},b(f.secondLevel),11,_r)])]),_:2},1032,["content"])])),T.type!==1?(p(),D("div",Cr,[(p(!0),D(_e,null,ke(f.list,(B,Y)=>(p(),D("div",{key:Y,class:be(["content text-ellipsis",{"bg-brand-7 text-[var(--bkc-el-color-primary)]":t(i)===B.airportCode}]),onClick:h=>t(A)(B)},[_(t(bt),{effect:"dark",content:B.airPortFormatName,placement:"top"},{default:$(()=>[n("div",Ar,[n("div",$r,[n("div",xr,b(B.airportCode.includes("#")?B.cityCode.slice(2,B.airportCode.length):B.airportCode),1)]),n("div",{class:be(["airport-name-text",t(i)===B.airportCode?"bg-brand-7 text-[var(--bkc-el-color-primary)]":""])},b(B.airPortFormatName),3)])]),_:2},1032,["content"])],10,Dr))),128))])):ge("",!0)])]))),128))]))),128))],512)])],34),[[fa,!t(v)&&t(d).length>0]]),mt(n("div",Tr,[n("span",Sr,b(t(l)),1)],512),[[fa,t(v)||!(t(d).length>0)]])],64))}});const _o=je(kr,[["__scopeId","data-v-70c79d4c"]]),Fr={key:0,class:"svg-icon iconfont icon-nav-booking"},Lr={key:1,class:"svg-icon iconfont icon-booking-to"},wr={key:0},Nr=Re({__name:"AgentAirportContainer",props:{position:{},prefixTitle:{},type:{},modelValue:{},name:{},segIndex:{},isAshingTransitTerminal:{type:Boolean},isAgentCity:{}},emits:["update:modelValue","update:name","change","triggerAirBlur"],setup(e,{emit:a}){const o=e,r=a,{tabShow:d,searchShow:i,domesticData:l,internationalData:A,searchAllData:g,inputValue:C,locale:v,inputClick:m,getPortValue:T,getSearchValue:E,showInputModel:N,clickOutside:P,visible:f,hotCitiesOfDomestic:F,hotCitiesOfInter:B,popoverHide:Y,airBlurClick:h,threeCharacterCodeInput:c}=Fi(o,r);return(u,J)=>(p(),Ae(t(_t),{visible:t(f),"onUpdate:visible":J[3]||(J[3]=y=>Ze(f)?f.value=y:null),trigger:"click",placement:u.position,"popper-class":"airportClass","show-arrow":!1,onHide:t(Y)},{reference:$(()=>[_(t(Tt),{modelValue:t(N),"onUpdate:modelValue":J[0]||(J[0]=y=>Ze(N)?N.value=y:null),disabled:u.isAshingTransitTerminal,placeholder:u.prefixTitle,clearable:"",class:"middlePosition",onClick:Et(t(m),["stop"]),onKeydown:Wt(t(P),["tab"]),onKeyup:Wt(t(m),["tab"]),onBlur:t(h)},{prefix:$(()=>[u.type!==""?(p(),D(_e,{key:0},[u.type==="takeOff"?(p(),D("em",Fr)):ge("",!0),u.type==="arrive"?(p(),D("em",Lr)):ge("",!0)],64)):lt(u.$slots,"default",{key:1},void 0,!0)]),_:3},8,["modelValue","disabled","placeholder","onClick","onKeydown","onKeyup","onBlur"])]),default:$(()=>[t(d)||t(i)?(p(),D("div",wr,[t(d)?(p(),Ae(t(uo),{key:0,type:"card",class:"diTabs"},{default:$(()=>[_(t(Zt),{label:u.$t("app.fastQuery.headerQuery.domestic")},{default:$(()=>[_(Ya,{modelValue:t(c),"onUpdate:modelValue":J[1]||(J[1]=y=>Ze(c)?c.value=y:null),"res-data":t(l),"hot-cities":t(F),difference:!0,locale:t(v),onSendPortValue:t(T)},null,8,["modelValue","res-data","hot-cities","locale","onSendPortValue"])]),_:1},8,["label"]),_(t(Zt),{label:u.$t("app.fastQuery.headerQuery.International")},{default:$(()=>[_(Ya,{modelValue:t(c),"onUpdate:modelValue":J[2]||(J[2]=y=>Ze(c)?c.value=y:null),"res-data":t(A),"hot-cities":t(B),difference:!1,locale:t(v),onSendPortValue:t(T)},null,8,["modelValue","res-data","hot-cities","locale","onSendPortValue"])]),_:1},8,["label"])]),_:1})):ge("",!0),t(i)?mt((p(),Ae(_o,{key:1,type:u.isAgentCity,"search-data":t(g),input:t(C),locale:t(v),onSendSearchValue:t(E)},null,8,["type","search-data","input","locale","onSendSearchValue"])),[[t(po),t(P)]]):ge("",!0)])):ge("",!0)]),_:3},8,["visible","placement","onHide"]))}});const Qt=je(Nr,[["__scopeId","data-v-e1ecf0b0"]]),Ir={class:"transit-terminal-airport-box custom-focus-tip-input"},Er=n("div",{class:"hidden bkc-el-form-item__error"},"A/B/..",-1),Pr={class:"airport-box"},Mr=Re({__name:"TransitTerminalAirport",props:{modelValue:{default:""},isAgentCity:{}},emits:["update:modelValue"],setup(e,{emit:a}){const o=e,r=a,d=O(ta()),i=O(!1),l=O(),A=O(!1),g=O([]),C=O(""),v=Ie({get:()=>o.modelValue.toUpperCase(),set:h=>{i.value=!!h,N(h)}}),m=()=>{N(v.value)},T=h=>{r("update:modelValue",h),A.value=!h},E=h=>{var U,z,V;const c=(U=l.value.input)!=null&&U.selectionStart?l.value.input.selectionStart-1:-1,u=h.split(""),J=h.slice(0,c+1),y=h.slice(c+1,h.length);let G=-1,Q=-1;if((z=u[c])!=null&&z.includes("/")&&(G=c+1),(V=u[c+1])!=null&&V.includes("/")&&(Q=c+1),G===-1){const K=J.lastIndexOf("/");K>-1&&(G=K+1)}if(Q===-1){const K=y.indexOf("/");K>-1&&(Q=K+J.length)}const x=G===-1?0:G,S=Q===-1?h.length:Q;return{start:x,end:S}},N=h=>{if(T(h),!h)return;const c=h==null?void 0:h.toUpperCase(),{start:u,end:J}=E(h);C.value=c.slice(u,J),i.value=!!C.value},P=h=>{g.value=h.map(c=>{const u={...c,type:c.firstLevel==="国内",airportEnName:c.airportEnName||c.airportCode,airportFullName:"",airPortFormatName:"",cityName:"",cityEnName:c.cityEnName||c.cityCode,searchName:[...c.nameArray,...c.namePYArray].toString(),searchCity:[...c.cityArray,...c.cityPYArray].toString()};return d.value==="en"?(u.cityName=u.cityEnName,u.airportFullName=u.airportEnName===u.airportCode?u.airportCode:`${u.airportEnName}${u.airportCode}`,u.airPortFormatName=u.airportEnName):(u.cityName=u.secondLevel,u.airportFullName=`${u.airportCnName}${u.airportCode}`,u.airPortFormatName=u.airportCnName),u}).sort((c,u)=>+(c.firstLevel==="国内")-+(u.firstLevel==="国内"))},f=async()=>{if(g.value.length)return;const h=await nt("searchLocalData");if(h&&(h!=null&&h.localData)){const c=JSON.parse(h==null?void 0:h.localData);P(c)}},F=()=>{i.value=!1},B=h=>{const{start:c,end:u}=E(v.value),J=v.value.slice(0,c),y=v.value.slice(u);T(`${J}${h}${y}`),F()},Y=async h=>{[37,38,39,40].includes(h.keyCode)&&(await gt(),N(v.value))};return pt(()=>{f()}),(h,c)=>{const u=Tt,J=_t;return p(),D("div",Ir,[_(J,{visible:i.value,persistent:!1,trigger:"click","popper-class":"transit-terminal-airport-popover","show-arrow":!1,width:"248"},{reference:$(()=>[n("div",{class:be(["input-focus-tip",A.value?"input-focus":""])},[_(u,{ref_key:"inputRef",ref:l,modelValue:v.value,"onUpdate:modelValue":c[0]||(c[0]=y=>v.value=y),placeholder:h.$t("app.avSearch.transitTerminal"),onKeyup:Y,onBlur:c[1]||(c[1]=y=>A.value=!1),onClick:Et(m,["stop"])},null,8,["modelValue","placeholder"]),Er],2)]),default:$(()=>[mt((p(),D("div",Pr,[_(_o,{locale:d.value,type:h.isAgentCity,"search-data":g.value,input:C.value,onSendSearchValue:B},null,8,["locale","type","search-data","input"])])),[[t(po),F]])]),_:1},8,["visible"])])}}});const ga=[{code:"Y8",icon:"icon-y8-c",logo:"#icon-y8-c",name:s.global.t("app.airlineList.JinpengAirlines")},{code:"TW",icon:"icon-tw-c",logo:"#icon-tw-c",name:s.global.t("app.airlineList.DulwichAir")},{code:"7C",icon:"icon-7c-c",logo:"#icon-7c-c",name:s.global.t("app.airlineList.JejuAir")},{code:"EK",icon:"icon-ek-c",logo:"#icon-ek-c",name:s.global.t("app.airlineList.Emirates")},{code:"EN",icon:"icon-en-c",logo:"#icon-en-c",name:s.global.t("app.airlineList.AirDolomites")},{code:"ET",icon:"icon-et-c",logo:"#icon-et-c",name:s.global.t("app.airlineList.EthiopianAirlines")},{code:"EU",icon:"icon-eu-c",logo:"#icon-eu-c",name:s.global.t("app.airlineList.ChengduAirlines")},{code:"EY",icon:"icon-ey-c",logo:"#icon-ey-c",name:s.global.t("app.airlineList.EtihadAirways")},{code:"F7",icon:"icon-f7-c",logo:"#icon-f7-c",name:s.global.t("app.airlineList.AirFrye")},{code:"F9",icon:"icon-f9-c",logo:"#icon-f9-c",name:s.global.t("app.airlineList.FrontierAirlines")},{code:"FB",icon:"icon-fb-c",logo:"#icon-fb-c",name:s.global.t("app.airlineList.BulgarianAirlines")},{code:"FE",icon:"icon-fe-c",logo:"#icon-fe-c",name:s.global.t("app.airlineList.FarEastAirlines")},{code:"FI",icon:"icon-fi-c",logo:"#icon-fi-c",name:s.global.t("app.airlineList.Icelandair")},{code:"FJ",icon:"icon-fj-c",logo:"#icon-fj-c",name:s.global.t("app.airlineList.PacificAirlines")},{code:"FM",icon:"icon-fm-c",logo:"#icon-fm-c",name:s.global.t("app.airlineList.ShanghaiAirlines")},{code:"FQ",icon:"icon-fq-c",logo:"#icon-fq-c",name:s.global.t("app.airlineList.BrindabellaAir")},{code:"FV",icon:"icon-fv-c",logo:"#icon-fv-c",name:s.global.t("app.airlineList.RussianNationalAviation")},{code:"G3",icon:"icon-g3-c",logo:"#icon-g3-c",name:s.global.t("app.airlineList.GowerTransportAir")},{code:"G5",icon:"icon-g5-c",logo:"#icon-g5-c",name:s.global.t("app.airlineList.HuaAirlines")},{code:"GA",icon:"icon-ga-c",logo:"#icon-ga-c",name:s.global.t("app.airlineList.GarudaIndonesia")},{code:"GE",icon:"icon-ge-c",logo:"#icon-ge-c",name:s.global.t("app.airlineList.TransasiaAirways")},{code:"GF",icon:"icon-gf-c",logo:"#icon-gf-c",name:s.global.t("app.airlineList.GulfAir")},{code:"GJ",icon:"icon-gj-c",logo:"#icon-gj-c",name:s.global.t("app.airlineList.YoloAir")},{code:"GR",icon:"icon-gr-c",logo:"#icon-gr-c",name:s.global.t("app.airlineList.AirOrigny")},{code:"GS",icon:"icon-gs-c",logo:"#icon-gs-c",name:s.global.t("app.airlineList.TianjinAirlines")},{code:"HA",icon:"icon-ha-c",logo:"#icon-ha-c",name:s.global.t("app.airlineList.HawaiianAirlines")},{code:"HF",icon:"icon-hf-c",logo:"#icon-hf-c",name:s.global.t("app.airlineList.LloydAir")},{code:"HM",icon:"icon-hm-c",logo:"#icon-hm-c",name:s.global.t("app.airlineList.AirSeychelles")},{code:"HO",icon:"icon-ho-c",logo:"#icon-ho-c",name:s.global.t("app.airlineList.JuneyaoAirlines")},{code:"HU",icon:"icon-hu-c",logo:"#icon-hu-c",name:s.global.t("app.airlineList.HainanAirlines")},{code:"HX",icon:"icon-hx-c",logo:"#icon-hx-c",name:s.global.t("app.airlineList.HongKongAirlines")},{code:"HY",icon:"icon-hy-c",logo:"#icon-hy-c",name:s.global.t("app.airlineList.UzbekistanAirways")},{code:"HZ",icon:"icon-hz-c",logo:"#icon-hz-c",name:s.global.t("app.airlineList.SakhalinAir")},{code:"IB",icon:"icon-ib-c",logo:"#icon-ib-c",name:s.global.t("app.airlineList.Spainair")},{code:"IC",icon:"icon-ic-c",logo:"#icon-ic-c",name:s.global.t("app.airlineList.AirIndia")},{code:"IE",icon:"icon-ie-c",logo:"#icon-ie-c",name:s.global.t("app.airlineList.SolomonAirlines")},{code:"IG",icon:"icon-ig-c",logo:"#icon-ig-c",name:s.global.t("app.airlineList.AirMeridian")},{code:"IR",icon:"icon-ir-c",logo:"#icon-ir-c",name:s.global.t("app.airlineList.IranAir")},{code:"IT",icon:"icon-it-c",logo:"#icon-it-c",name:s.global.t("app.airlineList.KingfisherAirlines")},{code:"IY",icon:"icon-iy-c",logo:"#icon-iy-c",name:s.global.t("app.airlineList.YemeniaAirways")},{code:"IZ",icon:"icon-iz-c",logo:"#icon-iz-c",name:s.global.t("app.airlineList.Ehang")},{code:"J2",icon:"icon-j2-c",logo:"#icon-j2-c",name:s.global.t("app.airlineList.AzerbaijanAirlines")},{code:"JD",icon:"icon-jd-c",logo:"#icon-jd-c",name:s.global.t("app.airlineList.CapitalAirlines")},{code:"JH",icon:"icon-jh-c",logo:"#icon-jh-c",name:s.global.t("app.airlineList.NortheastAirlines")},{code:"JJ",icon:"icon-jj-c",logo:"#icon-jj-c",name:s.global.t("app.airlineList.SouthAmericanAirlines")},{code:"JK",icon:"icon-jk-c",logo:"#icon-jk-c",name:s.global.t("app.airlineList.Spanair")},{code:"JL",icon:"icon-jl-c",logo:"#icon-jl-c",name:s.global.t("app.airlineList.JapanAirlines")},{code:"JP",icon:"icon-jp-c",logo:"#icon-jp-c",name:s.global.t("app.airlineList.AdriaticAir")},{code:"JQ",icon:"icon-jq-c",logo:"#icon-jq-c",name:s.global.t("app.airlineList.JetstarAirways")},{code:"JR",icon:"icon-jr-c",logo:"#icon-jr-c",name:s.global.t("app.airlineList.HappyAir")},{code:"JS",icon:"icon-js-c",logo:"#icon-js-c",name:s.global.t("app.airlineList.Headingforthesea")},{code:"JU",icon:"icon-ju-c",logo:"#icon-ju-c",name:s.global.t("app.airlineList.YugoslavAirlines")},{code:"K6",icon:"icon-k6-c",logo:"#icon-k6-c",name:s.global.t("app.airlineList.AngkorAir")},{code:"KA",icon:"icon-ka-c",logo:"#icon-ka-c",name:s.global.t("app.airlineList.Dragonair")},{code:"KC",icon:"icon-kc-c",logo:"#icon-kc-c",name:s.global.t("app.airlineList.KazakhAirlines")},{code:"KE",icon:"icon-ke-c",logo:"#icon-ke-c",name:s.global.t("app.airlineList.KoreanAir")},{code:"KF",icon:"icon-kf-c",logo:"#icon-kf-c",name:s.global.t("app.airlineList.PortniaBlueFirstAir")},{code:"KK",icon:"icon-kk-c",logo:"#icon-kk-c",name:s.global.t("app.airlineList.AtlasAir")},{code:"KL",icon:"icon-kl-c",logo:"#icon-kl-c",name:s.global.t("app.airlineList.KLM")},{code:"KM",icon:"icon-km-c",logo:"#icon-km-c",name:s.global.t("app.airlineList.AirMalta")},{code:"KN",icon:"icon-kn-c",logo:"#icon-kn-c",name:s.global.t("app.airlineList.ChinaUnitedAirlines")},{code:"KP",icon:"icon-kp-c",logo:"#icon-kp-c",name:s.global.t("app.airlineList.CapeAir")},{code:"KQ",icon:"icon-kq-c",logo:"#icon-kq-c",name:s.global.t("app.airlineList.KenyaAirways")},{code:"KS",icon:"icon-ks-c",logo:"#icon-ks-c",name:s.global.t("app.airlineList.PencilAviation")},{code:"KU",icon:"icon-ku-c",logo:"#icon-ku-c",name:s.global.t("app.airlineList.KuwaitAirways")},{code:"KY",icon:"icon-ky-c",logo:"#icon-ky-c",name:s.global.t("app.airlineList.KunmingAirlines")},{code:"LA",icon:"icon-la-c",logo:"#icon-la-c",name:s.global.t("app.airlineList.AirChile")},{code:"LG",icon:"icon-lg-c",logo:"#icon-lg-c",name:s.global.t("app.airlineList.LuxembourgAir")},{code:"LH",icon:"icon-lh-c",logo:"#icon-lh-c",name:s.global.t("app.airlineList.Lufthansa")},{code:"LI",icon:"icon-li-c",logo:"#icon-li-c",name:s.global.t("app.airlineList.LeewardAviation")},{code:"LN",icon:"icon-ln-c",logo:"#icon-ln-c",name:s.global.t("app.airlineList.LibyanAirlines")},{code:"LO",icon:"icon-lo-c",logo:"#icon-lo-c",name:s.global.t("app.airlineList.WaveNavigation")},{code:"LP",icon:"icon-lp-c",logo:"#icon-lp-c",name:s.global.t("app.airlineList.PeruvianAirlines")},{code:"LR",icon:"icon-lr-c",logo:"#icon-lr-c",name:s.global.t("app.airlineList.LascaAir")},{code:"9W",icon:"icon-9w-c",logo:"#icon-9w-c",name:s.global.t("app.airlineList.JetAirways")},{code:"A3",icon:"icon-a3-c",logo:"#icon-a3-c",name:s.global.t("app.airlineList.AegeanAir")},{code:"A5",icon:"icon-a5-c",logo:"#icon-a5-c",name:s.global.t("app.airlineList.LinelAir")},{code:"A9",icon:"icon-a9-c",logo:"#icon-a9-c",name:s.global.t("app.airlineList.AirGeorgia")},{code:"AA",icon:"icon-aa-c",logo:"#icon-aa-c",name:s.global.t("app.airlineList.AmericanAirlines")},{code:"AB",icon:"icon-ab-c",logo:"#icon-ab-c",name:s.global.t("app.airlineList.AirBerlin")},{code:"AC",icon:"icon-ac-c",logo:"#icon-ac-c",name:s.global.t("app.airlineList.AirCanada")},{code:"AD",icon:"icon-ad-c",logo:"#icon-ad-c",name:s.global.t("app.airlineList.IndonesianParadiseAirlines")},{code:"AE",icon:"icon-ae-c",logo:"#icon-ae-c",name:s.global.t("app.airlineList.CefcAirlines")},{code:"AF",icon:"icon-af-c",logo:"#icon-af-c",name:s.global.t("app.airlineList.AirFrance")},{code:"AH",icon:"icon-ah-c",logo:"#icon-ah-c",name:s.global.t("app.airlineList.AirAlgerie")},{code:"AI",icon:"icon-ai-c",logo:"#icon-ai-c",name:s.global.t("app.airlineList.IndiaAir")},{code:"AM",icon:"icon-am-c",logo:"#icon-am-c",name:s.global.t("app.airlineList.AirMexico")},{code:"AP",icon:"icon-ap-c",logo:"#icon-ap-c",name:s.global.t("app.airlineList.AirOne")},{code:"AR",icon:"icon-ar-c",logo:"#icon-ar-c",name:s.global.t("app.airlineList.AerolineasArgentinas")},{code:"AS",icon:"icon-as-c",logo:"#icon-as-c",name:s.global.t("app.airlineList.AlaskaAirlines")},{code:"AT",icon:"icon-at-c",logo:"#icon-at-c",name:s.global.t("app.airlineList.AirMaroc")},{code:"AV",icon:"icon-av-c",logo:"#icon-av-c",name:s.global.t("app.airlineList.ColumbiaAirlines")},{code:"AW",icon:"icon-aw-c",logo:"#icon-aw-c",name:s.global.t("app.airlineList.DecantaraAir")},{code:"AY",icon:"icon-ay-c",logo:"#icon-ay-c",name:s.global.t("app.airlineList.Finnair")},{code:"AZ",icon:"icon-az-c",logo:"#icon-az-c",name:s.global.t("app.airlineList.Alitalia")},{code:"B2",icon:"icon-b2-c",logo:"#icon-b2-c",name:s.global.t("app.airlineList.BelarusianAirlines")},{code:"B6",icon:"icon-b6-c",logo:"#icon-b6-c",name:s.global.t("app.airlineList.JetblueAirways")},{code:"B7",icon:"icon-b7-c",logo:"#icon-b7-c",name:s.global.t("app.airlineList.LirongAir")},{code:"BA",icon:"icon-ba-c",logo:"#icon-ba-c",name:s.global.t("app.airlineList.BritishAirways")},{code:"BD",icon:"icon-bd-c",logo:"#icon-bd-c",name:s.global.t("app.airlineList.MidlandAirlines")},{code:"BE",icon:"icon-be-c",logo:"#icon-be-c",name:s.global.t("app.airlineList.FlybyAir")},{code:"BG",icon:"icon-bg-c",logo:"#icon-bg-c",name:s.global.t("app.airlineList.BanglaAirlines")},{code:"BI",icon:"icon-bi-c",logo:"#icon-bi-c",name:s.global.t("app.airlineList.BruneiAirlines")},{code:"BK",icon:"icon-bk-c",logo:"#icon-bk-c",name:s.global.t("app.airlineList.OkAirways")},{code:"BL",icon:"icon-bl-c",logo:"#icon-bl-c",name:s.global.t("app.airlineList.PacificAirlines")},{code:"BM",icon:"icon-bm-c",logo:"#icon-bm-c",name:s.global.t("app.airlineList.PrayuthAirIndonesia")},{code:"BP",icon:"icon-bp-c",logo:"#icon-bp-c",name:s.global.t("app.airlineList.AirBotswana")},{code:"BR",icon:"icon-br-c",logo:"#icon-br-c",name:s.global.t("app.airlineList.EvaAir")},{code:"BT",icon:"icon-bt-c",logo:"#icon-bt-c",name:s.global.t("app.airlineList.AirBaltic")},{code:"BU",icon:"icon-bu-c",logo:"#icon-bu-c",name:s.global.t("app.airlineList.BrassensAir")},{code:"BV",icon:"icon-bv-c",logo:"#icon-bv-c",name:s.global.t("app.airlineList.BluePanoramaAviation")},{code:"BW",icon:"icon-bw-c",logo:"#icon-bw-c",name:s.global.t("app.airlineList.Tedosi")},{code:"BX",icon:"icon-bx-c",logo:"#icon-bx-c",name:s.global.t("app.airlineList.OceanfrontAir")},{code:"CA",icon:"icon-ca-c",logo:"#icon-ca-c",name:s.global.t("app.airlineList.AirChina")},{code:"CI",icon:"icon-ci-c",logo:"#icon-ci-c",name:s.global.t("app.airlineList.ChinaAirlines")},{code:"CM",icon:"icon-cm-c",logo:"#icon-cm-c",name:s.global.t("app.airlineList.PanamaAirlift")},{code:"CN",icon:"icon-cn-c",logo:"#icon-cn-c",name:s.global.t("app.airlineList.GrandChinaAirlines")},{code:"CU",icon:"icon-cu-c",logo:"#icon-cu-c",name:s.global.t("app.airlineList.AirCubana")},{code:"CX",icon:"icon-cx-c",logo:"#icon-cx-c",name:s.global.t("app.airlineList.CathayPacific")},{code:"CY",icon:"icon-cy-c",logo:"#icon-cy-c",name:s.global.t("app.airlineList.CyprusAirways")},{code:"CZ",icon:"icon-cz-c",logo:"#icon-cz-c",name:s.global.t("app.airlineList.ChinaSouthernAirlines")},{code:"DC",icon:"icon-dc-c",logo:"#icon-dc-c",name:s.global.t("app.airlineList.GoldenAir")},{code:"DE",icon:"icon-de-c",logo:"#icon-de-c",name:s.global.t("app.airlineList.CondorAir")},{code:"DJ",icon:"icon-dj-c",logo:"#icon-dj-c",name:s.global.t("app.airlineList.VirginAirlines")},{code:"DL",icon:"icon-dl-c",logo:"#icon-dl-c",name:s.global.t("app.airlineList.DeltaAirLines")},{code:"DN",icon:"icon-dn-c",logo:"#icon-dn-c",name:s.global.t("app.airlineList.AirDeccan")},{code:"DR",icon:"icon-dr-c",logo:"#icon-dr-c",name:s.global.t("app.airlineList.LinkAir")},{code:"DT",icon:"icon-dt-c",logo:"#icon-dt-c",name:s.global.t("app.airlineList.AirAngola")},{code:"DY",icon:"icon-dy-c",logo:"#icon-dy-c",name:s.global.t("app.airlineList.NorwegianAir")},{code:"DZ",icon:"icon-dz-c",logo:"#icon-dz-c",name:s.global.t("app.airlineList.DonghaiAirlines")},{code:"E3",icon:"icon-e3-c",logo:"#icon-e3-c",name:s.global.t("app.airlineList.DomodedovoAir")},{code:"EF",icon:"icon-ef-c",logo:"#icon-ef-c",name:s.global.t("app.airlineList.FarEastAir")},{code:"EI",icon:"icon-ei-c",logo:"#icon-ei-c",name:s.global.t("app.airlineList.AerLingus")},{code:"2P",icon:"icon-2p-c",logo:"#icon-2p-c",name:s.global.t("app.airlineList.AirPhilippine")},{code:"3K",icon:"icon-3k-c",logo:"#icon-3k-c",name:s.global.t("app.airlineList.JetstarAirways")},{code:"4D",icon:"icon-4d-c",logo:"#icon-4d-c",name:s.global.t("app.airlineList.SinaiAir")},{code:"4U",icon:"icon-4u-c",logo:"#icon-4u-c",name:s.global.t("app.airlineList.Germanwings")},{code:"5L",icon:"icon-5l-c",logo:"#icon-5l-c",name:s.global.t("app.airlineList.AlsoAir")},{code:"7B",icon:"icon-7b-c",logo:"#icon-7b-c",name:s.global.t("app.airlineList.KrasnoyarskAir")},{code:"8C",icon:"icon-8c-c",logo:"#icon-8c-c",name:s.global.t("app.airlineList.EastStarAirlines")},{code:"8L",icon:"icon-8l-c",logo:"#icon-8l-c",name:s.global.t("app.airlineList.LuckyAir")},{code:"8M",icon:"icon-8m-c",logo:"#icon-8m-c",name:s.global.t("app.airlineList.MyanmarAirlines")},{code:"8U",icon:"icon-8u-c",logo:"#icon-8u-c",name:s.global.t("app.airlineList.AfrikiaAir")},{code:"9B",icon:"icon-9b-c",logo:"#icon-9b-c",name:s.global.t("app.airlineList.NorwegianStateRailways")},{code:"9K",icon:"icon-9k-c",logo:"#icon-9k-c",name:s.global.t("app.airlineList.CapeCodAir")},{code:"9U",icon:"icon-9u-c",logo:"#icon-9u-c",name:s.global.t("app.airlineList.AirMoldova")},{code:"LX",icon:"icon-lx-c",logo:"#icon-lx-c",name:s.global.t("app.airlineList.SwissAir")},{code:"LY",icon:"icon-ly-c",logo:"#icon-ly-c",name:s.global.t("app.airlineList.Ehang")},{code:"MA",icon:"icon-ma-c",logo:"#icon-ma-c",name:s.global.t("app.airlineList.Malev")},{code:"MD",icon:"icon-md-c",logo:"#icon-md-c",name:s.global.t("app.airlineList.AirMadagascar")},{code:"ME",icon:"icon-me-c",logo:"#icon-me-c",name:s.global.t("app.airlineList.MiddleEastAirlines")},{code:"MF",icon:"icon-mf-c",logo:"#icon-mf-c",name:s.global.t("app.airlineList.XiamenAirlines")},{code:"MH",icon:"icon-mh-c",logo:"#icon-mh-c",name:s.global.t("app.airlineList.MalaysiaAirlines")},{code:"MI",icon:"icon-mi-c",logo:"#icon-mi-c",name:s.global.t("app.airlineList.Silkair")},{code:"UQ",icon:"icon-uq-c",logo:"#icon-uq-c",name:s.global.t("app.airlineList.UrumqiAir")},{code:"MO",icon:"icon-mo-c",logo:"#icon-mo-c",name:s.global.t("app.airlineList.StaticAir")},{code:"MP",icon:"icon-mp-c",logo:"#icon-mp-c",name:s.global.t("app.airlineList.MartinAir")},{code:"MJ",icon:"icon-mj-c",logo:"#icon-mj-c",name:s.global.t("app.airlineList.ARGAviation")},{code:"MK",icon:"icon-mk-c",logo:"#icon-mk-c",name:s.global.t("app.airlineList.AirMauritius")},{code:"MR",icon:"icon-mr-c",logo:"#icon-mr-c",name:s.global.t("app.airlineList.AirMauritania")},{code:"MS",icon:"icon-ms-c",logo:"#icon-ms-c",name:s.global.t("app.airlineList.Egyptair")},{code:"MU",icon:"icon-mu-c",logo:"#icon-mu-c",name:s.global.t("app.airlineList.ChinaEasternAirlines")},{code:"MX",icon:"icon-mx-c",logo:"#icon-mx-c",name:s.global.t("app.airlineList.Aeromexico")},{code:"NE",icon:"icon-ne-c",logo:"#icon-ne-c",name:s.global.t("app.airlineList.SkyEurope")},{code:"NF",icon:"icon-nf-c",logo:"#icon-nf-c",name:s.global.t("app.airlineList.AirVanuatu")},{code:"NH",icon:"icon-nh-c",logo:"#icon-nh-c",name:s.global.t("app.airlineList.AllNipponAirways")},{code:"NS",icon:"icon-ns-c",logo:"#icon-ns-c",name:s.global.t("app.airlineList.HebeiAirlines")},{code:"NX",icon:"icon-nx-c",logo:"#icon-nx-c",name:s.global.t("app.airlineList.AirMacau")},{code:"NZ",icon:"icon-nz-c",logo:"#icon-nz-c",name:s.global.t("app.airlineList.AirNewZealand")},{code:"OA",icon:"icon-oa-c",logo:"#icon-oa-c",name:s.global.t("app.airlineList.OlympicAir")},{code:"OD",icon:"icon-od-c",logo:"#icon-od-c",name:s.global.t("app.airlineList.AirNatalco")},{code:"OK",icon:"icon-ok-c",logo:"#icon-ok-c",name:s.global.t("app.airlineList.CzechAirlines")},{code:"OL",icon:"icon-ol-c",logo:"#icon-ol-c",name:s.global.t("app.airlineList.OLTAviation")},{code:"OM",icon:"icon-om-c",logo:"#icon-om-c",name:s.global.t("app.airlineList.MongolianAirlines")},{code:"OQ",icon:"icon-oq-c",logo:"#icon-oq-c",name:s.global.t("app.airlineList.ChongqingAirlines")},{code:"OS",icon:"icon-os-c",logo:"#icon-os-c",name:s.global.t("app.airlineList.OlympicAirlines")},{code:"OT",icon:"icon-ot-c",logo:"#icon-ot-c",name:s.global.t("app.airlineList.PelicanAir")},{code:"OU",icon:"icon-ou-c",logo:"#icon-ou-c",name:s.global.t("app.airlineList.CroatianAirlines")},{code:"OV",icon:"icon-ov-c",logo:"#icon-ov-c",name:s.global.t("app.airlineList.EstonianAir")},{code:"OZ",icon:"icon-oz-c",logo:"#icon-oz-c",name:s.global.t("app.airlineList.AsianaAirlines")},{code:"PG",icon:"icon-pg-c",logo:"#icon-pg-c",name:s.global.t("app.airlineList.BangkokAirways")},{code:"PK",icon:"icon-pk-c",logo:"#icon-pk-c",name:s.global.t("app.airlineList.PakistanAirlines")},{code:"PN",icon:"icon-pn-c",logo:"#icon-pn-c",name:s.global.t("app.airlineList.WesternAir")},{code:"PR",icon:"icon-pr-c",logo:"#icon-pr-c",name:s.global.t("app.airlineList.PhilippineAirlines")},{code:"PS",icon:"icon-ps-c",logo:"#icon-ps-c",name:s.global.t("app.airlineList.UkrainianAirlines")},{code:"PU",icon:"icon-pu-c",logo:"#icon-pu-c",name:s.global.t("app.airlineList.AirUruguay")},{code:"PW",icon:"icon-pw-c",logo:"#icon-pw-c",name:s.global.t("app.airlineList.PrecisionAviation")},{code:"PX",icon:"icon-px-c",logo:"#icon-px-c",name:s.global.t("app.airlineList.AirNewGuinea")},{code:"PY",icon:"icon-py-c",logo:"#icon-py-c",name:s.global.t("app.airlineList.SurinameAir")},{code:"PZ",icon:"icon-pz-c",logo:"#icon-pz-c",name:s.global.t("app.airlineList.AirParaguay")},{code:"QF",icon:"icon-qf-c",logo:"#icon-qf-c",name:s.global.t("app.airlineList.QantasAirways")},{code:"QR",icon:"icon-qr-c",logo:"#icon-qr-c",name:s.global.t("app.airlineList.QatarAirlines")},{code:"QV",icon:"icon-qv-c",logo:"#icon-qv-c",name:s.global.t("app.airlineList.LaoAir")},{code:"RA",icon:"icon-ra-c",logo:"#icon-ra-c",name:s.global.t("app.airlineList.NepalAirlines")},{code:"RC",icon:"icon-rc-c",logo:"#icon-rc-c",name:s.global.t("app.airlineList.FaroeIslandsAir")},{code:"RG",icon:"icon-rg-c",logo:"#icon-rg-c",name:s.global.t("app.airlineList.BrazilianAirlines")},{code:"RJ",icon:"icon-rj-c",logo:"#icon-rj-c",name:s.global.t("app.airlineList.JordanAviation")},{code:"RO",icon:"icon-ro-c",logo:"#icon-ro-c",name:s.global.t("app.airlineList.RomanianAirlines")},{code:"RQ",icon:"icon-rq-c",logo:"#icon-rq-c",name:s.global.t("app.airlineList.CAMAir")},{code:"S2",icon:"icon-s2-c",logo:"#icon-s2-c",name:s.global.t("app.airlineList.SaharaAir")},{code:"S7",icon:"icon-s7-c",logo:"#icon-s7-c",name:s.global.t("app.airlineList.SiberianAirlines")},{code:"SA",icon:"icon-sa-c",logo:"#icon-sa-c",name:s.global.t("app.airlineList.SouthAfricanAirways")},{code:"SB",icon:"icon-sb-c",logo:"#icon-sb-c",name:s.global.t("app.airlineList.AirCaledonia")},{code:"SC",icon:"icon-sc-c",logo:"#icon-sc-c",name:s.global.t("app.airlineList.ShandongAirlines")},{code:"SK",icon:"icon-sk-c",logo:"#icon-sk-c",name:s.global.t("app.airlineList.ScandinavianAirlines")},{code:"SN",icon:"icon-sn-c",logo:"#icon-sn-c",name:s.global.t("app.airlineList.BrusselsAirlines")},{code:"SQ",icon:"icon-sq-c",logo:"#icon-sq-c",name:s.global.t("app.airlineList.SingaporeAirlines")},{code:"SU",icon:"icon-su-c",logo:"#icon-su-c",name:s.global.t("app.airlineList.Aeroflot")},{code:"SV",icon:"icon-sv-c",logo:"#icon-sv-c",name:s.global.t("app.airlineList.SaudiAirlines")},{code:"SW",icon:"icon-sw-c",logo:"#icon-sw-c",name:s.global.t("app.airlineList.AirNamibia")},{code:"T5",icon:"icon-t5-c",logo:"#icon-t5-c",name:s.global.t("app.airlineList.TurkmenistanAir")},{code:"TA",icon:"icon-ta-c",logo:"#icon-ta-c",name:s.global.t("app.airlineList.TakaInternationalAirlines")},{code:"TF",icon:"icon-tf-c",logo:"#icon-tf-c",name:s.global.t("app.airlineList.MalmoAir")},{code:"TG",icon:"icon-tg-c",logo:"#icon-tg-c",name:s.global.t("app.airlineList.ThaiAirways")},{code:"TK",icon:"icon-tk-c",logo:"#icon-tk-c",name:s.global.t("app.airlineList.TurkishAirlines")},{code:"TM",icon:"icon-tm-c",logo:"#icon-tm-c",name:s.global.t("app.airlineList.RamAir")},{code:"TN",icon:"icon-tn-c",logo:"#icon-tn-c",name:s.global.t("app.airlineList.AirTahiti")},{code:"TO",icon:"icon-to-c",logo:"#icon-to-c",name:s.global.t("app.airlineList.PresidentialAviation")},{code:"TP",icon:"icon-tp-c",logo:"#icon-tp-c",name:s.global.t("app.airlineList.AirPortugal")},{code:"TU",icon:"icon-tu-c",logo:"#icon-tu-c",name:s.global.t("app.airlineList.TunisAir")},{code:"TV",icon:"icon-tv-c",logo:"#icon-tv-c",name:s.global.t("app.airlineList.TibetAirlines")},{code:"U6",icon:"icon-u6-c",logo:"#icon-u6-c",name:s.global.t("app.airlineList.UralAirlines")},{code:"UA",icon:"icon-ua-c",logo:"#icon-ua-c",name:s.global.t("app.airlineList.UnitedAirlines")},{code:"UL",icon:"icon-ul-c",logo:"#icon-ul-c",name:s.global.t("app.airlineList.SiHang")},{code:"UM",icon:"icon-um-c",logo:"#icon-um-c",name:s.global.t("app.airlineList.AirZimbabwe")},{code:"UN",icon:"icon-un-c",logo:"#icon-un-c",name:s.global.t("app.airlineList.AnnulusAviation")},{code:"UO",icon:"icon-uo-c",logo:"#icon-uo-c",name:s.global.t("app.airlineList.HongKongExpress")},{code:"UP",icon:"icon-up-c",logo:"#icon-up-c",name:s.global.t("app.airlineList.BahamasAirlines")},{code:"US",icon:"icon-us-c",logo:"#icon-us-c",name:s.global.t("app.airlineList.AmericanAir")},{code:"UU",icon:"icon-uu-c",logo:"#icon-uu-c",name:s.global.t("app.airlineList.AuslarAir")},{code:"UX",icon:"icon-ux-c",logo:"#icon-ux-c",name:s.global.t("app.airlineList.SpanishAirEurope")},{code:"V2",icon:"icon-v2-c",logo:"#icon-v2-c",name:s.global.t("app.airlineList.KaratAir")},{code:"VA",icon:"icon-va-c",logo:"#icon-va-c",name:s.global.t("app.airlineList.VirginAustralia")},{code:"VD",icon:"icon-vd-c",logo:"#icon-vd-c",name:s.global.t("app.airlineList.HenanAirlines")},{code:"VF",icon:"icon-vf-c",logo:"#icon-vf-c",name:s.global.t("app.airlineList.HuitravelAir")},{code:"VN",icon:"icon-vn-c",logo:"#icon-vn-c",name:s.global.t("app.airlineList.VietnamAirlines")},{code:"VR",icon:"icon-vr-c",logo:"#icon-vr-c",name:s.global.t("app.airlineList.CapeVerdeAirlines")},{code:"VS",icon:"icon-vs-c",logo:"#icon-vs-c",name:s.global.t("app.airlineList.VirginAtlantic")},{code:"VV",icon:"icon-vv-c",logo:"#icon-vv-c",name:s.global.t("app.airlineList.UkrainianAirlines")},{code:"VW",icon:"icon-vw-c",logo:"#icon-vw-c",name:s.global.t("app.airlineList.GreatLakesAir")},{code:"VX",icon:"icon-vx-c",logo:"#icon-vx-c",name:s.global.t("app.airlineList.WebbardAir")},{code:"VY",icon:"icon-vy-c",logo:"#icon-vy-c",name:s.global.t("app.airlineList.WellingAir")},{code:"WF",icon:"icon-wf-c",logo:"#icon-wf-c",name:s.global.t("app.airlineList.WidroAir")},{code:"WN",icon:"icon-wn-c",logo:"#icon-wn-c",name:s.global.t("app.airlineList.SouthwestAirlines")},{code:"WS",icon:"icon-ws-c",logo:"#icon-ws-c",name:s.global.t("app.airlineList.WesternJetAviation")},{code:"WY",icon:"icon-wy-c",logo:"#icon-wy-c",name:s.global.t("app.airlineList.OmanAir")},{code:"XF",icon:"icon-xf-c",logo:"#icon-xf-c",name:s.global.t("app.airlineList.AirVladivostok")},{code:"XK",icon:"icon-xk-c",logo:"#icon-xk-c",name:s.global.t("app.airlineList.CorsairAirlines")},{code:"YV",icon:"icon-yv-c",logo:"#icon-yv-c",name:s.global.t("app.airlineList.MesaAir")},{code:"ZH",icon:"icon-zh-c",logo:"#icon-zh-c",name:s.global.t("app.airlineList.ShenzhenAirlines")},{code:"ZI",icon:"icon-zi-c",logo:"#icon-zi-c",name:s.global.t("app.airlineList.AigleAzur")},{code:"ZK",icon:"icon-zk-c",logo:"#icon-zk-c",name:s.global.t("app.airlineList.GreatLakesAirlines")},{code:"ZL",icon:"icon-zl-c",logo:"#icon-zl-c",name:s.global.t("app.airlineList.RegionalExpressAirlines")},{code:"3U",icon:"icon-3u-c",logo:"#icon-3u-c",name:s.global.t("app.airlineList.SichuanAirlines")},{code:"QW",icon:"icon-qw-c",logo:"#icon-qw-c",name:s.global.t("app.airlineList.QingdaoAirlines")},{code:"KR",icon:"icon-kr-c",logo:"#icon-kr-c",name:s.global.t("app.airlineList.CambodiaAirways")},{code:"QD",icon:"icon-qd-c",logo:"#icon-qd-c",name:s.global.t("app.airlineList.JCAirlinesCambodia")},{code:"TR",icon:"icon-tr-c",logo:"#icon-tr-c",name:s.global.t("app.airlineList.Tigerair")},{code:"FU",icon:"icon-fu-c",logo:"#icon-fu-c",name:s.global.t("app.airlineList.FuzhouAirlines")},{code:"XW",icon:"icon-xw-c",logo:"#icon-xw-c",name:s.global.t("app.airlineList.CoolBirdAir")},{code:"GY",icon:"icon-gy-c",logo:"#icon-gy-c",name:s.global.t("app.airlineList.GuizhouAirlines")},{code:"9D",icon:"icon-9d-c",logo:"#icon-9d-c",name:s.global.t("app.airlineList.PermAir")},{code:"H9",icon:"icon-h9-c",logo:"#icon-h9-c",name:s.global.t("app.airlineList.HimalayaAirlines")},{code:"9H",icon:"icon-9h-c",logo:"#icon-9h-c",name:s.global.t("app.airlineList.ChanganAirlines")},{code:"GT",icon:"icon-gt-c",logo:"#icon-gt-c",name:s.global.t("app.airlineList.GuilinAir")},{code:"GX",icon:"icon-gx-c",logo:"#icon-gx-c",name:s.global.t("app.airlineList.BeibuGulfAir")},{code:"LT",icon:"icon-lt-c",logo:"#icon-lt-c",name:s.global.t("app.airlineList.LongjiangAirlines")},{code:"A6",icon:"icon-a6-c",logo:"#icon-a6-c",name:s.global.t("app.airlineList.RedEarthAir")},{code:"RY",icon:"icon-ry-c",logo:"#icon-ry-c",name:s.global.t("app.airlineList.JiangxiAirlines")}],Qr={class:"rounded bg-brand-7 flex flex-col border-t mt-[10px]"},Or={class:"font-normal text-xs leading-[20px] items-center text-gray-2"},Rr={class:"font-normal text-xs leading-[20px] items-center text-gray-2"},Vr={class:"font-normal text-xs leading-[20px] items-center text-gray-2"},af=Re({__name:"AirRoute",props:{routingSegList:{},needCloseIcon:{type:Boolean}},setup(e){return(a,o)=>(p(),D("div",Qr,[a.needCloseIcon?lt(a.$slots,"default",{key:0}):ge("",!0),(p(!0),D(_e,null,ke(a.routingSegList,(r,d)=>(p(),D("div",{key:d,class:"flex flex-col p-[10px] font-normal text-xs leading-[20px] break-all"},[(p(!0),D(_e,null,ke(r.routingRestrictions,(i,l)=>(p(),D("div",{key:l,class:"font-normal text-xs leading-[20px] text-gray-1"},[n("span",Or,b(`*${i}`),1)]))),128)),(p(!0),D(_e,null,ke(r.routingPaths,(i,l)=>(p(),D("div",{key:l,class:"font-normal text-xs leading-[20px] text-gray-1"},[n("span",Rr,b(`${l+1}*`),1),n("span",Vr,b(`${i}`),1)]))),128))]))),128))]))}}),Br=e=>{const a=e.match(/\.(\d+)$/);return a?a[1].length:0},of=e=>e&&e!=="-"&&e!=="--"&&e.indexOf("%")===-1&&!e.includes("+")?Br(e)>2?e:Number(e).toFixed(2):e,nf=(e,a,o)=>{let r,d,i,l;try{r=e.toString().split(".")[1].length}catch{r=0}try{d=a.toString().split(".")[1].length}catch{d=0}const A=Math.abs(r-d),g=10**Math.max(r,d);if(A>0){const C=10**A;r>d?(i=Number(e.toString().replace(".","")),l=Number(a.toString().replace(".",""))*C):(i=Number(e.toString().replace(".",""))*C,l=Number(a.toString().replace(".","")))}else i=Number(e.toString().replace(".","")),l=Number(a.toString().replace(".",""));return o==="+"?(i+l)/g:(i-l)/g},Yr=e=>($t("data-v-2f9e66d0"),e=e(),xt(),e),Ur={class:"min-h-[50px]"},Hr={key:0,class:"brand text-xs"},qr=Yr(()=>n("div",{class:"brand-line border-b mt-[10px]"},null,-1)),jr={key:1,className:"text-neutral-800 text-xs font-bold leading-tight"},Gr={key:2,className:"w-[100%] h-[30px] px-1.5 py-[7px] bg-[#f5f9ff] rounded-tl-md justify-start items-center inline-flex mt-[6px]"},Kr={key:3,className:"w-[100%] h-[30px] px-1.5 py-[7px] bg-[#f5f9ff] rounded-tl-md justify-start items-center inline-flex mt-[6px]"},zr={class:"row-base th ml-[-7px]"},Jr={key:4,class:"cell rounded-bl-md rounded-tr-md rounded-tl-md rounded-br-md flex flex-col"},Wr={key:6,class:"cell rounded-bl-md rounded-tr-md rounded-tl-md rounded-br-md flex flex-col"},Zr=Re({__name:"priceBasis",props:{priceBasis:{},ticketingTime:{},computingTime:{},priceFc:{},fcc:{},needCloseIcon:{type:Boolean}},setup(e){return(a,o)=>{var i,l,A,g;const r=Dn,d=An;return p(),D("div",Ur,[a.priceBasis?(p(),D("div",Hr,[qr,a.needCloseIcon?lt(a.$slots,"default",{key:0},void 0,!0):(p(),D("div",jr,b(a.$t("app.fareQuery.freightate.ticketPriceBasis")),1)),a.priceFc?(p(),D("div",Gr,b(a.priceFc),1)):ge("",!0),(l=(i=a.priceBasis)==null?void 0:i.segmentFare[0])!=null&&l.rate?(p(),D("div",Kr,[n("span",zr,b(a.$t("app.fareQuery.freightate.rate")),1),n("span",null,b((g=(A=a.priceBasis)==null?void 0:A.segmentFare[0])==null?void 0:g.rate),1)])):ge("",!0),_(d,{class:"row-base th mt-[2px]"},{default:$(()=>[_(r,{span:4},{default:$(()=>[me("PU")]),_:1}),_(r,{span:4},{default:$(()=>[me("Farebasis")]),_:1}),_(r,{span:4},{default:$(()=>[me(b(a.$t("app.fareQuery.freightate.currency")),1)]),_:1}),_(r,{span:4},{default:$(()=>[me("NUC")]),_:1}),_(r,{span:4},{default:$(()=>[me(b(a.$t("app.fareQuery.freightate.desc")),1)]),_:1}),_(r,{span:4},{default:$(()=>[me(b(a.$t("app.fareQuery.freightate.globalIndicator")),1)]),_:1})]),_:1}),a.priceBasis.segmentFare&&a.priceBasis.segmentFare.length>0?(p(),D("div",Jr,[(p(!0),D(_e,null,ke(a.priceBasis.segmentFare,(C,v)=>(p(),Ae(d,{key:v,class:"row-base"},{default:$(()=>[_(r,{span:4},{default:$(()=>[me(b(C.pu??"--"),1)]),_:2},1024),_(r,{span:4},{default:$(()=>[me(b(C.fareBasis??"--"),1)]),_:2},1024),_(r,{span:4},{default:$(()=>[me(b(C.currency??"--"),1)]),_:2},1024),_(r,{span:4},{default:$(()=>[me(b(C.nuc??"--"),1)]),_:2},1024),_(r,{span:4},{default:$(()=>[me(b(C.description??"--"),1)]),_:2},1024),_(r,{span:4},{default:$(()=>[me(b(C.globalIndicator??"--"),1)]),_:2},1024)]),_:2},1024))),128))])):(p(),Ae(d,{key:5,class:"row-base cell rounded-bl-md rounded-tr-md rounded-tl-md rounded-br-md"},{default:$(()=>[_(r,{span:4},{default:$(()=>[me("--")]),_:1}),_(r,{span:4},{default:$(()=>[me("--")]),_:1}),_(r,{span:4},{default:$(()=>[me("--")]),_:1}),_(r,{span:4},{default:$(()=>[me("--")]),_:1}),_(r,{span:4},{default:$(()=>[me("--")]),_:1}),_(r,{span:4},{default:$(()=>[me("--")]),_:1})]),_:1})),_(d,{class:"row-base mt-[10px] th"},{default:$(()=>[_(r,{span:4},{default:$(()=>[me("CNY TAX")]),_:1}),_(r,{span:4},{default:$(()=>[me("LST "+b(a.$t("app.fareQuery.freightate.currency")),1)]),_:1}),_(r,{span:4},{default:$(()=>[me("CODES TYP")]),_:1}),_(r,{span:8},{default:$(()=>[me(b(a.$t("app.fareQuery.freightate.desc")),1)]),_:1}),_(r,{span:4},{default:$(()=>[me("TAX")]),_:1})]),_:1}),a.priceBasis.taxFare&&a.priceBasis.taxFare.length>0?(p(),D("div",Wr,[(p(!0),D(_e,null,ke(a.priceBasis.taxFare,(C,v)=>(p(),Ae(d,{key:v,class:"row-base"},{default:$(()=>[_(r,{span:4},{default:$(()=>[me(b(C.cnyTax??"--"),1)]),_:2},1024),_(r,{span:4},{default:$(()=>[me(b(C.lstCurrency??"--"),1)]),_:2},1024),_(r,{span:4},{default:$(()=>[me(b(C.codesTyp??"--"),1)]),_:2},1024),_(r,{span:8},{default:$(()=>[me(b(C.description??"--"),1)]),_:2},1024),_(r,{span:4},{default:$(()=>[me(b(C.tax??"--"),1)]),_:2},1024)]),_:2},1024))),128))])):(p(),Ae(d,{key:7,class:"row-base cell rounded-bl-md rounded-tr-md rounded-tl-md rounded-br-md"},{default:$(()=>[_(r,{span:4},{default:$(()=>[me("--")]),_:1}),_(r,{span:4},{default:$(()=>[me("--")]),_:1}),_(r,{span:4},{default:$(()=>[me("--")]),_:1}),_(r,{span:8},{default:$(()=>[me("--")]),_:1}),_(r,{span:4},{default:$(()=>[me("--")]),_:1})]),_:1})),_(d,{class:"row-base mt-[10px] th"},{default:$(()=>[_(r,{span:8},{default:$(()=>[me(b(a.$t("app.fareQuery.freightate.computingTime")),1)]),_:1}),_(r,{span:8},{default:$(()=>[me(b(a.$t("app.fareQuery.freightate.ticketingTime")),1)]),_:1}),_(r,{span:4},{default:$(()=>[me("FCC")]),_:1})]),_:1}),_(d,{class:"row-base cell rounded-bl-md rounded-tr-md rounded-tl-md rounded-br-md flex"},{default:$(()=>[_(r,{span:8},{default:$(()=>[me(b(a.computingTime??"--"),1)]),_:1}),_(r,{span:8},{default:$(()=>[me(b(a.ticketingTime??"--"),1)]),_:1}),_(r,{span:4},{default:$(()=>[me(b(a.fcc??"--"),1)]),_:1})]),_:1})])):ge("",!0)])}}});const rf=je(Zr,[["__scopeId","data-v-2f9e66d0"]]),Xr={class:"cursor-default"},es={class:"w-full flex justify-between"},ts={class:"text-xs text-gray-2 mb-1.5"},as={class:"text-xs text-gray-2 font-bold mb-1.5"},os={class:"text-xs rounded bg-brand-7 mt-[5px]"},ns={class:"pr-[40px] text-gray-2"},is={class:"basis-[100px] text-gray-2"},rs={class:"text-gray-2"},ss={key:0,class:"mb-2.5"},ls={class:"flex justify-between items-center"},cs={key:0,class:"w-full overflow-x-hidden text-xs mr-5 bg-brand-7"},ds={class:"overflow-x-auto"},us={class:"flex p-0 mb-1 bg-[#FFFFFF] text-[#8C8C8C] whitespace-nowrap h-[30px] leading-[30px]"},ps={class:"w-60 px-2"},gs={class:"w-40 px-2"},fs={class:"min-w-[120px] px-2"},ms={class:"w-[60px] px-2"},hs={class:"w-60 px-2 overflow-hidden whitespace-nowrap text-ellipsis bg-brand-7"},vs={class:"w-40 bg-brand-7"},ys={class:"min-w-[125px] px-2"},bs={class:"w-[60px] px-2"},_s=Re({__name:"Package",props:{baggageAllowance:{},international:{type:Boolean},needCloseIcon:{type:Boolean},doBaggageApi:{type:Boolean}},emits:["closePanel"],setup(e){const a=O(!1),o=g=>g.index?`${g.baggageCurrency}${g.baggageAmout}${g.baggageType==="4"?`/${g.baggageUnit}`:""}`:"-",r=g=>g.baggageType??"-",d=St(),{activeTag:i}=Ot(d),l=Ie(()=>d.getOrderInfo),A=Ie(()=>{var g;return((g=l.value.get(i.value))==null?void 0:g.type)==="2"});return(g,C)=>{var N,P,f;const v=$a,m=He,T=ii,E=bt;return p(),D("div",Xr,[_(v),n("div",es,[n("p",ts,b(g.international?g.$t("app.fareQuery.freightate.baggageAllowanceTips"):""),1),g.needCloseIcon?(p(),Ae(m,{key:0,class:"cursor-pointer mr-[10px]",size:"16px",onClick:C[0]||(C[0]=F=>g.$emit("closePanel"))},{default:$(()=>[_(t(no))]),_:1})):ge("",!0)]),n("div",{class:be(["mb-[2px]",g.international&&A.value?"hidden":""])},[n("p",as,b(g.$t("app.fareQuery.freightate.freeBaggageAllowance")),1),!g.international&&g.doBaggageApi?(p(),Ae(T,{key:0,title:g.$t("app.fareQuery.freightate.domBaggageAllowanceTips"),type:"warning",closable:!1,"show-icon":""},null,8,["title"])):ge("",!0),n("div",os,[(p(!0),D(_e,null,ke(((N=g.baggageAllowance)==null?void 0:N.freeBaggageSegInfo)??[],(F,B)=>(p(),D("p",{key:B,class:"flex px-2 h-[30px] leading-[30px]"},[n("span",ns,b(g.$t("app.fareQuery.freightate.freeBaggageAllowance")),1),n("span",is,b(`${F.departurePlace}-${F.arrivalPlace}`),1),n("span",rs,b(F.freeBaggageFare),1)]))),128))])],2),g.international?(p(),D("div",ss,[n("div",ls,[n("p",{class:"flex items-center text-xs text-gray-1 font-bold mb-1.5",onClick:C[1]||(C[1]=F=>a.value=!a.value)},[n("span",null,b(g.$t("app.fareQuery.freightate.excessBaggageAllowance")),1),a.value?(p(),Ae(m,{key:0,size:16},{default:$(()=>[_(t(Pt))]),_:1})):(p(),Ae(m,{key:1,size:16},{default:$(()=>[_(t(qt))]),_:1}))])]),a.value?(p(),D("div",cs,[n("div",ds,[n("p",us,[n("span",ps,b(g.$t("app.fareQuery.freightate.baggageDescription")),1),n("span",gs,b(g.$t("app.fareQuery.freightate.segment")),1),(p(!0),D(_e,null,ke(((P=g.baggageAllowance)==null?void 0:P.payBaggageTitle)??[],(F,B)=>(p(),D(_e,{key:B},[n("span",fs,b(F),1),n("span",ms,b(g.$t("app.fareQuery.freightate.baggageType")),1)],64))),128))]),(p(!0),D(_e,null,ke(((f=g.baggageAllowance)==null?void 0:f.payBaggageInfo)??[],(F,B)=>(p(),D(_e,{key:B},[(p(!0),D(_e,null,ke(F.payBaggageSegInfo,(Y,h)=>(p(),D("div",{key:h,class:"rounded flex p-0 h-[30px] leading-[30px]"},[_(E,{effect:"dark",placement:"top",content:F.payBaggageStatement},{default:$(()=>[n("span",hs,b(F.payBaggageStatement),1)]),_:2},1032,["content"]),n("span",vs,b(`${Y.departurePlace}-${Y.arrivalPlace}`),1),(p(!0),D(_e,null,ke(Y.payBaggageFares,(c,u)=>(p(),D(_e,{key:u},[n("span",ys,b(o(c)),1),n("span",bs,b(r(c)),1)],64))),128))]))),128))],64))),128))])])):ge("",!0)])):ge("",!0)])}}});const sf=je(_s,[["__scopeId","data-v-66ec95ac"]]),Cs={class:"min-h-[50px] border-t mt-[10px]"},Ds={key:1,class:"brand text-xs flex flex-col"},As={class:"brand-title font-bold text-gray-2 mb-1 mt-0.5"},$s={key:0,class:"brand-info bg-brand-7 rounded-[1px] p-[8px] flex flex-col"},xs={class:"brand-free brand-one text-green-2 mr-4 font-bold h-5"},Ts={key:0,class:"brand-content text-gray-2 text-xs font-normal leading-tight flex h-5 items-center"},Ss={class:"min-w-[30px]"},ks=n("div",{class:"border-b border-dashed border-gray-300 mb-1 mt-1"},null,-1),Fs={class:"brand-pay brand-one text-yellow-1 mr-4 font-bold h-5"},Ls={key:0,class:"brand-content text-gray-2 text-xs font-normal leading-tight flex h-5 items-center"},ws={class:"min-w-[30px]"},Ns={key:0,class:"border-b border-dashed border-gray-300 mb-1 mt-1"},Is=n("div",{class:"brand-provide brand-one text-gray-1 mr-4 font-bold h-5"},"DISPALY AS NOT OFFERED",-1),Es={key:0,class:"brand-content text-gray-2 text-xs font-normal leading-tight flex h-5 items-center"},Ps={class:"min-w-[30px]"},Ms={key:2,class:"min-h-[50px]"},lf=Re({__name:"Brand",props:{brandInfoData:{},needCloseIcon:{type:Boolean}},setup(e){return(a,o)=>{var r;return p(),D("div",Cs,[a.needCloseIcon?lt(a.$slots,"default",{key:0}):ge("",!0),(r=a.brandInfoData)!=null&&r.length?(p(),D("div",Ds,[(p(!0),D(_e,null,ke(a.brandInfoData,(d,i)=>(p(),D(_e,{key:i},[n("div",As,b(d.brandName)+" "+b(d.tierCode),1),d.equityClassify.length>0?(p(),D("div",$s,[(p(!0),D(_e,null,ke(d.equityClassify,(l,A)=>(p(),D("div",{key:A,class:"mb-1"},[l.offeredForFree.length>0?(p(),D(_e,{key:0},[n("div",xs,b(a.$t("app.fareQuery.freightate.free")),1),(p(!0),D(_e,null,ke(l.offeredForFree,(g,C)=>(p(),D(_e,{key:C},[g.subCode||g.title?(p(),D("div",Ts,[n("div",Ss,b(g.subCode??""),1),me(b(g.title),1)])):ge("",!0)],64))),128)),ks],64)):ge("",!0),l.offeredForCharge.length>0?(p(),D(_e,{key:1},[n("div",Fs,b(a.$t("app.fareQuery.freightate.charge")),1),(p(!0),D(_e,null,ke(l.offeredForCharge,(g,C)=>(p(),D(_e,{key:C},[g.subCode||g.title?(p(),D("div",Ls,[n("div",ws,b(g.subCode??""),1),me(b(g.title),1)])):ge("",!0)],64))),128)),l.displayAsNotOffered.length>0?(p(),D("div",Ns)):ge("",!0)],64)):ge("",!0),l.displayAsNotOffered.length>0?(p(),D(_e,{key:2},[Is,(p(!0),D(_e,null,ke(l.displayAsNotOffered,(g,C)=>(p(),D(_e,{key:C},[g.subCode||g.title?(p(),D("div",Es,[n("div",Ps,b(g.subCode??""),1),me(b(g.title),1)])):ge("",!0)],64))),128))],64)):ge("",!0)]))),128))])):ge("",!0)],64))),128))])):(p(),D("div",Ms,b(a.$t("app.fareQuery.freightate.noData")),1))])}}}),Qs=St(),{activeTag:Os}=Ot(Qs),Ua=()=>({flightDataList:new Map,clickFlightSearchFlag:!1,seizeSeatInfoFlight:[],clickHistoryFlag:{},isInterSegment:!1,refrashFlight:!1,activeFlightIndex:0}),ia=tn("flight",{state:()=>({flightByDate:{[Os.value]:Ua()},simEtermIsOpen:!1}),actions:{initAgentSell(e){this.flightByDate[e]=Ua()},deleteAgentSell(e){delete this.flightByDate[e]},setActiveFlightIndex(e,a){this.flightByDate[e].activeFlightIndex=a},setFlightDataList(e,a,o,r,d){var l,A;o.currentPage=d||1,o.queryForm=a;const i=new Map([[r,o]]);(l=this.flightByDate[e])==null||l.flightDataList.forEach((g,C)=>{i.has(C)||i.set(C,g)}),(A=this.flightByDate[e])==null||A.flightDataList.clear(),this.flightByDate[e].flightDataList=i},delFlightDataList(e,a){this.flightByDate[e].flightDataList.delete(a)},updateFlightDataListKey(e,a,o,r){const d=[...this.flightByDate[e].flightDataList.keys()].indexOf(o),i=this.flightByDate[e].flightDataList.get(o);if(!i)return;i.queryForm=r;const l=[...this.flightByDate[e].flightDataList];l.splice(d,1,[a,i]);const A=new Map(l);this.flightByDate[e].flightDataList=A},delFlightByPnr(e){this.flightByDate=Wn(this.flightByDate,[e])},setHistoryQueryForm(e,a){this.flightByDate[e].clickHistoryFlag.flag=!this.flightByDate[e].clickHistoryFlag.flag,this.flightByDate[e].clickHistoryFlag.queryForm=a},setClickFlightSearchFlag(e,a){this.flightByDate[e]||this.initAgentSell(e),this.flightByDate[e].clickFlightSearchFlag=!!a},delSeizeSeatInfoFlight(e,a){this.flightByDate[e].seizeSeatInfoFlight=this.flightByDate[e].seizeSeatInfoFlight.filter(o=>o.key!==a)},setSeizeSeatInfoFlight(e,a){this.flightByDate[e].seizeSeatInfoFlight.push(a)},setRefrashFlight(e,a){this.flightByDate[e].refrashFlight=a},setSimEtermOpenFlag(e){this.simEtermIsOpen=e}}}),Rs={class:"custom-focus-tip-input"},Vs={class:"hidden bkc-el-form-item__error"},Bs=Re({__name:"CustomFocusTipInput",props:{modelValue:{default:""},tipText:{}},emits:["update:modelValue"],setup(e,{emit:a}){const o=e,r=a,d=Ie({get:()=>o.modelValue.toUpperCase(),set:A=>{i.value=!A,r("update:modelValue",A.toUpperCase())}}),i=O(!1),l=A=>{if(A){i.value=!d.value;return}i.value=A};return(A,g)=>{const C=Tt;return p(),D("div",Rs,[n("div",{class:be(["input-focus-tip",i.value?"input-focus":""])},[_(C,oo({ref:"inputRef",modelValue:d.value,"onUpdate:modelValue":g[0]||(g[0]=v=>d.value=v)},A.$attrs,{onBlur:g[1]||(g[1]=v=>l(!1)),onFocus:g[2]||(g[2]=v=>l(!0))}),ao({_:2},[ke(A.$slots,(v,m)=>({name:m,fn:$(()=>[lt(A.$slots,m,{},void 0,!0)])}))]),1040,["modelValue"]),n("div",Vs,b(A.tipText),1)],2)])}}});const Ha=je(Bs,[["__scopeId","data-v-6bab0367"]]);var Nt=(e=>(e[e.DIRECT=0]="DIRECT",e[e.STOP=-1]="STOP",e))(Nt||{});const It="FAST_QUERY_AV_QUERY",Ys=()=>te().format("YYYY-MM-DD"),Us=e=>te(e).add(1,"day").format("YYYY-MM-DD"),Hs=e=>te(e).subtract(1,"day").format("YYYY-MM-DD"),Co=(e,a)=>{var i;let o=[];const r=Array.from(a.keys()),d=r==null?void 0:r.find(l=>{var A,g;return l!==e&&((g=(A=a.get(l))==null?void 0:A.specialContent)==null?void 0:g.includes(s.global.t("app.basic.occupy")))});if(d){const l=a.get(d);o=((l==null?void 0:l.flight)??[]).filter(A=>A.segments.some(g=>g.segmentType==="2"))}else o=(((i=a.get(e))==null?void 0:i.flight)??[]).filter(l=>l.segments.some(A=>A.segmentType==="2"));return o},qs=e=>{var a,o;return!!(e!=null&&e.notchPath)||((o=(a=e==null?void 0:e.segments[0])==null?void 0:a.airlines)==null?void 0:o.flightNo)==="ARNK"},js=e=>{var a,o;return!!(e!=null&&e.openFlag)||((o=(a=e==null?void 0:e.segments[0])==null?void 0:a.airlines)==null?void 0:o.flightNo)==="OPEN"},Gs=e=>{const a=e==null?void 0:e.match(/[a-zA-Z]+/g);return a?a==null?void 0:a.join(""):""},ea=(e,a)=>{let o=[],r=Je(a);e&&(r=(r??[]).filter(d=>!js(d)&&!qs(d)));try{o=r.flatMap(d=>d.segments.map(i=>{var l,A,g,C,v;return{airline:i.airlines.airCode,flightNumber:parseInt((A=((l=i.airlines)==null?void 0:l.flightNo)??"")==null?void 0:A.replace(/[a-zA-Z]/g,"")),flightSuffix:Gs((g=i.airlines)==null?void 0:g.flightNo),cls:i.cabins[0].cabinName,departureDate:i.departureDate?te(i.departureDate).format("YYYY-MM-DD"):"",origin:i.departureAirportCode,destination:i.arrivalAirportCode,seats:(i==null?void 0:i.tktNum)??0,action:e?i.actionCode??"":i.seatTag??"",departureTime:(C=i.departureTime)==null?void 0:C.replace(/:/g,""),arrivalTime:(v=i.arrivalTime)==null?void 0:v.replace(/:/g,""),married:i.marriedSegmentNumber?parseInt(i.marriedSegmentNumber):0}}))}catch{o=[]}return o},Ks=async(e,a,o,r,d,i,l)=>{var E,N,P;const A={departureDate:te(a.departureDate).format("YYYY-MM-DD"),queryType:"REAL_TIME_AV",flightNo:e.toUpperCase()},g=(((E=d.get(o))==null?void 0:E.type)??"")==="2",C=Co(o,d),v=((N=r==null?void 0:r.get(o))==null?void 0:N.flight)??[];g&&C.length&&(A.preOccupySegmentInfoList=ea(!1,C)),!g&&v.length&&(A.preOccupySegmentInfoList=ea(!0,v));const m=await bi(A,i),T=((P=m==null?void 0:m.data.value)==null?void 0:P.flightInfoList)??[];if(T.length){const f=F=>{const{departureAirportCode:B,departureAirportCN:Y,arrivalAirportCode:h,arrivalAirportCN:c,departureDate:u,departureTime:J,arrivalDate:y,arrivalTime:G,departureTerminal:Q,arrivalTerminal:x,airlines:S,cabins:U,etInd:z}=F;if(l!=null&&l.length){const K=l.find(I=>I.airportCode.toUpperCase()===B.toUpperCase()),W=l.find(I=>I.airportCode.toUpperCase()===h.toUpperCase());K&&W&&(a.departureAirportCode.toUpperCase()===K.cityCode.toUpperCase()&&(a.departureAirportCode=K.airportCode),a.arrivalAirportCode.toUpperCase()===W.cityCode.toUpperCase()&&(a.arrivalAirportCode=W.airportCode))}const V=`${a.departureAirportCode}${a.arrivalAirportCode}`.toUpperCase()===`${B}${h}`.toUpperCase();if(V){const K=U==null?void 0:U.find(I=>{var L;return(I==null?void 0:I.cabinName.toUpperCase())===((L=a==null?void 0:a.cabin)==null?void 0:L.toUpperCase())});let W="";K&&(W=K.state??""),Object.assign(a,{isRealFlightNo:V,departureAirportCode:B,departureAirportCN:Y,arrivalAirportCode:h,arrivalAirportCN:c,departureDate:u,departureTime:J,arrivalDate:y,arrivalTime:G,departureTerminal:Q,arrivalTerminal:x,airCN:S.airCN,isShared:S.isShared,state:W,etInd:z})}return V};return T.some(F=>{if(F.segments.find(Y=>f(Y)))return!0;if(F.segments.length>1){const Y=F.segments[0],h=F.segments[F.segments.length-1],{arrivalAirportCode:c,arrivalDate:u,arrivalTime:J,arrivalTerminal:y}=h;return Object.assign(Y,{arrivalAirportCode:c,arrivalDate:u,arrivalTime:J,arrivalTerminal:y}),f(Y)}})}return!1},zs=async(e,a,o,r,d)=>{const i=e.map(async l=>{if(l.isOpen)return l.isRealFlightNo=!0,!0;const A=l.flightNumber.startsWith(l.airlines)?l.flightNumber:`${l.airlines}${l.flightNumber}`;return await Ks(A,l,o,r,d,an("01010215"),a),!!l.isRealFlightNo});await Promise.all(i)},Js=e=>(e??[]).map(a=>{const o=[],r=a.segments;return r.some(i=>Number((i==null?void 0:i.stopCity)??0))&&(o.includes(Nt.STOP)||o.push(Nt.STOP),r.length-1===0)?{...a,flyType:o}:(o.includes(r.length-1)||o.push(r.length-1),{...a,flyType:o})}),Ws=()=>{const e=Kt();return{cacheHistoryCondition:async r=>{var g;const d=await nt("QUERY_HISTORY"),{userName:i}=await e.getters.user;if(d===null){await ma("QUERY_HISTORY",JSON.stringify([{userName:i,qureyParam:[r]}]));return}const l=JSON.parse(d==null?void 0:d.localData).findIndex(C=>C.userName===i),A=JSON.parse(d==null?void 0:d.localData);if(l<0)A.push({userName:i,qureyParam:[r]});else{const C=JSON.parse(d==null?void 0:d.localData)[l];if((g=C==null?void 0:C.qureyParam)==null?void 0:g.map(m=>JSON.stringify(m)).some(m=>m===JSON.stringify(r)))return;C.qureyParam.length<5||C.qureyParam.shift(),C.qureyParam.push(r),A[l]=C}await ma("QUERY_HISTORY",JSON.stringify(A))},getHistory:async()=>{var l;const r=await nt("QUERY_HISTORY"),{userName:d}=await e.getters.user;if(!(r!=null&&r.localData))return null;const i=(l=JSON.parse(r==null?void 0:r.localData))==null?void 0:l.find(A=>A.userName===d);return(i==null?void 0:i.qureyParam)??null}}},qa=()=>({destName:"",originName:"",origin:"",destination:"",departureDate:"",departureDateTime:"",departureDateTimes:"",flightNumber:"",onlyDirectFlight:!1,airlines:"",onlyCompany:"",seamlessOrDa:"",unsharedFlight:!1,carrierFlight:!1,lowestPrice:!1,timeSequence:!1,transitTerminal:"",transitTerminalName:"",selectPassengers:{adult:{num:0,min:0,max:1},children:{num:0,min:0,max:1},baby:{num:0,min:0,max:1},chdOverseasStudent:{num:0,min:0,max:1},overseasStudent:{num:0,min:0,max:1},migrate:{num:0,min:0,max:1},chdMigrate:{num:0,min:0,max:1},seafarer:{num:0,min:0,max:1},labourer:{num:0,min:0,max:1}}}),Zs=(e,a)=>{const{t:o}=rt(),{cacheHistoryCondition:r,getHistory:d}=Ws(),i=O(),l=Kt(),A=Ie(()=>l.getters.userPreferences),g=na(),C=St(),{activeTag:v,orderInfo:m}=Ot(C),T=a.avQueryFromFastQuery?It:v.value,E=ia(),N=Ie(()=>{var H;return((H=m.value.get(v.value))==null?void 0:H.commandsAVCheckSuccess)??!1}),P=Ie(()=>{var re;const H=(re=m.value.get(v.value))==null?void 0:re.commands;return JSON.stringify(H?H.get("AV"):"")}),f=O(!1),F=O(""),B=O(),Y=O(),h=O(),c=O(!1),u=O(!0),J=O(!1),y=O(qa()),G=O(!1),Q=["H","E","P","O"].sort(),x=O(!1),S=et({render(){return ft("em",{class:"iconfont icon-calendar"})}}),U=O(),z=O([`${o("app.fastQuery.headerQuery.notHistory")}`]),V=(H,re,se)=>{if(u.value){if(re.length>3||!Ct.test(re)){se(new Error(o("app.fastQuery.headerQuery.formatError")));return}if(y.value.destination&&re===y.value.destination){se(new Error(o("app.fastQuery.headerQuery.identical")));return}}se()},K=(H,re,se)=>{if(u.value&&!re){se(new Error(o("app.fastQuery.headerQuery.must")));return}se()},W=(H,re,se)=>{if(u.value){if(re.length>3||!Ct.test(re)){se(new Error(o("app.fastQuery.headerQuery.formatError")));return}if(y.value.origin&&re===y.value.origin){se(new Error(o("app.fastQuery.headerQuery.identical")));return}}se()},I=(H,re,se)=>{if(u.value&&!re){se(new Error(o("app.fastQuery.headerQuery.must")));return}se()},L=(H,re,se)=>{!/^[A-Za-z]{3}([/][A-Za-z]{3})*$/.test(y.value.transitTerminal??"")&&re?se(new Error(o("app.fastQuery.headerQuery.AirportCode"))):se()},w=(H,re,se)=>{J.value&&!re&&se(new Error(o("app.fastQuery.headerQuery.must"))),se()},M={origin:[{validator:K,trigger:"change"},{validator:V,trigger:"blur"}],destination:[{validator:I,trigger:"change"},{validator:W,trigger:"blur"}],departureDate:[{required:!0,message:o("app.fastQuery.headerQuery.must"),trigger:"change"}],airlines:[{validator:w,trigger:"change"},{pattern:io,message:new Error(o("app.fastQuery.headerQuery.correctAirlineNum")),trigger:"change"}],flightNumber:[{pattern:Ia,message:new Error(o("app.avSearch.validateFltNo")),trigger:"change"}],departureDateTime:[{pattern:Ea,message:new Error(o("app.avSearch.timeFormatTip")),trigger:"change"}],transitTerminal:[{validator:L,trigger:"change"}]},k=async()=>{const{data:H}=await Di("");U.value=H.value},ae=()=>{var H,re;y.value.carrierFlight=((H=A.value)==null?void 0:H.unshared)??!1,y.value.onlyDirectFlight=((re=A.value)==null?void 0:re.nonstop)??!1},he=()=>{const H=y.value.origin;y.value.origin=y.value.destination,y.value.destination=H;const re=y.value.originName;y.value.originName=y.value.destName,y.value.destName=re},fe=H=>{const re=Ta();return H.getTime()<re.getTime()},de=H=>{y.value.seamlessOrDa===H?y.value.seamlessOrDa="":y.value.seamlessOrDa=H},oe=async(H,re,se)=>{var pe;const q=a.avQueryFromFastQuery?It:v.value;return E.setClickFlightSearchFlag(q,!((pe=E.flightByDate[q])!=null&&pe.clickFlightSearchFlag)),H&&re&&await r({dept:H,arrivel:re}),{...y.value,origin:H,destination:re,departureDate:te(se)?te(se).format("YYYY-MM-DD"):""}},j=(H,re,se)=>`${H}${re}${se}`,Z=async()=>{var se,q;F.value="",f.value=!1;let H=y.value.departureDate;if(G.value){H=((se=y.value.departureDate)==null?void 0:se[0])??"";const pe=((q=y.value.departureDate)==null?void 0:q[1])??"";F.value=`${j(y.value.origin,y.value.destination,H)}/${j(y.value.destination,y.value.origin,pe)}`;const ce=await oe(y.value.destination,y.value.origin,pe);e("searchClick",ce)}const re=await oe(y.value.origin,y.value.destination,H);await Ee(),e("searchClick",re),e("getRoundTripFlag",F.value),f.value=!0},X=Gt(()=>{var H;(H=B.value)==null||H.validate(async re=>{re&&(y.value.flightNumber&&(ne(!1),y.value.transitTerminal=""),Z())})},300),ne=H=>{H&&(y.value.flightNumber="",y.value.departureDate=""),y.value.carrierFlight=!1,y.value.lowestPrice=!1,y.value.timeSequence=!1,y.value.onlyDirectFlight=!1,y.value.airlines="",y.value.origin="",y.value.originName="",y.value.destination="",y.value.destName="",y.value.seamlessOrDa="",y.value.departureDateTime="",G.value=!1},ve=H=>{const re=H.filter(se=>Ct.test(se));return y.value.transitTerminal=re.join("/"),H.filter(se=>se&&!Ct.test(se))},ie=H=>H.length>5?yt(te(H).format("YYYY-MM-DD"),!0):yt(fo(H),!0),ee=H=>H&&(y.value.lowestPrice=H),ue=H=>{const re=H[0].split("").sort();return H[0].length<=4&&re.every(se=>Q.includes(se))&&(re.includes("E")&&(y.value.timeSequence=!0),re.includes("O")&&(y.value.carrierFlight=!0),ee(re.includes("P")),H.shift()),H},$e=H=>{let re=Je(H);ne(!0),re=ue(re),re=ve(re),re.forEach(se=>{if(se==="D"){y.value.onlyDirectFlight=!0;return}if(kn.test(se)){se.startsWith("*")?(y.value.seamlessOrDa="DA",y.value.airlines=se.slice(1)):(y.value.seamlessOrDa="seamless",y.value.airlines=se);return}if(Fn.test(se)){y.value.origin=se.slice(0,3),y.value.destination=se.slice(3);return}if(Ln.test(se)){y.value.departureDate=ie(se);return}Ia.test(se)&&(y.value.flightNumber=se),Ea.test(se)&&(y.value.departureDateTime=se)})},Fe=()=>{y.value=qa(),y.value.carrierFlight=!1,y.value.lowestPrice=!1,y.value.timeSequence=!1,u.value=!0,B.value.resetFields()},Ee=async()=>{const H=await d();(H??[]).length<1||(z.value=(H??[]).map(re=>`${re.dept}-${re.arrivel}`))},Pe=H=>{E.simEtermIsOpen||H.key==="Enter"&&(g.isOpened?T===It:v.value===T)&&X()},Te=async H=>{const re=H.split("-");re.length<2||(i.value=H,y.value.origin=(re==null?void 0:re[0])??"",y.value.destination=(re==null?void 0:re[1])??"",y.value.destName="",y.value.originName="")};return jt.on(`${Be.QUERY_MARRIED_FLIGHT}${v.value}`,async H=>{y.value=H,await gt(),X()}),Ge(()=>y.value.onlyDirectFlight,()=>{y.value.onlyDirectFlight?(y.value.transitTerminal="",x.value=!0):x.value=!1}),Ge(()=>y.value.transitTerminal,()=>{y.value.transitTerminal!==""?(y.value.onlyDirectFlight=!1,c.value=!0):c.value=!1}),Ge(()=>y.value.flightNumber,()=>{y.value.flightNumber?(u.value=!1,B.value.clearValidate("origin"),B.value.clearValidate("destination")):u.value=!0}),Ge(()=>y.value.seamlessOrDa,async()=>{y.value.seamlessOrDa?J.value=!0:J.value=!1,await B.value.validateField("airlines")}),Ge([()=>N.value,()=>P.value],async()=>{var H,re;if(N.value&&(P.value??[]).length){const se=(re=(H=JSON.parse(P.value)[0])==null?void 0:H.replace(/\s+/g," ").slice(3))==null?void 0:re.split("/").filter(q=>q);await $e(se),await X(),C.setCommandsAvCheckSuccess(v.value,!1)}}),Ge(()=>a.historyQueryForm,async H=>{H&&!G.value&&(y.value=Je(H))},{immediate:!0,deep:!0}),Ge(()=>A.value,H=>{H&&(y.value.carrierFlight=(H==null?void 0:H.unshared)??!1,y.value.onlyDirectFlight=(H==null?void 0:H.nonstop)??!1)}),pt(async()=>{ae(),await k(),await Ee(),window.addEventListener("keydown",Pe,!0)}),on(()=>{window.addEventListener("keydown",Pe,!0)}),kt(()=>{jt.off(`${Be.QUERY_MARRIED_FLIGHT}${v.value}`),window.removeEventListener("keydown",Pe,!0)}),nn(()=>{window.removeEventListener("keydown",Pe,!0)}),{queryFormRef:B,FORM_RULES:M,queryForm:y,datePrefix:S,isAshingTransitTerminal:x,onlyDirectFlightAshing:c,agentAirportOriginRef:Y,agentAirportDestinationRef:h,revertFromTo:he,disabledDate:fe,seamlessOrDaClick:de,search:X,resetClick:Fe,chooseItems:z,roundTripSwitch:G,selectedItem:i,handleSelectItem:Te}},La=e=>($t("data-v-4947a5f8"),e=e(),xt(),e),Xs={class:"flex"},el={class:"flex mr-[10px] self-stretch"},tl=La(()=>n("div",{class:"airlines-item bkc-el-input__wrapper"},"*",-1)),al=[tl],ol={class:"inline-flex"},nl={class:"carrier-only-direct"},il={class:"h-full pt-[2px] flex flex-col items-start mr-2.5"},rl={class:"lowest-price-box flex items-start"},sl={class:"lowest-price-box flex items-start"},ll={class:"flex-col justify-start items-start gap-5 inline-flex ml-[10px]"},cl={class:"inline-flex items-center justify-center gap-1"},dl=La(()=>n("span",{class:"iconfont icon-time-circle2"},null,-1)),ul={class:"text-xs"},pl={key:0,class:"flex items-center"},gl={class:"text-brand-2 text-xs font-normal leading-tight"},fl={key:1,class:"flex items-center"},ml=La(()=>n("div",{class:"w-[19px]"},null,-1)),hl={class:"text-gray-2 text-xs font-normal leading-tight"},vl=Re({__name:"HeaderAvQuery",props:{flightSearchFlag:{},cityOrAirport:{},historyQueryForm:{},avQueryFromFastQuery:{type:Boolean}},emits:["searchClick","isPnrExtraction","getRoundTripFlag"],setup(e,{expose:a,emit:o}){const r=e,d=o,{queryFormRef:i,queryForm:l,FORM_RULES:A,revertFromTo:g,datePrefix:C,resetClick:v,roundTripSwitch:m,seamlessOrDaClick:T,search:E,onlyDirectFlightAshing:N,agentAirportOriginRef:P,agentAirportDestinationRef:f,chooseItems:F,selectedItem:B,handleSelectItem:Y}=Zs(d,r);return a({search:E}),(h,c)=>{const u=Vt,J=He,y=Tt,G=Xt,Q=aa,x=Nn,S=In,U=En,z=Bt;return p(),D("div",Xs,[_(z,{ref_key:"queryFormRef",ref:i,class:"header-av-query-form","label-position":"top",model:t(l),rules:t(A)},{default:$(()=>[_(u,{class:be(["agent-airport-item",h.avQueryFromFastQuery?"fast-query-airport-item":"airport-item"]),prop:"origin"},{default:$(()=>[_(Qt,{ref_key:"agentAirportOriginRef",ref:P,modelValue:t(l).origin,"onUpdate:modelValue":c[0]||(c[0]=V=>t(l).origin=V),name:t(l).originName,"onUpdate:name":c[1]||(c[1]=V=>t(l).originName=V),"is-agent-city":h.cityOrAirport,"prefix-title":h.$t("app.avSearch.depAirport")},null,8,["modelValue","name","is-agent-city","prefix-title"])]),_:1},8,["class"]),n("div",{class:"mt-[6px] ml-[2px] mr-[2px] w-[14px] h-[14px] cursor-pointer",onClick:c[2]||(c[2]=(...V)=>t(g)&&t(g)(...V))},[_(J,{class:"sort-text"},{default:$(()=>[_(t(Ca))]),_:1})]),_(u,{class:be(["agent-airport-item",h.avQueryFromFastQuery?"fast-query-airport-item":"airport-item"]),prop:"destination"},{default:$(()=>[_(Qt,{ref_key:"agentAirportDestinationRef",ref:f,modelValue:t(l).destination,"onUpdate:modelValue":c[3]||(c[3]=V=>t(l).destination=V),name:t(l).destName,"onUpdate:name":c[4]||(c[4]=V=>t(l).destName=V),"is-agent-city":h.cityOrAirport,"prefix-title":h.$t("app.avSearch.arrAirport")},null,8,["modelValue","name","is-agent-city","prefix-title"])]),_:1},8,["class"]),_(u,{class:be(["departure-date",t(m)?"round-trip-date":""]),prop:"departureDate"},{default:$(()=>[_(Fa,{modelValue:t(l).departureDate,"onUpdate:modelValue":c[5]||(c[5]=V=>t(l).departureDate=V),roundTripSwitch:t(m),"onUpdate:roundTripSwitch":c[6]||(c[6]=V=>Ze(m)?m.value=V:null),type:t(m)?"daterange":"date","show-switch":"",clearable:!1,"prefix-icon":t(C),placeholder:h.$t("app.avSearch.date")},null,8,["modelValue","roundTripSwitch","type","prefix-icon","placeholder"])]),_:1},8,["class"]),_(u,{prop:"airlines",class:"airlines-input-item"},{default:$(()=>[n("div",el,[n("div",{class:be(["airlines-item-box",t(l).seamlessOrDa==="DA"?"radio-selected":"radio-cancel"]),onClick:c[7]||(c[7]=V=>t(T)("DA"))},al,2),_(Ha,{modelValue:t(l).airlines,"onUpdate:modelValue":c[8]||(c[8]=V=>t(l).airlines=V),modelModifiers:{trim:!0},class:"airlines-input","tip-text":"CA",placeholder:h.$t("app.avSearch.airline")},null,8,["modelValue","placeholder"])])]),_:1}),_(u,{prop:"flightNumber"},{default:$(()=>[_(Ha,{modelValue:t(l).flightNumber,"onUpdate:modelValue":c[9]||(c[9]=V=>t(l).flightNumber=V),modelModifiers:{trim:!0},class:"flightNumber-input","tip-text":"CA1234",placeholder:h.$t("app.avSearch.flightNumber")},null,8,["modelValue","placeholder"])]),_:1}),_(u,{prop:"departureDateTime"},{default:$(()=>[_(y,{modelValue:t(l).departureDateTime,"onUpdate:modelValue":c[10]||(c[10]=V=>t(l).departureDateTime=V),class:"small-width",placeholder:h.$t("app.avSearch.departureTime"),onInput:c[11]||(c[11]=V=>t(l).departureDateTime=t(l).departureDateTime.toUpperCase().trim())},null,8,["modelValue","placeholder"])]),_:1}),_(u,{class:"transit-terminal input-focus-tip",prop:"transitTerminal"},{default:$(()=>[_(Mr,{modelValue:t(l).transitTerminal,"onUpdate:modelValue":c[12]||(c[12]=V=>t(l).transitTerminal=V),modelModifiers:{trim:!0},"is-agent-city":h.cityOrAirport},null,8,["modelValue","is-agent-city"])]),_:1}),_(u,null,{default:$(()=>[n("div",ol,[n("div",nl,[_(G,{modelValue:t(l).carrierFlight,"onUpdate:modelValue":c[13]||(c[13]=V=>t(l).carrierFlight=V)},{default:$(()=>[me(b(h.$t("app.avSearch.nonSharedFlights")),1)]),_:1},8,["modelValue"]),_(G,{modelValue:t(l).onlyDirectFlight,"onUpdate:modelValue":c[14]||(c[14]=V=>t(l).onlyDirectFlight=V),disabled:t(N)},{default:$(()=>[me(b(h.$t("app.avSearch.onlyFight")),1)]),_:1},8,["modelValue","disabled"])])])]),_:1}),_(u,null,{default:$(()=>[n("div",il,[n("div",rl,[_(G,{modelValue:t(l).lowestPrice,"onUpdate:modelValue":c[15]||(c[15]=V=>t(l).lowestPrice=V)},{default:$(()=>[me(b(h.$t("app.avSearch.lowestPrice")),1)]),_:1},8,["modelValue"])]),n("div",sl,[_(G,{modelValue:t(l).timeSequence,"onUpdate:modelValue":c[16]||(c[16]=V=>t(l).timeSequence=V)},{default:$(()=>[me(b(h.$t("app.avSearch.timeSequence")),1)]),_:1},8,["modelValue"])])])]),_:1}),_(u,null,{default:$(()=>[_(Q,{type:"primary",onClick:t(E)},{default:$(()=>[me(b(h.$t("app.avSearch.searchText")),1)]),_:1},8,["onClick"])]),_:1}),_(u,null,{default:$(()=>[_(Q,{class:"ml-[8px]",onClick:t(v)},{default:$(()=>[me(b(h.$t("app.fastQuery.headerQuery.reset")),1)]),_:1},8,["onClick"])]),_:1}),h.avQueryFromFastQuery?ge("",!0):(p(),Ae(u,{key:0},{default:$(()=>[n("div",ll,[n("div",cl,[_(U,{"popper-class":"av-history-popper",onCommand:t(Y)},{dropdown:$(()=>[_(S,null,{default:$(()=>[(p(!0),D(_e,null,ke(t(F),(V,K)=>(p(),Ae(x,{key:K,command:V},{default:$(()=>[t(B)===V?(p(),D("span",pl,[_(J,{class:"bg-inherit !text-brand-2 !text-[14px]"},{default:$(()=>[_(t(rn))]),_:1}),n("span",gl,b(V),1)])):(p(),D("span",fl,[ml,n("span",hl,b(V),1)]))]),_:2},1032,["command"]))),128))]),_:1})]),default:$(()=>[_(Q,{link:"",type:"primary",class:"service-book-btn"},{default:$(()=>[dl,n("span",ul,b(h.$t("app.fastQuery.headerQuery.historyQuery")),1)]),_:1})]),_:1},8,["onCommand"])])])]),_:1}))]),_:1},8,["model","rules"])])}}});const yl=je(vl,[["__scopeId","data-v-4947a5f8"]]),bl=(e,a)=>{const o=ia(),r=et([]),{activeTag:d}=St(),i=e.avQueryFromFastQuery?It:d,l=Ie(()=>{var f;return((f=o.flightByDate[i])==null?void 0:f.activeFlightIndex)??0}),A=Ie(()=>{const f=o.flightByDate[i].flightDataList;return r.value=v(f),f}),g=Ie(()=>e.roundTripFlag??""),C=(f,F)=>{let B=f;if(B.includes(F[1])){const Y=B.filter(h=>h!==F[1]);B=[F[1],...Y]}if(B.includes(F[0])){const Y=B.filter(h=>h!==F[0]);B=[F[0],...Y]}return B},v=f=>{var h,c;const F=[],B=g.value?(c=((h=g.value)==null?void 0:h.split("/"))??[])==null?void 0:c.filter(u=>u.trim()!==""):[];return(g.value?C([...f.keys()],B):[...f.keys()]).forEach(u=>{var y;const J={};J.departureAirportCode=u.substring(0,3),J.arrivalAirportCode=u.substring(3,6),J.departureDate=u.substring(6),J.firstQueryDate=(y=f.get(u))==null?void 0:y.firstQueryDate,F.push(J)}),F},m=f=>`${f.departureAirportCode??""}${f.arrivalAirportCode??""}${f.departureDate??""}`,T=va((f,F)=>{l.value!==F&&(o.setActiveFlightIndex(i,F),a("queryData",m(f)))},100,{trailing:!1}),E=f=>{const F=r.value[f],B=m(F),Y=[...A.value.keys()][l.value];o.delFlightDataList(i,B);const h=[...A.value.keys()];if(h.length>=1)if(l.value===f)o.setActiveFlightIndex(i,0),a("queryData",h[0]);else{const c=h.findIndex(u=>u===Y);o.setActiveFlightIndex(i,c)}else a("queryData","");a("closeAVHistory",`${F.departureAirportCode}${F.arrivalAirportCode}${(F==null?void 0:F.firstQueryDate)??""}`)},N=(f,F)=>{const B=J=>te(J).isValid()?te(J).format("YYYY-MM-DD"):"",Y=(J,y,G)=>{const Q=B(G);return`${J??""}${y??""}${Q}`},h=Y(f.departureAirportCode,f.arrivalAirportCode,F),c=r.value.findIndex(J=>h===Y(J.departureAirportCode,J.arrivalAirportCode,J.departureDate));if(c>-1){T(r.value[c],c);return}const u=m(f);a("callQueryApi",u,Object.assign({},f,{departureDate:B(F)}))},P=()=>{var F;const f=(F=o.flightByDate[i])==null?void 0:F.flightDataList;f&&(r.value=v(f),r.value.length&&a("queryData",m(r.value[l.value])))};return Ge(()=>e.searchCompleteKey,()=>{if(!e.searchCompleteKey)return;const f=e.searchCompleteKey.split("+")[0],F=[...A.value.keys()].indexOf(f);o.setActiveFlightIndex(i,F),F<0&&o.setActiveFlightIndex(i,r.value.length)},{immediate:!0}),pt(()=>{P()}),kt(()=>{r.value.length=0}),{flightInfos:r,activeFlightIndex:l,flightClick:T,changeDate:N,clickClose:E}},_l=(e,a)=>{const o=O(a.segFlight.departureDate??""),r=et({render(){return ft("em",{class:"iconfont icon-calendar"})}}),d=Ie(()=>a.activeFlightIndex===a.segIndex?a.segFlight.firstQueryDate===a.segFlight.departureDate?"active-blue-box":"active-red-box":""),i=()=>{e("changeDate",o.value),o.value=a.segFlight.departureDate},l=C=>te(C).isBefore(te(new Date),"date"),A=C=>te(C).isValid()?te(C).format("MM-DD"):"",g=()=>{e("clickClose",a.segIndex)};return Ge(()=>a.segFlight.departureDate,()=>{o.value=a.segFlight.departureDate}),{clickClose:g,datePrefix:r,changeDate:i,activeStyle:d,formatDate:A,departureDate:o,disabledDate:l}},Cl=e=>($t("data-v-78242d0d"),e=e(),xt(),e),Dl={class:"flex"},Al={class:"flex font-bold text-[14px] text-[#676767] code"},$l=Cl(()=>n("span",null,"-",-1)),xl={class:"flex items-center text-[12px] text-[#8C8C8C] font-normal"},Tl={class:"date-text mx-1.5 whitespace-nowrap"},Sl=Re({__name:"FlightInfo",props:{activeFlightIndex:{},segIndex:{},segFlight:{}},emits:["clickClose","changeDate"],setup(e,{emit:a}){const o=e,r=a,{clickClose:d,changeDate:i,activeStyle:l,departureDate:A,datePrefix:g,formatDate:C,disabledDate:v}=_l(r,o);return(m,T)=>{const E=xa,N=He;return p(),D("div",null,[n("div",{class:be(["flex justify-between items-center mr-[10px] pl-[10px] pr-[10px] h-[32px] border border-[#D9D9D9] rounded cursor-pointer",t(l)])},[n("div",Dl,[n("div",Al,[n("div",null,b(m.segFlight.departureAirportCode??""),1),$l,n("div",null,b(m.segFlight.arrivalAirportCode??""),1)]),n("div",xl,[n("div",Tl,b(t(C)(m.segFlight.departureDate)),1),n("div",{class:"mr-[2px] h-[15px] date-icon flex items-center justify-center",onClick:T[1]||(T[1]=Et(()=>{},["stop"]))},[_(E,{modelValue:t(A),"onUpdate:modelValue":T[0]||(T[0]=P=>Ze(A)?A.value=P:null),"prefix-icon":t(g),type:"date",clearable:!1,disabled:m.activeFlightIndex!==m.segIndex,editable:!1,"disabled-date":t(v),format:"YYYY-MM-DD",onChange:t(i)},null,8,["modelValue","prefix-icon","disabled","disabled-date","onChange"])])])]),n("div",{class:"flex items-center",onClick:T[2]||(T[2]=Et((...P)=>t(d)&&t(d)(...P),["stop"]))},[_(N,null,{default:$(()=>[_(t(no),{class:"text-[#676767]"})]),_:1})])],2)])}}});const kl=je(Sl,[["__scopeId","data-v-78242d0d"]]),Fl={key:0},Ll={class:"flex"},wl=Re({__name:"AvHistory",props:{roundTripFlag:{},searchCompleteKey:{},queryForm:{},avQueryFromFastQuery:{type:Boolean}},emits:["queryData","callQueryApi","closeAVHistory"],setup(e,{emit:a}){const o=e,r=a,{flightInfos:d,flightClick:i,activeFlightIndex:l,clickClose:A,changeDate:g}=bl(o,r);return(C,v)=>{const m=mo;return t(d).length>0?(p(),D("div",Fl,[_(m,null,{default:$(()=>[n("div",Ll,[(p(!0),D(_e,null,ke(t(d),(T,E)=>(p(),Ae(kl,{key:E,"seg-flight":T,"seg-index":E,"active-flight-index":t(l),onClick:N=>t(i)(T,E),onChangeDate:N=>t(g)(T,N),onClickClose:t(A)},null,8,["seg-flight","seg-index","active-flight-index","onClick","onChangeDate","onClickClose"]))),128))])]),_:1})])):ge("",!0)}}}),Do="confirm",ba=[{startTime:"00:00",endTime:"06:00",label:"time_0"},{startTime:"06:00",endTime:"12:00",label:"time_6"},{startTime:"12:00",endTime:"18:00",label:"time_12"},{startTime:"18:00",endTime:"24:00",label:"time_18"}],ja=(e,a)=>{e.sort((o,r)=>{let d=0,i=0;return o.segments.forEach(l=>{d+=Number(l.flightTime.replaceAll(":",""))}),r.segments.forEach(l=>{i+=Number(l.flightTime.replaceAll(":",""))}),a?d-i:i-d})},Ao=(e,a,o)=>{const r=te(new Date).format("YYYY-MM-DD"),d=`${r} ${e}`,i=`${r} ${a}`,l=`${r} ${o}`;return(te(d).isSame(te(l))||te(l).isAfter(te(d)))&&te(l).isBefore(te(i))},$o=(e,a,o)=>{var r;if((r=e.segments)!=null&&r.length){const d=e.segments[0].departureTime;return Ao(a,o,d)}return!1},xo=(e,a,o)=>{var r;if((r=e.segments)!=null&&r.length){const d=e.segments[e.segments.length-1].arrivalTime;return Ao(a,o,d)}return!1},Nl=(e,a)=>{let o=[];if(e.filterDepartureTime.length){const r=ba.filter(d=>e.filterDepartureTime.includes(d.label));o=a.filter(d=>r.some(i=>$o(d,i.startTime,i.endTime)))}else o=a;if(e.filterArrivalTime.length){const r=ba.filter(d=>e.filterArrivalTime.includes(d.label));o=o.filter(d=>r.some(i=>xo(d,i.startTime,i.endTime)))}return o},Il=(e,a)=>{const{t:o}=rt(),r=O(!1),d=et(ga),i=O(ga),l=O([]),A=O([]),g=O({filterAirlines:""}),C={filterAirlines:[{pattern:Oa,message:new Error(o("app.avHistoryFlightCondition.enterAirlineCode")),trigger:"change"}]},v=O([]),m=O(!1),T=O([]),E=O({label:o("app.avHistoryFlightCondition.airport"),screenPopoverData:[]}),N=O({label:o("app.avHistoryFlightCondition.timeDepartureAndArrival"),screenPopoverData:[]}),P=O(!1),f=0,F=1,B=o("app.avHistoryFlightCondition.departureAirport"),Y=o("app.avHistoryFlightCondition.arrivalAirport"),h=o("app.avHistoryFlightCondition.departureTime"),c=o("app.avHistoryFlightCondition.arrivelTime"),u=ha({sortType:-1,ascNum:-1});let J={};const y=()=>{var ee,ue;const ie=[];if(((ee=e.moreDeparture)==null?void 0:ee.length)>1){const $e=e.moreDeparture.map(Fe=>({label:Fe,value:Fe}));ie.push({label:B,codes:$e,checked:[]})}if(((ue=e.moreArrival)==null?void 0:ue.length)>1){const $e=e.moreArrival.map(Fe=>({label:Fe,value:Fe}));ie.push({label:Y,codes:$e,checked:[]})}return ie},G=ie=>{const ee=[],ue=[];return ba.forEach($e=>{const Fe=ie==null?void 0:ie.find(Pe=>$o(Pe,$e.startTime,$e.endTime)),Ee=ie==null?void 0:ie.find(Pe=>xo(Pe,$e.startTime,$e.endTime));Fe&&ee.push($e.label),Ee&&ue.push($e.label)}),{departureFlightTimeCodes:ee,arrivalFlightTimeCodes:ue}},Q=()=>{const ie=[],{departureFlightTimeCodes:ee,arrivalFlightTimeCodes:ue}=G(e.avSearchData),$e=Fe=>Fe.map(Ee=>({label:o(`app.avHistoryFlightCondition.${Ee}`),value:Ee}));return ee.length>1&&ie.push({label:h,codes:$e(ee),checked:[]}),ue.length>1&&ie.push({label:c,codes:$e(ue),checked:[]}),ie},x=()=>te().format("YYYY-MM-DD"),S=()=>{l.value=J.filterCondition},U=()=>{v.value=J.transferTimes},z=()=>{var ie,ee,ue,$e,Fe,Ee,Pe,Te;J={filterCondition:l.value,sortTypeNum:u.ascNum,filterDeparture:((ee=(ie=E.value.screenPopoverData)==null?void 0:ie.find(H=>H.label===B))==null?void 0:ee.checked)??[],filterArrival:(($e=(ue=E.value.screenPopoverData)==null?void 0:ue.find(H=>H.label===Y))==null?void 0:$e.checked)??[],transferTimes:v.value,filterDepartureTime:((Ee=(Fe=N.value.screenPopoverData)==null?void 0:Fe.find(H=>H.label===h))==null?void 0:Ee.checked)??[],filterArrivalTime:((Te=(Pe=N.value.screenPopoverData)==null?void 0:Pe.find(H=>H.label===c))==null?void 0:Te.checked)??[]},de(),a("sortFlight",J)},V=()=>{r.value=!1,m.value=!1,z()},K=()=>{l.value=[],A.value=[],g.value.filterAirlines="",V()},W=()=>{v.value=[],V()},I=ie=>{E.value=ie,V()},L=ie=>{N.value=ie,V()},w=ie=>ga.filter(ee=>ie.includes(ee.code)),M=()=>{const ie=[];(e.avSearchData??[]).forEach(ee=>{var ue,$e;(ue=ee.segments)!=null&&ue.length&&(($e=ee.flyType)==null||$e.forEach(Fe=>ie.includes(Fe)||ie.push(Fe)))}),T.value=[...ie].sort((ee,ue)=>ee===Nt.STOP?1:ue===Nt.STOP?-1:ee-ue).map(ee=>({label:ee.toString(),value:ee.toString()}))},k=ie=>ie===Nt.STOP.toString()?o("app.avHistoryFlightCondition.transferTimes_stop"):o(`app.avHistoryFlightCondition.transferTimes_${ie}`),ae=(ie,ee)=>{var ue;(ue=ie.flyType)==null||ue.forEach($e=>ee.includes($e)||ee.push($e))},he=()=>{const ie=[],ee=[],ue=[],$e=[];e.currentFlightList.map(Te=>{Te.segments.map(re=>{var se,q,pe;ie.includes((se=re.airlines)==null?void 0:se.airCode)||(q=re.airlines)!=null&&q.airCode&&ie.push((pe=re.airlines)==null?void 0:pe.airCode)});const H=Te.segments;if(H.length){ae(Te,ee);const re=H[0],se=H[H.length-1];ue.includes(re.departureAirportCode)||ue.push(re.departureAirportCode??""),$e.includes(se.arrivalAirportCode)||$e.push(se.arrivalAirportCode??"")}});const{departureFlightTimeCodes:Fe,arrivalFlightTimeCodes:Ee}=G(e.currentFlightList);i.value.forEach(Te=>Te.disabled=!l.value.includes(Te.code)&&!ie.includes(Te.code)),T.value.forEach(Te=>Te.disabled=!v.value.includes(Te.value)&&!ee.includes(Number(Te.value)));const Pe=(Te,H)=>{Te.codes.forEach(re=>re.disabled=!Te.checked.includes(re.value)&&!H.includes(re.value))};N.value.screenPopoverData.forEach(Te=>{Te.label===h&&Pe(Te,Fe),Te.label===c&&Pe(Te,Ee)}),E.value.screenPopoverData.forEach(Te=>{Te.label===B&&Pe(Te,ue),Te.label===Y&&Pe(Te,$e)})},fe=()=>{v.value=[],e.queryFormParams.onlyDirectFlight&&v.value.push("0"),M(),d.value=e.airlines&&e.airlines.length?w(e.airlines):[],i.value=e.airlines&&e.airlines.length?w(e.airlines):[],l.value=[],A.value=[],u.sortType=-1,u.ascNum=-1,E.value.screenPopoverData=y(),N.value.screenPopoverData=Q()},de=()=>{A.value=w(l.value)},oe=ie=>{const ee=e.queryResDateGroupBySessionId.get(`${e.queryFormParams.origin}${e.queryFormParams.destination}${e.queryFormParams.departureDate}`)||e.queryFormParams.departureDate;return!(ie==="pre"&&ee===x())},j=ie=>{a("changeDate",ie)},Z=ie=>{if(P.value=!0,u.sortType=ie,ie)switch(u.ascNum){case 2:u.ascNum=3;break;case 3:u.ascNum=-1;break;default:u.ascNum=2;break}else switch(u.ascNum){case 0:u.ascNum=1;break;case 1:u.ascNum=-1;break;default:u.ascNum=0;break}z()},X=()=>{var ie,ee,ue,$e,Fe,Ee,Pe,Te;J={filterCondition:l.value,sortTypeNum:u.ascNum,filterDeparture:((ee=(ie=E.value.screenPopoverData)==null?void 0:ie.find(H=>H.label===B))==null?void 0:ee.checked)??[],filterArrival:(($e=(ue=E.value.screenPopoverData)==null?void 0:ue.find(H=>H.label===Y))==null?void 0:$e.checked)??[],transferTimes:v.value,filterDepartureTime:((Ee=(Fe=N.value.screenPopoverData)==null?void 0:Fe.find(H=>H.label===h))==null?void 0:Ee.checked)??[],filterArrivalTime:((Te=(Pe=N.value.screenPopoverData)==null?void 0:Pe.find(H=>H.label===c))==null?void 0:Te.checked)??[]}},ne=ie=>{g.value.filterAirlines=ie==null?void 0:ie.toUpperCase(),Oa.test(ie)&&(i.value=d.value.filter(ee=>ee.code.includes(ie==null?void 0:ie.toUpperCase())),l.value=[])},ve=()=>{g.value.filterAirlines="",i.value=d.value};return pt(()=>{fe(),X()}),Ge(()=>e.airlines,()=>{fe(),X()}),Ge(()=>e.updateFlightListFlag,()=>{he()}),kt(()=>{d.value.length=0,i.value.length=0,l.value.length=0,A.value.length=0}),{isAllowChangeDate:oe,changeDate:j,popoverVisible:r,listSort:u,canChooseAirlines:d,checkedAirlines:l,filterForm:g,FORM_RULES:C,tipAirlinesObj:A,FLY_TIME_SORT:F,DEPARTURE_TIME_SORT:f,openPopover:S,sortClick:Z,checkAirlines:de,reset:K,screeningAirlines:V,isPermitSortByTime:P,confirmAirportDepartureAndArrival:I,confirmTimeDepartureAndArrival:L,airportDepartureAndArrival:E,timeDepartureAndArrival:N,popoverTransferTimes:m,openPopoverTransferTimes:U,resetPopoverTransferTimes:W,canChooseTransferTimes:T,checkedTransferTimes:v,changeFilterAirlines:ne,filterCanChooseAirlines:i,getTransferTimes:k,showFilterPopver:ve}},El={class:"text-[12px] text-brand-2 cursor-pointer ml-4 whitespace-nowrap"},Pl={class:"popover-airlines"},Ml={class:"airport-title text-[12px] text-gray-1 font-normal mb-[5px]"},Ql=["textContent"],Ol={class:"button-operator"},Rl=Re({__name:"ScreenPopover",props:{screenPopover:{},width:{}},emits:["confirm"],setup(e,{emit:a}){const o=e,r=a,d=O(!1),i=O(o.screenPopover),l=()=>{i.value=Je(o.screenPopover)},A=()=>{d.value=!1},g=()=>{A(),r("confirm",i.value)},C=()=>{i.value.screenPopoverData.forEach(v=>v.checked=[]),A(),r("confirm",i.value)};return(v,m)=>{const T=He,E=Xt,N=go,P=_t;return p(),Ae(P,{visible:d.value,"onUpdate:visible":m[0]||(m[0]=f=>d.value=f),trigger:"click",placement:"bottom-start","show-arrow":!1,width:v.width,"popper-class":"airport-popper"},{reference:$(()=>[n("div",El,[n("span",{onClick:l},b(i.value.label),1),d.value?(p(),Ae(T,{key:1,class:"ml-[4px]"},{default:$(()=>[_(t(Pt))]),_:1})):(p(),Ae(T,{key:0,class:"ml-[4px]",onClick:l},{default:$(()=>[_(t(qt))]),_:1}))])]),default:$(()=>[n("div",Pl,[(p(!0),D(_e,null,ke(i.value.screenPopoverData,(f,F)=>(p(),D("div",{key:f.label,class:be(["popover-airport py-[8px]",F?"border-t border-gray-6":""])},[n("div",Ml,b(f.label),1),_(N,{modelValue:f.checked,"onUpdate:modelValue":B=>f.checked=B},{default:$(()=>[(p(!0),D(_e,null,ke(f.codes,(B,Y)=>(p(),Ae(E,{key:Y,label:B.value,disabled:B.disabled},{default:$(()=>[n("span",{class:"code-label inline-block w-[22px] text-xs text-gray-2",textContent:b(B.label)},null,8,Ql)]),_:2},1032,["label","disabled"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])],2))),128)),n("div",Ol,[n("div",null,[n("span",{onClick:g},b(v.$t("app.avHistoryFlightCondition.sure")),1),n("span",{onClick:C},b(v.$t("app.avHistoryFlightCondition.reset")),1)])])])]),_:1},8,["visible","width"])}}});const Ga=je(Rl,[["__scopeId","data-v-283a3918"]]),Vl={class:"mr-[6px] h-[40px] flex items-end"},Bl={class:"flex justify-between items-center h-[32px] px-2.5 w-full border-[1px] border-[#EBF2FF] rounded"},Yl={class:"flex items-center h-[24px]"},Ul={class:"mr-[14px] text-[12px] font-normal text-[#595959]"},Hl={class:"flex mr-[14px]"},ql={class:"text-[12px] text-brand-2 cursor-pointer mr-4 whitespace-nowrap"},jl={class:"popover-airport"},Gl={class:"code-label inline-block text-xs font-normal text-gray-2"},Kl={class:"button-operator"},zl={class:"text-[12px] text-brand-2 cursor-pointer whitespace-nowrap"},Jl={key:0},Wl={class:"flex justify-center items-center flex-wrap w-[300px] text-[13px]"},Zl={class:"popover-airlines"},Xl={class:"w-[13px] h-[14px]","aria-hidden":"true"},ec=["xlink:href"],tc=["textContent"],ac=["textContent"],oc={class:"button-operator"},nc={class:"flex items-center h-[24px] whitespace-nowrap"},ic={class:"ml-[4px] flex flex-col sort-box"},rc={class:"ml-[4px] flex flex-col sort-box"},sc=Re({__name:"FlightCondition",props:{avSearchData:{},airlines:{},queryFormParams:{},queryResDateGroupBySessionId:{},moreDeparture:{},moreArrival:{},currentFlightList:{},updateFlightListFlag:{type:Boolean}},emits:["screeningDirect","sortFlight","changeDate","isAllowChangeDate"],setup(e,{emit:a}){const o=e,r=a,{popoverVisible:d,listSort:i,filterCanChooseAirlines:l,checkedAirlines:A,filterForm:g,FORM_RULES:C,tipAirlinesObj:v,FLY_TIME_SORT:m,DEPARTURE_TIME_SORT:T,isPermitSortByTime:E,isAllowChangeDate:N,changeDate:P,sortClick:f,openPopover:F,reset:B,screeningAirlines:Y,airportDepartureAndArrival:h,timeDepartureAndArrival:c,confirmAirportDepartureAndArrival:u,confirmTimeDepartureAndArrival:J,popoverTransferTimes:y,openPopoverTransferTimes:G,resetPopoverTransferTimes:Q,canChooseTransferTimes:x,checkedTransferTimes:S,getTransferTimes:U,changeFilterAirlines:z,showFilterPopver:V}=Il(o,r);return(K,W)=>{var he,fe,de;const I=go,L=bt,w=Tt,M=Vt,k=Bt,ae=Pn;return p(),D("div",Vl,[n("div",Bl,[n("div",Yl,[n("span",Ul,b(K.$t("app.avHistoryFlightCondition.total",{total:((he=K.avSearchData)==null?void 0:he.length)??0})),1),n("div",Hl,[n("span",{class:be(["mr-[10px] operate",{"disable-to-operate":!t(N)("pre")}]),"data-gid":"01010209",onClick:W[0]||(W[0]=oe=>t(P)("pre"))},b(K.$t("app.avHistoryFlightCondition.previousDay")),3),n("span",{class:be(["operate",{"disable-to-operate":!t(N)("next")}]),"data-gid":"01010210",onClick:W[1]||(W[1]=oe=>t(P)("next"))},b(K.$t("app.avHistoryFlightCondition.nextDay")),3)]),_(t(_t),{visible:t(y),"onUpdate:visible":W[6]||(W[6]=oe=>Ze(y)?y.value=oe:null),trigger:"click",placement:"bottom-start","show-arrow":!1,"popper-class":"airport-popper-av transfer-proper"},{reference:$(()=>[n("div",ql,[n("span",{onClick:W[2]||(W[2]=(...oe)=>t(G)&&t(G)(...oe))},b(K.$t("app.avHistoryFlightCondition.directFlight")),1),t(y)?(p(),Ae(t(He),{key:1,class:"ml-[4px]"},{default:$(()=>[_(t(Pt))]),_:1})):(p(),Ae(t(He),{key:0,class:"ml-[4px]",onClick:t(G)},{default:$(()=>[_(t(qt))]),_:1},8,["onClick"]))])]),default:$(()=>[n("div",jl,[_(I,{modelValue:t(S),"onUpdate:modelValue":W[3]||(W[3]=oe=>Ze(S)?S.value=oe:null),class:"flex flex-col"},{default:$(()=>[(p(!0),D(_e,null,ke(t(x),oe=>(p(),Ae(t(Xt),{key:oe.value,label:oe.value,disabled:oe.disabled},{default:$(()=>[n("span",Gl,b(t(U)(oe.value)),1)]),_:2},1032,["label","disabled"]))),128))]),_:1},8,["modelValue"]),n("div",Kl,[n("div",null,[n("span",{onClick:W[4]||(W[4]=(...oe)=>t(Y)&&t(Y)(...oe))},b(K.$t("app.avHistoryFlightCondition.sure")),1),n("span",{onClick:W[5]||(W[5]=(...oe)=>t(Q)&&t(Q)(...oe))},b(K.$t("app.avHistoryFlightCondition.reset")),1)])])])]),_:1},8,["visible"]),_(t(_t),{visible:t(d),"onUpdate:visible":W[13]||(W[13]=oe=>Ze(d)?d.value=oe:null),trigger:"click",placement:"bottom-start","show-arrow":!1,width:500,onShow:t(V)},{reference:$(()=>[n("div",zl,[t(v).length>0?(p(),D("span",Jl,[_(L,{effect:"dark","popper-class":"airlineTip",placement:"bottom"},{content:$(()=>[n("div",Wl,[(p(!0),D(_e,null,ke(t(v),oe=>(p(),D("span",{key:oe.code,class:"flex items-center w-[40%] p-[7px]"},[n("span",{class:be([["iconfont",oe.icon],"inline-block mr-[10px]"])},null,2),me(" "+b(oe.code)+"-"+b(oe.name),1)]))),128))])]),default:$(()=>[n("span",{onClick:W[7]||(W[7]=(...oe)=>t(F)&&t(F)(...oe))},b(K.$t("app.avHistoryFlightCondition.airlineScreening"))+" "+b(t(v).length??""),1)]),_:1})])):(p(),D("span",{key:1,onClick:W[8]||(W[8]=(...oe)=>t(F)&&t(F)(...oe))},b(K.$t("app.avHistoryFlightCondition.airlineScreening")),1)),t(d)?(p(),Ae(t(He),{key:3,class:"ml-[4px]"},{default:$(()=>[_(t(Pt))]),_:1})):(p(),Ae(t(He),{key:2,class:"ml-[4px]",onClick:t(F)},{default:$(()=>[_(t(qt))]),_:1},8,["onClick"]))])]),default:$(()=>[n("div",Zl,[n("div",null,[_(k,{ref:"filterFormRef",class:"header-av-query-form","label-position":"top",model:t(g),rules:t(C)},{default:$(()=>[_(M,{prop:"filterAirlines"},{default:$(()=>[_(w,{modelValue:t(g).filterAirlines,"onUpdate:modelValue":W[9]||(W[9]=oe=>t(g).filterAirlines=oe),placeholder:K.$t("app.avHistoryFlightCondition.supportInput"),onInput:t(z)},null,8,["modelValue","placeholder","onInput"])]),_:1})]),_:1},8,["model","rules"])]),_(I,{modelValue:t(A),"onUpdate:modelValue":W[10]||(W[10]=oe=>Ze(A)?A.value=oe:null)},{default:$(()=>[(p(!0),D(_e,null,ke(t(l),oe=>(p(),Ae(t(Xt),{key:oe.code,label:oe.code,disabled:oe.disabled},{default:$(()=>[(p(),D("svg",Xl,[n("use",{"xlink:href":oe.logo},null,8,ec)])),oe.name.length>4?(p(),Ae(L,{key:0,effect:"dark",content:oe.name,placement:"top"},{default:$(()=>[n("span",{class:"inline-block ml-[4px] w-[160px] truncate",textContent:b(`${oe.code}-${oe.name}`)},null,8,tc)]),_:2},1032,["content"])):(p(),D("span",{key:1,class:"inline-block ml-[4px] w-[160px]",textContent:b(`${oe.code}-${oe.name}`)},null,8,ac))]),_:2},1032,["label","disabled"]))),128))]),_:1},8,["modelValue"]),n("div",oc,[n("div",null,[n("span",{onClick:W[11]||(W[11]=(...oe)=>t(Y)&&t(Y)(...oe))},b(K.$t("app.avHistoryFlightCondition.sure")),1),n("span",{onClick:W[12]||(W[12]=(...oe)=>t(B)&&t(B)(...oe))},b(K.$t("app.avHistoryFlightCondition.reset")),1)])])])]),_:1},8,["visible","onShow"]),(fe=t(c).screenPopoverData)!=null&&fe.length?(p(),Ae(Ga,{key:0,"screen-popover":t(c),width:120,onConfirm:t(J)},null,8,["screen-popover","onConfirm"])):ge("",!0),(de=t(h).screenPopoverData)!=null&&de.length?(p(),Ae(Ga,{key:1,"screen-popover":t(h),width:80,onConfirm:t(u)},null,8,["screen-popover","onConfirm"])):ge("",!0)]),n("div",nc,[_(ae,{underline:!1,class:"mr-[14px]",onClick:W[14]||(W[14]=oe=>t(f)(t(T)))},{default:$(()=>[n("span",{class:be(["text-[12px]",t(i).sortType===0&&t(E)?"sort-active":""])},b(K.$t("app.avHistoryFlightCondition.departureTime")),3),n("div",ic,[_(t(He),null,{default:$(()=>[_(t(Pa),{class:be(["h-[6px] leading-[6px]",t(i).ascNum===0?"text-brand-2":"text-gray-5"])},null,8,["class"])]),_:1}),_(t(He),null,{default:$(()=>[_(t(Ma),{class:be(["h-[6px] leading-[6px]",t(i).ascNum===1?"text-brand-2":"text-gray-5"])},null,8,["class"])]),_:1})])]),_:1}),_(ae,{underline:!1,onClick:W[15]||(W[15]=oe=>t(f)(t(m)))},{default:$(()=>[n("span",{class:be(["text-[12px]",t(i).sortType===1&&t(E)?"sort-active":""])},b(K.$t("app.avHistoryFlightCondition.flyTime")),3),n("div",rc,[_(t(He),null,{default:$(()=>[_(t(Pa),{class:be(t(i).ascNum===2?"text-brand-2":"text-gray-5")},null,8,["class"])]),_:1}),_(t(He),null,{default:$(()=>[_(t(Ma),{class:be(t(i).ascNum===3?"text-brand-2":"text-gray-5")},null,8,["class"])]),_:1})])]),_:1})])])])}}});const lc=je(sc,[["__scopeId","data-v-768517f4"]]),cc=(e,a)=>{const{t:o}=rt(),r=O(!1),d=O(o("app.avSearch.quantityBooked")),i=et([]);return{bookedNumStr:d,bookedTypeList:i,queryBookedType:async()=>{const A={flightNo:e.flightNo,option:"INFT,AVIH,WCHC,UMNR",departureDate:e!=null&&e.departureDate?te(e.departureDate).format("YYYY-MM-DD"):"",departureTime:e.departureDate?te(e.departureDate).format("HHmm"):""};try{r.value=!1,a("changeLoading",!0);const{data:g}=await Ai(A,"01020108"),C=(g==null?void 0:g.value)??[];i.value=C.map(v=>{var m;return{name:v.option,counts:((m=v==null?void 0:v.quotaNumber)==null?void 0:m.split(" "))??["-"]}}),r.value=!0}catch{i.value=[]}finally{a("changeLoading",!1)}},popoverVisible:r}},dc={class:"text-gray-2 text-xs font-normal leading-tight"},uc={class:"text-center text-gray-3 text-xs font-normal leading-none"},pc={class:"text-brand-2 text-xs font-normal leading-tight"},gc=Re({__name:"BookedPopover",props:{flightNo:{},departureDate:{}},emits:["changeLoading"],setup(e,{emit:a}){const o=e,r=a,{bookedNumStr:d,bookedTypeList:i,popoverVisible:l,queryBookedType:A}=cc(o,r);return(g,C)=>{const v=He,m=_t;return p(),Ae(m,{visible:t(l),"onUpdate:visible":C[1]||(C[1]=T=>Ze(l)?l.value=T:null),trigger:"hover","show-arrow":!1,disabled:t(i).length<=0,"popper-class":"booked-proper"},{reference:$(()=>[n("div",{class:be(["w-[84px] h-5 px-0.5 rounded-sm justify-end items-center gap-1 inline-flex cursor-pointer",t(i).length>0?"bg-yellow-2":""]),onClick:C[0]||(C[0]=(...T)=>t(A)&&t(A)(...T))},[n("span",pc,b(t(d)),1),t(l)?(p(),Ae(v,{key:1,class:"icon-drop"},{default:$(()=>[_(t(Pt))]),_:1})):(p(),Ae(v,{key:0,class:"icon-drop"},{default:$(()=>[_(t(qt))]),_:1}))],2)]),default:$(()=>[(p(!0),D(_e,null,ke(t(i),(T,E)=>(p(),D("div",{key:E,class:"self-stretch px-1.5 py-1 justify-start items-center gap-1.5 inline-flex"},[n("span",dc,b(g.$t(`app.avSearch.${T.name}`)),1),(p(!0),D(_e,null,ke(T.counts,(N,P)=>(p(),D("span",{key:P,class:"h-4 px-1 bg-gray-7 rounded-sm justify-center items-center inline-flex"},[n("span",uc,b(N),1)]))),128))]))),128))]),_:1},8,["visible","disabled"])}}});const fc=je(gc,[["__scopeId","data-v-8a886d53"]]),To=Re({__name:"CopyButton",props:{segInfo:{},stopData:{}},setup(e){const a=e,{copy:o,isSupported:r}=sn({legacy:!0}),{t:d}=rt(),i=C=>C?Number(C):0,l=C=>C?` ${C.replace(":","")}`:"",A=(C=[])=>{let v=`
`;return C.forEach((m,T)=>{let E="";const N=`${d("app.fightSell.stopPoint")}${T+1}`,P=te(m.departureDate).isValid()?` ${te(m.departureDate).format("MM-DD")}`:"",f=` ${m.arrivalAirportCN}`,F=te(m.arrivalTime,"HH:mm").isValid()?` ${te(m.arrivalTime,"HH:mm").format("HHmm")}`:"",B=te(m.departureTime,"HH:mm").isValid()?` ${te(m.departureTime,"HH:mm").format("HHmm")}`:"",Y=` ${d("app.avSearch.stop")}${zt((m==null?void 0:m.groundTime)??"")}`;E=`${N}${P}${f}${F}${B}${Y}${T===C.length-1?"":`
`}`,v+=E}),v},g=()=>{if(!r||!a.segInfo)return;const{airlines:C}=a.segInfo,v=`${C.airCN}${C.airCode}${C.flightNo}`,m=C.isShared?` ${d("app.querySearch.carrierCompany")}${C.isShared}`:"",T=te(a.segInfo.departureDate).isValid()?` ${te(a.segInfo.departureDate).format("MM-DD")}`:"",E=` ${a.segInfo.departureAirportCN}${a.segInfo.departureTerminal??""}`,N=`${a.segInfo.arrivalAirportCN}${a.segInfo.arrivalTerminal??""}`,P=l(a.segInfo.departureTime),f=l(a.segInfo.arrivalTime),F=i(a.segInfo.arrivalArrdays),B=i(a.segInfo.deptArrdays),Y=A(a.stopData);let h="";const c=F-B;c&&(h=c>0?`+${c}`:`-${c}`);const u=`${v}${m}${T}${E}—${N}${P}${f}${h}${Y}`;o(u),Da({message:d("app.batchRefund.copySuccess"),type:"success"})};return(C,v)=>(p(),D("div",{class:"copy-btn bg-gray-0 rounded border border-brand-2 text-brand-2 p-[4px] cursor-pointer hover:bg-brand-4",onClick:g},b(C.$t("app.original.copy")),1))}}),mc=e=>($t("data-v-82130f71"),e=e(),xt(),e),hc={class:"min-w-[343px] bg-gray-3 text-gray-0 p-2"},vc={class:"flex items-center justify-between"},yc={class:"mb-2 text-sm font-bold"},bc={class:"flex items-center my-2 airlines-info"},_c={class:"flex items-center px-2 py-0"},Cc={class:"!w-6 !h-6 mr-1 text-xl icon svg-icon airline-icon","aria-hidden":"true"},Dc=["xlink:href"],Ac={class:"h-6 justify-start items-center gap-1 inline-flex"},$c={class:"text-center text-gray-0 text-xs font-normal leading-tight"},xc={class:"justify-start items-center gap-0.5 flex"},Tc={class:"text-center text-gray-0 text-xs font-normal leading-tight"},Sc={key:0,class:"px-1 h-5 bg-brand-3 rounded-sm justify-center items-center flex"},kc={class:"text-center text-brand-1 text-xs font-normal leading-tight"},Fc=mc(()=>n("div",{class:"split-label w-[1px] h-[14px] bg-gray-6"},null,-1)),Lc={class:"flex items-center px-2 py-0 mx-2 my-0 h-5 rounded-sm bg-brand-3 actual-carrier-box whitespace-nowrap"},wc={class:"mr-2 text-gray-2"},Nc={class:"text-brand-2"},Ic={key:1},Ec={class:"mb-2 mr-2"},Pc={class:"mb-2 mr-2"},Mc=Re({__name:"FlightInformationPopover",props:{segInfo:{},airportsDbData:{}},setup(e){const a=e,o=O(ta()),r=Ie(()=>a.airportsDbData),d=O(""),i=O(""),l=(v,m,T,E)=>{const N=a.segInfo;N&&(d.value=`${v} ${T} ${N.departureAirportCode??"--"} ${N.departureAirportCN??"--"} ${N.departureTerminal??"--"} `,i.value=`${m} ${E} ${N.arrivalAirportCode??"--"} ${N.arrivalAirportCN??"--"} ${N.arrivalTerminal??"--"} `)},A=()=>{var N,P,f,F;let v="--",m="--";const T=((N=a.segInfo)==null?void 0:N.departureTime)||"--",E=((P=a.segInfo)==null?void 0:P.arrivalTime)||"--";a.segInfo&&(v=(f=a.segInfo)!=null&&f.departureDate?te(a.segInfo.departureDate).format("MM-DD"):"--",m=(F=a.segInfo)!=null&&F.arrivalDate?te(a.segInfo.arrivalDate).format("MM-DD"):"--"),l(v,m,T,E)},g=v=>{var m,T,E,N,P,f;return o.value==="en"?(E=(T=(m=r.value??[])==null?void 0:m.filter(F=>F.code===v))==null?void 0:T[0])==null?void 0:E.enName:(f=(P=(N=r.value??[])==null?void 0:N.filter(F=>F.code===v))==null?void 0:P[0])==null?void 0:f.cnName},C=v=>zt(v);return pt(()=>{A()}),(v,m)=>{const T=_t;return p(),Ae(T,{width:"auto",persistent:!1,class:"flight-common-info-detail","popper-class":"flight-common-info-detail-popover",placement:"top"},{reference:$(()=>[lt(v.$slots,"default",{},void 0,!0)]),default:$(()=>{var E,N,P,f,F,B,Y,h,c,u,J,y,G,Q,x,S,U,z,V;return[n("div",hc,[n("div",vc,[n("div",yc,b(`${g(((E=v.segInfo)==null?void 0:E.departureAirportCode)??"")}-${g(((N=v.segInfo)==null?void 0:N.arrivalAirportCode)??"")}`),1),_(To,{"seg-info":v.segInfo},null,8,["seg-info"])]),n("div",bc,[n("div",_c,[n("span",null,[(p(),D("svg",Cc,[n("use",{"xlink:href":"#icon-"+((F=(f=(P=v.segInfo)==null?void 0:P.airlines)==null?void 0:f.airCode)==null?void 0:F.toLowerCase())+"-c"},null,8,Dc)]))]),n("div",Ac,[n("div",$c,b(((Y=(B=v.segInfo)==null?void 0:B.airlines)==null?void 0:Y.airCN)??""),1),n("div",xc,[n("div",Tc,b(`${((c=(h=v.segInfo)==null?void 0:h.airlines)==null?void 0:c.airCode)??""}${((J=(u=v.segInfo)==null?void 0:u.airlines)==null?void 0:J.flightNo)??""}`),1),(G=(y=v.segInfo)==null?void 0:y.airlines)!=null&&G.isShared?(p(),D("div",Sc,[n("div",kc,b(v.$t("app.querySearch.shareFlight")),1)])):ge("",!0)])])]),(x=(Q=v.segInfo)==null?void 0:Q.airlines)!=null&&x.isShared?(p(),D(_e,{key:0},[Fc,n("div",Lc,[n("span",wc,b(v.$t("app.querySearch.carrierCompany")),1),n("span",Nc,b(`${((U=(S=v.segInfo)==null?void 0:S.airlines)==null?void 0:U.isShared)??""}`),1)])],64)):ge("",!0),(z=v.segInfo)!=null&&z.flightTime?(p(),D("div",Ic,[n("span",null,b(v.$t("app.querySearch.flightDuration")),1),n("span",null,b(C(((V=v.segInfo)==null?void 0:V.flightTime)??"")??""),1)])):ge("",!0)]),n("div",null,[n("div",Ec,b(d.value),1),n("div",Pc,b(i.value),1)])])]}),_:3})}}});const Ka=je(Mc,[["__scopeId","data-v-82130f71"]]),Qc={class:"flex items-center"},Oc={class:"flex items-center justify-center w-5 h-5"},Rc={class:"grow shrink basis-0 self-stretch pr-[1.43px] justify-center items-center inline-flex"},Vc={class:"icon svg-icon airline-icon text-[20px]","aria-hidden":"true"},Bc=["xlink:href"],Yc={class:"h-[22px] justify-start items-center gap-0.5 inline-flex"},Uc={class:"h-[22px] text-sm font-bold leading-[22px] text-gray-1"},Hc={key:0,class:"min-w-[28px] max-w-[37px] h-4 px-0.5 bg-brand-3 rounded-sm justify-center items-center flex"},qc={class:"text-center text-brand-1 text-xs font-normal leading-none"},jc={key:0,class:"h-4 px-0.5 bg-brand-3 rounded-sm justify-center items-center inline-flex relative top-[-6px]"},Gc={class:"text-brand-1 text-xs font-normal leading-none"},za=Re({__name:"AirlineInfo",props:{segInfo:{}},setup(e){return(a,o)=>{var r,d,i,l,A,g,C,v,m,T,E,N,P,f,F;return p(),D("div",Qc,[n("div",{class:be(["w-[114px] gap-0.5 justify-start inline-flex relative",(d=(r=a.segInfo)==null?void 0:r.airlines)!=null&&d.isShared?"h-[38px] items-start":"h-[22px] items-center"])},[n("div",Oc,[n("div",Rc,[(p(),D("svg",Vc,[n("use",{"xlink:href":"#icon-"+((i=a.segInfo)==null?void 0:i.airlines.airCode.toLowerCase())+"-c"},null,8,Bc)]))])]),n("div",{class:be([(A=(l=a.segInfo)==null?void 0:l.airlines)!=null&&A.isShared?"h-[38px]":"h-[22px]"])},[n("div",Yc,[n("div",Uc,b(((C=(g=a.segInfo)==null?void 0:g.airlines)==null?void 0:C.airCode)??"")+b(((m=(v=a.segInfo)==null?void 0:v.airlines)==null?void 0:m.flightNo)??""),1),((E=(T=a.segInfo)==null?void 0:T.airlines)==null?void 0:E.isShared)??""?(p(),D("div",Hc,[n("div",qc,b(a.$t("app.querySearch.shareFlight")),1)])):ge("",!0)]),(P=(N=a.segInfo)==null?void 0:N.airlines)!=null&&P.isShared?(p(),D("div",jc,[n("div",Gc,b(`${((F=(f=a.segInfo)==null?void 0:f.airlines)==null?void 0:F.isShared)??""}`),1)])):ge("",!0)],2)],2)])}}}),Kc={sellInternationalCreatePNR:"sell-international-createPNR",sellDomesticCreatePNR:"sell-domestic-createPNR",zhPassengerInfoQuery:"zh-passenger-info-query",sellInternationalMileageCreatePNR:"sell-mileage-international-createPNR",sellAgentAvUniversalStudios:"sell-agent-av-universal-studios",sellAgentCreatePNRSaveOrderToAIG:"sell-agent-create-pnr-save-order-to-aig"},zc="/sgui/assets/service-food-2cad3905.svg",Jc="/sgui/assets/service-wifi-b5879185.svg",Wc=(e,a)=>{const o=O(!1),{t:r}=rt(),d=O(),i=et({}),l=et({}),A=et([]),g=O([]),C=et({render(){return ft("em",{class:"iconfont icon-calendar"})}}),v=Q=>zt(Q),{personalizationRules:m}=Mn(Kc.sellAgentAvUniversalStudios),T=Q=>Q.length===0?"":te(Q).format("MM-DD"),E={FOOD:zc,NETWORK:Jc},N=Q=>{const x=$i();if(Qn(A.value))return x.isAfter(Q);const S=A.value[0].getTime(),U=9*24*3600*1e3;return Q.getTime()>S+U||Q.getTime()<S},P=()=>{const Q=te(g.value.length?g.value[0]:"").format("YYYY-MM-DD"),x=te(g.value.length?g.value[1]:"").format("YYYY-MM-DD");a("multiDayQuery",e.segIndex,Q,x)},f=()=>{a("retractMultiDayQuery",e.segIndex)},F=(Q,x,S,U,z)=>({date:T(Q)??"",time:x??"",code:S??"",airportCn:U??"",terminal:z??""}),B=()=>{if(!e.segInfo)return;const{departureDate:Q,departureTime:x,departureAirportCode:S,departureAirportCN:U,departureTerminal:z,arrivalDate:V,arrivalTime:K,arrivalAirportCode:W,arrivalAirportCN:I,arrivalTerminal:L}=e.segInfo;i.value=F(Q,x,S,U,z),l.value=F(V,K,W,I,L)},Y=async()=>{var x,S,U,z;const Q={airCode:e.segInfo.airlines.airCode,departureDate:te(e.segInfo.departureDate).format("YYYY-MM-DD"),flightNo:`${e.segInfo.airlines.airCode}${e.segInfo.airlines.flightNo}`};if((d.value??[]).length===0)try{o.value=!0;const{data:V}=await yo(Q,"01010201"),K=((U=(S=((x=V.value)==null?void 0:x.flightInfoList)??[])==null?void 0:S[0])==null?void 0:U.segments)??[],W=[];for(let I=0;I<K.length-1;I++){const{arrivalDate:L,arrivalTime:w,arrivalAirportCode:M,arrivalAirportCN:k,arrivalTerminal:ae,airlines:he,groundTime:fe}=K[I],de={arrivalDate:L,arrivalTime:w,arrivalAirportCode:M,arrivalAirportCN:k,arrivalTerminal:ae,airlines:he,groundTime:fe};if(I+1!==K.length){const{departureDate:oe,departureTime:j}=K[I+1]??[];de.departureDate=oe,de.departureTime=j}de.airlines=(z=K[I])==null?void 0:z.airlines,W.push(de)}d.value=W}finally{o.value=!1}},h=Q=>Q==="MEAL"?"FOOD":Q==="ADHOC"?"NETWORK":"",c=Q=>{A.value=Q},u=Q=>{o.value=Q},J=Q=>{var x,S;return(x=Q==null?void 0:Q.airlines)!=null&&x.isShared?(S=Q==null?void 0:Q.airlines)==null?void 0:S.isShared:`${Q==null?void 0:Q.airlines.airCode}${Q==null?void 0:Q.airlines.flightNo}`},y=Q=>Q&&Q!=="--"?`-${Q}`:"",G=Q=>{switch(Q){case"B":return`${Q}${r("app.avSearch.breakfast")}`;case"L":case"D":return`${Q}${r("app.avSearch.prepareDinner")}`;case"S":return`${Q}${r("app.avSearch.snacks")}`;case"M":return`${Q}${r("app.avSearch.prepareMeals")}`;default:return`${Q}${r("app.avSearch.meal")}`}};return pt(()=>{B()}),kt(()=>{(d.value??[]).length=0,A.value.length=0,g.value.length=0}),{getStopInfo:Y,flightTimeFormat:v,stopData:d,departureTitle:i,arrivalTitle:l,combineFlight:F,formatMMDD:T,personalizationRules:m,deptReturnDate:g,disabledDate:N,queryMultiDay:P,changeMultiDate:f,serviceIcon:E,checkServiceType:h,calendarChange:c,loading:o,datePrefix:C,buildTerminalText:y,changeLoading:u,getFlightNo:J,getMealDescription:G}},Zc=e=>{const a=O(),o=O(""),r=O(""),d=O(ta()),i=Ie(()=>e.airportsDbData),l=v=>v.length===0?"":te(v).format("MM-DD");return{stopData:a,departureTitle:o,arrivalTitle:r,combineFlight:(v,m,T,E,N)=>`${l(v)??""} ${m??""} ${T??""} ${E??""} ${N??""}`,formatMMDD:l,getAirCity:v=>{var m,T,E,N,P,f;return d.value==="en"?(E=(T=(m=i.value??[])==null?void 0:m.filter(F=>F.code===v))==null?void 0:T[0])==null?void 0:E.enName:(f=(P=(N=i.value??[])==null?void 0:N.filter(F=>F.code===v))==null?void 0:P[0])==null?void 0:f.cnName},flightTimeFormat:v=>zt(v)}},Xc=Zc,ed={class:"flex flex-col p-2 text-xs bg-gray-3 text-gray-0 min-w-[343px]"},td={class:"flex items-center justify-between"},ad={class:"text-sm font-bold"},od={class:"mx-0 mt-2 flex items-center h-6"},nd={class:"flex items-center stop-content-company-data"},id={class:"!w-6 !h-6 inline-block air-icon icon svg-icon airline-icon","aria-hidden":"true"},rd=["xlink:href"],sd={class:"h-6 justify-start items-center gap-1 inline-flex"},ld={class:"text-center text-gray-0 text-xs font-normal leading-tight"},cd={class:"justify-start items-center gap-0.5 flex"},dd={class:"text-center text-gray-0 text-xs font-normal leading-tight"},ud={key:0,class:"px-1 h-5 bg-brand-3 rounded-sm justify-center items-center flex"},pd={class:"text-center text-brand-1 text-xs font-normal leading-tight"},gd=n("span",{class:"w-[1px] h-[14px] split-label mx-[5px] bg-gray-6"},null,-1),fd={class:"px-2 py-0.5 my-0 rounded-sm bg-brand-3 actual-carrier-box whitespace-nowrap inline-block"},md={class:"mr-2 text-gray-2"},hd={class:"text-brand-2"},vd={key:1,class:"ml-2"},yd={class:"mt-[10px] mb-2"},bd={class:"justify-start items-center gap-5 inline-flex"},_d=n("div",{class:"text-center text-gray-0 text-xs font-normal leading-tight w-11 flex-grow-0 flex-shrink-0"},null,-1),Cd={class:"flex-col justify-start items-start gap-2 inline-flex"},Dd={class:"justify-start items-center gap-2.5 inline-flex"},Ad={class:"w-[92px] flex-col justify-center items-start inline-flex flex-shrink-0"},$d={class:"text-gray-0 text-xs font-normal leading-5"},xd={class:"text-gray-0 text-xs font-normal leading-tight"},Td={class:"h-10 justify-start items-center gap-5 inline-flex"},Sd={class:"text-center text-gray-0 text-xs font-normal leading-tight w-11 flex-grow-0 flex-shrink-0"},kd={class:"flex-col justify-start items-start gap-2 inline-flex"},Fd={class:"justify-start items-center gap-2.5 inline-flex"},Ld={class:"w-[92px] flex-col justify-center items-start inline-flex flex-shrink-0"},wd={class:"text-gray-0 text-xs font-normal leading-5"},Nd={class:"text-gray-0 text-xs font-normal leading-5"},Id={class:"text-gray-0 text-xs font-normal leading-tight"},Ed={class:"text-gray-0 text-xs font-normal leading-tight"},Pd={class:"mt-[8px]"},Md={class:"justify-start items-center gap-5 inline-flex"},Qd=n("div",{class:"text-center text-gray-0 text-xs font-normal leading-tight w-11 flex-grow-0 flex-shrink-0"},null,-1),Od={class:"flex-col justify-start items-start gap-2 inline-flex"},Rd={class:"justify-start items-center gap-2.5 inline-flex"},Vd={class:"w-[92px] flex-col justify-center items-start inline-flex flex-shrink-0"},Bd={class:"text-gray-0 text-xs font-normal leading-5"},Yd={class:"text-gray-0 text-xs font-normal leading-tight"},Ud=Re({__name:"AirStopMessagebox",props:{segInfo:{},stopData:{},departureTitle:{},arrivalTitle:{},airportsDbData:{}},setup(e){const a=e,{formatMMDD:o,getAirCity:r,flightTimeFormat:d}=Xc(a);return(i,l)=>{var g;const A=_t;return Number(((g=i.segInfo)==null?void 0:g.stopCity)??0)>0?(p(),Ae(A,{key:0,width:"auto",trigger:"click",teleported:!0,"popper-class":"flight-item-agent-stop-over-popover"},{reference:$(()=>[lt(i.$slots,"default")]),default:$(()=>{var C,v,m,T,E,N,P,f,F,B,Y,h,c,u,J,y,G;return[n("div",ed,[n("div",td,[n("div",ad,b(`${t(r)(((C=i.segInfo)==null?void 0:C.departureAirportCode)??"")}-${t(r)(((v=i.segInfo)==null?void 0:v.arrivalAirportCode)??"")}`),1),_(To,{"seg-info":i.segInfo,"stop-data":i.stopData},null,8,["seg-info","stop-data"])]),n("div",od,[n("div",nd,[n("span",null,[(p(),D("svg",id,[n("use",{"xlink:href":"#icon-"+((m=i.segInfo)==null?void 0:m.airlines.airCode.toLowerCase())+"-c"},null,8,rd)]))]),n("div",sd,[n("div",ld,b(((E=(T=i.segInfo)==null?void 0:T.airlines)==null?void 0:E.airCN)??""),1),n("div",cd,[n("div",dd,b(`${((P=(N=i.segInfo)==null?void 0:N.airlines)==null?void 0:P.airCode)??""}${((F=(f=i.segInfo)==null?void 0:f.airlines)==null?void 0:F.flightNo)??""}`),1),(Y=(B=i.segInfo)==null?void 0:B.airlines)!=null&&Y.isShared?(p(),D("div",ud,[n("div",pd,b(i.$t("app.querySearch.shareFlight")),1)])):ge("",!0)])])]),(c=(h=i.segInfo)==null?void 0:h.airlines)!=null&&c.isShared?(p(),D(_e,{key:0},[gd,n("div",fd,[n("span",md,b(i.$t("app.querySearch.carrierCompany")),1),n("span",hd,b(`${((J=(u=i.segInfo)==null?void 0:u.airlines)==null?void 0:J.isShared)??""}`),1)])],64)):ge("",!0),(y=i.segInfo)!=null&&y.flightTime?(p(),D("div",vd,[n("span",null,b(i.$t("app.querySearch.flightDuration")),1),n("span",null,b(t(d)(((G=i.segInfo)==null?void 0:G.flightTime)??"")??""),1)])):ge("",!0)]),n("div",null,[n("div",yd,[n("div",bd,[_d,n("div",Cd,[n("div",Dd,[n("div",Ad,[n("div",$d,b(`${i.departureTitle.date} ${i.departureTitle.time}`),1)]),n("div",xd,b(`${i.departureTitle.code} ${i.departureTitle.airportCn} ${i.departureTitle.terminal}`),1)])])])]),(p(!0),D(_e,null,ke(i.stopData,(Q,x)=>(p(),D("div",{key:Q.airlines.flightNo},[n("div",Td,[n("div",Sd,b(i.$t("app.fightSell.stopPoint"))+b(x+1),1),n("div",kd,[n("div",Fd,[n("div",Ld,[n("div",wd,b(`${t(o)(Q.arrivalDate??"")} ${Q.arrivalTime??""}`),1),n("div",Nd,b(`${t(o)(Q.departureDate??"")} ${Q.departureTime??""}`),1)]),n("div",Id,b(`${Q.arrivalAirportCode??""} ${Q.arrivalAirportCN??""} ${Q.arrivalTerminal??""}`),1),n("div",Ed,b(`${i.$t("app.avSearch.stop")}${t(zt)((Q==null?void 0:Q.groundTime)??"")}`),1)])])])]))),128)),n("div",Pd,[n("div",Md,[Qd,n("div",Od,[n("div",Rd,[n("div",Vd,[n("div",Bd,b(`${i.arrivalTitle.date} ${i.arrivalTitle.time}`),1)]),n("div",Yd,b(`${i.arrivalTitle.code} ${i.arrivalTitle.airportCn} ${i.arrivalTitle.terminal}`),1)])])])])])])]}),_:3})):ge("",!0)}}});const Ut=e=>($t("data-v-8f41af3a"),e=e(),xt(),e),Hd={key:0,class:"w-[150px] h-[18px] pl-[34px] justify-start items-center gap-1 inline-flex"},qd=Ut(()=>n("div",{class:"px-1.5 py-[3px] bg-yellow-3 rounded justify-start items-start gap-2.5 flex"},[n("div",{class:"text-xs font-normal leading-3 text-yellow-1"},"TCHB")],-1)),jd={class:"text-xs font-normal leading-3 text-yellow-1"},Gd={class:"flex items-center justify-center w-full flight-item text-gray-1"},Kd={key:0,class:"w-[24px] h-[24px] mr-2.5 px-2 py-0.5 bg-gray-7 rounded justify-center items-center inline-flex"},zd={class:"text-xs font-bold leading-tight text-center text-gray-2"},Jd={key:1,class:"w-[24px] h-[24px] mr-2.5 px-2 py-0.5 inline-flex"},Wd={class:"flex-grow-0 flex-shrink-0 header-left-box basis-30 md-hidden"},Zd=Ut(()=>n("div",{class:"border-r border-solid border-gray-7 min-h-[42px] mx-[16px] md-hidden"},null,-1)),Xd={class:"flex items-center header-right"},eu={class:"flight-base-info"},tu={class:"md-box justify-between w-[170px]"},au={class:"flex text-xs"},ou={class:"min-w-[20px] min-h-box-20"},nu={class:"ml-[5px] min-w-[20px] min-h-box-20"},iu={class:"flex items-end justify-center"},ru={class:"date-box w-12"},su={class:"flex flex-col justify-around"},lu={class:"relative text-sm font-bold min-h-box-22"},cu={class:"absolute top-0 text-xs font-normal stopping text-red-1 right-[-8px]"},du={class:"text-xs font-normal min-h-box-20 whitespace-nowrap"},uu=Ut(()=>n("div",{class:"space-divider"}," ",-1)),pu={class:"stop-info-box"},gu={class:"stop-divider"},fu={class:"flex h-[20px] leading-[20px] text-gray-4 text-xs font-normal"},mu=Ut(()=>n("div",{class:"space-divider"}," ",-1)),hu={class:"w-12"},vu={class:"flex flex-col justify-around"},yu={class:"relative text-sm font-bold min-h-box-22 text-end"},bu={class:"stopping text-xs font-normal text-red-1 absolute top-0 right-[-16px]"},_u={class:"text-xs font-normal text-right min-h-box-20 whitespace-nowrap"},Cu={class:"md-item-right flex items-center"},Du={class:"flex"},Au={class:"flex text-xs text-right flex-col justify-around ml-[16px] md-hidden"},$u={class:"min-w-[20px] min-h-box-20"},xu={class:"min-w-[20px] min-h-box-20"},Tu=Ut(()=>n("div",{class:"border-r border-solid border-gray-200 h-[42px] mx-[16px] md-hidden"},null,-1)),Su={class:"header-right-asr text-xs min-w-[36px] md-header-right-asr"},ku={key:0,class:"min-h-box-20 md:mr-2.5"},Fu={key:0,class:"whitespace-nowrap"},Lu={key:1,class:"whitespace-nowrap"},wu={class:"min-h-box-20"},Nu={class:"header-right-wifi-box"},Iu={key:0,class:"service-box"},Eu={key:0,class:"icon","aria-hidden":"true"},Pu=Ut(()=>n("use",{"xlink:href":"#icon-wifi"},null,-1)),Mu=[Pu],Qu={key:0,class:"header-right-personal-box"},Ou={class:"item-right"},Ru={class:"text-xs"},Vu={key:1,class:"flex justify-end multi-day-box relative cursor-pointer"},Bu={class:"text-xs text-brand-2 leading-5 absolute right-5 mr-0.5"},Yu={class:"flex-grow-[18]"},Uu={class:"flex flex-row items-center mt-[6px] gap-2.5 md:pl-12 lg:pl-[180px] xl:pl-[180px]"},Hu={key:0,class:"px-0.5 bg-gray-7 rounded-sm justify-center items-center inline-flex"},qu={class:"text-center text-gray-3 text-xs leading-4"},ju={key:1,class:"px-0.5 bg-gray-7 rounded-sm justify-center items-center inline-flex"},Gu={class:"text-center text-gray-3 text-xs leading-4"},Ku={class:"ml-1"},zu=Re({__name:"FlightCommonInfo",props:{segIndex:{},segInfo:{},multiDayCabin:{},sortIndex:{},segOrder:{},isTransfer:{type:Boolean},ignoreResolution:{type:Boolean},queryLowestFare:{type:Boolean},airportsDbData:{}},emits:["multiDayQuery","retractMultiDayQuery"],setup(e,{emit:a}){const o=e,r=a,{getStopInfo:d,personalizationRules:i,flightTimeFormat:l,departureTitle:A,stopData:g,arrivalTitle:C,deptReturnDate:v,disabledDate:m,queryMultiDay:T,changeMultiDate:E,calendarChange:N,loading:P,datePrefix:f,buildTerminalText:F,changeLoading:B,getFlightNo:Y,getMealDescription:h}=Wc(o,r);return(c,u)=>{var x,S,U,z,V,K,W,I,L,w,M,k,ae,he,fe,de,oe,j,Z,X,ne,ve,ie,ee,ue,$e,Fe,Ee,Pe,Te,H,re;const J=$a,y=bt,G=xa,Q=oa;return mt((p(),D("div",{class:be(["inline-flex flex-col items-start justify-center w-full",c.ignoreResolution?"":"need-resolution"])},[(x=c.segInfo)!=null&&x.tcFlight?(p(),D("div",Hd,[qd,n("div",jd,b((S=c.segInfo)==null?void 0:S.tcFlight),1)])):ge("",!0),n("div",Gd,[c.segOrder===0?(p(),D("div",Kd,[n("div",zd,b(isNaN(c.sortIndex)?"":c.sortIndex+1),1)])):(p(),D("div",Jd)),n("div",Wd,[_(Ka,{"seg-info":c.segInfo,"airports-db-data":c.airportsDbData},{default:$(()=>[_(za,{"seg-info":c.segInfo},null,8,["seg-info"])]),_:1},8,["seg-info","airports-db-data"])]),Zd,n("div",Xd,[n("div",eu,[n("div",tu,[_(Ka,{"seg-info":c.segInfo,"airports-db-data":c.airportsDbData},{default:$(()=>[_(za,{"seg-info":c.segInfo},null,8,["seg-info"])]),_:1},8,["seg-info","airports-db-data"]),n("div",au,[n("div",ou,b(((U=c.segInfo)==null?void 0:U.connectLevel)??""),1),n("div",nu,b(((V=(z=c.segInfo)==null?void 0:z.airlines)==null?void 0:V.planeType)??""),1)])]),n("div",iu,[n("div",ru,[n("span",su,[n("div",lu,[me(b(((K=c.segInfo)==null?void 0:K.departureTime)??"")+" ",1),n("span",cu,b(((W=c.segInfo)==null?void 0:W.deptArrdays)??""),1)]),n("div",du,b(((I=c.segInfo)==null?void 0:I.departureAirportCode)??"")+" "+b(t(F)(((L=c.segInfo)==null?void 0:L.departureTerminal)??"")),1)])]),uu,n("div",pu,[n("div",{class:be(Number(((w=c.segInfo)==null?void 0:w.stopCity)??0)>0?"stop-info":"no-stop-info")},[n("span",gu,[_(Ud,{"seg-info":c.segInfo,"stop-data":t(g),"departure-title":t(A),"arrival-title":t(C),"airports-db-data":c.airportsDbData},{default:$(()=>{var se;return[n("div",{class:"text-xs font-bold whitespace-nowrap text-center cursor-pointer h-[20px] leading-[20px] text-brand-2",onClick:u[0]||(u[0]=(...q)=>t(d)&&t(d)(...q))},b(`${c.$t("app.fightSell.afterStop")} ${(se=c.segInfo)==null?void 0:se.stopCity}`),1)]}),_:1},8,["seg-info","stop-data","departure-title","arrival-title","airports-db-data"]),_(J,{class:"divider"},{default:$(()=>{var se;return[n("div",{class:be(Number(((se=c.segInfo)==null?void 0:se.stopCity)??0)>0?"stop-point-circle":"")},null,2)]}),_:1})]),n("div",fu,[n("span",null,b(t(l)(((M=c.segInfo)==null?void 0:M.flightTime)??"")??""),1)])],2)]),mu,n("div",hu,[n("span",vu,[n("div",yu,[me(b(((k=c.segInfo)==null?void 0:k.arrivalTime)??"")+" ",1),n("span",bu,b(((ae=c.segInfo)==null?void 0:ae.arrivalArrdays)??""),1)]),n("div",_u,b(((he=c.segInfo)==null?void 0:he.arrivalAirportCode)??"")+" "+b(t(F)(((fe=c.segInfo)==null?void 0:fe.arrivalTerminal)??"")),1)])])])]),n("div",Cu,[n("div",Du,[n("div",Au,[n("div",$u,b(((de=c.segInfo)==null?void 0:de.connectLevel)??""),1),n("div",xu,b(((j=(oe=c.segInfo)==null?void 0:oe.airlines)==null?void 0:j.planeType)??""),1)]),Tu,n("div",Su,[(Z=c.segInfo)!=null&&Z.commonMeal?(p(),D("div",ku,[((X=c.segInfo)==null?void 0:X.commonMeal)==="S"?(p(),D("div",Fu,[_(y,{effect:"dark",content:`${(ne=c.segInfo)==null?void 0:ne.commonMeal}${c.$t("app.avSearch.snacksDimOrSum")}`,placement:"top"},{default:$(()=>{var se;return[me(b(t(h)(((se=c.segInfo)==null?void 0:se.commonMeal)??"")),1)]}),_:1},8,["content"])])):(p(),D("div",Lu,b(t(h)(((ve=c.segInfo)==null?void 0:ve.commonMeal)??"")),1))])):ge("",!0),n("div",wu,"ASR"+b(((ie=c.segInfo)==null?void 0:ie.asr)??""),1)]),n("div",Nu,[(ue=(ee=c.segInfo)==null?void 0:ee.airlines)!=null&&ue.airService?(p(),D("div",Iu,[c.segInfo.airlines.airService.find(se=>se.code==="ADHOC")?(p(),D("svg",Eu,Mu)):ge("",!0)])):ge("",!0)]),t(i)?(p(),D("div",Qu,[_(t(On),{"rule-info":t(i),origin:(($e=c.segInfo)==null?void 0:$e.arrivalAirportCode)??"",destination:(Fe=c.segInfo)==null?void 0:Fe.departureAirportCode},null,8,["rule-info","origin","destination"])])):ge("",!0)]),n("div",Ou,[c.queryLowestFare?ge("",!0):(p(),D(_e,{key:0},[c.multiDayCabin.day!==""?(p(),D("div",{key:0,class:"flex w-full h-5 text-brand-2 justify-end items-center px-0.5 gap-1",onClick:u[1]||(u[1]=(...se)=>t(E)&&t(E)(...se))},[n("span",Ru,b(c.$t("app.avSearch.fold")),1),_(t(He),{size:16,class:"mt-[-4px]"},{default:$(()=>[_(t(Pt))]),_:1})])):ge("",!0),c.multiDayCabin.day===""?(p(),D("div",Vu,[n("div",Bu,b(c.$t("app.avSearch.multiDayQuery")),1),c.multiDayCabin.day===""?(p(),Ae(G,{key:0,modelValue:t(v),"onUpdate:modelValue":u[2]||(u[2]=se=>Ze(v)?v.value=se:null),class:"av-range-picker",format:"YYYY-MM-DD",type:"daterange","prefix-icon":t(f),editable:!1,"disabled-date":t(m),"popper-class":"query-flight-date-picker-style",onChange:t(T),onCalendarChange:t(N)},null,8,["modelValue","prefix-icon","disabled-date","onChange","onCalendarChange"])):ge("",!0)])):ge("",!0)],64)),_(fc,{"flight-no":t(Y)(c.segInfo),"departure-date":((Ee=c.segInfo)==null?void 0:Ee.departureDate)??"",onChangeLoading:t(B)},null,8,["flight-no","departure-date","onChangeLoading"])])])]),n("div",Yu,[lt(c.$slots,"default",{},void 0,!0)])]),n("div",Uu,[(Pe=c.segInfo)!=null&&Pe.alliance?(p(),D("div",Hu,[n("div",qu,[n("span",null,b(((Te=c.segInfo)==null?void 0:Te.alliance)??""),1)])])):ge("",!0),(H=c.segInfo)!=null&&H.flightDistance?(p(),D("div",ju,[n("div",Gu,[n("span",null,b(c.$t("app.avSearch.flightDistance")),1),n("span",Ku,b(((re=c.segInfo)==null?void 0:re.flightDistance)??""),1)])])):ge("",!0)])],2)),[[Q,t(P)]])}}});const Ju=je(zu,[["__scopeId","data-v-8f41af3a"]]),Wu=(e,a)=>{const{t:o}=rt(),r=O({tktNum:e.tktNum??1}),d=O([]),i=f=>f==="A"||/^[1-9]$/.test(f),l=f=>["L","Q","S"].includes(f),A=f=>i(f)||l(f),g=async(f,F)=>{const{segIndex:B,day:Y,cabins:h,segInfo:c}=e;let u="",J="";f>-1&&h&&h[f]&&(u=h[f].cabinName,J=h[f].state);const y={segIndex:B??"",day:Y??"",cabinName:u,cabinStatus:J,cancel:!1,tktNum:F?r.value.tktNum:0,segInfo:c};if(e.activeCabin===u||f===-1){y.cancel=!0,a("selectCabin",y);return}a("selectCabin",y)},C=Gt(async(f,F)=>{const{notSelectable:B,activeTag:Y,avQueryFromFastQuery:h}=e;e.autoPlaceholder&&!h&&await ln(Y,o,ft)||B||g(f,F)}),v=(f,F,B)=>{if(f)return"cabin-item cabin-item-sk";const Y="cabin-item cabin-item-av",h=`${Y} cabin-item-gray`,c=`${Y} cabin-item-yellow`;let u=Y;return A(F)?l(F)&&(u=c):u=h,e.activeCabin===B&&(!e.avQueryFromFastQuery||e.autoPlaceholder)&&(u=`${u} cabin-item-active`),u};return{getCabinStyle:v,showChosenCabin:C,cabinClass:(f,F)=>{const{state:B,cabinName:Y}=F,h=e.notSelectable?"not-selectable":"";return`${v(f,B,Y)} ${h}`},form:r,confirm:()=>{a("closeCabinPopover",r.value.tktNum)},cancel:()=>{a("closeCabinPopover")},showPopover:()=>{r.value={tktNum:e.tktNum??1}},checkCharacter:f=>{const F=f.key,B=/^[1-9]$/;f.returnValue=B.test(F)?F:0},cabinRef:d}},Zu={class:"grow basis-32 cabin-info"},Xu=["onClick"],ep={class:"flex items-center justify-center"},tp={key:1},ap=Re({__name:"FlightCabin",props:{segIndex:{},cabins:{},skQuery:{type:Boolean},day:{},segInfo:{},notSelectable:{type:Boolean},activeCabin:{},activeTag:{},confirmText:{},avQueryFromFastQuery:{type:Boolean},autoPlaceholder:{type:Boolean},visibleCabinPopover:{type:Boolean},tktNum:{}},emits:["selectCabin","closeCabinPopover"],setup(e,{emit:a}){const o=e,r=a,{cabinClass:d,showChosenCabin:i,form:l,cancel:A,confirm:g,showPopover:C,checkCharacter:v,cabinRef:m}=Wu(o,r);return(T,E)=>{const N=Rn,P=Vt,f=Bt,F=aa,B=_t;return p(),D("div",Zu,[(p(!0),D(_e,null,ke(T.cabins??[],(Y,h)=>(p(),D("div",{key:`${h}${new Date}`,class:be(t(d)(T.skQuery,Y)),onClick:Et(c=>t(i)(h),["stop"])},[T.activeCabin===Y.cabinName&&T.visibleCabinPopover?(p(),Ae(B,{key:0,ref_for:!0,ref:c=>{c&&t(m).push(c)},teleported:!1,visible:T.activeCabin===Y.cabinName&&T.visibleCabinPopover,trigger:"manual",width:t(cn)()==="zh-cn"?"190":"300",onShow:t(C)},{reference:$(()=>[n("div",null,b(T.skQuery?Y.cabinName:Y.cabinName+Y.state),1)]),default:$(()=>[T.activeCabin===Y.cabinName?(p(),D("div",{key:0,class:"seize-seat-popover-content",onClick:E[1]||(E[1]=Et(()=>{},["stop"]))},[T.tktNum?ge("",!0):(p(),Ae(f,{key:0,model:t(l)},{default:$(()=>[_(P,{label:T.$t("app.pnrManagement.flight.numberOfOccupiedPassengers"),class:"num-input"},{default:$(()=>[_(N,{modelValue:t(l).tktNum,"onUpdate:modelValue":E[0]||(E[0]=c=>t(l).tktNum=c),min:1,max:9,size:"small",onKeydown:t(v)},null,8,["modelValue","onKeydown"])]),_:1},8,["label"])]),_:1},8,["model"])),n("div",ep,[_(F,{type:"primary",size:"mini",onClick:t(g)},{default:$(()=>[me(b(T.confirmText||T.$t("app.pnrManagement.flight.confirm")),1)]),_:1},8,["onClick"]),T.avQueryFromFastQuery?(p(),Ae(F,{key:0,size:"mini",onClick:t(A)},{default:$(()=>[me(b(T.$t("app.pnrManagement.flight.cancel")),1)]),_:1},8,["onClick"])):ge("",!0)])])):ge("",!0)]),_:2},1032,["visible","width","onShow"])):(p(),D("div",tp,b(T.skQuery?Y.cabinName:Y.cabinName+Y.state),1))],10,Xu))),128))])}}});const Ja=je(ap,[["__scopeId","data-v-6ec4ec34"]]),op={class:"relative mt-[10px] mb-[10px] border-b-[1px] border-dashed border-gray-7"},np={class:"absolute -top-[8px] left-[200px] flex justify-center items-center pl-[9px] pr-[9px] h-[18px] text-[12px] text-gray-2 rounded bg-brand-7"},ip=Re({__name:"TransferDivider",props:{transferText:{}},setup(e){return(a,o)=>(p(),D("div",op,[n("div",np,[n("span",null,b(a.transferText),1)])]))}}),rp=(e,a)=>{const{t:o}=rt(),r=O(!1),d=O(),i=O(""),l=et(!1),A=O([]),g=et([]),C=[],v=[],m=ro(e.segmentData),T=I=>`${m}${I}`,E=I=>te(I||e.departureDate).format("YYYY-MM-DD"),N=I=>{const L=I.split("/"),w=L[L.length-1],M=A.value.find(k=>{var ae;return(ae=k.multDayCabins)==null?void 0:ae.find(he=>{const fe=he.key===I;return fe&&(he.activeCabin=""),fe})});M&&v.forEach(k=>{`${M.segIndex}>${E(w)}`==`${k.segIndex}>${E(k.day)}`&&(k.cabinName="",k.cabinStatus="")})},P=I=>{A.value.find(L=>{var w;if(I.includes(L.segIndex))return(w=L.multDayCabins)==null?void 0:w.find(M=>{const k=I===`${L.segIndex}>${E(M.day)}`;return k&&(M.activeCabin=""),k})}),v.forEach(L=>{I===`${L.segIndex}>${E(L.day)}`&&(L.cabinName="",L.cabinStatus="")})},f=I=>{const L=[];return(I??[]).forEach(w=>{const M={cabinName:w.cabinName,state:w.state,day:w.day};L.push(M)}),L},F=I=>I.multDayCabins.length===1&&I.multDayCabins[0].day==="",B=()=>{A.value.forEach(I=>{I.multDayCabins.forEach(L=>{if(F(I))return;const w=(C??[]).find(M=>M.key===I.segIndex)??{};if(w){const M=(w.cabins??[]).find(k=>L.day===(k==null?void 0:k.dateTime))??{};if(M){const k=f(M.cabinNos);L.cabins=k,L.activeCabin=""}}})})},Y=()=>{if(e.flightNoQueryFlag||l.value)return!0;const I=v.filter(L=>L.cabinName!=="");return(I==null?void 0:I.length)===e.segmentData.segments.length},h=(I,L,w,M)=>v.filter(k=>k.segIndex===I).some(k=>k.cabinName===L&&k.cabinStatus===w&&k.day===M),c=(I,L)=>{const{cabinName:w,cancel:M,day:k}=I;return!e.autoSelectedCabin||e.flightNoQueryFlag||L||A.value.length<2||M?!1:(A.value.forEach(ae=>{ae.multDayCabins.forEach(fe=>{if(fe.activeCabin="",E(fe.day)===E(k)&&fe.cabins.find(oe=>oe.cabinName===w)){const oe=fe.cabins.find(j=>j.cabinName===w);if(oe){const j=v.find(Z=>Z.segIndex===ae.segIndex);j&&(j.cabinName=w,j.cabinStatus=oe.state,j.day=k,fe.activeCabin=w)}}});const he={transferCabinCache:v,day:k,travelKey:ae.segIndex,segmentData:e.segmentData,tktType:e.tktType};a("selectCabin",he,i.value)}),!0)},u=()=>{var ae;if(!i.value)return;const I=i.value.split("-"),L=Number(I[0]),w=Number(I[1]),M=(ae=A.value[L])==null?void 0:ae.multDayCabins[w];M.activeCabin="";const k=v.find(he=>`${he.segIndex}>${E(he.day)}`==`${T(L)}>${E(M.day)}`);k&&(k.cabinName="",k.cabinStatus=""),i.value=""},J=(I,L,w)=>{i.value=`${L}-${w}`;const{segIndex:M,day:k,cabinName:ae,cabinStatus:he,cancel:fe,segInfo:de,tktNum:oe}=I;if(c(I,L))return;A.value.forEach(Z=>{Z.segIndex===M?Z.multDayCabins.forEach(X=>{E(X.day)===E(k)&&(X.activeCabin=fe?"":ae)}):e.flightNoQueryFlag&&Z.multDayCabins.forEach(X=>{E(X.day)===E(k)&&(X.activeCabin="")})});const j={transferCabinCache:v,day:k,travelKey:M,segmentData:e.segmentData,tktType:e.tktType,tktNum:oe};if(fe||h(M,ae,he,k)){v.filter(Z=>Z.segIndex===M).forEach(Z=>{Z.cabinName="",Z.cabinStatus="",Z.day=k}),a("cancelSelectCabin",j);return}v.forEach(Z=>{Z.segIndex===M?(Z.cabinName=ae,Z.cabinStatus=he,Z.day=k):e.flightNoQueryFlag&&(Z.cabinName="",Z.cabinStatus="",Z.day="")}),j.transferCabinCache=v.filter(Z=>Z.cabinName),de&&(Y()||e.autoPlaceholder)&&a("selectCabin",j,i.value)},y=(I,L,w,M)=>{const k=M,ae=[];if(k&&k.airlines){const fe={airCode:k.airlines.airCode,flightNo:`${k.airlines.airCode}${k.airlines.flightNo}`,departureCity:k.departureAirportCode,arrivalCity:k.arrivalAirportCode};ae.push(fe)}return{airlines:ae,departureStart:L,departureEnd:w,index:e.segmentData.pkId}},G=(I,L)=>{l.value=!1,A.value.filter(w=>w.segIndex===I).forEach(w=>{const M=[],k=L;if(k){const ae=f(k.cabins),he=E(k.departureDate),fe={day:"",isShowMultDayQuery:!0,cabins:ae,activeCabin:"",key:la(k,he)};M.push(fe),w.multDayCabins=M,C.forEach((de,oe)=>{I===de.key&&C.splice(oe,1)})}})},Q=async(I,L,w,M)=>{var k,ae,he,fe,de;try{r.value=!0,l.value=!0;const{data:oe}=await _i(y(I,L,w,M),"01010209"),j=((k=oe.value)==null?void 0:k.flightInfoList)??[];if(j.length>0&&((he=(ae=j[0])==null?void 0:ae.segments)==null?void 0:he.length)>0){const Z=j[0],X=(de=(fe=Z==null?void 0:Z.segments)==null?void 0:fe[0])==null?void 0:de.cabins;if(X){const ne={key:I,cabins:X};C.push(ne),A.value.filter(ve=>ve.segIndex===I).forEach(ve=>{var ee;const ie=[];X.forEach((ue,$e)=>{const Fe=M,Ee=E(ue.dateTime),Pe={day:ue.dateTime,isShowMultDayQuery:$e===0,cabins:ue.cabinNos,activeCabin:"",key:la(Fe,Ee)};ie.push(Pe)}),g.value=f((ee=ve==null?void 0:ve.multDayCabins)==null?void 0:ee[0].cabins),ve.multDayCabins=ie}),B()}}}catch{G(I,M)}finally{r.value=!1}},x=(I,L)=>{var k;const w=[],M={day:"",isShowMultDayQuery:((k=e.segmentData)==null?void 0:k.segments.length)===1,cabins:I,activeCabin:"",key:la(L)};return w.push(M),w},S=()=>{var I;A.value=[],v.length=0,i.value="",e.segmentData&&(((I=e.segmentData)==null?void 0:I.segments)??[]).forEach((L,w)=>{const M=f(L.cabins),k={segIndex:T(w),multDayCabins:x(M,L)};A.value.push(k);const{arrivalAirportCode:ae,departureAirportCode:he,departureDate:fe,arrivalDate:de,airlines:oe}=L,j={segIndex:T(w),day:"",cabinName:"",cabinStatus:"",flightNo:`${(oe==null?void 0:oe.airCode)??""}${(oe==null?void 0:oe.flightNo)??""}`,arrivalAirportCode:ae,departureAirportCode:he,departureDate:fe,arrivalDate:de,segInfo:L};v.push(j)});for(let L=0;L<e.segmentData.segments.length;L++)if(e.segmentData.segments[L].transferDate="",L<e.segmentData.segments.length-1){const w=e.segmentData.segments[L],M=e.segmentData.segments[L+1],k=te(`${w.arrivalDate}`).format("YYYY-MM-DD HHmm"),ae=te(`${M.departureDate}`).format("YYYY-MM-DD HHmm");e.segmentData.segments[L].transferDate=xi(k,ae)}},U=I=>{if(!I)return"";const L=I.transferDate??"";return I.tcFlight?`${o("app.fastQuery.assistedSearch.travelDistance",{time:L})}`:e.flightNoQueryFlag?`${o("app.fightSell.afterStopTime",{time:L})}`:`${o("app.fightSell.transfer",{time:L})}`},z=async()=>{const I=await nt("searchLocalData");d.value=(JSON.parse(I.localData??"")??[]).map(L=>({code:L.airportCode,cnName:L.cityArray[1],enName:L.cityArray[0]}))},V=async I=>{var L;(((L=e.segmentData)==null?void 0:L.segments)??[]).length&&e.avQueryFromFastQuery&&S(),a("closeCabinPopover",I)},K=I=>{const L=I.split("-"),w=i.value.split("-");return(L==null?void 0:L[0])===e.sortIndex.toString()&&(L==null?void 0:L[1])===w[0]&&e.showPlaceholderPopoverIndex.includes(Do)?(i.value="",!0):!1},W=I=>{const L=I.split("-"),w=i.value.split("-");((L==null?void 0:L[0])!==e.sortIndex.toString()||(L==null?void 0:L[1])===w[0]&&(L!=null&&L[2])&&(L==null?void 0:L[2])!==w[1])&&u()};return Ge(()=>e.showPlaceholderPopoverIndex,I=>{if(e.autoPlaceholder&&i.value){if(K(I))return;W(I)}}),pt(()=>{var I;z(),S(),(I=A.value)!=null&&I.length&&B(),jt.on(`${Be.DELETE_LEFT_FLIGHT}${e.activeTag}`,L=>{if(L.includes(Be.DELETE_LEFT_PLACEHOLDER_FLIGHT)){const w=L.replace(Be.DELETE_LEFT_PLACEHOLDER_FLIGHT,"");N(w);return}P(L)})}),kt(()=>{A.value.length=0,g.value.length=0,C.length=0,v.length=0,jt.off(`${Be.DELETE_LEFT_FLIGHT}${e.activeTag}`)}),{selectCabin:J,multDayQuery:Q,retractMultDayQuery:G,segCabins:A,multDayCabinQ:l,loading:r,transferText:U,visibleCabinPopoverIndex:i,closeCabinPopover:u,confirmTktNum:V,airportsDbData:d}},sp={class:"w-full box-border border border-brand-3 mb-[10px] last:mb-0 rounded flex-none flex flex-col items-start px-[10px] py-[6px] gap-2.5 order-3 grow-0 self-stretch"},lp={key:0},cp={class:"flex"},dp=n("div",{class:"self-center bg-gray-7 h-px flex-1 ml-1 text-xs font-bold"},null,-1),up={key:0},pp={key:1,class:"text-center leading-6"},gp={key:1},fp={class:"flex"},mp={key:0,class:"px-0.5 bg-brand-3 rounded-sm justify-center items-center inline-flex"},hp={class:"text-center text-brand-1 text-xs leading-4"},vp={key:0,class:"ml-1"},yp={key:1,class:"ml-1"},bp=Re({__name:"FlightItemInfo",props:{segmentData:{},tktType:{},sortIndex:{},departureDate:{},notSelectable:{type:Boolean},autoSelectedCabin:{type:Boolean},flightNoQueryFlag:{type:Boolean},queryLowestFare:{type:Boolean},activeTag:{},visibleCabinPopover:{type:Boolean},showPlaceholderPopoverIndex:{},autoPlaceholder:{type:Boolean},avQueryFromFastQuery:{type:Boolean},confirmText:{},tktNum:{}},emits:["selectCabin","closeCabinPopover"],setup(e,{expose:a,emit:o}){const r=e,d=o,{segCabins:i,multDayQuery:l,retractMultDayQuery:A,selectCabin:g,multDayCabinQ:C,loading:v,transferText:m,airportsDbData:T,visibleCabinPopoverIndex:E,closeCabinPopover:N,confirmTktNum:P}=rp(r,d);return a({closeCabinPopover:N}),(f,F)=>{const B=oa;return mt((p(),D("div",sp,[(p(!0),D(_e,null,ke(t(i)??[],(Y,h)=>{var c,u,J,y;return p(),D("div",{key:`${h}${new Date}`,class:"w-full"},[_(Ju,{"seg-info":(c=f.segmentData)==null?void 0:c.segments[h],"seg-index":Y.segIndex,"seg-order":h,"is-transfer":t(i).length>1,"sort-index":f.sortIndex,"ignore-resolution":f.notSelectable,"multi-day-cabin":Y.multDayCabins[0],"query-lowest-fare":f.queryLowestFare,"airports-db-data":t(T),onMultiDayQuery:(G,Q,x)=>{var S;return t(l)(G,Q,x,(S=f.segmentData)==null?void 0:S.segments[h])},onRetractMultiDayQuery:G=>{var Q;return t(A)(G,(Q=f.segmentData)==null?void 0:Q.segments[h])}},{default:$(()=>[t(C)?(p(),D("div",lp,[(p(!0),D(_e,null,ke((Y==null?void 0:Y.multDayCabins)??[],(G,Q)=>{var x;return p(),D("div",{key:`${Q}${new Date}`},[n("div",cp,[n("div",null,b((G==null?void 0:G.day)??""),1),dp]),G.cabins.length>0?(p(),D("div",up,[_(Ja,{"visible-cabin-popover":f.visibleCabinPopover&&t(E)===`${h}-${Q}`,cabins:G.cabins,"active-cabin":G.activeCabin,"sk-query":!1,"seg-index":Y.segIndex,day:G.day,"not-selectable":f.notSelectable,"seg-info":(x=f.segmentData)==null?void 0:x.segments[h],"active-tag":f.activeTag,"auto-placeholder":f.autoPlaceholder,"tkt-num":f.tktNum,"confirm-text":f.confirmText,"av-query-from-fast-query":f.avQueryFromFastQuery,onSelectCabin:S=>t(g)(S,h,Q),onCloseCabinPopover:t(P)},null,8,["visible-cabin-popover","cabins","active-cabin","seg-index","day","not-selectable","seg-info","active-tag","auto-placeholder","tkt-num","confirm-text","av-query-from-fast-query","onSelectCabin","onCloseCabinPopover"])])):(p(),D("div",pp,"NO-OP"))])}),128))])):(p(),D("div",gp,[(p(!0),D(_e,null,ke((Y==null?void 0:Y.multDayCabins)??[],(G,Q)=>{var x,S,U,z,V,K,W,I,L;return p(),D("div",{key:`${Q}${new Date}`},[n("div",fp,[n("div",null,b((G==null?void 0:G.day)??""),1)]),f.queryLowestFare?(p(),D("div",mp,[n("div",hp,[n("span",null,b(f.$t("app.avSearch.lowestFare")),1),(S=(x=f.segmentData)==null?void 0:x.segments[h])!=null&&S.fare||(z=(U=f.segmentData)==null?void 0:U.segments[h])!=null&&z.fareClass?(p(),D("span",vp,b(((K=(V=f.segmentData)==null?void 0:V.segments[h])==null?void 0:K.fareClass)??"")+b(`: ${((I=(W=f.segmentData)==null?void 0:W.segments[h])==null?void 0:I.fare)??""}`),1)):(p(),D("span",yp,"NO PRICE"))])])):ge("",!0),_(Ja,{"visible-cabin-popover":f.visibleCabinPopover&&t(E)===`${h}-${Q}`,cabins:G.cabins,"active-cabin":G.activeCabin,"sk-query":!1,"seg-index":Y.segIndex,day:G.day,"not-selectable":f.notSelectable,"seg-info":(L=f.segmentData)==null?void 0:L.segments[h],"active-tag":f.activeTag,"auto-placeholder":f.autoPlaceholder,"tkt-num":f.tktNum,"confirm-text":f.confirmText,"av-query-from-fast-query":f.avQueryFromFastQuery,onSelectCabin:w=>t(g)(w,h,Q),onCloseCabinPopover:t(P)},null,8,["visible-cabin-popover","cabins","active-cabin","seg-index","day","not-selectable","seg-info","active-tag","auto-placeholder","tkt-num","confirm-text","av-query-from-fast-query","onSelectCabin","onCloseCabinPopover"])])}),128))]))]),_:2},1032,["seg-info","seg-index","seg-order","is-transfer","sort-index","ignore-resolution","multi-day-cabin","query-lowest-fare","airports-db-data","onMultiDayQuery","onRetractMultiDayQuery"]),((J=(u=f.segmentData)==null?void 0:u.segments[h])==null?void 0:J.transferDate)!==""?(p(),Ae(ip,{key:0,"transfer-text":t(m)((y=f.segmentData)==null?void 0:y.segments[h]),class:"my-3.5"},null,8,["transfer-text"])):ge("",!0)])}),128))])),[[B,t(v)]])}}}),_p=(e,a)=>{const{t:o}=rt(),r=dn(),d=un(),i=na(),l="",A=O([]),g=O(l),C=O(e.avQueryFromFastQuery?o("app.pnrManagement.flight.bookTicket"):""),v=66,m=O(!1),T=O(!1),E=Kt(),N=St(),{activeTag:P,orderInfo:f}=Ot(N),{saveCrsTemporaryOrder:F}=Vn(),B=ia(),Y=Ie(()=>{var pe;const q=e.avQueryFromFastQuery?It:N.activeTag;return(pe=B.flightByDate[q])==null?void 0:pe.flightDataList.size}),h=O([]),c=O({filterCondition:[],sortTypeNum:0,filterDeparture:[],filterArrival:[],transferTimes:[],filterDepartureTime:[],filterArrivalTime:[]}),u=O(),J=O(),y=O(),G=O(!1),Q=Ie(()=>{const q=[];return[...e.avSearchData??[]].forEach(pe=>{((pe==null?void 0:pe.segments)??[]).forEach(ce=>{var xe,Ce;q.includes(((xe=ce==null?void 0:ce.airlines)==null?void 0:xe.airCode)??"")||q.push(((Ce=ce==null?void 0:ce.airlines)==null?void 0:Ce.airCode)??"")})}),q}),x=Ie(()=>Array.from(new Set([...e.avSearchData??[]].map(q=>{var pe;return((pe=q.segments??[])==null?void 0:pe[0].departureAirportCode)??""})))),S=Ie(()=>Array.from(new Set([...e.avSearchData??[]].map(q=>{var pe,ce;return((ce=(pe=q.segments??[])==null?void 0:pe[q.segments.length-1])==null?void 0:ce.arrivalAirportCode)??""})))),U=Ie(()=>{var q;return(q=E.getters.userPreferences)==null?void 0:q.autoSelectCabinClass}),z=O(0),V=Ie(()=>{var q;return((q=E.getters.userPreferences)==null?void 0:q.autoOccupy)&&(e.avQueryFromFastQuery||!Number(f.value.get(P.value).psgType)&&f.value.get(P.value).type==="2")}),K=O(0);let W=!1;const I=Ie(()=>e.currentPage??1),L=O(0),w=q=>q||te(e.queryForm.departureDate).format("YYYY-MM-DD"),M=async q=>{var ce;switch(m.value=(e.avSearchData??[]).length,await gt(),h.value=[...e.avSearchData??[]],q.filterCondition.length>0&&(h.value=h.value.filter(xe=>{let Ce=!1;return q.filterCondition.forEach(we=>xe.segments.forEach(Oe=>{we===Oe.airlines.airCode&&(Ce=!0)})),Ce})),q.filterDeparture.length>0&&(h.value=h.value.filter(xe=>{let Ce=!1;return q.filterDeparture.forEach(we=>{var Oe,Ne;we===((Ne=(Oe=xe.segments??[])==null?void 0:Oe[0])==null?void 0:Ne.departureAirportCode)&&(Ce=!0)}),Ce})),q.filterArrival.length>0&&(h.value=h.value.filter(xe=>{let Ce=!1;return q.filterArrival.forEach(we=>{var Oe,Ne;we===((Ne=(Oe=xe.segments??[])==null?void 0:Oe[xe.segments.length-1])==null?void 0:Ne.arrivalAirportCode)&&(Ce=!0)}),Ce})),q.transferTimes.length>0&&(h.value=h.value.filter(xe=>{const Ce=xe.segments.length-1,we=[];return xe.segments.some(Ne=>Number((Ne==null?void 0:Ne.stopCity)??0))?(we.push(-1),Ce&&we.push(Ce)):we.push(Ce),we.some(Ne=>q.transferTimes.includes(Ne.toString()))})),h.value=Nl(q,h.value),q.sortTypeNum){case 0:h.value.sort((xe,Ce)=>{var we,Oe;return te((((we=xe.segments)==null?void 0:we[0])??[]).departureDate).unix()-te((((Oe=Ce.segments)==null?void 0:Oe[0])??[]).departureDate).unix()});break;case 1:h.value.sort((xe,Ce)=>{var we,Oe;return te((((we=Ce.segments)==null?void 0:we[0])??[]).departureDate).unix()-te((((Oe=xe.segments)==null?void 0:Oe[0])??[]).departureDate).unix()});break;case 2:ja(h.value,!0);break;case 3:ja(h.value,!1);break}K.value+=1;const pe=((ce=J.value)==null?void 0:ce.getBoundingClientRect().height)/v+3;z.value=(h.value??[]).length<pe?(h.value??[]).length:Number(pe.toFixed(0)),G.value=!G.value,h.value.length||$e(!1)},k=()=>{const q=z.value+3;q>(h.value??[]).length?z.value=(h.value??[]).length:z.value=q},ae=q=>{const{transferCabinCache:pe,day:ce,travelKey:xe}=q;if(e.queryForm.flightNumber){const Ce=`${xe}>${w(ce)}`;N.delSeizeSeatInfoFlight(P.value,Ce)}else pe.forEach(Ce=>w(ce)===w(Ce.day)&&N.delSeizeSeatInfoFlight(P.value,`${Ce.segIndex}>${w(Ce.day)}`));it(Be.UPDATE_FLIGHT_LIST,"")},he=(q,pe)=>(q??[]).map(ce=>{var Ce,we;const xe=ce.cabins;return{airline:"",airNo:`${ce.airlines.airCode??""}${ce.airlines.flightNo??""}`,fltClass:((we=(Ce=xe==null?void 0:xe[0])==null?void 0:Ce.cabinName)==null?void 0:we.substring(0,1))??"",orgCity:ce.departureAirportCode??"",actionCode:ce.actionCode??"",desCity:ce.arrivalAirportCode??"",tktNum:pe.length,departureTime:te(ce.departureDate).format("YYYY-MM-DD"),type:0}}),fe=async(q,pe)=>{T.value=!0;try{const{data:ce}=await bo(pe);ce.value==="OK"&&(Da({message:o("app.avSearch.addSegmentSuccess"),type:"success"}),F(pn(Cn.UPDATE,f.value.get(P.value))),it(`PnrManagement.vue${q}`,q))}finally{T.value=!1}},de=(q,pe)=>{var ce,xe;if(((ce=q.segments)==null?void 0:ce[0].segmentType)==="1"){const Ce=q.segments.map(Ne=>{var Xe;return(Xe=Ne.cabins[0])==null?void 0:Xe.cabinName}).join(""),we=pe.map(Ne=>Ne==null?void 0:Ne.cabinName).join("");return(Ce!==we||!["RR","HK"].includes(((xe=q.segments)==null?void 0:xe[0].actionCode)??""))&&(q.disabled=!0),!0}return!1},oe=q=>{const{transferCabinCache:pe}=q,xe=(f.value.get(`${P.value}`).flight??[]).find(Ce=>Ce.disabled?!1:pe.find(we=>{const Oe=we.segInfo,Ne=te(Oe.departureDate).format("YYYY-MM-DD"),Xe=`${Oe.airlines.airCode}${Oe.airlines.flightNo}${Ne}`,Le=Ce.segments[0],tt=te(Le.departureDate).format("YYYY-MM-DD"),at=`${Le.airlines.airCode}${Le.airlines.flightNo}${tt}`;return Le.segmentType!=="2"&&Xe===at}));if(xe){if(de(xe,pe))return;N.delSeizeSeatInfoFlight(P.value,xe.key)}},j=(q,pe,ce)=>{if((pe==null?void 0:pe.segments)??[])for(let Ce=0;Ce<pe.segments.length;Ce++){const we=pe.segments[Ce];if(q===ce)return we}return null},Z=q=>{const{transferCabinCache:pe,day:ce,travelKey:xe,segmentData:Ce,tktType:we}=Je(q),Oe=[];return pe.forEach((Ne,Xe)=>{var tt;const Le=Ne.segInfo;if(Le&&`${w(Ne.day)}`==`${w(ce)}`){const at=[],ct={cabinName:Ne.cabinName,state:Ne.cabinStatus,day:Ne.day};at.push(ct);const st={airlines:Le.airlines,arrDays:Le.arrDays,deptArrdays:Le.deptArrdays,arrivalArrdays:Le.arrivalArrdays,arrivalAirportCN:Le.arrivalAirportCN,arrivalAirportCode:Le.arrivalAirportCode,arrivalDate:(ce==null?void 0:ce.length)>0?`${ce}${Le.arrivalDate.substring(10)}`:Le.arrivalDate,arrivalTerminal:Le.arrivalTerminal,arrivalTime:Le.arrivalTime,asr:Le.asr,cabins:at,connectLevel:Le.connectLevel,departureAirportCN:Le.departureAirportCN,departureAirportCode:Le.departureAirportCode,departureDate:(ce==null?void 0:ce.length)>0?`${ce}${Le.departureDate.substring(10)}`:Le.departureDate,departureTerminal:Le.departureTerminal,departureTime:Le.departureTime,flightDistance:Le.flightDistance,flightTime:Le.flightTime,stopCity:Le.stopCity,marriedSegmentNumber:Le.marriedSegmentNumber,transferDate:Xe>0?(tt=j(pe[Xe-1].segIndex,Ce,xe))==null?void 0:tt.transferDate:"",tcFlight:Le.tcFlight,commonMeal:Le.commonMeal,etInd:Le.etInd??!1,segmentType:"0"},R=te(st.departureDate).format("YYYY-MM-DD"),le={arriveCity:{code:Ne.arrivalAirportCode,name:""},departureCity:{code:Ne.departureAirportCode,name:""},date:{departureDate:R},segments:[st],tktType:we??"",key:`${Ne.segIndex}>${w(Ne.day)}`,openFlag:!1};Oe.push(le)}}),Oe},X=(q,pe)=>{const ce=q.split("-");return pe?`${ce[0]}-${ce[1]}-${pe}`:`${ce[0]}-${ce[1]}`},ne=async q=>{if(!q){g.value=l;return}let pe=q??1,ce=Be.PLACEHOLDER_FLIGHT;e.avQueryFromFastQuery&&(i.closeFastQuery(),e.placeholderFlagTag||(ce=Be.FAST_QUERY_AV_PLACEHOLDER_FLIGHT),d.path.includes("crs/pnrManagement")||(E.dispatch("setFullLoading",!0),await r.push({path:"v2/crs/pnrManagement"}),await gt()),e.placeholderFlagTag&&(await N.setActiveTag(e.placeholderFlagTag),pe=L.value)),g.value=X(g.value,Do),N.setPlaceholderFlightsParams({flightKeys:[],params:{bookAirSegs:[]},seizeSeatInfoFlight:[]},pe),it(Be.UPDATE_FLIGHT_LIST,`${ce}-${P.value}`),E.dispatch("setFullLoading",!1)},ve=async(q,pe,ce)=>{var ct;const{transferCabinCache:xe,day:Ce,segmentData:we,tktNum:Oe}=q,Ne=f.value.get(P.value),Xe=(ct=Ne==null?void 0:Ne.specialContent)==null?void 0:ct.includes(o("app.basic.occupy"));if(!e.queryForm.flightNumber&&we.segments.length>1&&!Ce){const st=xe.filter(R=>`${w(R.day)}`==`${w(Ce)}`&&R.cabinName).length===we.segments.length;if(g.value=X(pe),!st)return}g.value=Xe?X(pe):pe;const Le=ce.map(st=>st.key??"");let tt=Oe??1;if(Xe){tt=Ne.flight.find(le=>le.segments.some(Se=>Se.segmentType==="2")).segments[0].tktNum??1;const R=ca(tt??1,ce);N.setPlaceholderFlightsParams({flightKeys:Le,params:R,seizeSeatInfoFlight:ce}),it(Be.UPDATE_FLIGHT_LIST,`${Be.PLACEHOLDER_FLIGHT}-${P.value}`);return}const at=ca(tt??1,ce);N.setPlaceholderFlightsParams({flightKeys:Le,params:at,seizeSeatInfoFlight:ce})},ie=async(q,pe,ce)=>{var Le,tt,at;const{transferCabinCache:xe,day:Ce,segmentData:we,tktNum:Oe}=q;if(!e.queryForm.flightNumber&&we.segments.length>1&&!Ce){const ct=xe.filter(st=>`${w(st.day)}`==`${w(Ce)}`&&st.cabinName).length===we.segments.length;if(g.value=X(pe),!ct)return}const Ne=ce.map(ct=>ct.key??"");g.value=pe,L.value=e.placeholderFlagTag?((at=(tt=(Le=f.value.get(e.placeholderFlagTag))==null?void 0:Le.flight)==null?void 0:tt[0])==null?void 0:at.segments[0].tktNum)??0:0;const Xe=ca(Oe??1,ce);N.setPlaceholderFlightsParams({flightKeys:Ne,params:Xe,seizeSeatInfoFlight:ce}),N.setPlaceholderFlightsQueryForm(e.queryForm)},ee=(q,pe)=>{const{transferCabinCache:ce,day:xe,segmentData:Ce}=Je(q);!e.queryForm.flightNumber&&Ce.segments.length>1&&!xe&&!(ce.filter(Oe=>`${w(Oe.day)}`==`${w(xe)}`&&Oe.cabinName).length===Ce.segments.length)||(pe.forEach(we=>N.setFlight(P.value,we)),it(Be.UPDATE_FLIGHT_LIST,""))},ue=(q,pe)=>{const ce=q,xe=Z(q);if(V.value){if(e.avQueryFromFastQuery){ie(q,pe,xe);return}ve(q,pe,xe);return}g.value=X(pe),oe(ce),ee(ce,xe)},$e=q=>a("update:scrollHide",q),Fe=()=>{I.value!==1&&a("turnPage",I.value,"P")},Ee=q=>{a("screeningDirect",q)},Pe=q=>{a("changeDate",q)},Te=()=>{a("turnPage",I.value,"N")},H=q=>!((q==="pre"||!q)&&I.value===1),re=Gt(q=>{if(!y.value||e.avQueryFromFastQuery)return;const pe=q.deltaY<0,ce=q.deltaY>0;if(pe){W=!1,$e(W);return}if(ce){W=!0,$e(W);return}},300),se=()=>{var q;(q=y.value)==null||q.scrollIntoView({behavior:"smooth",block:"center",inline:"nearest"}),W=!1,$e(!1)};return Ge(()=>e.searchCompleteKey,async()=>{h.value=[],await gt(),M(c.value)}),pt(async()=>{c.value.sortTypeNum=-1,M(c.value),document.addEventListener("click",()=>{ne()})}),kt(()=>{h.value.length=0}),{loadFlightData:k,cancelSelectCabin:ae,selectCabin:ue,flightDataListSize:Y,updateKey:K,count:z,airlineList:Q,SortFlightData:M,flightData:h,turnPre:Fe,currentPage:I,turnNext:Te,screeningDirect:Ee,isAllowTurn:H,changeDate:Pe,dynamicScrollerRef:u,flightsContainerRef:J,flightsBoxRef:y,scrollToTop:se,scrollFlightsContent:re,loading:T,moreDeparture:x,moreArrival:S,activeTag:P,buildSegmentInfo:he,doAddSegment:fe,updateFlightListFlag:G,autoSelectedCabin:U,showPlaceholderPopoverIndex:g,flightItemInfoRefs:A,autoPlaceholder:V,closeCabinPopover:ne,confirmText:C,placeholderNum:L,showContainer:m}},Cp={key:0,class:"w-full h-8 mt-2.5 px-2.5 bg-yellow-3 rounded border border-solid border-yellow-2 flex-col justify-center items-start gap-2.5 inline-flex"},Dp={class:"justify-start items-center gap-1 inline-flex text-yellow-1"},Ap={class:"text-xs leading-5"},$p={class:"pagination"},xp={class:"page-panel"},Tp={key:1,class:"dynamic-no-data"},Sp=Re({__name:"FlightContainer",props:{queryForm:{},avSearchData:{},currentPage:{},searchCompleteKey:{default:""},queryResDateGroupBySessionId:{},flightOpRightTop:{},avQueryFromFastQuery:{type:Boolean},isQueryCurrentDay:{type:Boolean},placeholderFlagTag:{}},emits:["screeningDirect","turnPage","changeDate","update:scrollHide"],setup(e,{expose:a,emit:o}){const r=e,d=o,{updateKey:i,airlineList:l,SortFlightData:A,screeningDirect:g,flightData:C,selectCabin:v,scrollToTop:m,dynamicScrollerRef:T,updateFlightListFlag:E,autoPlaceholder:N,placeholderNum:P,changeDate:f,flightDataListSize:F,cancelSelectCabin:B,scrollFlightsContent:Y,flightsContainerRef:h,flightsBoxRef:c,showContainer:u,activeTag:J,closeCabinPopover:y,confirmText:G,flightItemInfoRefs:Q,isAllowTurn:x,turnPre:S,turnNext:U,loadFlightData:z,count:V,loading:K,moreDeparture:W,moreArrival:I,autoSelectedCabin:L,showPlaceholderPopoverIndex:w}=_p(r,d);return a({closeCabinPopover:y}),(M,k)=>{const ae=He,he=oa,fe=pi;return p(),D("div",{ref_key:"dynamicScrollerRef",ref:T,class:"dynamic-scroller relative"},[_(lc,{airlines:t(l),"av-search-data":M.avSearchData,"current-flight-list":t(C),"update-flight-list-flag":t(E),"query-form-params":M.queryForm,"query-res-date-group-by-session-id":M.queryResDateGroupBySessionId,"more-departure":t(W),"more-arrival":t(I),onSortFlight:t(A),onScreeningDirect:t(g),onChangeDate:t(f)},null,8,["airlines","av-search-data","current-flight-list","update-flight-list-flag","query-form-params","query-res-date-group-by-session-id","more-departure","more-arrival","onSortFlight","onScreeningDirect","onChangeDate"]),t(u)?(p(),D(_e,{key:0},[M.isQueryCurrentDay?ge("",!0):(p(),D("div",Cp,[n("div",Dp,[_(ae,{size:16},{default:$(()=>[_(t(gn))]),_:1}),n("div",Ap,b(M.$t("app.avSearch.noFlightTip")),1)])])),mt((p(),D("div",{ref_key:"flightsContainerRef",ref:h,"infinite-scroll-distance":"3",class:"infinite-list infinite-list-height flight-list-scroller overflow-hidden flex-1",onWheel:k[0]||(k[0]=(...de)=>t(Y)&&t(Y)(...de))},[t(F)>0&&t(C).length>0?(p(),D("div",{key:0,ref_key:"flightsBoxRef",ref:c,class:"py-[10px]"},[(p(!0),D(_e,null,ke(t(V),de=>{var oe;return p(),Ae(bp,{key:`${de}${t(i)}`,ref_for:!0,ref:j=>{j&&t(Q).push(j)},"not-selectable":!!M.avQueryFromFastQuery&&!t(N),"flight-no-query-flag":!!M.queryForm.flightNumber,"sort-index":de-1,"tkt-type":t(C)[de-1].tktType,"segment-data":t(C)[de-1],"departure-date":M.queryForm.departureDate,"active-tag":t(J),"show-placeholder-popover-index":t(w),"more-departure":t(W),"more-arrival":t(I),"query-lowest-fare":M.queryForm.lowestPrice,"auto-selected-cabin":t(L),"visible-cabin-popover":t(N)&&Number((oe=t(w).split("-"))==null?void 0:oe[0])===de-1&&t(w).split("-").length===3,"tkt-num":t(P),"av-query-from-fast-query":M.avQueryFromFastQuery,"confirm-text":t(G),"auto-placeholder":t(N),onCloseCabinPopover:t(y),onSelectCabin:(j,Z)=>t(v)(j,`${de-1}-${Z}`),onCancelSelectCabin:t(B)},null,8,["not-selectable","flight-no-query-flag","sort-index","tkt-type","segment-data","departure-date","active-tag","show-placeholder-popover-index","more-departure","more-arrival","query-lowest-fare","auto-selected-cabin","visible-cabin-popover","tkt-num","av-query-from-fast-query","confirm-text","auto-placeholder","onCloseCabinPopover","onSelectCabin","onCancelSelectCabin"])}),128))],512)):ge("",!0)],32)),[[he,t(K)],[fe,t(z)]]),n("div",$p,[n("div",{class:be(["operate",t(x)("pre")?"able-to-operate":"disable-to-operate"]),"data-gid":"01010211",onClick:k[1]||(k[1]=(...de)=>t(S)&&t(S)(...de))},b(M.$t("app.avSearch.previousPage")),3),n("span",xp,b(M.$t("app.avSearch.noPage",{currentPage:M.currentPage})),1),n("div",{class:be(["operate",t(x)("next")?"able-to-operate":"disable-to-operate"]),"data-gid":"01010212",onClick:k[2]||(k[2]=(...de)=>t(U)&&t(U)(...de))},b(M.$t("app.avSearch.nextPage")),3)]),M.avQueryFromFastQuery?ge("",!0):(p(),D("div",{key:1,class:"absolute right-[1px] bottom-[60px] cursor-pointer w-[28px] h-[28px] p-[6px] bg-white rounded shadow border border-brand-2 animate-bounce flex items-center justify-center",onClick:k[3]||(k[3]=(...de)=>t(m)&&t(m)(...de))},[_(ae,{class:"w-[16px] h-[16px]",size:"16px"},{default:$(()=>[_(t(fn),{class:"text-brand-2"})]),_:1})]))],64)):(p(),D("div",Tp,b(M.$t("app.avSearch.noData")),1))],512)}}});const kp=je(Sp,[["__scopeId","data-v-f1d7d1e5"]]),Fp=(e,a)=>{const{t:o}=rt(),r=Kt(),d=so("--bkc-el-color-primary",null),i=O(),l=O(),A=O(),g=St(),{orderInfo:C,activeTag:v}=Ot(g),m=O(""),T=O({...e.ssQueryForm,tktNum:e.tktNum||e.ssQueryForm.tktNum}),E=et({render(){return ft("em",{class:"iconfont icon-calendar"})}});let N="";const P=(w,M,k)=>{if(M.length>3||!Ct.test(M)){k(new Error(o("app.fastQuery.headerQuery.formatError")));return}if(T.value.arrivalAirportCode&&M===T.value.arrivalAirportCode){k(new Error(o("app.fastQuery.headerQuery.identical")));return}k()},f=(w,M,k)=>{if(M.length>3||!Ct.test(M)){k(new Error(o("app.fastQuery.headerQuery.formatError")));return}if(T.value.departureAirportCode&&M===T.value.departureAirportCode){k(new Error(o("app.fastQuery.headerQuery.identical")));return}k()},F=(w,M,k)=>{if(M){const ae=/^\d+$/.test(M.slice(0,2))?"":M.slice(0,2);yn.test(M)?ae?!T.value.airlines||ae===T.value.airlines?k():k(new Error(o("app.avSearch.validateFltAirline"))):k():k(new Error(o("app.avSearch.validateFltNo")));return}},B=(w,M,k)=>{if(M&&N&&Number(M)!==Number(N)){k(new Error(o("app.fastQuery.headerQuery.inconsistentNum")));return}k()},Y=Ie(()=>({departureAirportCode:[{required:!0,message:o("app.fastQuery.headerQuery.must"),trigger:"change"},{validator:P,trigger:"blur"}],arrivalAirportCode:[{required:!0,message:o("app.fastQuery.headerQuery.must"),trigger:"change"},{validator:f,trigger:"blur"}],departureDate:[{required:!T.value.isOpen,message:o("app.fastQuery.headerQuery.must"),trigger:"blur"}],cabinCode:[{required:!0,message:o("app.fastQuery.headerQuery.must"),trigger:"blur"},{pattern:mn,message:new Error(o("app.fastQuery.headerQuery.inputCorrectCabin")),trigger:"blur"}],airlines:[{required:!0,message:o("app.fastQuery.headerQuery.must"),trigger:"blur"},{pattern:io,message:new Error(o("app.fastQuery.headerQuery.correctAirlineNum")),trigger:"blur"}],flightNumber:[{required:!T.value.isOpen,message:o("app.fastQuery.headerQuery.must"),trigger:"blur"},{validator:F,trigger:"blur"}],actionCode:[{required:!0,message:o("app.fastQuery.headerQuery.must"),trigger:"blur"},{pattern:hn,message:new Error(o("app.fastQuery.headerQuery.inputCorrectActionCode")),trigger:"blur"}],tktNum:[{required:!0,message:o("app.fastQuery.headerQuery.must"),trigger:"blur"},{pattern:vn,message:new Error(o("app.fastQuery.headerQuery.inputCorrectTktNum")),trigger:"blur"},{validator:B,trigger:"blur"}]})),h=()=>{const w=T.value.departureAirportCode;T.value.departureAirportCode=T.value.arrivalAirportCode,T.value.arrivalAirportCode=w;const M=T.value.originName;T.value.originName=T.value.destName,T.value.destName=M},c=()=>{a("inputTktNumBlur")},u=w=>{const M=Ta();return w.getTime()<M.getTime()},J=w=>{T.value.seamlessOrDa===w?T.value.seamlessOrDa="":T.value.seamlessOrDa=w},y=w=>{a("deleteSsFlight",w)},G=async(w,M)=>{a("update:addSegLoading",!0);try{const{data:k}=await bo(M);k.value==="OK"&&(Da({message:o("app.avSearch.addSegmentSuccess"),type:"success"}),it(`PnrManagement.vue${w}`,w))}finally{a("update:addSegLoading",!1)}},Q=(w,M)=>{let k=-1;return T.value.arrivalAirportCode===w.segments[0].departureAirportCode?T.value.departureAirportCode===w.segments[w.segments.length-1].arrivalAirportCode?(k=M+1,k):(k=M,k):((T.value.departureAirportCode===w.segments[w.segments.length-1].arrivalAirportCode||!w.openFlag)&&(k=M+1),k)},x=(w,M)=>{const k=C.value.get(w);Qa(k)?k.rebook.flight=Je(M):k.flight=Je(M),k.flightNotNeedSort=!1,C.value.set(w,k),it(Be.UPDATE_FLIGHT_LIST,"")},S=(w,M)=>{M.forEach(k=>{y(k);const{departureAirportCode:ae,arrivalAirportCode:he,departureDate:fe,departureTime:de,arrivalDate:oe,arrivalTime:j,departureTerminal:Z,arrivalTerminal:X,departureAirportCN:ne,arrivalAirportCN:ve,actionCode:ie}=k,ee={},ue={};ue.airCode=k.airlines,ue.airCN=k.airCN??"",ue.isShared=k.isShared??"",ue.flightNo=k.isOpen?"OPEN":/^\d+[a_zA_Z]?$/.test(k.flightNumber)?k.flightNumber:k.flightNumber.slice(2),ee.departureAirportCode=ae,ee.departureAirportCN=ne??"",ee.arrivalAirportCode=he,ee.arrivalAirportCN=ve??"",ee.departureDate=fe?`${te(fe).format("YYYY-MM-DD HH:mm")}`:"",ee.cabins=[{cabinName:k.cabinCode,state:""}],ee.airlines=ue,ie&&(ee.actionCode=ie),ee.segmentType="0",Object.assign(ee,{departureTime:de,arrivalDate:oe,arrivalTime:j,departureTerminal:Z,arrivalTerminal:X});const $e={code:k.arrivalAirportCode,name:""},Fe={code:k.departureAirportCode,name:""},Ee={};if(Ee.openFlag=k.isOpen,Ee.segments=[ee],Ee.arriveCity=$e,Ee.departureCity=Fe,Ee.key=ro(Ee),k.departureDate&&k.isOpen){let Pe=0;w.some((Te,H)=>{if(te(k.departureDate).isBefore(te(Te.segments[0].departureDate),"day"))return Pe=H,!0;if(te(k.departureDate).isSame(te(Te.segments[0].departureDate),"day")){if(w.length>1&&w.length!==H+1&&Te.segments[Te.segments.length-1].arrivalAirportCode===w[H+1].segments[0].departureAirportCode)return!1;if(Pe=Q(Te,H),Pe>-1)return!0}return Pe=H+1,!1}),w.splice(Pe,0,Ee),x(v.value,w);return}w.push(Ee),g.setFlight(v.value,Ee),it(Be.UPDATE_FLIGHT_LIST,"")})},U=async w=>{N=w;let M=!1;return await i.value.validate(k=>{M=k}),M},z=w=>te(w).isValid()?te(w).format("YYYY-MM-DDTHH:mm"):"",V=async(w,M)=>{N=M,T.value[w]&&await i.value.validateField(w)},K=w=>{const M={bookAirSegs:[]};return w.forEach(k=>{const{tktNum:ae,actionCode:he,cabinCode:fe,departureAirportCode:de,arrivalAirportCode:oe,departureDate:j,airlines:Z,flightNumber:X,departureTerminal:ne,arrivalTerminal:ve}=k,ie=/^\d+[a_zA_Z]?$/.test(X)?X:X.slice(2),ee={fltClass:fe??"",orgCity:de,desCity:oe,departureTime:z(j),arrivalTime:z(j),airCode:Z+ie,tktNum:(ae==null?void 0:ae.toString())??"",equipmentCode:"",etInd:!0,departureTerminal:ne??"",arrivalTerminal:ve??"",actionCode:he};M.bookAirSegs.push(ee)}),M},W=w=>{var j;const M=v.value,k=Qa(C.value.get(M));let ae=[],he=[];const fe=C.value.get(M).flight??[],de=((j=C.value.get(M).rebook)==null?void 0:j.flight)??[];if(k?(ae=[...de],he=[...fe,...de]):(ae=[...fe],he=[...fe]),he.length>0?he.length&&he.some(Z=>!Z.openFlag):w.some(Z=>!Z.isOpen)){const Z=[],X=[];if(w.forEach(ve=>ve.isOpen||e.isGroup||C.value.get(M).type==="1"?Z.push(ve):X.push(ve)),S(ae,Z),!X.length){it(Be.UPDATE_FLIGHT_LIST,Be.SS_ADD_FLIGHT_SUCCESS);return}const ne=K(X);g.setPlaceholderFlightsParams({flightKeys:[],params:ne,seizeSeatInfoFlight:[]}),r.dispatch("setFullLoading",!0),it(Be.UPDATE_FLIGHT_LIST,`${Be.EDIT_FLIGHT_PLACEHOLDER}${Be.SS_ADD_FLIGHT_PLACEHOLDER}`)}else lo.confirm(o("app.avSearch.addOpenFlightTips"),{icon:ft(He,{color:d.value,size:32},()=>ft(co)),customClass:"warning-p-msg crs-btn-ui",dangerouslyUseHTMLString:!0,closeOnClickModal:!1,showClose:!1,showCancelButton:!1,confirmButtonText:o("app.avSearch.confirm")})},I=()=>{T.value.flightNumber="",T.value.actionCode="",T.value.tktNum=e.tktNum??"",i.value.clearValidate()},L=w=>{m.value=w};return Ge(()=>e.tktNum,()=>{e.tktNum&&(T.value.tktNum=e.tktNum)}),{queryFormRef:i,queryForm:T,FORM_RULES:Y,revertFromTo:h,disabledDate:u,datePrefix:E,seamlessOrDaClick:J,agentAirportOriginRef:l,agentAirportDestinationRef:A,openChange:I,ssValidate:U,ssValidateField:V,doAddSegment:G,addSsFlightsToCache:W,focusErrorTip:L,inputTktNumBlur:c,deleteSsFlight:y,currentError:m}},Lp={class:"add-ss-flight flex justify-between"},wp={class:"header-av-query-form flex flex-1"},Np=Re({__name:"AddSSFlight",props:{cityOrAirport:{},ssQueryForm:{},index:{},showDelete:{type:Boolean},addSegLoading:{type:Boolean},isGroup:{type:Boolean},tktNum:{}},emits:["deleteSsFlight","update:addSegLoading","inputTktNumBlur"],setup(e,{expose:a,emit:o}){const r=e,d=o,{FORM_RULES:i,queryFormRef:l,revertFromTo:A,disabledDate:g,datePrefix:C,openChange:v,queryForm:m,deleteSsFlight:T,inputTktNumBlur:E,agentAirportOriginRef:N,agentAirportDestinationRef:P,ssValidate:f,ssValidateField:F,addSsFlightsToCache:B,focusErrorTip:Y,currentError:h}=Fp(r,d);return a({ssValidate:f,ssValidateField:F,queryForm:m,addSsFlightsToCache:B}),(c,u)=>{const J=Bn,y=Vt,G=He,Q=Tt,x=Bt;return p(),D("div",Lp,[_(x,{ref_key:"queryFormRef",ref:l,model:t(m),rules:t(i),"validate-on-rule-change":!1,class:"flight-from"},{default:$(()=>[n("div",null,[_(y,{class:"open-item",label:c.$t("app.avSearch.openFlight")},{default:$(()=>[_(J,{modelValue:t(m).isOpen,"onUpdate:modelValue":u[0]||(u[0]=S=>t(m).isOpen=S),"inline-prompt":"","active-text":c.$t("app.avSearch.openYes"),"inactive-text":c.$t("app.avSearch.openNo"),onChange:t(v)},null,8,["modelValue","active-text","inactive-text","onChange"])]),_:1},8,["label"])]),n("div",wp,[_(y,{label:" ",class:"agent-airport-item",prop:"departureAirportCode"},{default:$(()=>[_(Qt,{ref_key:"agentAirportOriginRef",ref:N,modelValue:t(m).departureAirportCode,"onUpdate:modelValue":u[1]||(u[1]=S=>t(m).departureAirportCode=S),name:t(m).originName,"onUpdate:name":u[2]||(u[2]=S=>t(m).originName=S),"is-agent-city":c.cityOrAirport,"prefix-title":c.$t("app.avSearch.depAirport")},null,8,["modelValue","name","is-agent-city","prefix-title"])]),_:1}),n("div",{class:"mt-[6px] ml-[2px] mr-[2px] w-[14px] h-[14px] cursor-pointer",onClick:u[3]||(u[3]=(...S)=>t(A)&&t(A)(...S))},[_(G,{class:"sort-text"},{default:$(()=>[_(t(Ca))]),_:1})]),_(y,{class:"agent-airport-item",prop:"arrivalAirportCode"},{default:$(()=>[_(Qt,{ref_key:"agentAirportDestinationRef",ref:P,modelValue:t(m).arrivalAirportCode,"onUpdate:modelValue":u[4]||(u[4]=S=>t(m).arrivalAirportCode=S),name:t(m).destName,"onUpdate:name":u[5]||(u[5]=S=>t(m).destName=S),"is-agent-city":c.cityOrAirport,"prefix-title":c.$t("app.avSearch.arrAirport")},null,8,["modelValue","name","is-agent-city","prefix-title"])]),_:1}),_(y,{label:" ",class:"departure-date",prop:"departureDate"},{default:$(()=>[_(Fa,{modelValue:t(m).departureDate,"onUpdate:modelValue":u[6]||(u[6]=S=>t(m).departureDate=S),type:"date","value-format":"YYYY-MM-DD","auto-compute-date":"","disabled-date":t(g),"disable-end-date":new Date().toString(),"prefix-icon":t(C),placeholder:c.$t("app.avSearch.date")},null,8,["modelValue","disabled-date","disable-end-date","prefix-icon","placeholder"])]),_:1}),_(y,{label:" ",class:be(["airline-item",t(h)==="cabin"?"currentError":""]),prop:"cabinCode"},{default:$(()=>[_(Q,{modelValue:t(m).cabinCode,"onUpdate:modelValue":u[7]||(u[7]=S=>t(m).cabinCode=S),modelModifiers:{trim:!0},class:"airlines-input",placeholder:c.$t("app.avSearch.cabinCode"),onInput:u[8]||(u[8]=S=>t(m).cabinCode=t(m).cabinCode.toUpperCase()),onFocus:u[9]||(u[9]=S=>t(Y)("cabin"))},null,8,["modelValue","placeholder"])]),_:1},8,["class"]),_(y,{label:" ",class:be(["airline-item",t(h)==="airline"?"currentError":""]),prop:"airlines"},{default:$(()=>[_(Q,{modelValue:t(m).airlines,"onUpdate:modelValue":u[10]||(u[10]=S=>t(m).airlines=S),modelModifiers:{trim:!0},class:"airlines-input",placeholder:c.$t("app.avSearch.airline"),onInput:u[11]||(u[11]=S=>t(m).airlines=t(m).airlines.toUpperCase()),onFocus:u[12]||(u[12]=S=>t(Y)("airline"))},null,8,["modelValue","placeholder"])]),_:1},8,["class"]),t(m).isOpen?ge("",!0):(p(),Ae(y,{key:0,label:" ",class:be([t(h)==="flightNo"?"currentError":""]),prop:"flightNumber"},{default:$(()=>[_(Q,{modelValue:t(m).flightNumber,"onUpdate:modelValue":u[13]||(u[13]=S=>t(m).flightNumber=S),modelModifiers:{trim:!0},class:"small-width",placeholder:c.$t("app.avSearch.flightNumber"),onInput:u[14]||(u[14]=S=>t(m).flightNumber=t(m).flightNumber.toUpperCase()),onFocus:u[15]||(u[15]=S=>t(Y)("flightNo"))},null,8,["modelValue","placeholder"])]),_:1},8,["class"])),!t(m).isOpen&&!c.isGroup?(p(),Ae(y,{key:1,label:" ",class:be([t(h)==="actionCode"?"currentError":""]),prop:"actionCode"},{default:$(()=>[_(Q,{modelValue:t(m).actionCode,"onUpdate:modelValue":u[16]||(u[16]=S=>t(m).actionCode=S),modelModifiers:{trim:!0},class:"small-width",placeholder:c.$t("app.avSearch.applicationStatus"),onInput:u[17]||(u[17]=S=>{var U;return t(m).actionCode=(U=t(m).actionCode)==null?void 0:U.toUpperCase()}),onFocus:u[18]||(u[18]=S=>t(Y)("actionCode"))},null,8,["modelValue","placeholder"])]),_:1},8,["class"])):ge("",!0),!t(m).isOpen&&!c.isGroup?(p(),Ae(y,{key:2,label:" ",class:be([t(h)==="tktNum"?"currentError":""]),prop:"tktNum"},{default:$(()=>[_(Q,{modelValue:t(m).tktNum,"onUpdate:modelValue":u[19]||(u[19]=S=>t(m).tktNum=S),modelModifiers:{trim:!0},class:"airlines-input",disabled:!!c.tktNum,placeholder:c.$t("app.avSearch.numberOfPeople"),onFocus:u[20]||(u[20]=S=>t(Y)("tktNum")),onBlur:t(E)},null,8,["modelValue","disabled","placeholder","onBlur"])]),_:1},8,["class"])):ge("",!0)])]),_:1},8,["model","rules"]),c.showDelete?(p(),D("div",{key:0,class:"text-center mb-[18px] ml-[5px]",onClick:u[21]||(u[21]=S=>t(T)(c.ssQueryForm))},[_(G,{class:"cursor-pointer iconfont icon-delete",size:"20",color:"var(--bkc-el-color-primary)"})])):ge("",!0)])}}});const Ip=je(Np,[["__scopeId","data-v-92e010ae"]]),Ep={class:"add-group-flight"},Pp={class:"flex justify-end"},Mp=Re({__name:"AddGroupFlight",props:{cityOrAirport:{}},emits:["addGroupFlight"],setup(e,{emit:a}){const o=a,{t:r}=rt(),d=O(),i=O({departureAirportCode:"",originName:"",arrivalAirportCode:"",destName:"",departureDate:""}),l=et({render(){return ft("em",{class:"iconfont icon-calendar"})}}),A=(N,P,f)=>{if(P.length>3||!Ct.test(P)){f(new Error(r("app.fastQuery.headerQuery.formatError")));return}if(i.value.arrivalAirportCode&&P===i.value.arrivalAirportCode){f(new Error(r("app.fastQuery.headerQuery.identical")));return}f()},g=(N,P,f)=>{if(P.length>3||!Ct.test(P)){f(new Error(r("app.fastQuery.headerQuery.formatError")));return}if(i.value.departureAirportCode&&P===i.value.departureAirportCode){f(new Error(r("app.fastQuery.headerQuery.identical")));return}f()},C=Ie(()=>({departureAirportCode:[{required:!0,message:r("app.fastQuery.headerQuery.must"),trigger:"change"},{validator:A,trigger:"blur"}],arrivalAirportCode:[{required:!0,message:r("app.fastQuery.headerQuery.must"),trigger:"change"},{validator:g,trigger:"blur"}]})),v=()=>{const N=i.value.departureAirportCode;i.value.departureAirportCode=i.value.arrivalAirportCode,i.value.arrivalAirportCode=N;const P=i.value.originName;i.value.originName=i.value.destName,i.value.destName=P},m=N=>{const P=Ta();return N.getTime()<P.getTime()},T=()=>{i.value={departureAirportCode:"",originName:"",arrivalAirportCode:"",destName:"",departureDate:""},d.value.clearValidate(),d.value.resetFields()},E=()=>{d.value.validate(N=>{if(N){const{departureAirportCode:P,arrivalAirportCode:f,departureDate:F}=i.value,B={date:{departureDate:F},segments:[{airlines:{flightNo:"ARNK"},departureAirportCode:P,arrivalAirportCode:f,departureDate:te(F).isValid()?`${te(F).format("YYYY-MM-DD")} 00:00`:"",segmentType:"0"}]};T(),o("addGroupFlight",B)}})};return(N,P)=>{const f=Vt,F=He,B=Bt,Y=aa;return p(),D("div",Ep,[_(B,{ref_key:"groupSegmentFormRef",ref:d,model:i.value,rules:C.value,"validate-on-rule-change":!1,class:"group-flight-from",onKeyup:Wt(E,["enter"])},{default:$(()=>[_(f,{label:" ",class:"agent-airport-item",prop:"departureAirportCode"},{default:$(()=>[_(Qt,{ref:"agentAirportOriginRef",modelValue:i.value.departureAirportCode,"onUpdate:modelValue":P[0]||(P[0]=h=>i.value.departureAirportCode=h),name:i.value.originName,"onUpdate:name":P[1]||(P[1]=h=>i.value.originName=h),"is-agent-city":N.cityOrAirport,"prefix-title":N.$t("app.avSearch.depAirport")},null,8,["modelValue","name","is-agent-city","prefix-title"])]),_:1}),n("div",{class:"revert-icon",onClick:v},[_(F,{class:"sort-text"},{default:$(()=>[_(t(Ca))]),_:1})]),_(f,{class:"agent-airport-item",prop:"arrivalAirportCode"},{default:$(()=>[_(Qt,{ref:"agentAirportDestinationRef",modelValue:i.value.arrivalAirportCode,"onUpdate:modelValue":P[2]||(P[2]=h=>i.value.arrivalAirportCode=h),name:i.value.destName,"onUpdate:name":P[3]||(P[3]=h=>i.value.destName=h),"is-agent-city":N.cityOrAirport,"prefix-title":N.$t("app.avSearch.arrAirport")},null,8,["modelValue","name","is-agent-city","prefix-title"])]),_:1}),_(f,{label:" ",class:"departure-date",prop:"departureDate"},{default:$(()=>[_(Fa,{modelValue:i.value.departureDate,"onUpdate:modelValue":P[4]||(P[4]=h=>i.value.departureDate=h),type:"date","value-format":"YYYY-MM-DD","auto-compute-date":"","disabled-date":m,"disable-end-date":new Date().toString(),"prefix-icon":l.value,placeholder:N.$t("app.avSearch.date")},null,8,["modelValue","disable-end-date","prefix-icon","placeholder"])]),_:1})]),_:1},8,["model","rules"]),n("div",Pp,[_(Y,{type:"primary",onClick:E},{default:$(()=>[me(b(N.$t("app.avSearch.addEnter")),1)]),_:1})])])}}});const Qp=je(Mp,[["__scopeId","data-v-384be854"]]),Op=e=>{const a={[Ue.FLIGHT_SS]:{value:"",required:!0},[Ue.COMPANY_CODE]:{value:"",required:!0},[Ue.FLIGHT_NUMBER]:{value:"",required:!0},[Ue.CABIN_CODE]:{value:"",required:!0},[Ue.DEPARTURE_DATE]:{value:"",required:!0},[Ue.DEPARTURE_AIRPORT]:{value:"",required:!0},[Ue.ARRIVAL_AIRPORT]:{value:"",required:!0}};return e||Object.assign(a,{[Ue.ACTION_CODE]:{value:"",required:!0},[Ue.TKT_NUM]:{value:"",required:!0}}),a},_a=(e,a)=>{const o=a?Ue.ARRIVAL_AIRPORT:Ue.TKT_NUM,r=Op(a);let d=e.replaceAll(/ */gi,"");try{if(Object.keys(r).forEach(i=>{const l=bn.get(i);if(l){const A=l.exec(d),g=r[i];if(!A||A.index){if(g!=null&&g.required)throw new Error;return}g.value=A[0],d=d.slice(A[0].length),i===o&&(d=""),i===Ue.FLIGHT_NUMBER&&/^[a-zA-Z]+$/.test(d.slice(0,2))&&(g.value=`${g.value}${d.slice(0,1)}`,d=d.slice(1))}}),d)throw new Error;if(r[Ue.DEPARTURE_DATE].value.length>5){const i=So(r[Ue.DEPARTURE_DATE].value);if(te(`${i}`).isBefore(te(new Date).format("YYYY-MM-DD")))return{valid:!0,dateValid:!1,flightInfoForm:r}}return{valid:!0,dateValid:!0,flightInfoForm:r}}catch{return{valid:!1,dateValid:!0,flightInfoForm:r}}},Rp=e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase(),So=e=>{if(wn.test(e)){const a=te(new Date).format("YYYY").slice(0,2),[o,r,d]=e.match(/(\d{2})([A-Za-z]{3})(\d{2})/).slice(1),i=`${a}${d}`,l=te(`${i}-${Rp(r)}-${o}`,"YYYY-MMM-DD");if(!l.isValid())throw new Error;return l.format("YYYY-MM-DD")}else{const a=fo(e,te(new Date).format("YYYY-MM-DD").substring(0,4)),o=te(`${a}`);return o.isBefore(te(new Date).format("YYYY-MM-DD"))?o.add(1,"year").format("YYYY-MM-DD"):o.format("YYYY-MM-DD")}},Vp=(e,a)=>{var r,d;const o={id:Aa(),airlines:e[Ue.COMPANY_CODE].value,isOpen:!1,destName:"",originName:"",seamlessOrDa:"",departureAirportCode:e[Ue.DEPARTURE_AIRPORT].value,arrivalAirportCode:e[Ue.ARRIVAL_AIRPORT].value,departureDate:So(e[Ue.DEPARTURE_DATE].value),cabinCode:e[Ue.CABIN_CODE].value,flightNumber:e[Ue.FLIGHT_NUMBER].value};return a||(o.actionCode=((r=e[Ue.ACTION_CODE])==null?void 0:r.value)??"",o.tktNum=((d=e[Ue.TKT_NUM])==null?void 0:d.value)??""),o},Bp=(e,a,o)=>{const r=[];return e.some(l=>{const{valid:A,flightInfoForm:g}=_a(l,o);return a&&!o&&(g[Ue.TKT_NUM].value=a),r.push(g),!A})?[]:r.map(l=>Vp(l,o))??[]},Wa=e=>({isOpen:!1,destName:"",originName:"",departureAirportCode:"",arrivalAirportCode:"",departureDate:"",airlines:"",cabinCode:"",seamlessOrDa:"",flightNumber:"",actionCode:"",tktNum:e,id:Aa()}),Yp=e=>{const{t:a}=rt(),o=O(!1),r=O(),d=O(!1),i=O(),l=St(),A=Kt(),{activeTag:g,orderInfo:C,originPnrData:v}=Ot(l),m=e.avQueryFromFastQuery?It:g.value,T=Ie(()=>{var R,le;return C.value.get(g.value).type==="1"?(R=C.value.get(g.value))==null?void 0:R.group:((le=C.value.get(g.value))==null?void 0:le.psgType)==="1"}),E=Ie(()=>{var R,le;return(le=(R=C.value.get(g.value))==null?void 0:R.newChange)==null?void 0:le.isNewVoluntaryRescheduling}),N=Ie(()=>{var R;return(((R=C.value.get(g.value))==null?void 0:R.type)??"")==="2"}),P=Ie(()=>{var R;return((R=Array.from(C.value.keys()))==null?void 0:R.find(le=>{var Se,De;return(De=(Se=C.value.get(le))==null?void 0:Se.specialContent)==null?void 0:De.includes(a("app.basic.occupy"))}))??""}),f=Ie(()=>{var le,Se,De;const R=(Se=(le=C.value.get(g.value))==null?void 0:le.flight)==null?void 0:Se.find(Me=>Me.segments.some(qe=>Number(qe.segmentType)));return((De=R==null?void 0:R.segments[0])==null?void 0:De.tktNum)??""}),F=ia(),B=O(),Y=O(0),h=O(!1),c=O({}),u=O(""),J=O(),y=O("AV"),G=Ie(()=>{const R=F.flightByDate[m].flightDataList;return R.size>0||(S.value=""),R}),Q=O(!0),x=O(""),S=O(""),U=new Map,z=new Map,V=new Map,K=et(),W=O(1),I=O({departureCity:"",arriveCity:""});let L=[];const w=O(),M=O(),k=O([Wa(f.value)]),ae=O([]),he=O(),fe=O({segmentList:""}),de=(R,le,Se)=>{if(le){const qe=le.replace(/\n+/gi,`
`).replace(/ +/gi," ").trim().split(`
`),Ke=qe.some(ze=>!_a(ze,T.value).valid),Ve=qe.some(ze=>!_a(ze,T.value).dateValid);!Ke&&Ve?Se(new Error(a("app.fastQuery.useInternationalPriceQuery.flightDateError"))):Ke&&!Ve&&Se(new Error(a("app.fastQuery.useInternationalPriceQuery.flightErrorError")))}Se()},oe={segmentList:[{required:!0,message:a("app.fastQuery.useInternationalPriceQuery.required"),trigger:"blur"},{validator:de,trigger:"blur"}]},j=R=>{const le=[];return R.carrierFlight&&le.push("O"),R.lowestPrice&&le.push("P"),R.timeSequence&&le.push("E"),R.unsharedFlight&&le.push("NO"),le.length>0?le.join("|"):""},Z=(R,le,Se,De)=>{var Ft,Qe,ot,ht,vt;const Me=Co(g.value,C.value),qe=((Qe=(Ft=v.value)==null?void 0:Ft.get(g.value))==null?void 0:Qe.flight)??[],Ke=De?X(R.origin,R.destination,De):X(R.origin,R.destination,R.departureDate),Ve=((ht=(ot=F.flightByDate[m].flightDataList.get(Ke))==null?void 0:ot.queryForm)==null?void 0:ht.departureDate)??"";u.value=((vt=F.flightByDate[m].flightDataList.get(Ke))==null?void 0:vt.sessionId)??"";const ze=le&&Se?Ve:R.departureDate,ye={departureCity:R.origin??"",arriveCity:R.destination??"",departureTime:R.departureDateTime??"",departureDate:ze??"",flightNo:R.flightNumber??"",onlyDirect:R.onlyDirectFlight?"D":"",airCode:R.airlines??"",seamlessOrDa:R.seamlessOrDa==="DA"?R.seamlessOrDa:"seamless",queryType:"REAL_TIME_AV",pagingType:"R",sessionId:"",flightType:"",transitCity:(R.transitTerminal??"").toUpperCase()};return N.value&&Me.length&&!e.avQueryFromFastQuery&&(ye.preOccupySegmentInfoList=ea(!1,Me)),!N.value&&qe.length&&!e.avQueryFromFastQuery&&(ye.preOccupySegmentInfoList=ea(!0,qe)),ye.flightType=j(R),u.value&&(ye.sessionId=u.value??""),Se&&(ye.pagingType=Se),ye.pagingType==="R"&&(W.value=1,ye.sessionId=""),ye},X=(R,le,Se)=>`${R}${le}${Se}`,ne=async R=>{c.value=R,ve()},ve=async(R,le,Se)=>{var De,Me,qe,Ke,Ve,ze;o.value=!0,h.value=!1;try{const ye=R&&le&&Se&&JSON.stringify(Se)!=="{}"?Je(Se):Je(c.value),Qe=(await yo(Z(ye,R,le,U.get(`${ye.origin}${ye.destination}${ye.departureDate}`)),"01010207")).data.value;if(((Qe==null?void 0:Qe.flightInfoList)??[]).length<=0){K.value=[];return}if(J.value=ye,R&&le&&(le==="N"?W.value=R+1:le==="P"&&(W.value=R-1)),Qe.sessionId){if(u.value=Qe.sessionId,Qe.firstQueryDate=ye.departureDate,z.set(`${ye.origin}${ye.destination}${ye.departureDate}`,ye.departureDate),(Qe.flightInfoList??[]).length>0&&Qe.flightInfoList[0].segments&&Qe.flightInfoList[0].segments[0].departureDate){const dt=te(Qe.flightInfoList[0].segments[0].departureDate).format("YYYY-MM-DD");ye.departureDate!==dt&&U.set(`${ye.origin}${ye.destination}${ye.departureDate}`,dt)}}else Qe.sessionId=u.value,Qe.firstQueryDate=z.get(`${ye.origin}${ye.destination}${ye.departureDate}`)||ye.departureDate;Q.value=Qe.flightInfoList[0].segments[0].departureDate?te(Qe.flightInfoList[0].segments[0].departureDate).format("YYYY-MM-DD")===ye.departureDate:!0,K.value=Js(Qe.flightInfoList),Qe.flightInfoList=K.value;let ot="",ht="",vt="";if((Qe.flightInfoList??[]).length>0&&Qe.flightInfoList[0].segments){let dt="",Ht="";const At=Qe.flightInfoList[0].segments,wa=At[0].departureDate?te(At[0].departureDate).format("YYYY-MM-DD"):ye.departureDate;if(V.set(`${ye.origin}${ye.destination}${ye.departureDate}`,wa),ye.flightNumber){if(!L.length){const ra=await nt("searchLocalData");L=JSON.parse((ra==null?void 0:ra.localData)??"")}dt=((De=At==null?void 0:At[0])==null?void 0:De.departureAirportCode)??"",Ht=((Me=At[At.length-1])==null?void 0:Me.arrivalAirportCode)??""}else dt=ye.origin,Ht=ye.destination;const Na=U.get(`${ye.origin}${ye.destination}${ye.departureDate}`);vt=Na?X(dt,Ht,Na):X(dt,Ht,ye.departureDate),ht=X(dt,Ht,wa),ot=ht}else ot=X(ye.origin,ye.destination,ye.departureDate);if(Qe.sessionId!==null){const dt={...ye};I.value.departureCity=dt.origin,I.value.arriveCity=dt.destination,S.value=`${ot}+${new Date().getTime().toString()}`}Qe||(W.value=((Ke=(qe=G==null?void 0:G.value)==null?void 0:qe.get(ot))==null?void 0:Ke.currentPage)??1),(le==="P"||le==="N")&&(ht!==vt&&F.delFlightDataList(m,vt),(Qe.flightInfoList??[]).length>0&&Qe.flightInfoList[0].segments&&Qe.flightInfoList[0].segments[0].departureDate?U.set(`${ye.origin}${ye.destination}${ye.departureDate}`,te((ze=(Ve=Qe.flightInfoList[0])==null?void 0:Ve.segments[0])==null?void 0:ze.departureDate).format("YYYY-MM-DD")):U.set(`${ye.origin}${ye.destination}${ye.departureDate}`,ye.departureDate)),x.value=ot.substring(ot.length-10),F.setFlightDataList(m,Je(ye),Qe,ot,W.value)}finally{o.value=!1}},ie=async R=>{c.value=R,await ve(),Gt(()=>{!e.avQueryFromFastQuery&&(K.value??[]).length>0&&(h.value=!0)},300)()},ee=R=>{w.value=R},ue=R=>{const le=x.value||c.value.departureDate;R==="pre"&&le!==Ys()&&(c.value.departureDate=Hs(le),ve()),R==="next"&&(c.value.departureDate=Us(le),ve())},$e=R=>{I.value.departureCity=R.substring(0,3),I.value.arriveCity=R.substring(3,6)},Fe=R=>{var le,Se,De,Me,qe,Ke,Ve,ze;x.value=(R==null?void 0:R.substring(R.length-10))??"",G.value.size>=1?(K.value=((Se=(le=G==null?void 0:G.value)==null?void 0:le.get(R))==null?void 0:Se.flightInfoList)??[],W.value=((Me=(De=G==null?void 0:G.value)==null?void 0:De.get(R))==null?void 0:Me.currentPage)??1,u.value=((Ke=(qe=G==null?void 0:G.value)==null?void 0:qe.get(R))==null?void 0:Ke.sessionId)??"",c.value=((ze=(Ve=G==null?void 0:G.value)==null?void 0:Ve.get(R))==null?void 0:ze.queryForm)??{},c.value.departureDate=z.get(`${c.value.origin}${c.value.destination}${c.value.departureDate}`)||c.value.departureDate,S.value=R,$e(R),J.value=c.value,Q.value=(R==null?void 0:R.substring(6))===c.value.departureDate):(!R&&(h.value=!1),K.value=[],Q.value=!0)},Ee=(R,le)=>{Object.assign(c.value,{departureDate:le.departureDate,destination:le.arrivalAirportCode,origin:le.departureAirportCode});const Se=X(c.value.origin,c.value.destination,c.value.departureDate);F.updateFlightDataListKey(m,Se,R,{...c.value}),ve()},Pe=(R,le)=>{var Ke,Ve,ze,ye,Ft,Qe,ot,ht,vt;const Se=((Ke=F.flightByDate[m])==null?void 0:Ke.activeFlightIndex)??0,De=Array.from(F.flightByDate[m].flightDataList.entries())[Se][1],Me=(Ve=S.value)==null?void 0:Ve.slice(6,16);!U.get(`${(ze=De==null?void 0:De.queryForm)==null?void 0:ze.origin}${(ye=De==null?void 0:De.queryForm)==null?void 0:ye.destination}${(Ft=De==null?void 0:De.queryForm)==null?void 0:Ft.departureDate}`)&&Me!==((Qe=De==null?void 0:De.queryForm)==null?void 0:Qe.departureDate)&&U.set(`${(ot=De==null?void 0:De.queryForm)==null?void 0:ot.origin}${(ht=De==null?void 0:De.queryForm)==null?void 0:ht.destination}${(vt=De==null?void 0:De.queryForm)==null?void 0:vt.departureDate}`,Me),ve(W.value,le,(De==null?void 0:De.queryForm)??{})},Te=async()=>{const{data:R}=await Ci();M.value=R.value},H=R=>{l.setAvTabCheck(g.value,R==="AV"),R==="AV"&&at(),y.value=R,h.value=!1},re=()=>{var R;Y.value=((R=B.value)==null?void 0:R.getBoundingClientRect().top)??0},se=()=>{l.setDefaultJumpHistory(g.value)},q=()=>{k.value.push({isOpen:!1,destName:"",originName:"",departureAirportCode:"",arrivalAirportCode:"",departureDate:"",airlines:"",cabinCode:"",seamlessOrDa:"",flightNumber:"",id:Aa()})},pe=(R,le,Se)=>{R&&(ae.value[Se]=R)},ce=()=>{var le,Se;const R=((Se=(le=ae.value.find(De=>{var Me,qe;return(qe=(Me=De.queryForm)==null?void 0:Me.tktNum)==null?void 0:qe.toString()}))==null?void 0:le.queryForm)==null?void 0:Se.tktNum)??"";ae.value.forEach(De=>De.ssValidateField("tktNum",R))},xe=async()=>{var qe,Ke;const R=((Ke=(qe=ae.value.find(Ve=>{var ze,ye;return(ye=(ze=Ve.queryForm)==null?void 0:ze.tktNum)==null?void 0:ye.toString()}))==null?void 0:qe.queryForm)==null?void 0:Ke.tktNum)??"";k.value=[];const le=[];for(let Ve=0;Ve<ae.value.length;Ve++)le.push(ae.value[Ve].ssValidate(R)),k.value.push(ae.value[Ve].queryForm);if((await Promise.all(le)).some(Ve=>!Ve))return;const De=C.value.get(g.value).flight??[];if(!(De.length>0?De.length&&De.some(Ve=>!Ve.openFlag):k.value.some(Ve=>!Ve.isOpen))){lo.confirm(a("app.avSearch.addOpenFlightTips"),{icon:ft(He,{color:so("--bkc-el-color-primary",null).value,size:32},()=>ft(co)),customClass:"warning-p-msg crs-btn-ui",dangerouslyUseHTMLString:!0,closeOnClickModal:!1,showClose:!1,showCancelButton:!1,confirmButtonText:a("app.avSearch.confirm")});return}T.value&&(d.value=!0,await zs(k.value,L,g.value,v.value,C.value),d.value=!1),k.value.length&&ae.value[0].addSsFlightsToCache(k.value)},Ce=()=>{ae.value=[],at(),k.value=[Wa(f.value)],y.value="AV"},we=R=>{k.value=k.value.filter(le=>le.id!==R.id),ae.value=ae.value.filter(le=>le.queryForm.id!==R.id)},Oe=R=>{var le;F.setClickFlightSearchFlag(m,!((le=F.flightByDate[m])!=null&&le.clickFlightSearchFlag)),J.value=Je(R),c.value=R},Ne=R=>{U.delete(R)},Xe=R=>R.replace(/\n+/gi,`
`).replace(/ +/gi," ").trim().split(`
`),Le=()=>{var R;(R=he.value)==null||R.validate(async le=>{if(le){const Se=Bp(Xe(fe.value.segmentList),f.value,T.value);k.value.push(...Se);const De=k.value.filter(Me=>!(Me.departureAirportCode&&Me.arrivalAirportCode&&Me.departureDate&&Me.cabinCode&&Me.flightNumber&&Me.airlines)).map(Me=>Me.id);ae.value=ae.value.filter(Me=>!De.includes(Me.queryForm.id)),k.value=k.value.filter(Me=>Me.departureAirportCode||Me.arrivalAirportCode||Me.departureDate||Me.cabinCode||Me.flightNumber||Me.airlines)}})},tt=()=>{var R;e.avQueryFromFastQuery&&((R=i.value)==null||R.closeCabinPopover())},at=()=>{var R;(R=he.value)==null||R.resetFields(),fe.value.segmentList=""},ct=R=>{var le;R!=null&&R.includes(Be.PLACEHOLDER_FLIGHT)&&l.placeholderFlightsQueryForm.origin&&(J.value=Je(l.placeholderFlightsQueryForm),l.setPlaceholderFlightsQueryForm({}),(le=r.value)==null||le.search())},st=R=>{const le=g.value,Se=`ARNK/${le}/${new Date().getTime().toString()}`;l.setFlight(le,{...R,key:Se}),it(Be.UPDATE_FLIGHT_LIST,""),y.value="AV"};return pt(async()=>{A.dispatch("setFullLoading",!1);const R=g.value,le=await nt("searchLocalData");L=JSON.parse((le==null?void 0:le.localData)??""),re(),F.flightByDate[m]||F.initAgentSell(m),await Te(),jt.on(Be.UPDATE_FLIGHT_LIST,Se=>{!e.avQueryFromFastQuery&&R&&R===g.value&&ct(Se),Se!=null&&Se.includes(Be.SS_ADD_FLIGHT_SUCCESS)&&R===g.value&&Ce()}),!e.avQueryFromFastQuery&&R&&ct(Be.PLACEHOLDER_FLIGHT)}),kt(()=>{l.setPlaceholderFlightsParams({flightKeys:[],params:{bookAirSegs:[]},seizeSeatInfoFlight:[]}),L.length=0,K.value=[]}),{searchClick:ie,placeholderFlagTag:P,title:I,checkTab:H,avSearchData:K,screeningDirect:ne,loadingInstance:o,queryData:Fe,searchCompleteKey:S,turnPage:Pe,currentPage:W,queryResDateGroupBySessionId:V,changeDate:ue,queryForm:c,tabType:y,callQueryApi:Ee,scrollHide:h,flightOpRightRef:B,flightOpRightTop:Y,cityOrAirport:M,newChangeFlag:E,toBack:se,addMultSs:q,ssQueryFormList:k,addSsFlight:xe,deleteSsFlight:we,handleSetFromRefList:pe,historyQueryForm:J,addSegLoading:d,echoAndQuery:Oe,closeAVHistory:Ne,isQueryCurrentDay:Q,importFormRef:he,importForm:fe,IMPORT_FORM_RULES:oe,importSsFlight:Le,flightContainerRef:i,closeCabinPopover:tt,addGroupFlight:st,headerAvQueryRef:r,resetSsImportClick:at,tktNum:f,inputTktNumBlur:ce,isGroup:T,fromRefList:ae,flightSearchFlag:m,getRoundTripFlag:ee,roundTripFlag:w}},ko=e=>($t("data-v-ee8a00fe"),e=e(),xt(),e),Up={class:"overflow-hidden"},Hp={class:"ml-[4px] h-[20px] leading-[20px]"},qp={class:"mr-[20px] font-bold leading-normal text-gray-1"},jp={key:0,class:"h-full flex flex-col gap-[14px]"},Gp=ko(()=>n("div",{class:"mb-0 border-b border-dashed border-gray-5"},null,-1)),Kp={class:"flex-1 overflow-hidden"},zp={class:"flex justify-center items-center flex-col mt-[86px]"},Jp=["alt"],Wp={class:"mt-[19px] text-lg leading-[24px] font-bold text-gray-2"},Zp={class:"text-base text-gray-4"},Xp={key:1,class:"w-full ss-box"},eg={class:"justify-end flex items-end"},tg={key:0,class:"warning-tip mb-[16px] p-[4px] flex items-center text-yellow-1 bg-yellow-3 border border-solid border-yellow-2 rounded-sm text-[12px]"},ag={class:"flex items-center"},og=ko(()=>n("em",{class:"iconfont icon-warning-circle-fill mr-[6px]"},null,-1)),ng={class:"flex justify-end"},ig={key:2},rg=Re({__name:"FlightOpRight",props:{editFlag:{type:Boolean},avQueryFromFastQuery:{type:Boolean}},setup(e,{expose:a}){const o=e,{avSearchData:r,screeningDirect:d,queryData:i,tabType:l,searchCompleteKey:A,turnPage:g,currentPage:C,callQueryApi:v,scrollHide:m,flightOpRightTop:T,flightContainerRef:E,closeCabinPopover:N,isGroup:P,queryResDateGroupBySessionId:f,changeDate:F,queryForm:B,searchClick:Y,cityOrAirport:h,loadingInstance:c,checkTab:u,newChangeFlag:J,toBack:y,flightOpRightRef:G,historyQueryForm:Q,addGroupFlight:x,inputTktNumBlur:S,addMultSs:U,ssQueryFormList:z,addSsFlight:V,deleteSsFlight:K,handleSetFromRefList:W,addSegLoading:I,echoAndQuery:L,closeAVHistory:w,isQueryCurrentDay:M,importFormRef:k,importForm:ae,tktNum:he,fromRefList:fe,IMPORT_FORM_RULES:de,importSsFlight:oe,resetSsImportClick:j,placeholderFlagTag:Z,headerAvQueryRef:X,flightSearchFlag:ne,getRoundTripFlag:ve,roundTripFlag:ie}=Yp(o);return a({echoAndQuery:L}),(ee,ue)=>{const $e=He,Fe=aa,Ee=Tt,Pe=Vt,Te=Bt,H=$a,re=mo,se=oa;return p(),D("div",{ref_key:"flightOpRightRef",ref:G,class:be([ee.avQueryFromFastQuery?"fast-query-flight-op-right":"flight-op-right"]),onClick:ue[10]||(ue[10]=q=>t(N)())},[n("div",Up,[ee.editFlag?(p(),D("div",{key:0,class:be(["transition-height duration-700 flex items-center relative",t(m)?"h-0":"h-[20px] my-1"])},[lt(ee.$slots,"default",{},void 0,!0)],2)):ee.avQueryFromFastQuery?ge("",!0):(p(),D("div",{key:1,class:be(["transition-height duration-700 flex items-center relative",t(m)?"h-0":"h-[20px] my-1"])},[t(J)?(p(),D("div",{key:0,class:"back-box text-brand-2 cursor-pointer flex items-center mr-[10px]",onClick:ue[0]||(ue[0]=(...q)=>t(y)&&t(y)(...q))},[_($e,{class:"icon iconfont icon-left"}),n("div",Hp,b(ee.$t("app.pnrManagement.btnGroups.Back")),1)])):ge("",!0),n("div",qp,b(ee.$t("app.pnrManagement.flight.addSegmentTitle")),1),n("div",null,[n("div",{class:be(["tab-container rounded-tl-sm rounded-bl-sm border-y border-l",{"active-container border-r":t(l)==="AV"}]),onClick:ue[1]||(ue[1]=q=>t(u)("AV"))},[n("div",{class:be(["tab-container-box",{"active-box":t(l)==="AV"}])},b(`AV${ee.$t("app.avSearch.search")}[AV]`),3)],2),n("div",{class:be(["tab-container border-y border-x",{"active-container border-l":t(l)==="SS"}]),onClick:ue[2]||(ue[2]=q=>t(u)("SS"))},[n("div",{class:be(["tab-container-box",{"active-box":t(l)==="SS"}])},b(`${ee.$t("app.avSearch.addSeg")}[SS]`),3)],2),n("div",{class:be(["tab-container rounded-tr-sm rounded-br-sm border-y border-r",{"active-container border-l":t(l)==="SA"}]),onClick:ue[3]||(ue[3]=q=>t(u)("SA"))},[n("div",{class:be(["tab-container-box",{"active-box":t(l)==="SA"}])},b(`${ee.$t("app.pnrManagement.flight.addGroundSection")}[SA]`),3)],2)]),t(l)==="SS"?(p(),Ae(Fe,{key:1,link:"",type:"primary",size:"small",class:"ml-auto mr-3.5",onClick:t(U)},{default:$(()=>[me(b(ee.$t("app.avSearch.addMult")),1)]),_:1},8,["onClick"])):ge("",!0)],2))]),n("div",{class:be(["mt-[14px] flex-1 flex-col overflow-hidden pr-[6px]",t(m)||ee.avQueryFromFastQuery?"!mt-0":""])},[t(l)==="AV"?(p(),D("div",jp,[n("div",{class:be(["transition-height duration-75 overflow-hidden",t(m)?"max-h-0":"max-h-[200px]"])},[_(yl,{ref_key:"headerAvQueryRef",ref:X,"av-query-from-fast-query":ee.avQueryFromFastQuery,"city-or-airport":t(h),"history-query-form":t(Q),"flight-search-flag":t(ne),onSearchClick:t(Y),onGetRoundTripFlag:t(ve)},null,8,["av-query-from-fast-query","city-or-airport","history-query-form","flight-search-flag","onSearchClick","onGetRoundTripFlag"]),Gp],2),_(wl,{class:"mr-[6px]","av-query-from-fast-query":ee.avQueryFromFastQuery,"search-complete-key":t(A),"query-form":t(B),"round-trip-flag":t(ie),onQueryData:t(i),onCallQueryApi:t(v),onCloseAVHistory:t(w)},null,8,["av-query-from-fast-query","search-complete-key","query-form","round-trip-flag","onQueryData","onCallQueryApi","onCloseAVHistory"]),mt((p(),D("div",Kp,[(t(r)??[]).length>0?(p(),Ae(kp,{key:0,ref_key:"flightContainerRef",ref:E,scrollHide:t(m),"onUpdate:scrollHide":ue[4]||(ue[4]=q=>Ze(m)?m.value=q:null),"search-complete-key":t(A),"query-form":t(B),"av-search-data":t(r),"current-page":t(C),"query-res-date-group-by-session-id":t(f),"flight-op-right-top":t(T),"placeholder-flag-tag":t(Z),"av-query-from-fast-query":ee.avQueryFromFastQuery,"is-query-current-day":t(M),onScreeningDirect:t(d),onChangeDate:t(F),onTurnPage:t(g)},null,8,["scrollHide","search-complete-key","query-form","av-search-data","current-page","query-res-date-group-by-session-id","flight-op-right-top","placeholder-flag-tag","av-query-from-fast-query","is-query-current-day","onScreeningDirect","onChangeDate","onTurnPage"])):ge("",!0),n("div",zp,[n("img",{src:$n,alt:ee.$t("app.fastQuery.skQuerys.nodata")},null,8,Jp),n("div",Wp,b(ee.$t("app.fastQuery.skQuerys.noOrderInfo")),1),n("div",Zp,b(ee.$t("app.fastQuery.skQuerys.plChangeConditionsSearchAgain")),1)])])),[[se,t(c)]])])):t(l)==="SS"?mt((p(),D("div",Xp,[_(re,null,{default:$(()=>[_(Te,{ref_key:"importFormRef",ref:k,model:t(ae),rules:t(de),inline:!0,class:"import-flight-from"},{default:$(()=>[_(Pe,{label:ee.$t("app.avSearch.fastImport"),class:"import-flight-input",prop:"segmentList"},{default:$(()=>[_(Ee,{modelValue:t(ae).segmentList,"onUpdate:modelValue":ue[5]||(ue[5]=q=>t(ae).segmentList=q),type:"textarea",autosize:"",placeholder:ee.$t(`app.avSearch.${t(P)?"ssImportTipGroup":"ssImportTip"}`),class:"seg-item",onBlur:ue[6]||(ue[6]=q=>t(ae).segmentList=t(ae).segmentList.toUpperCase())},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),n("div",eg,[_(Fe,{type:"primary",onClick:ue[7]||(ue[7]=q=>t(oe)())},{default:$(()=>[me(b(ee.$t("app.fastQuery.useInternationalPriceQuery.import")),1)]),_:1}),_(Fe,{onClick:t(j)},{default:$(()=>[me(b(ee.$t("app.fastQuery.headerQuery.reset")),1)]),_:1},8,["onClick"])])]),_:1},8,["model","rules"]),_(H,{class:"el-bkc-divider"}),n("div",{class:"mt-[14px]",onKeyup:ue[9]||(ue[9]=Wt((...q)=>t(V)&&t(V)(...q),["enter"]))},[(p(!0),D(_e,null,ke(t(z),(q,pe)=>{var ce,xe;return p(),Ae(Ip,{key:q.id,ref_for:!0,ref:Ce=>t(W)(Ce,q.id,pe),addSegLoading:t(I),"onUpdate:addSegLoading":ue[8]||(ue[8]=Ce=>Ze(I)?I.value=Ce:null),"city-or-airport":t(h),index:pe,"ss-query-form":((xe=(ce=t(fe))==null?void 0:ce[pe])==null?void 0:xe.queryForm)||q,"show-delete":t(z).length>1,"tkt-num":t(he),"is-group":t(P),onInputTktNumBlur:t(S),onDeleteSsFlight:t(K)},null,8,["addSegLoading","city-or-airport","index","ss-query-form","show-delete","tkt-num","is-group","onInputTktNumBlur","onDeleteSsFlight"])}),128)),t(P)?ge("",!0):(p(),D("div",tg,[n("div",ag,[og,me(b(ee.$t("app.fastQuery.headerQuery.addSegmentTip")),1)])])),n("div",ng,[_(Fe,{type:"primary",onClick:t(V)},{default:$(()=>[me(b(ee.$t("app.avSearch.addEnter")),1)]),_:1},8,["onClick"])])],32)]),_:1})])),[[se,t(I)]]):(p(),D("div",ig,[_(Qp,{"city-or-airport":t(h),onAddGroupFlight:t(x)},null,8,["city-or-airport","onAddGroupFlight"])]))],2)],2)}}});const cf=je(rg,[["__scopeId","data-v-ee8a00fe"]]),df=(e,a,o)=>Ye(`${Rt}/v2/queue/queryQueueList`,{headers:{gid:a}},{ignoreError:o,originalValue:!0}).post(e).json(),uf=(e,a,o)=>Ye(`${Rt}/v2/query/pnr/queuePnr`,{headers:{gid:a}},{ignoreError:o,originalValue:!0}).post(e).json(),pf=(e,a)=>Ye(`${Rt}/v2/queue/subQueueCrsPnr`,{headers:{gid:a}},{originalValue:!0}).post(e).json(),gf=(e,a)=>Ye(`${Rt}/v2/queue/unsubscribeCrsQueue`,{headers:{gid:a}}).post(e).json(),ff=(e,a)=>Ye(`${Rt}/v2/queue/updateCrsQueueReadStatus`,{headers:{gid:a}},{ignoreError:!0,originalValue:!0}).post(e).json(),mf=(e,a)=>Ye(`${Rt}/v2/queue/subQueueCrsPnrDetail`,{headers:{gid:a}},{originalValue:!0}).post(e).json();export{Qt as A,Vg as B,Fa as C,Ka as D,pi as E,It as F,Ud as G,qg as H,jg as I,zg as J,Gg as K,Wg as L,yt as M,Jg as N,ef as O,rf as P,nf as Q,mf as R,ii as S,Ks as T,Zg as U,Xg as V,tf as W,ff as X,Rg as Y,Ug as Z,Mr as _,Kg as a,Un as b,ga as c,zt as d,yo as e,af as f,Yt as g,sf as h,lf as i,ia as j,cf as k,hi as l,Hg as m,df as n,Dt as o,uf as p,Ci as q,Mg as r,Qg as s,Yg as t,of as u,vi as v,pf as w,Og as x,Bg as y,gf as z};
