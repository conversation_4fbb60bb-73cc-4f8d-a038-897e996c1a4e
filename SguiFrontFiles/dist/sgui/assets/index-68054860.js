import{fL as Rl,f3 as Hl,ie as Ql,O as We,w as c,dF as Se,dJ as gl,ed as Ne,e9 as N,ac as R,A as S,_ as Re,q as Ie,v as ce,hl as Kl,b0 as He,cL as yl,e6 as jl,aF as P,a5 as we,a6 as Cl,x as h,B as M,F as j,P as z,Q as x,D as y,E as Z,r as K,o as Ol,el as zl,J as B,C as oe,N as Sl,a2 as Gl,s as Il,ef as Ul,ei as Jl,ej as Xl,ek as Yl,dL as te,ig as Oe,e8 as Zl,ec as xl,e7 as _l,ih as hl,d1 as Tl,V as Ll,em as _,dM as Al,ii as en,ee as ln,en as ql,ah as nn,H as on,hq as tn,dO as El,ea as an,h4 as rn,W as Bl,ij as sn,a4 as Y,ae as un,G as de,z as T,y as F,T as Ml,ai as Fe,aj as bl,b1 as W,eo as dn,dz as Vl,b9 as $l,K as cn,Z as Fl}from"./index-a2fbd71b.js";import{E as pn,u as fn,a as vn}from"./index-fe3402a7.js";import{E as mn}from"./index-350461d4.js";import{E as hn,t as bn}from"./index-ac58700d.js";import{e as gn}from"./strings-cc725bd5.js";import{i as Dl}from"./isEqual-c950930c.js";import{b as yn,C as Cn}from"./index-4e9a4449.js";var kl=1/0,On=17976931348623157e292;function Sn(e){if(!e)return e===0?e:0;if(e=Rl(e),e===kl||e===-kl){var l=e<0?-1:1;return l*On}return e===e?e:0}function wn(e){var l=Sn(e),a=l%1;return l===l?a?l-a:l:0}var In=Math.max,Tn=Math.min;function Ln(e,l,a){var v=e==null?0:e.length;if(!v)return-1;var p=v-1;return a!==void 0&&(p=wn(a),p=a<0?In(v+p,0):Tn(p,v-1)),Hl(e,yn(l),p,!0)}const En=e=>Ql[e||"default"],Wl=Symbol("ElSelectGroup"),Qe=Symbol("ElSelect");function Mn(e,l){const a=We(Qe),v=We(Wl,{disabled:!1}),p=c(()=>Se(e.value)),f=c(()=>a.props.multiple?g(a.props.modelValue,e.value):I(e.value,a.props.modelValue)),s=c(()=>{if(a.props.multiple){const d=a.props.modelValue||[];return!f.value&&d.length>=a.props.multipleLimit&&a.props.multipleLimit>0}else return!1}),m=c(()=>e.label||(p.value?"":e.value)),u=c(()=>e.value||e.label||""),w=c(()=>e.disabled||l.groupDisabled||s.value),b=gl(),g=(d=[],C)=>{if(p.value){const t=a.props.valueKey;return d&&d.some(O=>Ne(N(O,t))===N(C,t))}else return d&&d.includes(C)},I=(d,C)=>{if(p.value){const{valueKey:t}=a.props;return N(d,t)===N(C,t)}else return d===C},E=()=>{!e.disabled&&!v.disabled&&(a.hoverIndex=a.optionsArray.indexOf(b.proxy))};R(()=>m.value,()=>{!e.created&&!a.props.remote&&a.setSelected()}),R(()=>e.value,(d,C)=>{const{remote:t,valueKey:O}=a.props;if(Object.is(d,C)||(a.onOptionDestroy(C,b.proxy),a.onOptionCreate(b.proxy)),!e.created&&!t){if(O&&Se(d)&&Se(C)&&d[O]===C[O])return;a.setSelected()}}),R(()=>v.disabled,()=>{l.groupDisabled=v.disabled},{immediate:!0});const{queryChange:L}=Ne(a);return R(L,d=>{const{query:C}=S(d),t=new RegExp(gn(C),"i");l.visible=t.test(m.value)||e.created,l.visible||a.filteredOptionsCount--},{immediate:!0}),{select:a,currentLabel:m,currentValue:u,itemSelected:f,isDisabled:w,hoverItem:E}}const Vn=Ie({name:"ElOption",componentName:"ElOption",props:{value:{required:!0,type:[String,Number,Boolean,Object]},label:[String,Number],created:Boolean,disabled:Boolean},setup(e){const l=ce("select"),a=Kl(),v=c(()=>[l.be("dropdown","item"),l.is("disabled",S(m)),{selected:S(s),hover:S(g)}]),p=He({index:-1,groupDisabled:!1,visible:!0,hitState:!1,hover:!1}),{currentLabel:f,itemSelected:s,isDisabled:m,select:u,hoverItem:w}=Mn(e,p),{visible:b,hover:g}=yl(p),I=gl().proxy;u.onOptionCreate(I),jl(()=>{const L=I.value,{selected:d}=u,t=(u.props.multiple?d:[d]).some(O=>O.value===I.value);P(()=>{u.cachedOptions.get(L)===I&&!t&&u.cachedOptions.delete(L)}),u.onOptionDestroy(L,I)});function E(){e.disabled!==!0&&p.groupDisabled!==!0&&u.handleOptionSelect(I)}return{ns:l,id:a,containerKls:v,currentLabel:f,itemSelected:s,isDisabled:m,select:u,hoverItem:w,visible:b,hover:g,selectOptionClick:E,states:p}}}),$n=["id","aria-disabled","aria-selected"];function Dn(e,l,a,v,p,f){return we((h(),M("li",{id:e.id,class:y(e.containerKls),role:"option","aria-disabled":e.isDisabled||void 0,"aria-selected":e.itemSelected,onMouseenter:l[0]||(l[0]=(...s)=>e.hoverItem&&e.hoverItem(...s)),onClick:l[1]||(l[1]=Z((...s)=>e.selectOptionClick&&e.selectOptionClick(...s),["stop"]))},[j(e.$slots,"default",{},()=>[z("span",null,x(e.currentLabel),1)])],42,$n)),[[Cl,e.visible]])}var wl=Re(Vn,[["render",Dn],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/select/src/option.vue"]]);const kn=Ie({name:"ElSelectDropdown",componentName:"ElSelectDropdown",setup(){const e=We(Qe),l=ce("select"),a=c(()=>e.props.popperClass),v=c(()=>e.props.multiple),p=c(()=>e.props.fitInputWidth),f=K("");function s(){var m;f.value=`${(m=e.selectWrapper)==null?void 0:m.offsetWidth}px`}return Ol(()=>{s(),zl(e.selectWrapper,s)}),{ns:l,minWidth:f,popperClass:a,isMultiple:v,isFitInputWidth:p}}});function Pn(e,l,a,v,p,f){return h(),M("div",{class:y([e.ns.b("dropdown"),e.ns.is("multiple",e.isMultiple),e.popperClass]),style:oe({[e.isFitInputWidth?"width":"minWidth"]:e.minWidth})},[e.$slots.header?(h(),M("div",{key:0,class:y(e.ns.be("dropdown","header"))},[j(e.$slots,"header")],2)):B("v-if",!0),j(e.$slots,"default"),e.$slots.footer?(h(),M("div",{key:1,class:y(e.ns.be("dropdown","footer"))},[j(e.$slots,"footer")],2)):B("v-if",!0)],6)}var Kn=Re(kn,[["render",Pn],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/select/src/select-dropdown.vue"]]);function zn(e){const{t:l}=Sl();return He({options:new Map,cachedOptions:new Map,disabledOptions:new Map,createdLabel:null,createdSelected:!1,selected:e.multiple?[]:{},inputLength:20,inputWidth:0,optionsCount:0,filteredOptionsCount:0,visible:!1,selectedLabel:"",hoverIndex:-1,query:"",previousQuery:null,inputHovering:!1,cachedPlaceHolder:"",currentPlaceholder:l("el.select.placeholder"),menuVisibleOnFocus:!1,isOnComposition:!1,prefixWidth:11,mouseEnter:!1,focused:!1})}const An=(e,l,a)=>{const{t:v}=Sl(),p=ce("select");Gl({from:"suffixTransition",replacement:"override style scheme",version:"2.3.0",scope:"props",ref:"https://element-plus.org/en-US/component/select.html#select-attributes"},c(()=>e.suffixTransition===!1));const f=K(null),s=K(null),m=K(null),u=K(null),w=K(null),b=K(null),g=K(null),I=K(null),E=K(),L=Il({query:""}),d=Il(""),C=K([]);let t=0;const{form:O,formItem:G}=Ul(),je=c(()=>!e.filterable||e.multiple||!l.visible),U=c(()=>e.disabled||(O==null?void 0:O.disabled)),Te=c(()=>{const n=e.multiple?Array.isArray(e.modelValue)&&e.modelValue.length>0:e.modelValue!==void 0&&e.modelValue!==null&&e.modelValue!=="";return e.clearable&&!U.value&&l.inputHovering&&n}),Le=c(()=>e.remote&&e.filterable&&!e.remoteShowSuffix?"":e.suffixIcon),Ge=c(()=>p.is("reverse",Le.value&&l.visible&&e.suffixTransition)),pe=c(()=>(O==null?void 0:O.statusIcon)&&(G==null?void 0:G.validateState)&&Jl[G==null?void 0:G.validateState]),Ee=c(()=>e.remote?300:0),fe=c(()=>e.loading?e.loadingText||v("el.select.loading"):e.remote&&l.query===""&&l.options.size===0?!1:e.filterable&&l.query&&l.options.size>0&&l.filteredOptionsCount===0?e.noMatchText||v("el.select.noMatch"):l.options.size===0?e.noDataText||v("el.select.noData"):null),V=c(()=>{const n=Array.from(l.options.values()),o=[];return C.value.forEach(i=>{const r=n.findIndex($=>$.currentLabel===i);r>-1&&o.push(n[r])}),o.length>=n.length?o:n}),Ue=c(()=>Array.from(l.cachedOptions.values())),Je=c(()=>{const n=V.value.filter(o=>!o.created).some(o=>o.currentLabel===l.query);return e.filterable&&e.allowCreate&&l.query!==""&&!n}),ee=Xl(),Xe=c(()=>["small"].includes(ee.value)?"small":"default"),Ye=c({get(){return l.visible&&fe.value!==!1},set(n){l.visible=n}});R([()=>U.value,()=>ee.value,()=>O==null?void 0:O.size],()=>{P(()=>{H()})}),R(()=>e.placeholder,n=>{l.cachedPlaceHolder=l.currentPlaceholder=n,e.multiple&&Array.isArray(e.modelValue)&&e.modelValue.length>0&&(l.currentPlaceholder="")}),R(()=>e.modelValue,(n,o)=>{e.multiple&&(H(),n&&n.length>0||s.value&&l.query!==""?l.currentPlaceholder="":l.currentPlaceholder=l.cachedPlaceHolder,e.filterable&&!e.reserveKeyword&&(l.query="",X(l.query))),ve(),e.filterable&&!e.multiple&&(l.inputLength=20),!Dl(n,o)&&e.validateEvent&&(G==null||G.validate("change").catch(i=>Yl()))},{flush:"post",deep:!0}),R(()=>l.visible,n=>{var o,i,r,$,D;n?((i=(o=u.value)==null?void 0:o.updatePopper)==null||i.call(o),e.filterable&&(l.filteredOptionsCount=l.optionsCount,l.query=e.remote?"":l.selectedLabel,($=(r=m.value)==null?void 0:r.focus)==null||$.call(r),e.multiple?(D=s.value)==null||D.focus():l.selectedLabel&&(l.currentPlaceholder=`${l.selectedLabel}`,l.selectedLabel=""),X(l.query),!e.multiple&&!e.remote&&(L.value.query="",Oe(L),Oe(d)))):(e.filterable&&(te(e.filterMethod)&&e.filterMethod(""),te(e.remoteMethod)&&e.remoteMethod("")),l.query="",l.previousQuery=null,l.selectedLabel="",l.inputLength=20,l.menuVisibleOnFocus=!1,Ze(),P(()=>{s.value&&s.value.value===""&&l.selected.length===0&&(l.currentPlaceholder=l.cachedPlaceHolder)}),e.multiple||(l.selected&&(e.filterable&&e.allowCreate&&l.createdSelected&&l.createdLabel?l.selectedLabel=l.createdLabel:l.selectedLabel=l.selected.currentLabel,e.filterable&&(l.query=l.selectedLabel)),e.filterable&&(l.currentPlaceholder=l.cachedPlaceHolder))),a.emit("visible-change",n)}),R(()=>l.options.entries(),()=>{var n,o,i;if(!Zl)return;(o=(n=u.value)==null?void 0:n.updatePopper)==null||o.call(n),e.multiple&&H();const r=((i=g.value)==null?void 0:i.querySelectorAll("input"))||[];(!e.filterable&&!e.defaultFirstOption&&!xl(e.modelValue)||!Array.from(r).includes(document.activeElement))&&ve(),e.defaultFirstOption&&(e.filterable||e.remote)&&l.filteredOptionsCount&&Ve()},{flush:"post"}),R(()=>l.hoverIndex,n=>{_l(n)&&n>-1?E.value=V.value[n]||{}:E.value={},V.value.forEach(o=>{o.hover=E.value===o})});const H=()=>{P(()=>{var n,o;if(!f.value)return;const i=f.value.$el.querySelector("input");t=t||(i.clientHeight>0?i.clientHeight+2:0);const r=b.value,$=getComputedStyle(i).getPropertyValue(p.cssVarName("input-height")),D=Number.parseFloat($)||En(ee.value||(O==null?void 0:O.size)),A=ee.value||D===t||t<=0?D:t;!(i.offsetParent===null)&&(i.style.height=`${(l.selected.length===0?A:Math.max(r?r.clientHeight+(r.clientHeight>A?6:0):0,A))-2}px`),l.visible&&fe.value!==!1&&((o=(n=u.value)==null?void 0:n.updatePopper)==null||o.call(n))})},X=async n=>{if(!(l.previousQuery===n||l.isOnComposition)){if(l.previousQuery===null&&(te(e.filterMethod)||te(e.remoteMethod))){l.previousQuery=n;return}l.previousQuery=n,P(()=>{var o,i;l.visible&&((i=(o=u.value)==null?void 0:o.updatePopper)==null||i.call(o))}),l.hoverIndex=-1,e.multiple&&e.filterable&&P(()=>{if(!U.value){const o=s.value.value.length*15+20;l.inputLength=e.collapseTags?Math.min(50,o):o,Me()}H()}),e.remote&&te(e.remoteMethod)?(l.hoverIndex=-1,e.remoteMethod(n)):te(e.filterMethod)?(e.filterMethod(n),Oe(d)):(l.filteredOptionsCount=l.optionsCount,L.value.query=n,Oe(L),Oe(d)),e.defaultFirstOption&&(e.filterable||e.remote)&&l.filteredOptionsCount&&(await P(),Ve())}},Me=()=>{l.currentPlaceholder!==""&&(l.currentPlaceholder=s.value.value?"":l.cachedPlaceHolder)},Ve=()=>{const n=V.value.filter(r=>r.visible&&!r.disabled&&!r.states.groupDisabled),o=n.find(r=>r.created),i=n[0];l.hoverIndex=me(V.value,o||i)},ve=()=>{var n;if(e.multiple)l.selectedLabel="";else{const i=$e(e.modelValue);(n=i.props)!=null&&n.created?(l.createdLabel=i.props.value,l.createdSelected=!0):l.createdSelected=!1,l.selectedLabel=i.currentLabel,l.selected=i,e.filterable&&(l.query=l.selectedLabel);return}const o=[];Array.isArray(e.modelValue)&&e.modelValue.forEach(i=>{o.push($e(i))}),l.selected=o,P(()=>{H()})},$e=n=>{let o;const i=hl(n).toLowerCase()==="object",r=hl(n).toLowerCase()==="null",$=hl(n).toLowerCase()==="undefined";for(let Q=l.cachedOptions.size-1;Q>=0;Q--){const q=Ue.value[Q];if(i?N(q.value,e.valueKey)===N(n,e.valueKey):q.value===n){o={value:n,currentLabel:q.currentLabel,isDisabled:q.isDisabled};break}}if(o)return o;const D=i?n.label:!r&&!$?n:"",A={value:n,currentLabel:D};return e.multiple&&(A.hitState=!1),A},Ze=()=>{setTimeout(()=>{const n=e.valueKey;e.multiple?l.selected.length>0?l.hoverIndex=Math.min.apply(null,l.selected.map(o=>V.value.findIndex(i=>N(i,n)===N(o,n)))):l.hoverIndex=-1:l.hoverIndex=V.value.findIndex(o=>ue(o)===ue(l.selected))},300)},xe=()=>{var n,o;_e(),(o=(n=u.value)==null?void 0:n.updatePopper)==null||o.call(n),e.multiple&&H()},_e=()=>{var n;l.inputWidth=(n=f.value)==null?void 0:n.$el.offsetWidth},el=()=>{e.filterable&&l.query!==l.selectedLabel&&(l.query=l.selectedLabel,X(l.query))},ll=Tl(()=>{el()},Ee.value),nl=Tl(n=>{X(n.target.value)},Ee.value),le=n=>{Dl(e.modelValue,n)||a.emit(ql,n)},De=n=>Ln(n,o=>!l.disabledOptions.has(o)),ol=n=>{if(n.code!==Ll.delete){if(n.target.value.length<=0&&!be()){const o=e.modelValue.slice(),i=De(o);if(i<0)return;o.splice(i,1),a.emit(_,o),le(o)}n.target.value.length===1&&e.modelValue.length===0&&(l.currentPlaceholder=l.cachedPlaceHolder)}},ie=(n,o)=>{const i=l.selected.indexOf(o);if(i>-1&&!U.value){const r=e.modelValue.slice();r.splice(i,1),a.emit(_,r),le(r),a.emit("remove-tag",o.value)}n.stopPropagation(),re()},ke=n=>{n.stopPropagation();const o=e.multiple?[]:"";if(!Al(o))for(const i of l.selected)i.isDisabled&&o.push(i.value);a.emit(_,o),le(o),l.hoverIndex=-1,l.visible=!1,a.emit("clear"),re()},Pe=n=>{var o;if(e.multiple){const i=(e.modelValue||[]).slice(),r=me(i,n.value);r>-1?i.splice(r,1):(e.multipleLimit<=0||i.length<e.multipleLimit)&&i.push(n.value),a.emit(_,i),le(i),n.created&&(l.query="",X(""),l.inputLength=20),e.filterable&&((o=s.value)==null||o.focus())}else a.emit(_,n.value),le(n.value),l.visible=!1;tl(),!l.visible&&P(()=>{ae(n)})},me=(n=[],o)=>{if(!Se(o))return n.indexOf(o);const i=e.valueKey;let r=-1;return n.some(($,D)=>Ne(N($,i))===N(o,i)?(r=D,!0):!1),r},tl=()=>{const n=s.value||f.value;n&&(n==null||n.focus())},ae=n=>{var o,i,r,$,D;const A=Array.isArray(n)?n[0]:n;let Q=null;if(A!=null&&A.value){const q=V.value.filter(Be=>Be.value===A.value);q.length>0&&(Q=q[0].$el)}if(u.value&&Q){const q=($=(r=(i=(o=u.value)==null?void 0:o.popperRef)==null?void 0:i.contentRef)==null?void 0:r.querySelector)==null?void 0:$.call(r,`.${p.be("dropdown","wrap")}`);q&&en(q,Q)}(D=I.value)==null||D.handleScroll()},he=n=>{l.optionsCount++,l.filteredOptionsCount++,l.options.set(n.value,n),l.cachedOptions.set(n.value,n),n.disabled&&l.disabledOptions.set(n.value,n)},il=(n,o)=>{l.options.get(n)===o&&(l.optionsCount--,l.filteredOptionsCount--,l.options.delete(n))},al=n=>{n.code!==Ll.backspace&&be(!1),l.inputLength=s.value.value.length*15+20,H()},be=n=>{if(!Array.isArray(l.selected))return;const o=De(l.selected.map(r=>r.value)),i=l.selected[o];if(i)return n===!0||n===!1?(i.hitState=n,n):(i.hitState=!i.hitState,i.hitState)},rl=n=>{const o=n.target.value;if(n.type==="compositionend")l.isOnComposition=!1,P(()=>X(o));else{const i=o[o.length-1]||"";l.isOnComposition=!ln(i)}},sl=()=>{P(()=>ae(l.selected))},ul=n=>{l.focused||((e.automaticDropdown||e.filterable)&&(e.filterable&&!l.visible&&(l.menuVisibleOnFocus=!0),l.visible=!0),l.focused=!0,a.emit("focus",n))},re=()=>{var n,o;l.visible?(n=s.value||f.value)==null||n.focus():(o=f.value)==null||o.focus()},dl=()=>{var n,o,i;l.visible=!1,(n=f.value)==null||n.blur(),(i=(o=m.value)==null?void 0:o.blur)==null||i.call(o)},J=n=>{var o,i,r;(o=u.value)!=null&&o.isFocusInsideContent(n)||(i=w.value)!=null&&i.isFocusInsideContent(n)||(r=g.value)!=null&&r.contains(n.relatedTarget)||(l.visible&&ge(),l.focused=!1,a.emit("blur",n))},se=n=>{ke(n)},ge=()=>{l.visible=!1},ye=n=>{l.visible&&(n.preventDefault(),n.stopPropagation(),l.visible=!1)},Ke=n=>{n&&!l.mouseEnter||U.value||(l.menuVisibleOnFocus?l.menuVisibleOnFocus=!1:(!u.value||!u.value.isFocusInsideContent())&&(l.visible=!l.visible),re())},cl=()=>{l.visible?V.value[l.hoverIndex]&&Pe(V.value[l.hoverIndex]):Ke()},ue=n=>Se(n.value)?N(n.value,e.valueKey):n.value,ze=c(()=>V.value.filter(n=>n.visible).every(n=>n.disabled)),pl=c(()=>e.multiple?l.selected.slice(0,e.maxCollapseTags):[]),Ce=c(()=>e.multiple?l.selected.slice(e.maxCollapseTags):[]),Ae=n=>{if(!l.visible){l.visible=!0;return}if(!(l.options.size===0||l.filteredOptionsCount===0)&&!l.isOnComposition&&!ze.value){n==="next"?(l.hoverIndex++,l.hoverIndex===l.options.size&&(l.hoverIndex=0)):n==="prev"&&(l.hoverIndex--,l.hoverIndex<0&&(l.hoverIndex=l.options.size-1));const o=V.value[l.hoverIndex];(o.disabled===!0||o.states.groupDisabled===!0||!o.visible)&&Ae(n),P(()=>ae(E.value))}},fl=()=>{l.mouseEnter=!0},qe=()=>{l.mouseEnter=!1},vl=(n,o)=>{var i,r;ie(n,o),(r=(i=w.value)==null?void 0:i.updatePopper)==null||r.call(i)},ml=c(()=>({maxWidth:`${S(l.inputWidth)-32-(pe.value?22:0)}px`,width:"100%"}));return{optionList:C,optionsArray:V,hoverOption:E,selectSize:ee,handleResize:xe,debouncedOnInputChange:ll,debouncedQueryChange:nl,deletePrevTag:ol,deleteTag:ie,deleteSelected:ke,handleOptionSelect:Pe,scrollToOption:ae,readonly:je,resetInputHeight:H,showClose:Te,iconComponent:Le,iconReverse:Ge,showNewOption:Je,collapseTagSize:Xe,setSelected:ve,managePlaceholder:Me,selectDisabled:U,emptyText:fe,toggleLastOptionHitState:be,resetInputState:al,handleComposition:rl,onOptionCreate:he,onOptionDestroy:il,handleMenuEnter:sl,handleFocus:ul,focus:re,blur:dl,handleBlur:J,handleClearClick:se,handleClose:ge,handleKeydownEscape:ye,toggleMenu:Ke,selectOption:cl,getValueKey:ue,navigateOptions:Ae,handleDeleteTooltipTag:vl,dropMenuVisible:Ye,queryChange:L,groupQueryChange:d,showTagList:pl,collapseTagList:Ce,selectTagsStyle:ml,reference:f,input:s,iOSInput:m,tooltipRef:u,tagTooltipRef:w,tags:b,selectWrapper:g,scrollbar:I,handleMouseEnter:fl,handleMouseLeave:qe}};var qn=Ie({name:"ElOptions",emits:["update-options"],setup(e,{slots:l,emit:a}){let v=[];function p(f,s){if(f.length!==s.length)return!1;for(const[m]of f.entries())if(f[m]!=s[m])return!1;return!0}return()=>{var f,s;const m=(f=l.default)==null?void 0:f.call(l),u=[];function w(b){Array.isArray(b)&&b.forEach(g=>{var I,E,L,d;const C=(I=(g==null?void 0:g.type)||{})==null?void 0:I.name;C==="ElOptionGroup"?w(!Al(g.children)&&!Array.isArray(g.children)&&te((E=g.children)==null?void 0:E.default)?(L=g.children)==null?void 0:L.default():g.children):C==="ElOption"?u.push((d=g.props)==null?void 0:d.label):Array.isArray(g.children)&&w(g.children)})}return m.length&&w((s=m[0])==null?void 0:s.children),p(u,v)||(v=u,a("update-options",u)),m}}});const Pl="ElSelect",Bn=Ie({name:Pl,componentName:Pl,components:{ElInput:nn,ElSelectMenu:Kn,ElOption:wl,ElOptions:qn,ElTag:hn,ElScrollbar:mn,ElTooltip:pn,ElIcon:on},directives:{ClickOutside:Cn},props:{name:String,id:String,modelValue:{type:[Array,String,Number,Boolean,Object],default:void 0},autocomplete:{type:String,default:"off"},automaticDropdown:Boolean,size:{type:String,validator:tn},effect:{type:String,default:"light"},disabled:Boolean,clearable:Boolean,filterable:Boolean,allowCreate:Boolean,loading:Boolean,popperClass:{type:String,default:""},popperOptions:{type:Object,default:()=>({})},remote:Boolean,loadingText:String,noMatchText:String,noDataText:String,remoteMethod:Function,filterMethod:Function,multiple:Boolean,multipleLimit:{type:Number,default:0},placeholder:{type:String},defaultFirstOption:Boolean,reserveKeyword:{type:Boolean,default:!0},valueKey:{type:String,default:"value"},collapseTags:Boolean,collapseTagsTooltip:Boolean,maxCollapseTags:{type:Number,default:1},teleported:fn.teleported,persistent:{type:Boolean,default:!0},clearIcon:{type:El,default:an},fitInputWidth:Boolean,suffixIcon:{type:El,default:rn},tagType:{...bn.type,default:"info"},validateEvent:{type:Boolean,default:!0},remoteShowSuffix:Boolean,suffixTransition:{type:Boolean,default:!0},placement:{type:String,values:vn,default:"bottom-start"},ariaLabel:{type:String,default:void 0}},emits:[_,ql,"remove-tag","clear","visible-change","focus","blur"],setup(e,l){const a=ce("select"),v=ce("input"),{t:p}=Sl(),f=Kl(),s=zn(e),{optionList:m,optionsArray:u,hoverOption:w,selectSize:b,readonly:g,handleResize:I,collapseTagSize:E,debouncedOnInputChange:L,debouncedQueryChange:d,deletePrevTag:C,deleteTag:t,deleteSelected:O,handleOptionSelect:G,scrollToOption:je,setSelected:U,resetInputHeight:Te,managePlaceholder:Le,showClose:Ge,selectDisabled:pe,iconComponent:Ee,iconReverse:fe,showNewOption:V,emptyText:Ue,toggleLastOptionHitState:Je,resetInputState:ee,handleComposition:Xe,onOptionCreate:Ye,onOptionDestroy:H,handleMenuEnter:X,handleFocus:Me,focus:Ve,blur:ve,handleBlur:$e,handleClearClick:Ze,handleClose:xe,handleKeydownEscape:_e,toggleMenu:el,selectOption:ll,getValueKey:nl,navigateOptions:le,handleDeleteTooltipTag:De,dropMenuVisible:ol,reference:ie,input:ke,iOSInput:Pe,tooltipRef:me,tagTooltipRef:tl,tags:ae,selectWrapper:he,scrollbar:il,queryChange:al,groupQueryChange:be,handleMouseEnter:rl,handleMouseLeave:sl,showTagList:ul,collapseTagList:re,selectTagsStyle:dl}=An(e,s,l),{inputWidth:J,selected:se,inputLength:ge,filteredOptionsCount:ye,visible:Ke,selectedLabel:cl,hoverIndex:ue,query:ze,inputHovering:pl,currentPlaceholder:Ce,menuVisibleOnFocus:Ae,isOnComposition:fl,options:qe,cachedOptions:vl,optionsCount:ml,prefixWidth:n}=yl(s),o=c(()=>{const k=[a.b()],ne=S(b);return ne&&k.push(a.m(ne)),e.disabled&&k.push(a.m("disabled")),k}),i=c(()=>[a.e("tags"),a.is("disabled",S(pe))]),r=c(()=>[a.b("tags-wrapper"),{"has-prefix":S(n)&&S(se).length}]),$=c(()=>[a.e("input"),a.is(S(b)),a.is("disabled",S(pe))]),D=c(()=>[a.e("input"),a.is(S(b)),a.em("input","iOS")]),A=c(()=>[a.is("empty",!e.allowCreate&&!!S(ze)&&S(ye)===0)]),Q=c(()=>({maxWidth:`${S(J)>123&&S(se).length>e.maxCollapseTags?S(J)-123:S(J)-75}px`})),q=c(()=>({marginLeft:`${S(n)}px`,flexGrow:1,width:`${S(ge)/(S(J)-32)}%`,maxWidth:`${S(J)-42}px`}));Bl(Qe,He({props:e,options:qe,optionsArray:u,cachedOptions:vl,optionsCount:ml,filteredOptionsCount:ye,hoverIndex:ue,handleOptionSelect:G,onOptionCreate:Ye,onOptionDestroy:H,selectWrapper:he,selected:se,setSelected:U,queryChange:al,groupQueryChange:be})),Ol(()=>{s.cachedPlaceHolder=Ce.value=e.placeholder||(()=>p("el.select.placeholder")),e.multiple&&Array.isArray(e.modelValue)&&e.modelValue.length>0&&(Ce.value=""),zl(he,I),e.remote&&e.multiple&&Te(),P(()=>{const k=ie.value&&ie.value.$el;if(k&&(J.value=k.getBoundingClientRect().width,l.slots.prefix)){const ne=k.querySelector(`.${v.e("prefix")}`);n.value=Math.max(ne.getBoundingClientRect().width+11,30)}}),U()}),e.multiple&&!Array.isArray(e.modelValue)&&l.emit(_,[]),!e.multiple&&Array.isArray(e.modelValue)&&l.emit(_,"");const Be=c(()=>{var k,ne;return(ne=(k=me.value)==null?void 0:k.popperRef)==null?void 0:ne.contentRef});return{isIOS:sn,onOptionsRendered:k=>{m.value=k},prefixWidth:n,selectSize:b,readonly:g,handleResize:I,collapseTagSize:E,debouncedOnInputChange:L,debouncedQueryChange:d,deletePrevTag:C,deleteTag:t,handleDeleteTooltipTag:De,deleteSelected:O,handleOptionSelect:G,scrollToOption:je,inputWidth:J,selected:se,inputLength:ge,filteredOptionsCount:ye,visible:Ke,selectedLabel:cl,hoverIndex:ue,query:ze,inputHovering:pl,currentPlaceholder:Ce,menuVisibleOnFocus:Ae,isOnComposition:fl,options:qe,resetInputHeight:Te,managePlaceholder:Le,showClose:Ge,selectDisabled:pe,iconComponent:Ee,iconReverse:fe,showNewOption:V,emptyText:Ue,toggleLastOptionHitState:Je,resetInputState:ee,handleComposition:Xe,handleMenuEnter:X,handleFocus:Me,focus:Ve,blur:ve,handleBlur:$e,handleClearClick:Ze,handleClose:xe,handleKeydownEscape:_e,toggleMenu:el,selectOption:ll,getValueKey:nl,navigateOptions:le,dropMenuVisible:ol,reference:ie,input:ke,iOSInput:Pe,tooltipRef:me,popperPaneRef:Be,tags:ae,selectWrapper:he,scrollbar:il,wrapperKls:o,tagsKls:i,tagWrapperKls:r,inputKls:$,iOSInputKls:D,scrollbarKls:A,selectTagsStyle:dl,nsSelect:a,tagTextStyle:Q,inputStyle:q,handleMouseEnter:rl,handleMouseLeave:sl,showTagList:ul,collapseTagList:re,tagTooltipRef:tl,contentId:f,hoverOption:w}}}),Fn=["disabled","autocomplete","aria-activedescendant","aria-controls","aria-expanded","aria-label"],Wn=["disabled"],Nn={style:{height:"100%",display:"flex","justify-content":"center","align-items":"center"}};function Rn(e,l,a,v,p,f){const s=Y("el-tag"),m=Y("el-tooltip"),u=Y("el-icon"),w=Y("el-input"),b=Y("el-option"),g=Y("el-options"),I=Y("el-scrollbar"),E=Y("el-select-menu"),L=un("click-outside");return we((h(),M("div",{ref:"selectWrapper",class:y(e.wrapperKls),onMouseenter:l[22]||(l[22]=(...d)=>e.handleMouseEnter&&e.handleMouseEnter(...d)),onMouseleave:l[23]||(l[23]=(...d)=>e.handleMouseLeave&&e.handleMouseLeave(...d)),onClick:l[24]||(l[24]=Z((...d)=>e.toggleMenu&&e.toggleMenu(...d),["stop"]))},[de(m,{ref:"tooltipRef",visible:e.dropMenuVisible,placement:e.placement,teleported:e.teleported,"popper-class":[e.nsSelect.e("popper"),e.popperClass],"popper-options":e.popperOptions,"fallback-placements":["bottom-start","top-start","right","left"],effect:e.effect,pure:"",trigger:"click",transition:`${e.nsSelect.namespace.value}-zoom-in-top`,"stop-popper-mouse-event":!1,"gpu-acceleration":!1,persistent:e.persistent,onShow:e.handleMenuEnter},{default:T(()=>{var d,C;return[z("div",{class:"select-trigger",onMouseenter:l[20]||(l[20]=t=>e.inputHovering=!0),onMouseleave:l[21]||(l[21]=t=>e.inputHovering=!1)},[e.multiple?(h(),M("div",{key:0,ref:"tags",tabindex:"-1",class:y(e.tagsKls),style:oe(e.selectTagsStyle),onClick:l[15]||(l[15]=(...t)=>e.focus&&e.focus(...t))},[e.collapseTags&&e.selected.length?(h(),F(Ml,{key:0,onAfterLeave:e.resetInputHeight},{default:T(()=>[z("span",{class:y(e.tagWrapperKls)},[(h(!0),M(Fe,null,bl(e.showTagList,t=>(h(),F(s,{key:e.getValueKey(t),closable:!e.selectDisabled&&!t.isDisabled,size:e.collapseTagSize,hit:t.hitState,type:e.tagType,"disable-transitions":"",onClose:O=>e.deleteTag(O,t)},{default:T(()=>[z("span",{class:y(e.nsSelect.e("tags-text")),style:oe(e.tagTextStyle)},x(t.currentLabel),7)]),_:2},1032,["closable","size","hit","type","onClose"]))),128)),e.selected.length>e.maxCollapseTags?(h(),F(s,{key:0,closable:!1,size:e.collapseTagSize,type:e.tagType,"disable-transitions":""},{default:T(()=>[e.collapseTagsTooltip?(h(),F(m,{key:0,ref:"tagTooltipRef",disabled:e.dropMenuVisible,"fallback-placements":["bottom","top","right","left"],effect:e.effect,placement:"bottom",teleported:e.teleported},{default:T(()=>[z("span",{class:y(e.nsSelect.e("tags-text"))},"+ "+x(e.selected.length-e.maxCollapseTags),3)]),content:T(()=>[z("div",{class:y(e.nsSelect.e("collapse-tags"))},[(h(!0),M(Fe,null,bl(e.collapseTagList,t=>(h(),M("div",{key:e.getValueKey(t),class:y(e.nsSelect.e("collapse-tag"))},[de(s,{class:"in-tooltip",closable:!e.selectDisabled&&!t.isDisabled,size:e.collapseTagSize,hit:t.hitState,type:e.tagType,"disable-transitions":"",style:{margin:"2px"},onClose:O=>e.handleDeleteTooltipTag(O,t)},{default:T(()=>[z("span",{class:y(e.nsSelect.e("tags-text")),style:oe({maxWidth:e.inputWidth-75+"px"})},x(t.currentLabel),7)]),_:2},1032,["closable","size","hit","type","onClose"])],2))),128))],2)]),_:1},8,["disabled","effect","teleported"])):(h(),M("span",{key:1,class:y(e.nsSelect.e("tags-text"))},"+ "+x(e.selected.length-e.maxCollapseTags),3))]),_:1},8,["size","type"])):B("v-if",!0)],2)]),_:1},8,["onAfterLeave"])):B("v-if",!0),e.collapseTags?B("v-if",!0):(h(),F(Ml,{key:1,onAfterLeave:e.resetInputHeight},{default:T(()=>[z("span",{class:y(e.tagWrapperKls),style:oe(e.prefixWidth&&e.selected.length?{marginLeft:`${e.prefixWidth}px`}:"")},[(h(!0),M(Fe,null,bl(e.selected,t=>(h(),F(s,{key:e.getValueKey(t),closable:!e.selectDisabled&&!t.isDisabled,size:e.collapseTagSize,hit:t.hitState,type:e.tagType,"disable-transitions":"",onClose:O=>e.deleteTag(O,t)},{default:T(()=>[z("span",{class:y(e.nsSelect.e("tags-text")),style:oe({maxWidth:e.inputWidth-75+"px"})},x(t.currentLabel),7)]),_:2},1032,["closable","size","hit","type","onClose"]))),128))],6)]),_:1},8,["onAfterLeave"])),e.filterable&&!e.selectDisabled?we((h(),M("input",{key:2,ref:"input","onUpdate:modelValue":l[0]||(l[0]=t=>e.query=t),type:"text",class:y(e.inputKls),disabled:e.selectDisabled,autocomplete:e.autocomplete,style:oe(e.inputStyle),role:"combobox","aria-activedescendant":((d=e.hoverOption)==null?void 0:d.id)||"","aria-controls":e.contentId,"aria-expanded":e.dropMenuVisible,"aria-label":e.ariaLabel,"aria-autocomplete":"none","aria-haspopup":"listbox",onFocus:l[1]||(l[1]=(...t)=>e.handleFocus&&e.handleFocus(...t)),onBlur:l[2]||(l[2]=(...t)=>e.handleBlur&&e.handleBlur(...t)),onKeyup:l[3]||(l[3]=(...t)=>e.managePlaceholder&&e.managePlaceholder(...t)),onKeydown:[l[4]||(l[4]=(...t)=>e.resetInputState&&e.resetInputState(...t)),l[5]||(l[5]=W(Z(t=>e.navigateOptions("next"),["prevent"]),["down"])),l[6]||(l[6]=W(Z(t=>e.navigateOptions("prev"),["prevent"]),["up"])),l[7]||(l[7]=W((...t)=>e.handleKeydownEscape&&e.handleKeydownEscape(...t),["esc"])),l[8]||(l[8]=W(Z((...t)=>e.selectOption&&e.selectOption(...t),["stop","prevent"]),["enter"])),l[9]||(l[9]=W((...t)=>e.deletePrevTag&&e.deletePrevTag(...t),["delete"])),l[10]||(l[10]=W(t=>e.visible=!1,["tab"]))],onCompositionstart:l[11]||(l[11]=(...t)=>e.handleComposition&&e.handleComposition(...t)),onCompositionupdate:l[12]||(l[12]=(...t)=>e.handleComposition&&e.handleComposition(...t)),onCompositionend:l[13]||(l[13]=(...t)=>e.handleComposition&&e.handleComposition(...t)),onInput:l[14]||(l[14]=(...t)=>e.debouncedQueryChange&&e.debouncedQueryChange(...t))},null,46,Fn)),[[dn,e.query]]):B("v-if",!0)],6)):B("v-if",!0),e.isIOS&&!e.multiple&&e.filterable&&e.readonly?(h(),M("input",{key:1,ref:"iOSInput",class:y(e.iOSInputKls),disabled:e.selectDisabled,type:"text"},null,10,Wn)):B("v-if",!0),de(w,{id:e.id,ref:"reference",modelValue:e.selectedLabel,"onUpdate:modelValue":l[16]||(l[16]=t=>e.selectedLabel=t),type:"text",placeholder:typeof e.currentPlaceholder=="function"?e.currentPlaceholder():e.currentPlaceholder,name:e.name,autocomplete:e.autocomplete,size:e.selectSize,disabled:e.selectDisabled,readonly:e.readonly,"validate-event":!1,class:y([e.nsSelect.is("focus",e.visible)]),tabindex:e.multiple&&e.filterable?-1:void 0,role:"combobox","aria-activedescendant":((C=e.hoverOption)==null?void 0:C.id)||"","aria-controls":e.contentId,"aria-expanded":e.dropMenuVisible,label:e.ariaLabel,"aria-autocomplete":"none","aria-haspopup":"listbox",onFocus:e.handleFocus,onBlur:e.handleBlur,onInput:e.debouncedOnInputChange,onPaste:e.debouncedOnInputChange,onCompositionstart:e.handleComposition,onCompositionupdate:e.handleComposition,onCompositionend:e.handleComposition,onKeydown:[l[17]||(l[17]=W(Z(t=>e.navigateOptions("next"),["stop","prevent"]),["down"])),l[18]||(l[18]=W(Z(t=>e.navigateOptions("prev"),["stop","prevent"]),["up"])),W(Z(e.selectOption,["stop","prevent"]),["enter"]),W(e.handleKeydownEscape,["esc"]),l[19]||(l[19]=W(t=>e.visible=!1,["tab"]))]},Vl({suffix:T(()=>[e.iconComponent&&!e.showClose?(h(),F(u,{key:0,class:y([e.nsSelect.e("caret"),e.nsSelect.e("icon"),e.iconReverse])},{default:T(()=>[(h(),F($l(e.iconComponent)))]),_:1},8,["class"])):B("v-if",!0),e.showClose&&e.clearIcon?(h(),F(u,{key:1,class:y([e.nsSelect.e("caret"),e.nsSelect.e("icon")]),onClick:e.handleClearClick},{default:T(()=>[(h(),F($l(e.clearIcon)))]),_:1},8,["class","onClick"])):B("v-if",!0)]),_:2},[e.$slots.prefix?{name:"prefix",fn:T(()=>[z("div",Nn,[j(e.$slots,"prefix")])])}:void 0]),1032,["id","modelValue","placeholder","name","autocomplete","size","disabled","readonly","class","tabindex","aria-activedescendant","aria-controls","aria-expanded","label","onFocus","onBlur","onInput","onPaste","onCompositionstart","onCompositionupdate","onCompositionend","onKeydown"])],32)]}),content:T(()=>[de(E,null,Vl({default:T(()=>[we(de(I,{id:e.contentId,ref:"scrollbar",tag:"ul","wrap-class":e.nsSelect.be("dropdown","wrap"),"view-class":e.nsSelect.be("dropdown","list"),class:y(e.scrollbarKls),role:"listbox","aria-label":e.ariaLabel,"aria-orientation":"vertical"},{default:T(()=>[e.showNewOption?(h(),F(b,{key:0,value:e.query,created:!0},null,8,["value"])):B("v-if",!0),de(g,{onUpdateOptions:e.onOptionsRendered},{default:T(()=>[j(e.$slots,"default")]),_:3},8,["onUpdateOptions"])]),_:3},8,["id","wrap-class","view-class","class","aria-label"]),[[Cl,e.options.size>0&&!e.loading]]),e.emptyText&&(!e.allowCreate||e.loading||e.allowCreate&&e.options.size===0)?(h(),M(Fe,{key:0},[e.$slots.empty?j(e.$slots,"empty",{key:0}):(h(),M("p",{key:1,class:y(e.nsSelect.be("dropdown","empty"))},x(e.emptyText),3))],64)):B("v-if",!0)]),_:2},[e.$slots.header?{name:"header",fn:T(()=>[j(e.$slots,"header")])}:void 0,e.$slots.footer?{name:"footer",fn:T(()=>[j(e.$slots,"footer")])}:void 0]),1024)]),_:3},8,["visible","placement","teleported","popper-class","popper-options","effect","transition","persistent","onShow"])],34)),[[L,e.handleClose,e.popperPaneRef]])}var Hn=Re(Bn,[["render",Rn],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/select/src/select.vue"]]);const Qn=Ie({name:"ElOptionGroup",componentName:"ElOptionGroup",props:{label:String,disabled:Boolean},setup(e){const l=ce("select"),a=K(!0),v=gl(),p=K([]);Bl(Wl,He({...yl(e)}));const f=We(Qe);Ol(()=>{p.value=s(v.subTree)});const s=u=>{const w=[];return Array.isArray(u.children)&&u.children.forEach(b=>{var g;b.type&&b.type.name==="ElOption"&&b.component&&b.component.proxy?w.push(b.component.proxy):(g=b.children)!=null&&g.length&&w.push(...s(b))}),w},{groupQueryChange:m}=Ne(f);return R(m,()=>{a.value=p.value.some(u=>u.visible===!0)},{flush:"post"}),{visible:a,ns:l}}});function jn(e,l,a,v,p,f){return we((h(),M("ul",{class:y(e.ns.be("group","wrap"))},[z("li",{class:y(e.ns.be("group","title"))},x(e.label),3),z("li",null,[z("ul",{class:y(e.ns.b("group"))},[j(e.$slots,"default")],2)])],2)),[[Cl,e.visible]])}var Nl=Re(Qn,[["render",jn],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/select/src/option-group.vue"]]);const eo=cn(Hn,{Option:wl,OptionGroup:Nl}),lo=Fl(wl),no=Fl(Nl);export{lo as E,eo as a,no as b,wn as t};
