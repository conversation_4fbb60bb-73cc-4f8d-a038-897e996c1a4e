import{_ as Zt}from"./theme-light_empty-0081a108.js";import{q as Me,r as V,w as Se,ac as _t,o as Be,x as s,B as y,G as i,z as v,y as le,J as we,ai as q,aj as ne,ak as fe,Q as u,P as a,H as rt,ah as Nt,al as ct,am as ea,an as ta,A as e,aU as aa,ao as da,hh as ma,ab as Rt,b0 as lt,s as Tt,aS as je,aE as ya,aX as na,ad as st,R as Oe,a5 as Ct,D as xe,aZ as wt,c3 as Y,a9 as xt,aG as vt,gu as fa,bu as ga,hi as ha,at as Jt,a4 as ba,b3 as kt}from"./index-a2fbd71b.js";import{E as va,a as ka}from"./index-e149ca58.js";import{E as _a}from"./index-350461d4.js";import{b as Ta}from"./index-b5333773.js";import{_ as oa}from"./_plugin-vue_export-helper-c27b6911.js";import{E as it,a as pt}from"./index-68054860.js";import{E as St,a as $t}from"./index-96be5bee.js";import{E as la,a as sa}from"./index-7a3e9562.js";import{E as ra}from"./index-ac58700d.js";import{E as ca}from"./index-d88b3135.js";import{e as xa}from"./exceljs.min-bcd6e76f.js";import{s as Ie}from"./subtract-21d41b77.js";import{a as Na}from"./date-380b46f4.js";import{T as Ra}from"./TicketRefundForm-02cce8a1.js";import{P as Ca}from"./PrintNoSelect-2c6cab30.js";import{D as wa}from"./regular-crs-531dcd3f.js";import{h as Sa}from"./ticketOperationApi-4e3e854b.js";import{_ as $a}from"./Page.vue_vue_type_script_setup_true_lang-7acd8256.js";import{E as Ea}from"./index-3fe919b5.js";import{E as Da}from"./index-61b49c38.js";import"./isEqual-c950930c.js";import"./flatten-b73d2287.js";import"./index-fe3402a7.js";import"./isUndefined-aa0326a0.js";import"./dropdown-7f4180b8.js";import"./castArray-2ae6cabb.js";import"./refs-b36b7130.js";import"./strings-cc725bd5.js";import"./index-4e9a4449.js";import"./_createMathOperation-9624aa42.js";import"./refundUtil-46d54931.js";import"./html2canvas.esm-cf66fc0f.js";import"./add-2f19224f.js";import"./throttle-d22b7fb0.js";import"./index-e246ba26.js";import"./index-a1d2eacb.js";import"./index-819b3a8a.js";const ia=_=>(ea("data-v-b25f494f"),_=_(),ta(),_),Fa={class:"sales-daily-filter"},Aa=ia(()=>a("em",{class:"iconfont icon-down"},null,-1)),Oa=ia(()=>a("em",{class:"iconfont icon-up"},null,-1)),Ia={class:"filter-bottom-sales crs-btn-ui"},Pa={name:"FilterSelect"},Va=Me({...Pa,props:{filters:{},columnKey:{},showFilterInput:{type:Boolean,default:!1},filterInputPlaceholder:{default:""},notDisabled:{type:Boolean}},emits:["handleConfrim"],setup(_,{expose:r,emit:t}){const x=_,c=t,g=V(),k=V(!1),U=Se(()=>x.filters),O=V([]),F=V([]),j=V(""),L=E=>{k.value=E},B=E=>{k.value=E,R(E)},T=()=>k.value,R=E=>{E?g.value.handleOpen():g.value.handleClose()},I=()=>{O.value=U.value},f=()=>{k.value=!1,g.value.handleClose(),c("handleConfrim",x.columnKey,F.value,j.value)};_t(()=>U.value,()=>{I()},{deep:!0});const G=E=>{if(E.includes(",")){const $=E.split(",").filter(h=>h!==""&&h!=null).map(h=>U.value.filter(C=>C.indexOf(h)!==-1)).reduce((h,C)=>h.concat(C),[]);O.value=Array.from(new Set($))}else O.value=U.value.filter(A=>A.indexOf(E)!==-1)};Be(()=>{I()});const H=()=>{F.value=[],j.value="",I(),f()};return r({showDropDown:B,getVisibleValue:T}),(E,A)=>{const $=rt,h=Nt,C=va,Q=_a,ee=ka,J=ct,D=Ta;return s(),y("div",Fa,[i(D,{ref_key:"dropdown",ref:g,trigger:"contextmenu",placement:"bottom","popper-class":"sale-daily-filter-popper",onVisibleChange:L},{dropdown:v(()=>{var b;return[E.notDisabled&&E.showFilterInput?(s(),le(h,{key:0,modelValue:j.value,"onUpdate:modelValue":A[2]||(A[2]=d=>j.value=d),class:"filter-input",placeholder:E.filterInputPlaceholder,onInput:A[3]||(A[3]=d=>{j.value=j.value.toUpperCase(),G(j.value)})},null,8,["modelValue","placeholder"])):E.showFilterInput?(s(),le(h,{key:1,modelValue:j.value,"onUpdate:modelValue":A[4]||(A[4]=d=>j.value=d),disabled:F.value.length>0,class:"filter-input",placeholder:E.filterInputPlaceholder,onInput:A[5]||(A[5]=d=>G(j.value))},null,8,["modelValue","disabled","placeholder"])):we("",!0),i(ee,{modelValue:F.value,"onUpdate:modelValue":A[6]||(A[6]=d=>F.value=d)},{default:v(()=>[i(Q,{"max-height":"300px",height:"fitcontent","min-size":"20"},{default:v(()=>[(s(!0),y(q,null,ne(O.value,d=>(s(),le(C,{key:d,label:d,class:"scrollbar-demo-item"},{default:v(()=>[fe(u(d),1)]),_:2},1032,["label"]))),128))]),_:1})]),_:1},8,["modelValue"]),a("div",Ia,[i(J,{disabled:((b=F.value)==null?void 0:b.length)===0,link:"",type:"primary",size:"small",onClick:f},{default:v(()=>[fe(u(E.$t("app.agentReport.confirmBtn")),1)]),_:1},8,["disabled"]),a("div",{onClick:H},u(E.$t("app.agentReport.reset")),1)])]}),default:v(()=>[k.value?(s(),le($,{key:1,color:"var(--bkc-el-color-primary)",onClick:A[1]||(A[1]=b=>B(!1))},{default:v(()=>[Oa]),_:1})):(s(),le($,{key:0,color:"var(--bkc-el-color-primary)",onClick:A[0]||(A[0]=b=>B(!0))},{default:v(()=>[Aa]),_:1}))]),_:1},512)])}}});const Re=oa(Va,[["__scopeId","data-v-b25f494f"]]),Ua={key:1,class:"ml-4"},ja=Me({__name:"TicketTypeSelect",props:{ticketType:{},tktTypes:{}},emits:["update:ticket-type"],setup(_,{emit:r}){const t=r,x=c=>{t("update:ticket-type",c)};return(c,g)=>{const k=rt,U=it,O=pt;return s(),le(O,{"model-value":c.ticketType,placeholder:c.$t("app.agentReport.selectTicketType"),onChange:x},{default:v(()=>[(s(!0),y(q,null,ne(c.tktTypes,F=>(s(),le(U,{key:F.value,value:F.value,label:c.$t(`app.agentReport.${F.label}`)},{default:v(()=>[c.ticketType===F.value?(s(),le(k,{key:0,class:"bg-inherit"},{default:v(()=>[i(e(aa))]),_:1})):(s(),y("span",Ua)),fe(" "+u(c.$t(`app.agentReport.${F.label}`)),1)]),_:2},1032,["value","label"]))),128))]),_:1},8,["model-value","placeholder"])}}}),ot=_=>da(`${ma}/crs/apiReport/crsDailySales`).post(_).json(),Ba=/^[1-9][0-9]{0,4}$/,Ma=(_,r)=>{const{t}=Rt(),x=V(),c=V({saleStatusCode:["ISSU","RFND"],deviceNumber:"",tktType:"BSP"}),g=V(["ISSU","RFND"]),k=V(),U=lt({saleStatusCode:[{required:!0,message:t("app.agentReport.selectStatus"),trigger:"change"}],deviceNumber:[{required:!0,message:t("app.agentReport.printTicketNumber"),trigger:"change"},{pattern:Ba,message:t("app.agentReport.deviceNumberErr"),trigger:["change","blur"]}]}),O=V([]),F=V({}),j=V([]),L=Tt(!1),B=V(!1);let T={};const R=V([]);let I=[];const f=p=>!p||p==="",G=async p=>{const M=document.createElement("input");M.value=p,document.body.appendChild(M),M.select(),document.execCommand("Copy"),M.style.display="none",na({message:t("app.agentReport.tip"),type:"success",duration:2*1e3})},H=async p=>{O.value[p].showDropDown(!O.value[p].getVisibleValue())},E=p=>{d(),st.setLink(`/v2/crs/pnrManagement?pnrNo=${p}`)},A=(p,M)=>{d(),st.setLink(`/v2/crs/ticketOperation?ticketNumber=${p}&secondFactorCode=CN&secondFactorValue=${M??""}`)},$=p=>{var te;const M=[];return(((te=F.value)==null?void 0:te.items)??[]).forEach(se=>{se[p]&&!M.includes(se[p])&&M.push(se[p])}),M},h=p=>p.ticketType==="4"&&p.passengerType==="Y"?t("app.ticketType.GPDomesticTicket"):p.ticketType==="2"&&p.passengerType==="Y"?t("app.ticketType.GPInternationalTicket"):t("app.ticketType.BSPDomesticTicket"),C=p=>{switch(p==null?void 0:p.ticketType){case"1":return t("app.ticketType.internationalAirlineTickets");case"2":return t("app.ticketType.BSPInternationalTicket");case"3":return t("app.ticketType.domesticAirlineTickets");case"4":return h(p);case"7":return t("app.ticketType.BOPTicket");default:return p==null?void 0:p.ticketType}},Q=()=>{const p={};p.ticketTypes=$("saleStatusCode"),p.jobNos=$("agent"),p.ticketKinds=$("ticketType"),p.payTypes=$("paymentTypeCode"),F.value.filter=p},ee=()=>{T={},k.value={},R.value=[],F.value={},I=[]},J=async()=>{var p;(p=x.value)==null||p.validate(async M=>{var se,ve,re,me,Z,m,oe,ke,_e,X;if(!M)return;B.value=!1,ee();const te={deviceNumber:c.value.deviceNumber,saleStatusCodes:c.value.saleStatusCode,tktType:c.value.tktType,value:"2",date:Oe().format("YYYY-MM-DD"),outerSign:!1};L.value=!0;try{const ye=(await ot(te)).data.value;ye.items=(se=ye==null?void 0:ye.items)==null?void 0:se.map(w=>({...w,ticketType:C(w)})),F.value=ye,k.value={...ye.queryTSLHeader,ticketingDate:(ve=ye==null?void 0:ye.queryTSLHeader)!=null&&ve.ticketingDate?Oe(ye.queryTSLHeader.ticketingDate).format("YYYY-MM-DD"):""},Q(),j.value=je((re=F.value)==null?void 0:re.items),I=((me=F.value)==null?void 0:me.items)??[];const Pe=((Z=r==null?void 0:r.salesForm)==null?void 0:Z.prntNo)===te.deviceNumber&&((m=r==null?void 0:r.salesForm)==null?void 0:m.tktType)===te.tktType;(oe=r==null?void 0:r.salesForm)!=null&&oe.tktType&&Pe&&_("errorNumber",(ke=F==null?void 0:F.value)==null?void 0:ke.totalErrorNumber,(_e=F==null?void 0:F.value)==null?void 0:_e.issueErrorNumber,(X=F==null?void 0:F.value)==null?void 0:X.refundErrorNumber)}finally{L.value=!1}})},D=(p,M)=>{if(M!=null&&M.length)R.value.includes(p)||R.value.push(p);else{const te=R.value.indexOf(p);te!==-1&&R.value.splice(te,1)}},b=(p,M,te)=>{var ve,re,me,Z,m,oe,ke,_e;D(p,M),B.value=!0;const se=je(M);te&&se.push(te),T[p]=se,I=((ve=F.value)==null?void 0:ve.items)??[],((re=T==null?void 0:T.airline)==null?void 0:re.length)>0&&(I=I.filter(X=>T.airline.includes(X.airline))),((me=T==null?void 0:T.prntNo)==null?void 0:me.length)>0&&(I=I.filter(X=>T.prntNo.includes(X.pnr))),((Z=T==null?void 0:T.ticketStatus)==null?void 0:Z.length)>0&&(I=I.filter(X=>T.ticketStatus.includes(X.saleStatusCode)),I.sort((X,ye)=>X.salesDateTime.localeCompare(ye.salesDateTime))),((m=T==null?void 0:T.payType)==null?void 0:m.length)>0&&(I=I.filter(X=>T.payType.includes(X.paymentTypeCode))),((oe=T==null?void 0:T.jobNo)==null?void 0:oe.length)>0&&(I=I.filter(X=>T.jobNo.includes(X.agent))),((ke=T==null?void 0:T.ticketType)==null?void 0:ke.length)>0&&(I=I.filter(X=>T.ticketType.includes(X.ticketType))),((_e=T==null?void 0:T.currencyType)==null?void 0:_e.length)>0&&(I=I.filter(X=>T.currencyType.includes(X.currency))),j.value=I},d=()=>{_("update:modelValue",!1)};return Be(()=>{var M,te,se,ve,re,me,Z,m,oe,ke,_e,X;c.value.saleStatusCode=g.value.map(ye=>ye),c.value.deviceNumber=(M=r==null?void 0:r.salesForm)==null?void 0:M.prntNo,c.value.tktType=((te=r==null?void 0:r.salesForm)==null?void 0:te.tktType)??"BSP",((se=c.value)==null?void 0:se.tktType)===((ve=r==null?void 0:r.salesForm)==null?void 0:ve.tktType)&&((re=c.value)==null?void 0:re.deviceNumber)===((me=r==null?void 0:r.salesForm)==null?void 0:me.prntNo)&&c.value.saleStatusCode.includes("ISSU")&&c.value.saleStatusCode.includes("RFND")&&((oe=(m=(Z=r==null?void 0:r.storeTodayError)==null?void 0:Z.querySaleDailyErrorRes)==null?void 0:m.items)==null?void 0:oe.length)>0&&(F.value=(ke=r==null?void 0:r.storeTodayError)==null?void 0:ke.querySaleDailyErrorRes,j.value=je((_e=F.value)==null?void 0:_e.items),I=((X=F.value)==null?void 0:X.items)??[])}),ya(()=>{U.deviceNumber[0].required=["BSP","CDS","PYN"].includes(c.value.tktType)}),{todayExceptionsFormRef:x,todayExceptionsForm:c,saleStatusCodeList:g,todayExceptionsRules:U,isLoading:L,todayExceptionsList:j,filterRptRef:O,filterTypeList:R,querySaleDailyRes:F,headDetail:k,handleSearch:J,buildTicketType:C,isEmptyData:f,filterChange:b,jumpToPnrEtQuery:E,jumpToTcTicketQuery:A,doCopy:G,openFilter:H,closeDialog:d}},Ha=Ma,La={class:"today-exceptions-dialog-content"},za={class:"flex"},Ya={key:0,class:"h-8 w-full px-2.5 bg-[#f6f6f6] rounded justify-start items-center gap-2.5 flex flex-1"},Ga=a("span",{class:"text-[#8c8c8c] text-xs font-normal leading-tight"},"OFFICE",-1),Qa={class:"text-neutral-800 text-xs font-normal leading-tight"},Xa=a("span",{class:"text-[#8c8c8c] text-xs font-normal leading-tight"},"IATA NO",-1),qa={class:"text-neutral-800 text-xs font-normal leading-tight"},Ka={class:"text-[#8c8c8c] text-xs font-normal leading-tight"},Wa={class:"text-neutral-800 text-xs font-normal leading-tight"},Ja={key:0,class:"result"},Za=["onClick"],en=["onClick"],tn={key:1},an=["onClick"],nn=["onClick"],on={key:1,class:"empty-info"},ln=a("img",{src:Zt,alt:"$t('app.agentReport.noErrordata')"},null,-1),sn={class:"main-info"},rn=Me({__name:"TodayExceptionsDialog",props:{salesForm:{},storeTodayError:{},tktTypes:{}},emits:["update:modelValue","errorNumber"],setup(_,{emit:r}){const t=_,x=r,{todayExceptionsFormRef:c,todayExceptionsForm:g,headDetail:k,saleStatusCodeList:U,todayExceptionsRules:O,isLoading:F,todayExceptionsList:j,filterRptRef:L,filterTypeList:B,querySaleDailyRes:T,handleSearch:R,openFilter:I,filterChange:f,jumpToTcTicketQuery:G,doCopy:H,isEmptyData:E,jumpToPnrEtQuery:A,closeDialog:$}=Ha(x,t);return(h,C)=>{const Q=St,ee=it,J=pt,D=Nt,b=ct,d=$t,p=la,M=ra,te=sa,se=ca,ve=wt;return s(),le(se,{width:"1000",title:h.$t("app.agentReport.abnormalRecords"),"close-on-click-modal":!1,class:"today-exceptions-dialog",onClose:e($)},{footer:v(()=>[i(b,{class:"w-[80px]",onClick:e($)},{default:v(()=>[fe(u(h.$t("app.agentReport.close")),1)]),_:1},8,["onClick"])]),default:v(()=>{var re,me,Z;return[Ct((s(),y("div",La,[a("div",za,[i(d,{ref_key:"todayExceptionsFormRef",ref:c,model:e(g),inline:!0,rules:e(O),"require-asterisk-position":"right"},{default:v(()=>[i(Q,{label:h.$t("app.agentReport.tktType"),class:"ticket-type",required:""},{default:v(()=>[i(ja,{"tkt-types":h.tktTypes,"ticket-type":e(g).tktType,"onUpdate:ticketType":C[0]||(C[0]=m=>e(g).tktType=m)},null,8,["tkt-types","ticket-type"])]),_:1},8,["label"]),i(Q,{label:h.$t("app.agentReport.saleStatus"),prop:"saleStatusCode",class:"sale-status"},{default:v(()=>[i(J,{modelValue:e(g).saleStatusCode,"onUpdate:modelValue":C[1]||(C[1]=m=>e(g).saleStatusCode=m),multiple:"","collapse-tags":"",placeholder:h.$t("app.agentReport.selectTicketType")},{default:v(()=>[(s(!0),y(q,null,ne(e(U),m=>(s(),le(ee,{key:m,label:m,value:m},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"])]),_:1},8,["label"]),i(Q,{label:h.$t("app.agentReport.ticketMachineNumber"),prop:"deviceNumber",class:"print-ticket-number"},{default:v(()=>[i(D,{modelValue:e(g).deviceNumber,"onUpdate:modelValue":C[2]||(C[2]=m=>e(g).deviceNumber=m),modelModifiers:{trim:!0},size:"default",clearable:"",placeholder:h.$t("app.agentReport.printTicketNumber")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),i(Q,null,{default:v(()=>[i(b,{"data-gid":"11280201",type:"primary",onClick:e(R)},{default:v(()=>[fe(u(h.$t("app.agentReport.search")),1)]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model","rules"]),(re=e(k))!=null&&re.office?(s(),y("div",Ya,[a("div",null,[Ga,a("span",Qa,"："+u(e(k).office),1)]),a("div",null,[Xa,a("span",qa,"："+u(e(k).iataNumber),1)]),a("div",null,[a("span",Ka,u(h.$t("app.agentReport.saleDate")),1),a("span",Wa,"："+u(e(k).ticketingDate),1)])])):we("",!0)]),(Z=(me=e(T))==null?void 0:me.items)!=null&&Z.length?(s(),y("div",Ja,[i(te,{ref:"todayExceptionsRef",height:"550px",data:e(j),stripe:"",style:{"min-width":"100%"},class:"sales-daily-table",onFilterChange:e(f)},{default:v(()=>[i(p,{prop:"ticket",label:h.$t("app.agentReport.tktNo"),width:"150px",flexible:"true"},{default:v(({row:m})=>[a("span",{class:"pointer-span",onClick:oe=>e(G)(m.ticket,m.pnr)},u(m.ticket),9,Za),a("em",{class:"pointer-span iconfont icon-copy copy-em",onClick:oe=>e(H)(m.ticket)},null,8,en)]),_:1},8,["label"]),i(p,{prop:"ticketStatus",width:"110px"},{header:v(()=>{var m;return[a("span",{class:xe(["pointer-span-drop",e(B).includes("ticketStatus")?"text-brand-2":""]),onClick:C[3]||(C[3]=oe=>e(I)(1))},u(h.$t("app.agentReport.tktStatus")),3),i(Re,{ref:oe=>{oe&&(e(L)[1]=oe)},filters:(m=e(T).filter)==null?void 0:m.ticketTypes,"column-key":"ticketStatus",onHandleConfrim:e(f)},null,8,["filters","onHandleConfrim"])]}),default:v(({row:m})=>[i(M,{class:"tag"},{default:v(()=>[fe(u(m.ticketStatus),1)]),_:2},1024)]),_:1}),i(p,{prop:"jobNo","min-width":"80px"},{header:v(()=>[a("span",{class:xe(["pointer-span-drop",e(B).includes("jobNo")?"text-brand-2":""]),onClick:C[4]||(C[4]=m=>e(I)(3))},u(h.$t("app.agentReport.agent")),3),i(Re,{ref:m=>{m&&(e(L)[3]=m)},filters:e(T).filter.jobNos,"show-filter-input":!0,"not-disabled":!0,"filter-input-placeholder":h.$t("app.agentReport.agentTip"),"column-key":"jobNo",onHandleConfrim:e(f)},null,8,["filters","filter-input-placeholder","onHandleConfrim"])]),_:1}),i(p,{prop:"desArr",label:h.$t("app.agentReport.segSE"),"min-width":"90px"},null,8,["label"]),i(p,{prop:"pnr",label:"PNR","min-width":"105px"},{default:v(({row:m})=>[e(E)(m.pnr)?(s(),y(q,{key:0},[],64)):(s(),y("div",tn,[a("span",{class:"pointer-span",onClick:oe=>e(A)(m.pnr)},u(m.pnr),9,an),a("em",{class:"pointer-span iconfont icon-copy copy-em",onClick:oe=>e(H)(m.pnr)},null,8,nn)]))]),_:1}),i(p,{prop:"ticketType","min-width":"125px"},{header:v(()=>[a("span",{class:xe(["pointer-span-drop",e(B).includes("ticketType")?"text-brand-2":""]),onClick:C[5]||(C[5]=m=>e(I)(6))},u(h.$t("app.agentReport.tktType")),3),i(Re,{ref:m=>{m&&(e(L)[6]=m)},filters:e(T).filter.ticketKinds,"column-key":"ticketType",onHandleConfrim:e(f)},null,8,["filters","onHandleConfrim"])]),_:1}),i(p,{prop:"payType","min-width":"100px"},{header:v(()=>[a("span",{class:xe(["pointer-span-drop",e(B).includes("payType")?"text-brand-2":""]),onClick:C[6]||(C[6]=m=>e(I)(4))},u(h.$t("app.agentReport.payment")),3),i(Re,{ref:m=>{m&&(e(L)[4]=m)},filters:e(T).filter.payTypes,"column-key":"payType",onHandleConfrim:e(f)},null,8,["filters","onHandleConfrim"])]),_:1})]),_:1},8,["data","onFilterChange"])])):(s(),y("div",on,[ln,a("div",sn,u(h.$t("app.agentReport.noErrordata")),1)]))])),[[ve,e(F)]])]}),_:1},8,["title","onClose"])}}});const cn=[{name:Y.global.t("app.agentReport.tktNo"),wch:14.75},{name:Y.global.t("app.agentReport.segSE"),wch:10.5},{name:Y.global.t("app.agentReport.tktSettle"),wch:15.63},{name:Y.global.t("app.agentReport.tax"),wch:12},{name:Y.global.t("app.agentReport.agency"),wch:12},{name:Y.global.t("app.agentReport.agencyRate"),wch:12},{name:"PNR",wch:9.75},{name:Y.global.t("app.agentReport.agent"),wch:8.63},{name:Y.global.t("app.agentReport.tktType"),wch:13.5},{name:Y.global.t("app.agentReport.ticketMachineNumber"),wch:8.75},{name:Y.global.t("app.agentReport.payment"),wch:8.75}],pn=_=>{const r=(_.items??[]).filter(t=>t.ticketStatus==="ISSU"||t.ticketStatus==="EXCH").map(t=>({ticket:t.ticket,desArr:t.desArr,amount:t.amount,taxAmount:t.taxAmount,agencyFee:t.agencyFee,agencyFeePercent:t.agencyFeePercent,pnr:t.pnr,jobNo:t.jobNo,tktType:t.ticketType,ticketMachineNumber:t.prntNo,payment:t.payType}));return{officeNo:_==null?void 0:_.office,iataNO:_==null?void 0:_.iata,salesDate:_.items[0].salesDate,items:r}},un=_=>{const r={},t={},x={},c={};return(_??[]).forEach(g=>{r[g.currencyType]=r[g.currencyType]?r[g.currencyType]+Number(g.amount):Number(g.amount),t[g.currencyType]=t[g.currencyType]?t[g.currencyType]+Number(g.taxAmount):Number(g.taxAmount),x[g.currencyType]=x[g.currencyType]?x[g.currencyType]+Number(g.agencyFee):Number(g.agencyFee);const k=Ie(Number(g.amount),Number(g.agencyFee));c[g.currencyType]=c[g.currencyType]?c[g.currencyType]+k:k}),{totalAmount:r,taxAmount:t,agencyFee:x,carriers:c}},dn=(_,r)=>{const t=r.addWorksheet(Y.global.t("app.agentReport.issueChangeReportSheet"));t.properties.defaultRowHeight=17,t.mergeCells("A1:K1"),t.getRow(1).height=30;const x=t.getCell("A1");x.value=Y.global.t("app.agentReport.issueChangeReport"),x.alignment={vertical:"middle",horizontal:"center"},x.font={size:14,bold:!0};const c=pn(_),g=(c==null?void 0:c.officeNo)??"",k=(c==null?void 0:c.iataNO)??"",U=(c==null?void 0:c.salesDate)??"",O=[`OFFICE:${g}`,null,`IATA NO:${k}`,null,null,`日期:${U}`],F=t.addRow(O);F.font={size:14,bold:!0};const j=t.addRow([]);j.height=9;const L=[],B=[];cn.forEach(D=>{L.push(D.name),B.push({wch:D.wch})}),t.addRow(L).eachCell(D=>{D.font={bold:!0}}),t.columns.forEach((D,b)=>{D.width=B[b].wch});const I=[3,4,5,6];(c.items??[]).forEach(D=>{t.addRow(Object.values(D)).eachCell((d,p)=>{I.includes(p)&&(d.value=Number(d.value),d.numFmt="0.00",d.alignment={horizontal:"left"})})});const f=t.addRow([]),{totalAmount:G,taxAmount:H,agencyFee:E,carriers:A}=un(_.items.filter(D=>D.ticketStatus==="ISSU"||D.ticketStatus==="EXCH")),$=["NORMAL FARE","-- AMOUNT："];for(const D in G)if(G[D]){const b=G[D];$.push(b),$.push(D)}const h=["NORMAL TAX","-- AMOUNT："];for(const D in H)if(H[D]){const b=H[D];h.push(b),h.push(D)}const C=["NORMAL COMMIT","-- AMOUNT："];for(const D in E)if(E[D]){const b=E[D];C.push(b),C.push(D)}const Q=["CARRIERS","-- AMOUNT："];for(const D in A)if(A[D]){const b=A[D];Q.push(b),Q.push(D)}t.addRow($),t.addRow(Q),t.addRow(h),t.addRow(C);const ee=[1,3,4],J=f.number+1;for(let D=J;D<=J+3;D++)for(let b=1;b<=11;b++){const d=t.getCell(D,b);ee.includes(b)&&(d.alignment={horizontal:"right"}),b==3&&(d.value=Number(d.value),d.numFmt="0.00"),d.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFDCE6F1"}}}},mn=[{name:Y.global.t("app.agentReport.tktNo"),wch:14.75},{name:Y.global.t("app.agentReport.refundOrder"),wch:10.5},{name:Y.global.t("app.agentReport.refundAmount"),wch:15.63},{name:Y.global.t("app.agentReport.handFee"),wch:12},{name:Y.global.t("app.agentReport.refundTax"),wch:12},{name:Y.global.t("app.agentReport.agency"),wch:9.75},{name:Y.global.t("app.agentReport.agencyRate"),wch:9.75},{name:Y.global.t("app.agentReport.agent"),wch:8.63},{name:Y.global.t("app.agentReport.tktType"),wch:13.5},{name:Y.global.t("app.agentReport.ticketMachineNumber"),wch:8.75},{name:Y.global.t("app.agentReport.payment"),wch:8.75}],yn=_=>{const r=(_.items??[]).filter(t=>t.ticketStatus==="RFND").map(t=>({ticket:t.ticket,refundNo:!t.refundNo||t.refundNo===""?"-":t.refundNo,amount:t.amount,serviceCharge:t.serviceCharge,taxAmount:t.taxAmount,agencyFee:t.agencyFee,agencyFeePercent:t.agencyFeePercent,jobNo:t.jobNo,tktType:t.ticketType,ticketMachineNumber:t.prntNo,payment:t.payType}));return{officeNo:_==null?void 0:_.office,iataNO:_==null?void 0:_.iata,salesDate:_.items[0].salesDate,items:r}},fn=_=>{const r={},t={},x={},c={},g={};return(_??[]).forEach(k=>{r[k.currencyType]=r[k.currencyType]?r[k.currencyType]+Number(k.amount):Number(k.amount),t[k.currencyType]=t[k.currencyType]?t[k.currencyType]+Number(k.taxAmount):Number(k.taxAmount),x[k.currencyType]=x[k.currencyType]?x[k.currencyType]+Number(k.agencyFee):Number(k.agencyFee),c[k.currencyType]=c[k.currencyType]?c[k.currencyType]+Number(k.serviceCharge):Number(k.serviceCharge);const U=Ie(Number(k.amount),Number(k.agencyFee));g[k.currencyType]=g[k.currencyType]?g[k.currencyType]+U:U}),{totalAmount:r,taxAmount:t,agencyFee:x,refundAmount:c,carriers:g}},gn=(_,r)=>{const t=r.addWorksheet(Y.global.t("app.agentReport.refundReportSheet"));t.properties.defaultRowHeight=17,t.mergeCells("A1:K1"),t.getRow(1).height=30;const x=t.getCell("A1");x.value=Y.global.t("app.agentReport.refundReport"),x.alignment={vertical:"middle",horizontal:"center"},x.font={size:14,bold:!0};const c=yn(_),g=(c==null?void 0:c.officeNo)??"",k=(c==null?void 0:c.iataNO)??"",U=(c==null?void 0:c.salesDate)??"",O=[`OFFICE:${g}`,null,`IATA NO:${k}`,null,null,`日期:${U}`],F=t.addRow(O);F.font={size:14,bold:!0};const j=t.addRow([]);j.height=9;const L=[],B=[];mn.forEach(d=>{L.push(d.name),B.push({wch:d.wch})}),t.addRow(L).eachCell(d=>{d.font={bold:!0}}),t.columns.forEach((d,p)=>{d.width=B[p].wch});const I=[3,4,5,6,7];(c.items??[]).forEach(d=>{t.addRow(Object.values(d)).eachCell((M,te)=>{I.includes(te)&&(M.value=Number(M.value),M.numFmt="0.00",M.alignment={horizontal:"left"})})});const f=t.addRow([]),{totalAmount:G,taxAmount:H,agencyFee:E,refundAmount:A,carriers:$}=fn(_.items.filter(d=>d.ticketStatus==="RFND")),h=["REFUND FARE","-- AMOUNT："];for(const d in G)if(G[d]){const p=G[d];h.push(p),h.push(d)}const C=["REFUND TAX","-- AMOUNT："];for(const d in H)if(H[d]){const p=H[d];C.push(p),C.push(d)}const Q=["DEDUCTION","-- AMOUNT："];for(const d in A)if(A[d]){const p=A[d];Q.push(p),Q.push(d)}const ee=["COMMIT","-- AMOUNT："];for(const d in E)if(E[d]){const p=E[d];ee.push(p),ee.push(d)}const J=["CARRIERS","-- AMOUNT："];for(const d in $)if($[d]){const p=$[d];J.push(p),J.push(d)}t.addRow(h),t.addRow(J),t.addRow(C),t.addRow(Q),t.addRow(ee);const D=[1,3,4],b=f.number+1;for(let d=b;d<=b+4;d++)for(let p=1;p<=11;p++){const M=t.getCell(d,p);D.includes(p)&&(M.alignment={horizontal:"right"}),p==3&&(M.value=Number(M.value),M.numFmt="0.00"),M.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFDCE6F1"}}}},hn=[{name:Y.global.t("app.agentReport.tktNo"),wch:14.75},{name:Y.global.t("app.agentReport.airline"),wch:8.2},{name:Y.global.t("app.agentReport.ticketMachineNumber"),wch:10.5},{name:"PNR",wch:9.75},{name:Y.global.t("app.agentReport.agent"),wch:8.63},{name:Y.global.t("app.agentReport.tktType"),wch:13.5},{name:Y.global.t("app.agentReport.payment"),wch:8.75}],bn=_=>{const r=(_.items??[]).filter(t=>t.ticketStatus==="VOID").map(t=>({ticket:t.ticket,airline:t.airline,deviceNo:t.prntNo,pnr:t.pnr,jobNo:t.jobNo,tktType:t.ticketType,payment:t.payType}));return{officeNo:_==null?void 0:_.office,iataNO:_==null?void 0:_.iata,salesDate:_.items[0].salesDate,items:r}},vn=(_,r)=>{const t=r.addWorksheet(Y.global.t("app.agentReport.voidReportSheet"));t.properties.defaultRowHeight=17,t.mergeCells("A1:H1"),t.getRow(1).height=30;const x=t.getCell("A1");x.value=Y.global.t("app.agentReport.voidReport"),x.alignment={vertical:"middle",horizontal:"center"},x.font={size:14,bold:!0};const c=bn(_),g=(c==null?void 0:c.officeNo)??"",k=(c==null?void 0:c.iataNO)??"",U=(c==null?void 0:c.salesDate)??"",O=[`OFFICE:${g}`,null,`IATA NO:${k}`,null,null,`日期:${U}`],F=t.addRow(O);F.font={size:14,bold:!0};const j=t.addRow([]);j.height=9;const L=[],B=[];hn.forEach($=>{L.push($.name),B.push({wch:$.wch})}),t.addRow(L).eachCell($=>{$.font={bold:!0}}),t.columns.forEach(($,h)=>{var C;$.width=((C=B[h])==null?void 0:C.wch)??12}),(c.items??[]).forEach($=>{t.addRow(Object.values($))});const I=t.addRow([]),f=["NORMAL FARE","-- AMOUNT：",null,null],G=["NORMAL TAX","-- AMOUNT：",null,null],H=["NORMAL COMMIT","-- AMOUNT：",null,null];t.addRow(f),t.addRow(["CARRIERS","-- AMOUNT：",null,null]),t.addRow(G),t.addRow(H);const E=[1,3,4],A=I.number+1;for(let $=A;$<=A+3;$++)for(let h=1;h<=8;h++){const C=t.getCell($,h);E.includes(h)&&(C.alignment={horizontal:"right"}),h==3&&(C.value=Number(C.value),C.numFmt="0.00"),C.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFDCE6F1"}}}},kn=_=>{const{t:r}=Rt(),t=xt(),x=xt(),c=V(""),g=V(),k=V(""),U=V(),O=V(),F=V(!0),j=Se(()=>{var l;return((l=t.state.user)==null?void 0:l.externalLink)==="SAT"}),L=V(""),B=V(),T=V([]),R=V(),I=lt({querySaleDailyErrorRes:{},totalNumber:0,issueNumber:0,refundNumber:0}),f=V(),G=V(),H=V(0),E=V(0),A=V(0),$=V([]),h=V(),C=V({}),Q=V(0),ee=Tt(!1),J=V([]),D=V(!1);let b={};const d=Se(()=>{var l;return((l=x.state.user)==null?void 0:l.defaultOfficeInternational)??!1}),p=Se(()=>x.getters.userPreferences??{}),M=Se(()=>{var l;return((l=x.state.user)==null?void 0:l.entityType)??""}),te=[{label:"BSP",value:"BSP"},{label:"BOP",value:"BOP"},{label:"CDS",value:"CDS"},{label:"PYN",value:"PYN"},{label:"GP_BSP",value:"GP"}],se=V([]),ve=[{label:"AL",value:"AL"},{label:"CA",value:"CA"},{label:"CC",value:"CC"},{label:"CK",value:"CK"}],re=V([]),me=V(_.cmd),Z=V({pageNumber:1,pageSize:20});let m=[];const oe=V(!1),ke=V(!1),_e=V(!1),X=V({}),ye=V({}),Pe=l=>!l||l==="",w=lt({searchDate:Oe(new Date).format("YYYY-MM-DD"),prntNo:"",ticketNumber:"",isFilterTime:!0,tktType:"",filterPayType:"AL",filterPayTypeCopy:""}),ut=Tt({render(){return vt("em",{class:"iconfont icon-calendar"})}}),He=Se(()=>Oe(new Date).format("YYYY-MM-DD")===w.searchDate),dt=Se(()=>({searchDate:[{required:!0,message:r("app.agentReport.selectDate"),trigger:"change"}],prntNo:[{required:j.value&&He.value&&!(w.tktType==="BOP"||w.tktType==="GP"||w.tktType==="CDS"),message:r("app.agentReport.printTicketNumber"),trigger:"change"},{pattern:fa,message:r("app.agentReport.deviceNumberErr"),trigger:["change","blur"]}],ticketNum:[{required:!1,message:r("app.agentReport.ticketNumber"),trigger:"change"},{pattern:ga,message:r("app.agentReport.ticketNumTip"),trigger:["change","blur"]}],filterPayType:[{pattern:ha,message:r("app.agentReport.payTypeTip"),trigger:["change","blur"]}]})),mt=()=>{D.value||(w.isFilterTime?$.value=Ce(J.value):$.value=Ce(m))};function N(l){return l.ticketNo="",l.passengerName="",l.airlineCode="",l.ticketNoEnd="",l.currency="",l.payMethod="",l.ticketType="",l.segmentInfos={},l.segInfos={},l.cmdNo="",l.cmdOption="",l.operator="",l.office="",l.taxInfo={},l.taxInfos={},l.check="",l.passengerType="",l.conjunction=0,l.couponNo=["0"],l.grossRefund=0,l.refund="Y",l.remark="",l.ticketNoEnd="",l.netRefund=0,l.creditCard="",l.commission=0,l.commissionRate=0,l.deduction=0,l.querySuccess=!1,l.totalTaxs=0,l}const z=l=>{X.value=l,oe.value=!0},yt=async l=>{const P=document.createElement("input");P.value=l,document.body.appendChild(P),P.select(),document.execCommand("Copy"),P.style.display="none",na({message:r("app.agentReport.tip"),type:"success",duration:2*1e3})},$e=async l=>{h.value=l,ke.value=!0,L.value=l!=null&&l.ticketTypeCode?l.ticketTypeCode:""},Le=async l=>{T.value[l].showDropDown(!T.value[l].getVisibleValue())},Fe=async()=>{var l;(l=B.value)==null||l.handleSizeChange(20)},ze=l=>{st.setLink(`/v2/crs/pnrManagement?pnrNo=${l}`)},Ye=(l,P)=>{st.setLink(`/v2/crs/ticketOperation?ticketNumber=${l}&secondFactorCode=CN&secondFactorValue=${P??""}`)},Ce=l=>(l??[]).slice((Z.value.pageNumber-1)*Z.value.pageSize,Z.value.pageNumber*Z.value.pageSize),Ve=()=>{Z.value.pageNumber=1,b={},re.value=[],C.value={},f.value={},m=[]},Ge=l=>{const P=l??[],K={},ie={},be={},W={amount:{},taxAmount:{},agencyFee:{},carriers:{}},pe={amount:{},taxAmount:{},agencyFee:{},carriers:{}},ue={amount:{},taxAmount:{},agencyFee:{},carriers:{}},de={amount:{},taxAmount:{},agencyFee:{},carriers:{}};return P.forEach(o=>{if(o.ticketStatus==="ISSU"){W.amount[o.currencyType]=W.amount[o.currencyType]?W.amount[o.currencyType]+Number(o.amount):Number(o.amount),W.taxAmount[o.currencyType]=W.taxAmount[o.currencyType]?W.taxAmount[o.currencyType]+Number(o.taxAmount):Number(o.taxAmount),W.agencyFee[o.currencyType]=W.agencyFee[o.currencyType]?W.agencyFee[o.currencyType]+Number(o.agencyFee):Number(o.agencyFee);const ge=Ie(Number(o.amount),Number(o.agencyFee));W.carriers[o.currencyType]=W.carriers[o.currencyType]?W.carriers[o.currencyType]+ge:ge}if(o.ticketStatus==="RFND"){pe.amount[o.currencyType]=pe.amount[o.currencyType]?pe.amount[o.currencyType]+Number(o.amount):Number(o.amount),pe.taxAmount[o.currencyType]=pe.taxAmount[o.currencyType]?pe.taxAmount[o.currencyType]+Number(o.taxAmount):Number(o.taxAmount),pe.agencyFee[o.currencyType]=pe.agencyFee[o.currencyType]?pe.agencyFee[o.currencyType]+Number(o.agencyFee):Number(o.agencyFee);const ge=Ie(Number(o.amount),Number(o.agencyFee));pe.carriers[o.currencyType]=pe.carriers[o.currencyType]?pe.carriers[o.currencyType]+ge:ge}if(o.ticketStatus==="EXCH"){ue.amount[o.currencyType]=ue.amount[o.currencyType]?ue.amount[o.currencyType]+Number(o.amount):Number(o.amount),ue.taxAmount[o.currencyType]=ue.taxAmount[o.currencyType]?ue.taxAmount[o.currencyType]+Number(o.taxAmount):Number(o.taxAmount),ue.agencyFee[o.currencyType]=ue.agencyFee[o.currencyType]?ue.agencyFee[o.currencyType]+Number(o.agencyFee):Number(o.agencyFee);const ge=Ie(Number(o.amount),Number(o.agencyFee));ue.carriers[o.currencyType]=ue.carriers[o.currencyType]?ue.carriers[o.currencyType]+ge:ge}if(o.ticketStatus==="VOID"){de.amount[o.currencyType]=de.amount[o.currencyType]?de.amount[o.currencyType]+Number(o.amount):Number(o.amount),de.taxAmount[o.currencyType]=de.taxAmount[o.currencyType]?de.taxAmount[o.currencyType]+Number(o.taxAmount):Number(o.taxAmount),de.agencyFee[o.currencyType]=de.agencyFee[o.currencyType]?de.agencyFee[o.currencyType]+Number(o.agencyFee):Number(o.agencyFee);const ge=Ie(Number(o.amount),Number(o.agencyFee));de.carriers[o.currencyType]=de.carriers[o.currencyType]?de.carriers[o.currencyType]+ge:ge}K[o.currencyType]=K[o.currencyType]?K[o.currencyType]+Number(o.amount):Number(o.amount),ie[o.currencyType]=ie[o.currencyType]?ie[o.currencyType]+Number(o.taxAmount):Number(o.taxAmount),be[o.currencyType]=be[o.currencyType]?be[o.currencyType]+Number(o.agencyFee):Number(o.agencyFee)}),{ISSUA:W,REFUND:pe,EXCHANGE:ue,VOID:de,amount:K,taxAmount:ie,agencyFee:be,totalTicket:P.filter(o=>o.ticketStatus==="ISSU").length??0,totalVoid:P.filter(o=>o.ticketStatus==="VOID").length??0,totalRefund:P.filter(o=>o.ticketStatus==="RFND").length??0,totalExchange:P.filter(o=>o.ticketStatus==="EXCH").length??0}},De=()=>{if(g.value&&g.value.getBoundingClientRect().width!==0){const l=g.value.getBoundingClientRect(),P=$.value.length*50+50,K=`${document.documentElement.clientHeight-77-45-62-150-20-100}`;P>parseInt(K,10)?l.left<100?(k.value=`${document.documentElement.clientHeight-70-100+50+78+20}px`,c.value=`${document.documentElement.clientHeight-77-45-62-150-20-100-20+50+50+78+20-50}`):(k.value=`${document.documentElement.clientHeight-70-100+50+20}px`,c.value=`${document.documentElement.clientHeight-77-45-62-150-20-100-20+50+50+20-50}`):(k.value="100%",c.value=P.toString())}else{const l=$.value.length*50+50,P=`${document.documentElement.clientHeight-77-45-62-150-20-100}`;l>parseInt(P,10)?(k.value=`${document.documentElement.clientHeight-70-100}px`,c.value=`${document.documentElement.clientHeight-77-45-62-150-20-100-20+50}`):(k.value="100%",c.value=l.toString())}},ae=async()=>{var l;D.value=!1,Fe(),(l=U.value)==null||l.validate(async P=>{var be,W,pe,ue,de,o,ge,Ue,ce,Ae,Et,Dt,Ft,At,Ot,It,Pt,Vt,Ut,jt,Bt,Mt,Ht,Lt,zt,Yt,Gt,Qt,Xt,qt;if(!P)return;Ve();const K={date:w.searchDate,deviceNumber:w.prntNo,tktType:w.tktType,saleStatusCodes:[],value:"1",outerSign:!1},ie={date:w.searchDate,deviceNumber:w.prntNo,tktType:w.tktType,saleStatusCodes:["ISSU","RFND"],value:"2",outerSign:!0};ee.value=!0;try{if(w.searchDate!==Oe().format("YYYY-MM-DD")){const Te=await ot(K);f.value=Te.data.value}else{const[Te,Ne]=await Promise.allSettled([ot(K),ot(ie)]);Te.status==="fulfilled"&&(f.value=(W=(be=Te==null?void 0:Te.value)==null?void 0:be.data)==null?void 0:W.value),Ne.status==="fulfilled"&&(G.value=(ue=(pe=Ne==null?void 0:Ne.value)==null?void 0:pe.data)==null?void 0:ue.value,A.value=(de=G.value)==null?void 0:de.totalErrorNumber,H.value=(o=G.value)==null?void 0:o.issueErrorNumber,E.value=(ge=G.value)==null?void 0:ge.refundErrorNumber,I.querySaleDailyErrorRes=G.value,I.totalNumber=A.value,I.issueNumber=H.value,I.refundNumber=E.value)}if(w.ticketNumber||w.filterPayType){const Te=[],Ne=[],Ze=[],et=[],tt=[],at=[],nt=[];w.ticketNumber&&(f.value.items=(Ue=f==null?void 0:f.value)==null?void 0:Ue.items.filter(he=>{var Kt,Wt;return he.ticket===((Kt=w==null?void 0:w.ticketNumber)!=null&&Kt.includes("-")?w.ticketNumber:(Wt=w.ticketNumber)==null?void 0:Wt.replace(/(\d{3})(\d{7})/,"$1-$2"))}),Te.push((ce=f.value.items[0])==null?void 0:ce.airline),Ne.push((Ae=f.value.items[0])==null?void 0:Ae.ticketStatus),Ze.push((Et=f.value.items[0])==null?void 0:Et.prntNo),et.push((Dt=f.value.items[0])==null?void 0:Dt.jobNo),tt.push((Ft=f.value.items[0])==null?void 0:Ft.ticketType),at.push((At=f.value.items[0])==null?void 0:At.payType),nt.push((Ot=f.value.items[0])==null?void 0:Ot.currencyType),f.value.filter.airlines=Te,f.value.filter.currencyTypes=nt,f.value.filter.jobNos=et,f.value.filter.ticketKinds=tt,f.value.filter.ticketTypes=Ne,f.value.filter.prntNos=Ze,f.value.filter.payTypes=at),w.filterPayType&&(f.value.items=(w==null?void 0:w.filterPayType)==="AL"?(It=f==null?void 0:f.value)==null?void 0:It.items:(Pt=f==null?void 0:f.value)==null?void 0:Pt.items.filter(he=>he.payType===(w==null?void 0:w.filterPayType)),(w==null?void 0:w.filterPayType)!=="AL"&&(Te.push(...Array.from(new Set((Vt=f.value.items)==null?void 0:Vt.map(he=>he.airline)))),Ne.push(...Array.from(new Set((Ut=f.value.items)==null?void 0:Ut.map(he=>he.ticketStatus)))),Ze.push(...Array.from(new Set((jt=f.value.items)==null?void 0:jt.map(he=>he.prntNo)))),et.push(...Array.from(new Set((Bt=f.value.items)==null?void 0:Bt.map(he=>he.jobNo)))),tt.push(...Array.from(new Set((Mt=f.value.items)==null?void 0:Mt.map(he=>he.ticketType)))),at.push(...Array.from(new Set((Ht=f.value.items)==null?void 0:Ht.map(he=>he.payType)))),nt.push(...Array.from(new Set((Lt=f.value.items)==null?void 0:Lt.map(he=>he.currencyType)))),f.value.filter.airlines=Te,f.value.filter.currencyTypes=nt,f.value.filter.jobNos=et,f.value.filter.ticketKinds=tt,f.value.filter.ticketTypes=Ne,f.value.filter.prntNos=Ze,f.value.filter.payTypes=at))}C.value=Ge((zt=f.value)==null?void 0:zt.items);const bt=je((Yt=f.value)==null?void 0:Yt.items);J.value=bt==null?void 0:bt.sort((Te,Ne)=>new Date(Te.salesTime).getTime()-new Date(Ne.salesTime).getTime()).reverse(),$.value=w.isFilterTime?Ce(J.value):Ce((Gt=f.value)==null?void 0:Gt.items),Q.value=(Xt=(Qt=f.value)==null?void 0:Qt.items)==null?void 0:Xt.length,m=((qt=f.value)==null?void 0:qt.items)??[],De()}finally{ee.value=!1}})},ft=(l,P)=>{if(P!=null&&P.length)re.value.includes(l)||re.value.push(l);else{const K=re.value.indexOf(l);K!==-1&&re.value.splice(K,1)}},gt=(l,P,K)=>{var be,W,pe,ue,de,o,ge,Ue;ft(l,P),D.value=!0;const ie=je(P);K&&ie.push(K),b[l]=ie,m=((be=f.value)==null?void 0:be.items)??[],((W=b==null?void 0:b.airline)==null?void 0:W.length)>0&&(m=m.filter(ce=>b.airline.includes(ce.airline))),((pe=b==null?void 0:b.prntNo)==null?void 0:pe.length)>0&&(m=m.filter(ce=>b.prntNo.includes(ce.prntNo))),((ue=b==null?void 0:b.ticketStatus)==null?void 0:ue.length)>0&&(m=m.filter(ce=>b.ticketStatus.includes(ce.ticketStatus)),m.sort((ce,Ae)=>ce.salesTime.localeCompare(Ae.salesTime))),((de=b==null?void 0:b.payType)==null?void 0:de.length)>0&&(m=m.filter(ce=>b.payType.includes(ce.payType))),((o=b==null?void 0:b.jobNo)==null?void 0:o.length)>0&&(m=m.filter(ce=>b.jobNo.includes(ce.jobNo))),((ge=b==null?void 0:b.ticketType)==null?void 0:ge.length)>0&&(m=m.filter(ce=>b.ticketType.includes(ce.ticketType))),((Ue=b==null?void 0:b.currencyType)==null?void 0:Ue.length)>0&&(m=m.filter(ce=>b.currencyType.includes(ce.currencyType))),C.value=Ge(m),m=m.sort((ce,Ae)=>new Date(ce.salesTime).getTime()-new Date(Ae.salesTime).getTime()).reverse(),Z.value.pageNumber=1,$.value=Ce(m),Q.value=m.length,De()},ht=(l,P)=>{Z.value.pageNumber=l===0?1:l,Z.value.pageSize=P,w.isFilterTime&&!D.value?$.value=Ce(J.value):$.value=Ce(m),De()},Qe=[{name:r("app.agentReport.airline"),wch:17},{name:r("app.agentReport.tktNo"),wch:21},{name:r("app.agentReport.tktStatus"),wch:19},{name:r("app.agentReport.deviceNo"),wch:12},{name:r("app.agentReport.agent"),wch:8},{name:r("app.agentReport.segSE"),wch:12},{name:r("app.agentReport.tktSettle"),wch:14},{name:r("app.agentReport.tax"),wch:10},{name:r("app.agentReport.obTax"),wch:10},{name:r("app.agentReport.agency"),wch:12},{name:r("app.agentReport.agencyRate"),wch:12},{name:r("app.agentReport.handFee"),wch:12},{name:r("app.agentReport.refundNo"),wch:12},{name:"PNR",wch:8},{name:r("app.agentReport.payment"),wch:10},{name:r("app.agentReport.tktSymbol"),wch:10},{name:r("app.agentReport.saleDate"),wch:14},{name:r("app.agentReport.salesTime"),wch:14},{name:r("app.agentReport.curryType"),wch:12},{name:r("app.agentReport.tktType"),wch:18}],Xe=l=>{const P=new xa.Workbook;dn({...f.value,items:m},P),gn({...f.value,items:m},P),vn({...f.value,items:m},P),P.xlsx.writeBuffer().then(K=>{const ie=new Blob([K],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),be=URL.createObjectURL(ie),W=document.createElement("a");W.href=be,W.download=`${l}.xlsx`,W.click(),URL.revokeObjectURL(be)})},qe=()=>{const l=[],P=[];Qe.forEach(K=>{l.push(K.name),P.push({wch:K.wch})}),Xe(`${w.searchDate}销售日报`)},Ke=l=>{if(l)switch(l){case"全部":w.filterPayType="AL";break;case"(CA) 现金":w.filterPayType="CA";break;case"(CC) 信用卡":w.filterPayType="CC";break;case"(CK) 支票":w.filterPayType="CK";break;default:w.filterPayType=l;break}else w.filterPayType=w.filterPayTypeCopy;w.filterPayType=w.filterPayType.trim().toUpperCase()},We=()=>{w.filterPayTypeCopy=w.filterPayType},Je=async()=>{await Jt.confirm(r("app.agentReport.confirmExport"),"",{icon:vt("em",{class:"iconfont icon-info-circle-line"}),customClass:"sales-daily-msg-box crs-btn-ui",confirmButtonText:r("app.agentReport.confirm"),cancelButtonText:r("app.agentReport.cancel"),showClose:!1,autofocus:!1}),(m??[]).length<1?await Jt.alert(r("app.agentReport.dataNoEmpty"),{icon:vt("em",{class:"iconfont icon-info-circle-line"}),customClass:"iconStyle",showConfirmButton:!1,type:"warning"}):qe()};Be(()=>{window.addEventListener("resize",()=>{De()}),De(),Fe(),N(X.value)});const n=()=>{var be,W;if(!me.value||!me.value.includes("-"))return;const l=me.value.split("-")[0],P=((W=(be=l==null?void 0:l.substring(l.indexOf("TSL:")+4))==null?void 0:be.trim())==null?void 0:W.split("/"))??[],K=P[0]??"",ie=P[1];w.searchDate=Oe(ie?Na(ie):new Date).format("YYYY-MM-DD"),w.prntNo=K,ae()},S=async()=>{_e.value=!0},pa=(l,P,K)=>{A.value=l,H.value=P,E.value=K},ua=(l,P)=>{if(P==="")return[];if(P==="$$$")return l;const K=P.split(";").map(ie=>ie.trim()==="本票"?"PYN":ie.trim().toUpperCase());return l.filter(ie=>K.includes(ie.value))};return _t(me,()=>{n()}),_t(p,()=>{var l;d.value&&(w.prntNo=((l=p.value)==null?void 0:l.internationalPrinterno)??"")}),Be(()=>{var l,P;d.value&&(w.prntNo=((l=p.value)==null?void 0:l.internationalPrinterno)??""),se.value=ua(te,M.value),w.tktType=((P=se.value[0])==null?void 0:P.label)??"",j.value||(ae(),n())}),{isSatLink:j,salesForm:w,filterTypeList:re,querySaleDailyRes:f,salesDaliyDatas:$,rowSalesDaliyData:h,salesRules:dt,handleSearch:ae,salesRef:U,daliyTableRef:R,handleChangePage:ht,totalInfo:C,handleExport:Je,filterChange:gt,pageTotal:Q,isEmptyData:Pe,pageInfo:Z,refundTicket:X,showDialog:oe,showTodayExceptionsDialog:_e,showDialogFun:$e,showPrintNoDialog:ke,jumpToPnrEtQuery:ze,jumpToTcTicketQuery:Ye,doCopy:yt,params:ye,innerHeight:c,wrapHeight:k,salesDailyRef:g,openFilter:Le,filterRptRef:T,updatePageSizeNum:Fe,pageRef:B,isSalesDaily:F,datePrefix:ut,isLoading:ee,filterSaleTime:mt,handleTodayException:S,tktTypes:se,isToday:He,totalNumber:A,issueNumber:H,refundNumber:E,getErrorNumber:pa,storeTodayError:I,payTypes:ve,filterPayTypeRef:O,selectPayTypeBlur:Ke,selectPayTypeFocus:We,setRefundData:z,refundTicketType:L}},_n=(_,r)=>{const{t}=Rt(),x=xt(),c=Se(()=>{var H;return(H=x.state.user)==null?void 0:H.entityType}),g=V(),k=Se(()=>r.rowSalesDaliyData),U=V(!1),O=lt({printerNo:"",ticketOrganization:""}),F={ticketOrganization:[{required:!0,message:t("app.ticketStatus.deviceNumNull"),trigger:"blur"}],printerNo:[{required:!0,message:t("app.ticketStatus.deviceNumNull"),trigger:"blur"},{pattern:wa,trigger:"blur",message:t("app.ticketStatus.deviceError")}]},j=V([]),L={BSP:{label:t("app.agentTicketQuery.BSPTicket"),value:"BSP"},GPBSP:{label:t("app.agentTicketQuery.GPTicket"),value:"GPBSP"},BOPBSP:{label:t("app.agentTicketQuery.BOPTicket"),value:"BOPBSP"},CDS:{label:t("app.agentTicketQuery.CDSTicket"),value:"CDS"},GPCDS:{label:t("app.agentTicketQuery.GP_CDSTicket"),value:"GPCDS"},ARL:{label:t("app.agentTicketQuery.OWNTicket"),value:"ARL"}},B=Se(()=>!["CDS","GPCDS"].includes(O.ticketOrganization)),T=()=>{g.value.validate(H=>{!H&&k.value||I("",k.value.refundNo,k.value.ticketTypeCode=="D",O.printerNo,O.ticketOrganization)})},R=(H,E,A,$,h)=>({ticketNo:H,refundNo:E,ticketType:A?"D":"I",printerNo:$,ticketManagementOrganizationCode:h}),I=async(H,E,A,$,h)=>{var C;try{U.value=!0;const{data:Q}=await Sa(R(H,E,A,$,h),"");_("setRefundData",(C=Q.value)==null?void 0:C.data),_("update:modelValue",!1),U.value=!1}finally{U.value=!1}},f=()=>{_("update:modelValue",!1)},G=()=>{var H,E,A,$,h,C,Q,ee,J,D,b,d;((H=c.value)!=null&&H.includes("$$$")||(E=c.value)!=null&&E.includes("BSP"))&&(j.value.push(L.BSP),j.value.push(L.GPBSP)),!((A=c.value)!=null&&A.includes("BSP"))&&(($=c.value)!=null&&$.includes("GP"))&&j.value.push(L.GPBSP),((h=c.value)!=null&&h.includes("$$$")||(C=c.value)!=null&&C.includes("BOP"))&&j.value.push(L.BOPBSP),((Q=c.value)!=null&&Q.includes("$$$")||(ee=c.value)!=null&&ee.includes("CDS"))&&(j.value.push(L.CDS),j.value.push(L.GPCDS)),((J=c.value)!=null&&J.includes("$$$")||(D=c.value)!=null&&D.includes("本票"))&&j.value.push(L.ARL),O.ticketOrganization=((d=(b=j.value)==null?void 0:b[0])==null?void 0:d.value)??""};return Be(()=>{G(),O.printerNo=k.value.prntNo}),{formDate:g,printNoFrom:O,PRINTER_NO_RULES:F,ticketOrganizationList:j,isShowPrintNo:B,confirmPrinterNo:T,closeDialog:f,loading:U}},Tn=_n,xn=a("i",{class:"iconfont icon-close"},null,-1),Nn=[xn],Rn={class:"carType-option-panel"},Cn=Me({__name:"RefundParameterDialog",props:{rowSalesDaliyData:{}},emits:["update:modelValue","setRefundData"],setup(_,{emit:r}){const t=r,x=_,{formDate:c,printNoFrom:g,PRINTER_NO_RULES:k,ticketOrganizationList:U,isShowPrintNo:O,confirmPrinterNo:F,closeDialog:j,loading:L}=Tn(t,x);return(B,T)=>{const R=rt,I=it,f=pt,G=St,H=$t,E=ct,A=ca,$=wt;return s(),le(A,{title:B.$t("app.queryRefunds.selectPrint"),width:"680px",class:"print-no-dialog","show-close":!1,"close-on-click-modal":!1,onClose:e(j)},{footer:v(()=>[a("div",null,[Ct((s(),le(E,{type:"primary","data-gid":"091T0107",onClick:T[4]||(T[4]=h=>e(F)())},{default:v(()=>[fe(u(B.$t("app.ticketStatus.confirmBtn")),1)]),_:1})),[[$,e(L),void 0,{fullscreen:!0,lock:!0}]]),i(E,{onClick:e(j)},{default:v(()=>[fe(u(B.$t("app.ticketStatus.cancelBtn")),1)]),_:1},8,["onClick"])])]),default:v(()=>[a("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:T[0]||(T[0]=(...h)=>e(j)&&e(j)(...h))},Nn),i(H,{ref_key:"formDate",ref:c,model:e(g),rules:e(k),"label-position":"left","require-asterisk-position":"right"},{default:v(()=>[i(G,{prop:"ticketOrganization",label:B.$t("app.agentTicketQuery.ticketOrganization")},{default:v(()=>[i(f,{modelValue:e(g).ticketOrganization,"onUpdate:modelValue":T[1]||(T[1]=h=>e(g).ticketOrganization=h),class:"ticket-management-organization",disabled:e(g).ticketOrganization==="",placeholder:e(g).ticketOrganization===""?B.$t("app.agentTicketQuery.noData"):""},{default:v(()=>[(s(!0),y(q,null,ne(e(U),h=>(s(),le(I,{key:h.value,label:h.label,value:h.value},{default:v(()=>[a("div",Rn,[a("div",{class:xe(e(g).ticketOrganization===h.value?"show-select":"hidden-select")},[i(R,null,{default:v(()=>[i(e(aa))]),_:1})],2),a("span",null,u(h.label),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled","placeholder"])]),_:1},8,["label"]),e(O)?(s(),le(G,{key:0,prop:"printerNo",label:B.$t("app.ticketStatus.deviceNum")},{default:v(()=>[i(Ca,{modelValue:e(g).printerNo,"onUpdate:modelValue":[T[2]||(T[2]=h=>e(g).printerNo=h),T[3]||(T[3]=h=>e(c).validateField("printerNo"))],"select-class":"w-[340px]"},null,8,["modelValue"])]),_:1},8,["label"])):we("",!0)]),_:1},8,["model","rules"])]),_:1},8,["title","onClose"])}}});const Ee=_=>(ea("data-v-fe65f932"),_=_(),ta(),_),wn={class:"sales-daily-flex"},Sn={key:0,class:"card-checked"},$n={key:1,class:"card-no-checked"},En={key:0,class:"card-checked"},Dn={key:1,class:"card-no-checked"},Fn={class:"inline-block todayAbnormal"},An={key:1,class:"text-red-1 inline-block bg-red-3 leading-8 text-sm height-[32px] error-tip"},On={key:0,class:"result"},In={class:"search-info min-w-[380px]"},Pn=Ee(()=>a("span",{class:"search-info-title"},"OFFICE：",-1)),Vn=Ee(()=>a("span",{class:"search-info-title space-span"},"IATA NO：",-1)),Un={class:"search-info-title space-span"},jn={key:0},Bn=["onClick"],Mn=["onClick"],Hn={key:1},Ln=["onClick"],zn=["onClick"],Yn={key:1},Gn=["onClick"],Qn=["onClick"],Xn=Ee(()=>a("div",{class:"w-[100%] h-[135px] bg-white"},null,-1)),qn={class:"gap-5 shadow-[0px_-2px_7px_0px_rgba(0,0,0,0.04)] bottom-wrap"},Kn={class:"bottom-wrap-title"},Wn={class:"bottom-wrap-title-item"},Jn={class:"min-w-[220px] h-[22px] bg-[#f3ffe9] rounded-sm justify-start items-start gap-1 flex"},Zn=Ee(()=>a("div",{class:"w-[150px] h-[22px] px-1.5 bg-[#d9f7be] rounded-sm justify-end items-center flex"},[a("div",{class:"text-center text-[#237804] text-sm font-normal"},"TICKETS ISSU ")],-1)),eo={class:"h-[22px] text-neutral-800 text-sm font-normal items-center flex"},to={class:"min-w-[220px] h-[22px] bg-[#fff7f7] rounded-sm justify-start items-start gap-1 flex"},ao=Ee(()=>a("div",{class:"w-[150px] h-[22px] px-1.5 bg-[#fce6e6] rounded-sm justify-end items-center flex"},[a("div",{class:"text-center text-[#ff3636] text-sm font-normal"},"TICKETS REFUND ")],-1)),no={class:"h-[22px] text-neutral-800 text-sm font-normal items-center flex"},oo={class:"min-w-[220px] h-[22px] bg-[#fff7de] rounded-sm justify-start items-start gap-1 flex"},lo=Ee(()=>a("div",{class:"w-[150px] h-[22px] px-1.5 bg-[#feeeb9] rounded-sm justify-start items-center flex"},[a("div",{class:"text-center text-[#d48806] text-sm font-normal"},"TICKETS EXCHANGE ")],-1)),so={class:"h-[22px] text-neutral-800 text-sm font-normal items-center flex"},ro={class:"min-w-[220px] h-[22px] bg-[#ebf2ff] rounded-sm justify-start items-start gap-1 flex"},co=Ee(()=>a("div",{class:"w-[150px] h-[22px] px-1.5 bg-[#d6e4ff] rounded-sm justify-end items-center flex"},[a("div",{class:"text-center text-[#143889] text-sm font-normal"},"TICKETS VOID")],-1)),io={class:"h-[22px] text-neutral-800 text-sm font-normal items-center flex"},po={class:"bottom-wrap-desc gap-[30px]"},uo={class:"bottom-wrap-desc-item"},mo={class:"text-[#8c8c8c] text-sm font-normal"},yo={class:"!gap-2.5"},fo={key:0,class:"text-neutral-800 text-sm font-normal"},go={class:"text-[#8c8c8c] text-sm font-normal"},ho={class:"!gap-2.5"},bo={key:0,class:"text-neutral-800 text-sm font-normal"},vo={class:"text-[#8c8c8c] text-sm font-normal"},ko={class:"!gap-2.5"},_o={key:0,class:"text-neutral-800 text-sm font-normal"},To={class:"text-[#8c8c8c] text-sm font-normal"},xo={class:"!gap-2.5"},No={key:0,class:"text-neutral-800 text-sm font-normal"},Ro={class:"bottom-wrap-desc-item"},Co={class:"text-[#8c8c8c] text-sm font-normal"},wo={class:"!gap-2.5"},So={key:0,class:"text-neutral-800 text-sm font-normal"},$o={class:"text-[#8c8c8c] text-sm font-normal"},Eo={class:"!gap-2.5"},Do={key:0,class:"text-neutral-800 text-sm font-normal"},Fo={class:"text-[#8c8c8c] text-sm font-normal"},Ao={class:"!gap-2.5"},Oo={key:0,class:"text-neutral-800 text-sm font-normal"},Io={class:"text-[#8c8c8c] text-sm font-normal"},Po={class:"!gap-2.5"},Vo={key:0,class:"text-neutral-800 text-sm font-normal"},Uo={class:"bottom-wrap-desc-item"},jo={class:"text-[#8c8c8c] text-sm font-normal"},Bo={class:"!gap-2.5"},Mo={key:0,class:"text-neutral-800 text-sm font-normal"},Ho={class:"text-[#8c8c8c] text-sm font-normal"},Lo={class:"!gap-2.5"},zo={key:0,class:"text-neutral-800 text-sm font-normal"},Yo={class:"text-[#8c8c8c] text-sm font-normal"},Go={class:"!gap-2.5"},Qo={key:0,class:"text-neutral-800 text-sm font-normal"},Xo={class:"text-[#8c8c8c] text-sm font-normal"},qo={class:"!gap-2.5"},Ko={key:0,class:"text-neutral-800 text-sm font-normal"},Wo={class:"bottom-wrap-desc-item"},Jo={class:"text-[#8c8c8c] text-sm font-normal"},Zo={class:"!gap-2.5"},el={key:0,class:"text-neutral-800 text-sm font-normal"},tl={class:"text-[#8c8c8c] text-sm font-normal"},al={class:"!gap-2.5"},nl={key:0,class:"text-neutral-800 text-sm font-normal"},ol={class:"text-[#8c8c8c] text-sm font-normal"},ll={class:"!gap-2.5"},sl={key:0,class:"text-neutral-800 text-sm font-normal"},rl={class:"text-[#8c8c8c] text-sm font-normal"},cl={class:"!gap-2.5"},il={key:0,class:"text-neutral-800 text-sm font-normal"},pl={key:1,class:"empty-info"},ul=Ee(()=>a("img",{src:Zt,alt:"$t('app.agentReport.nodata')"},null,-1)),dl={class:"main-info"},ml={name:"SalesDaily"},yl=Me({...ml,props:{cmd:{}},setup(_){const r=_,{isSatLink:t,salesForm:x,salesRef:c,filterTypeList:g,daliyTableRef:k,querySaleDailyRes:U,salesDaliyDatas:O,rowSalesDaliyData:F,salesRules:j,handleSearch:L,filterChange:B,handleChangePage:T,totalInfo:R,handleExport:I,pageTotal:f,salesDailyRef:G,filterSaleTime:H,showPrintNoDialog:E,isEmptyData:A,pageInfo:$,refundTicket:h,showDialog:C,handleTodayException:Q,showTodayExceptionsDialog:ee,doCopy:J,params:D,openFilter:b,filterRptRef:d,pageRef:p,jumpToPnrEtQuery:M,jumpToTcTicketQuery:te,isLoading:se,isSalesDaily:ve,datePrefix:re,showDialogFun:me,tktTypes:Z,isToday:m,totalNumber:oe,issueNumber:ke,refundNumber:_e,getErrorNumber:X,storeTodayError:ye,payTypes:Pe,filterPayTypeRef:w,selectPayTypeBlur:ut,selectPayTypeFocus:He,setRefundData:dt,refundTicketType:mt}=kn(r);return(N,z)=>{var Qe,Xe,qe,Ke,We,Je;const yt=Ea,$e=St,Le=ba("Select"),Fe=rt,ze=it,Ye=pt,Ce=Nt,Ve=ct,Ge=Da,De=$t,ae=la,ft=ra,gt=sa,ht=wt;return Ct((s(),y("div",{ref_key:"salesDailyRef",ref:G,class:"crs-new-ui-init-cls sales-daily crs-btn-ui h-[100%]"},[a("div",wn,[i(De,{ref_key:"salesRef",ref:c,model:e(x),inline:!0,rules:e(j),"require-asterisk-position":"right",class:"sale-form"},{default:v(()=>[i($e,{label:N.$t("app.agentReport.date"),prop:"searchDate",class:"title-label"},{default:v(()=>[i(yt,{modelValue:e(x).searchDate,"onUpdate:modelValue":z[0]||(z[0]=n=>e(x).searchDate=n),editable:!1,type:"date",clearable:!1,"prefix-icon":e(re),"value-format":"YYYY-MM-DD"},null,8,["modelValue","prefix-icon"])]),_:1},8,["label"]),e(t)&&e(m)?(s(),le($e,{key:0,label:N.$t("app.agentReport.tktType")},{default:v(()=>[i(Ye,{modelValue:e(x).tktType,"onUpdate:modelValue":z[1]||(z[1]=n=>e(x).tktType=n),class:"card-type-select","popper-class":"select_card_popper"},{default:v(()=>[(s(!0),y(q,null,ne(e(Z),n=>(s(),le(ze,{key:n.value,label:N.$t(`app.agentReport.${n.label}`),value:n.value},{default:v(()=>[e(x).tktType===n.value?(s(),y("span",Sn,[i(Fe,{size:14},{default:v(()=>[i(Le)]),_:1})])):(s(),y("span",$n)),a("span",null,u(N.$t(`app.agentReport.${n.label}`)),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"])):we("",!0),i($e,{label:N.$t("app.agentReport.ticketMachineNumber"),prop:"prntNo",class:"print-ticket-number"},{default:v(()=>[i(Ce,{modelValue:e(x).prntNo,"onUpdate:modelValue":z[2]||(z[2]=n=>e(x).prntNo=n),modelModifiers:{trim:!0},size:"default",clearable:"",placeholder:N.$t("app.agentReport.printTicketNumber")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),i($e,{label:N.$t("app.agentReport.ticketNumber"),prop:"ticketNum",class:"print-ticket-number"},{default:v(()=>[i(Ce,{modelValue:e(x).ticketNumber,"onUpdate:modelValue":z[3]||(z[3]=n=>e(x).ticketNumber=n),modelModifiers:{trim:!0},size:"default",clearable:"",placeholder:N.$t("app.agentReport.ticketNumHolder")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),i($e,{label:N.$t("app.queryRefunds.payType"),prop:"filterPayType",class:"print-ticket-number"},{default:v(()=>[i(Ye,{ref_key:"filterPayTypeRef",ref:w,modelValue:e(x).filterPayType,"onUpdate:modelValue":z[4]||(z[4]=n=>e(x).filterPayType=n),modelModifiers:{trim:!0},"popper-class":"select_card_popper",clearable:"",filterable:"","allow-create":"","default-first-option":"","reserve-keyword":!1,style:{width:"240px"},onChange:e(ut),onVisibleChange:e(He)},{default:v(()=>[(s(!0),y(q,null,ne(e(Pe),n=>(s(),le(ze,{key:n.value,label:N.$t(`app.agentReport.${n.label}`),value:n.value},{default:v(()=>[e(x).filterPayType===n.value?(s(),y("span",En,[i(Fe,{size:14},{default:v(()=>[i(Le)]),_:1})])):(s(),y("span",Dn)),a("span",null,u(N.$t(`app.agentReport.${n.label}`)),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","onChange","onVisibleChange"])]),_:1},8,["label"]),i($e,null,{default:v(()=>[i(Ve,{"data-gid":"11280201",type:"primary",onClick:e(L)},{default:v(()=>[fe(u(N.$t("app.agentReport.search")),1)]),_:1},8,["onClick"])]),_:1}),a("div",Fn,[e(t)?(s(),le(Ve,{key:0,class:"today-error",type:"error",onClick:e(Q)},{default:v(()=>[fe(u(N.$t("app.agentReport.todayAbnormal")),1)]),_:1},8,["onClick"])):we("",!0),e(t)?(s(),y("span",An,u(N.$t("app.agentReport.totalError",{total:e(oe),issueError:e(ke),refundError:e(_e)})),1)):we("",!0)]),i($e,{label:N.$t("app.agentReport.sortedTime")},{default:v(()=>[i(Ge,{modelValue:e(x).isFilterTime,"onUpdate:modelValue":z[5]||(z[5]=n=>e(x).isFilterTime=n),"inline-prompt":"","active-text":N.$t("app.queryRefunds.yes"),"inactive-text":N.$t("app.queryRefunds.no"),onChange:e(H)},null,8,["modelValue","active-text","inactive-text","onChange"])]),_:1},8,["label"])]),_:1},8,["model","rules"]),(Qe=e(U))!=null&&Qe.office?(s(),y("div",On,[a("div",In,[Pn,fe(u((Xe=e(U))==null?void 0:Xe.office)+" ",1),Vn,fe(u((qe=e(U))==null?void 0:qe.iata)+" ",1),a("span",Un,u(N.$t("app.agentReport.saleDate"))+"：",1),fe(u((Ke=e(U))!=null&&Ke.items[0]?(We=e(U))==null?void 0:We.items[0].salesDate:e(x).searchDate),1)])])):we("",!0),i(Ve,{class:"export-btn","data-gid":"11280202",onClick:e(I)},{default:v(()=>[fe(u(N.$t("app.agentReport.export")),1)]),_:1},8,["onClick"])]),(Je=e(U))!=null&&Je.office?(s(),y("div",jn,[i(gt,{ref_key:"daliyTableRef",ref:k,data:e(O),stripe:"",style:{"min-width":"100%"},class:"sales-daily-table",onFilterChange:e(B)},{default:v(()=>[i(ae,{prop:"airline",width:"60px",fixed:"left"},{header:v(()=>[a("span",{class:xe(["pointer-span-drop",e(g).includes("airline")?"text-brand-2":""]),onClick:z[6]||(z[6]=n=>e(b)(0))},u(N.$t("app.agentReport.airline")),3),i(Re,{ref:n=>{n&&(e(d)[0]=n)},filters:e(U).filter.airlines,"show-filter-input":!0,"not-disabled":!0,"filter-input-placeholder":N.$t("app.agentReport.agentTip"),"column-key":"airline",onHandleConfrim:e(B)},null,8,["filters","filter-input-placeholder","onHandleConfrim"])]),_:1}),i(ae,{prop:"ticket",label:N.$t("app.agentReport.tktNo"),width:"150px",flexible:"true",fixed:"left"},{default:v(({row:n})=>[a("span",{class:"pointer-span",onClick:S=>e(te)(n.ticket,n.pnr)},u(n.ticket),9,Bn),a("em",{class:"pointer-span iconfont icon-copy copy-em",onClick:S=>e(J)(n.ticket)},null,8,Mn)]),_:1},8,["label"]),i(ae,{prop:"ticketStatus",width:"88px",fixed:"left"},{header:v(()=>[a("span",{class:xe(["pointer-span-drop",e(g).includes("ticketStatus")?"text-brand-2":""]),onClick:z[7]||(z[7]=n=>e(b)(1))},u(N.$t("app.agentReport.tktStatus")),3),i(Re,{ref:n=>{n&&(e(d)[1]=n)},filters:e(U).filter.ticketTypes,"column-key":"ticketStatus",onHandleConfrim:e(B)},null,8,["filters","onHandleConfrim"])]),default:v(({row:n})=>[i(ft,{class:xe(`${n.ticketStatus}-tag`)},{default:v(()=>[fe(u(n.ticketStatus),1)]),_:2},1032,["class"])]),_:1}),i(ae,{prop:"prntNo",width:"84px",fixed:""},{header:v(()=>[a("span",{class:xe(["pointer-span-drop",e(g).includes("prntNo")?"text-brand-2":""]),onClick:z[8]||(z[8]=n=>e(b)(2))},u(N.$t("app.agentReport.ticketMachineNumber")),3),i(Re,{ref:n=>{n&&(e(d)[2]=n)},filters:e(U).filter.prntNos,"column-key":"prntNo",onHandleConfrim:e(B)},null,8,["filters","onHandleConfrim"])]),_:1}),i(ae,{prop:"jobNo","min-width":"80px"},{header:v(()=>[a("span",{class:xe(["pointer-span-drop",e(g).includes("jobNo")?"text-brand-2":""]),onClick:z[9]||(z[9]=n=>e(b)(3))},u(N.$t("app.agentReport.agent")),3),i(Re,{ref:n=>{n&&(e(d)[3]=n)},filters:e(U).filter.jobNos,"show-filter-input":!0,"not-disabled":!0,"filter-input-placeholder":N.$t("app.agentReport.agentTip"),"column-key":"jobNo",onHandleConfrim:e(B)},null,8,["filters","filter-input-placeholder","onHandleConfrim"])]),_:1}),i(ae,{prop:"desArr",label:N.$t("app.agentReport.segSE"),"min-width":"90px"},null,8,["label"]),i(ae,{prop:"amount",label:N.$t("app.agentReport.tktSettle"),"min-width":"80px"},null,8,["label"]),i(ae,{prop:"taxAmount",label:N.$t("app.agentReport.tax"),"min-width":"80px"},null,8,["label"]),i(ae,{prop:"obTax",label:N.$t("app.agentReport.obTax"),"min-width":"80px"},{default:v(({row:n})=>[n.obTax==="0"?(s(),y(q,{key:0},[fe("0.00")],64)):(s(),y(q,{key:1},[fe(u(n.obTax),1)],64))]),_:1},8,["label"]),i(ae,{prop:"agencyFee",label:N.$t("app.agentReport.agency"),"min-width":"75px"},null,8,["label"]),i(ae,{prop:"agencyFeePercent",label:N.$t("app.agentReport.agencyRate"),"min-width":"75px"},null,8,["label"]),i(ae,{prop:"serviceCharge",label:N.$t("app.agentReport.handFee"),"min-width":"75px"},null,8,["label"]),i(ae,{prop:"refundNo",label:N.$t("app.agentReport.refundNo"),"min-width":"120px"},{default:v(({row:n})=>[e(A)(n.refundNo)?(s(),y(q,{key:0},[fe(" - ")],64)):(s(),y("div",Hn,[a("span",{class:"pointer-span",onClick:S=>e(me)(n)},u(n.refundNo),9,Ln),a("em",{class:"pointer-span iconfont icon-copy copy-em",onClick:S=>e(J)(n.refundNo)},null,8,zn)]))]),_:1},8,["label"]),i(ae,{prop:"pnr",label:"PNR","min-width":"105px"},{default:v(({row:n})=>[e(A)(n.pnr)?(s(),y(q,{key:0},[],64)):(s(),y("div",Yn,[a("span",{class:"pointer-span",onClick:S=>e(M)(n.pnr)},u(n.pnr),9,Gn),a("em",{class:"pointer-span iconfont icon-copy copy-em",onClick:S=>e(J)(n.pnr)},null,8,Qn)]))]),_:1}),i(ae,{prop:"ticketType","min-width":"125px"},{header:v(()=>[a("span",{class:xe(["pointer-span-drop",e(g).includes("ticketType")?"text-brand-2":""]),onClick:z[10]||(z[10]=n=>e(b)(6))},u(N.$t("app.agentReport.tktType")),3),i(Re,{ref:n=>{n&&(e(d)[6]=n)},filters:e(U).filter.ticketKinds,"column-key":"ticketType",onHandleConfrim:e(B)},null,8,["filters","onHandleConfrim"])]),_:1}),i(ae,{prop:"payType","min-width":"100px"},{header:v(()=>[a("span",{class:xe(["pointer-span-drop",e(g).includes("payType")?"text-brand-2":""]),onClick:z[11]||(z[11]=n=>e(b)(4))},u(N.$t("app.agentReport.payment")),3),i(Re,{ref:n=>{n&&(e(d)[4]=n)},filters:e(U).filter.payTypes,"column-key":"payType",onHandleConfrim:e(B)},null,8,["filters","onHandleConfrim"])]),_:1}),i(ae,{prop:"couponNo",label:N.$t("app.agentReport.tktSymbol")},null,8,["label"]),i(ae,{prop:"currencyType","min-width":"75px"},{header:v(()=>[a("span",{class:xe(["pointer-span-drop",e(g).includes("currencyType")?"text-brand-2":""]),onClick:z[12]||(z[12]=n=>e(b)(5))},u(N.$t("app.agentReport.curryType")),3),i(Re,{ref:n=>{n&&(e(d)[5]=n)},filters:e(U).filter.currencyTypes,"column-key":"currencyType",onHandleConfrim:e(B)},null,8,["filters","onHandleConfrim"])]),_:1})]),_:1},8,["data","onFilterChange"]),i(e($a),{ref:n=>{n&&(p.value=n)},class:"sales-daily-page crs-pagination-ui",total:e(f),"current-page":e($).pageNumber,"page-size":20,onHandleChange:e(T)},null,8,["total","current-page","onHandleChange"]),Xn,a("div",qn,[a("div",Kn,[a("div",Wn,[a("div",null,[a("div",Jn,[Zn,a("div",eo,u(e(R).totalTicket),1)])]),a("div",null,[a("div",to,[ao,a("div",no,u(e(R).totalRefund),1)])]),a("div",null,[a("div",oo,[lo,a("div",so,u(e(R).totalExchange),1)])]),a("div",null,[a("div",ro,[co,a("div",io,u(e(R).totalVoid),1)])])])]),a("div",po,[a("div",uo,[a("div",null,[a("label",mo,u(N.$t("app.agentReport.tktSettleBottom")),1),a("div",yo,[e(O).length<1||Object.keys(e(R).ISSUA.amount).length===0?(s(),y("span",fo,"0")):(s(!0),y(q,{key:1},ne(e(R).ISSUA.amount,(n,S)=>(s(),y("span",{key:S,class:"text-neutral-800 text-sm font-normal"},u(`${S} ${n.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",go,u(N.$t("app.agentReport.tktSettleBottom")),1),a("div",ho,[e(O).length<1||Object.keys(e(R).REFUND.amount).length===0?(s(),y("span",bo,"0")):(s(!0),y(q,{key:1},ne(e(R).REFUND.amount,(n,S)=>(s(),y("span",{key:S,class:"text-neutral-800 text-sm font-normal"},u(`${S} ${n.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",vo,u(N.$t("app.agentReport.tktSettleBottom")),1),a("div",ko,[e(O).length<1||Object.keys(e(R).EXCHANGE.amount).length===0?(s(),y("span",_o,"0")):(s(!0),y(q,{key:1},ne(e(R).EXCHANGE.amount,(n,S)=>(s(),y("span",{key:S,class:"text-neutral-800 text-sm font-normal"},u(`${S} ${n.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",To,u(N.$t("app.agentReport.tktSettleBottom")),1),a("div",xo,[e(O).length<1||Object.keys(e(R).VOID.amount).length===0?(s(),y("span",No,"0")):(s(!0),y(q,{key:1},ne(e(R).VOID.amount,(n,S)=>(s(),y("span",{key:S,class:"text-neutral-800 text-sm font-normal"},u(`${S} ${n.toFixed(2)}`),1))),128))])])]),a("div",Ro,[a("div",null,[a("label",Co,u(N.$t("app.agentReport.taxBottom")),1),a("div",wo,[e(O).length<1||Object.keys(e(R).ISSUA.taxAmount).length===0?(s(),y("span",So,"0")):(s(!0),y(q,{key:1},ne(e(R).ISSUA.taxAmount,(n,S)=>(s(),y("span",{key:S,class:"text-neutral-800 text-sm font-normal"},u(`${S} ${n.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",$o,u(N.$t("app.agentReport.taxBottom")),1),a("div",Eo,[e(O).length<1||Object.keys(e(R).REFUND.taxAmount).length===0?(s(),y("span",Do,"0")):(s(!0),y(q,{key:1},ne(e(R).REFUND.taxAmount,(n,S)=>(s(),y("span",{key:S,class:"text-neutral-800 text-sm font-normal"},u(`${S} ${n.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",Fo,u(N.$t("app.agentReport.taxBottom")),1),a("div",Ao,[e(O).length<1||Object.keys(e(R).EXCHANGE.taxAmount).length===0?(s(),y("span",Oo,"0")):(s(!0),y(q,{key:1},ne(e(R).EXCHANGE.taxAmount,(n,S)=>(s(),y("span",{key:S,class:"text-neutral-800 text-sm font-normal"},u(`${S} ${n.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",Io,u(N.$t("app.agentReport.taxBottom")),1),a("div",Po,[e(O).length<1||Object.keys(e(R).VOID.taxAmount).length===0?(s(),y("span",Vo,"0")):(s(!0),y(q,{key:1},ne(e(R).VOID.taxAmount,(n,S)=>(s(),y("span",{key:S,class:"text-neutral-800 text-sm font-normal"},u(`${S} ${n.toFixed(2)}`),1))),128))])])]),a("div",Uo,[a("div",null,[a("label",jo,u(N.$t("app.agentReport.agencyBottom")),1),a("div",Bo,[e(O).length<1||Object.keys(e(R).ISSUA.agencyFee).length===0?(s(),y("span",Mo,"0")):(s(!0),y(q,{key:1},ne(e(R).ISSUA.agencyFee,(n,S)=>(s(),y("span",{key:S,class:"text-neutral-800 text-sm font-normal"},u(`${S} ${n.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",Ho,u(N.$t("app.agentReport.agencyBottom")),1),a("div",Lo,[e(O).length<1||Object.keys(e(R).REFUND.agencyFee).length===0?(s(),y("span",zo,"0")):(s(!0),y(q,{key:1},ne(e(R).REFUND.agencyFee,(n,S)=>(s(),y("span",{key:S,class:"text-neutral-800 text-sm font-normal"},u(`${S} ${n.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",Yo,u(N.$t("app.agentReport.agencyBottom")),1),a("div",Go,[e(O).length<1||Object.keys(e(R).EXCHANGE.agencyFee).length===0?(s(),y("span",Qo,"0")):(s(!0),y(q,{key:1},ne(e(R).EXCHANGE.agencyFee,(n,S)=>(s(),y("span",{key:S,class:"text-neutral-800 text-sm font-normal"},u(`${S} ${n.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",Xo,u(N.$t("app.agentReport.agencyBottom")),1),a("div",qo,[e(O).length<1||Object.keys(e(R).VOID.agencyFee).length===0?(s(),y("span",Ko,"0")):(s(!0),y(q,{key:1},ne(e(R).VOID.agencyFee,(n,S)=>(s(),y("span",{key:S,class:"text-neutral-800 text-sm font-normal"},u(`${S} ${n.toFixed(2)}`),1))),128))])])]),a("div",Wo,[a("div",null,[a("label",Jo,u(N.$t("app.agentReport.carriers")),1),a("div",Zo,[e(O).length<1||Object.keys(e(R).ISSUA.carriers).length===0?(s(),y("span",el,"0")):(s(!0),y(q,{key:1},ne(e(R).ISSUA.carriers,(n,S)=>(s(),y("span",{key:S,class:"text-neutral-800 text-sm font-normal"},u(`${S} ${n.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",tl,u(N.$t("app.agentReport.carriers")),1),a("div",al,[e(O).length<1||Object.keys(e(R).REFUND.carriers).length===0?(s(),y("span",nl,"0")):(s(!0),y(q,{key:1},ne(e(R).REFUND.carriers,(n,S)=>(s(),y("span",{key:S,class:"text-neutral-800 text-sm font-normal"},u(`${S} ${n.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",ol,u(N.$t("app.agentReport.carriers")),1),a("div",ll,[e(O).length<1||Object.keys(e(R).EXCHANGE.carriers).length===0?(s(),y("span",sl,"0")):(s(!0),y(q,{key:1},ne(e(R).EXCHANGE.carriers,(n,S)=>(s(),y("span",{key:S,class:"text-neutral-800 text-sm font-normal"},u(`${S} ${n.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",rl,u(N.$t("app.agentReport.carriers")),1),a("div",cl,[e(O).length<1||Object.keys(e(R).VOID.carriers).length===0?(s(),y("span",il,"0")):(s(!0),y(q,{key:1},ne(e(R).VOID.carriers,(n,S)=>(s(),y("span",{key:S,class:"text-neutral-800 text-sm font-normal"},u(`${S} ${n.toFixed(2)}`),1))),128))])])])])]),e(C)?(s(),le(Ra,{key:0,modelValue:e(C),"onUpdate:modelValue":z[13]||(z[13]=n=>kt(C)?C.value=n:null),"printer-no":e(D).printerNo,"printer-type":e(mt),"is-supplement-refund":!1,"refund-operation-condition":e(D),"refund-ticket-data":e(h),"is-sales-daily":e(ve),onReSalesDaily:e(L)},null,8,["modelValue","printer-no","printer-type","refund-operation-condition","refund-ticket-data","is-sales-daily","onReSalesDaily"])):we("",!0)])):(s(),y("div",pl,[a("div",null,[ul,a("div",dl,u(N.$t("app.agentReport.nodata")),1)])])),e(ee)?(s(),le(rn,{key:2,modelValue:e(ee),"onUpdate:modelValue":z[14]||(z[14]=n=>kt(ee)?ee.value=n:null),"sales-form":e(x),"store-today-error":e(ye),"tkt-types":e(Z),onErrorNumber:e(X)},null,8,["modelValue","sales-form","store-today-error","tkt-types","onErrorNumber"])):we("",!0),e(E)?(s(),le(Cn,{key:3,modelValue:e(E),"onUpdate:modelValue":z[15]||(z[15]=n=>kt(E)?E.value=n:null),"row-sales-daliy-data":e(F),onSetRefundData:e(dt)},null,8,["modelValue","row-sales-daliy-data","onSetRefundData"])):we("",!0)])),[[ht,e(se)]])}}});const Zl=oa(yl,[["__scopeId","data-v-fe65f932"]]);export{Zl as default};
