import{b as d,u as a,E as R}from"./index-fe3402a7.js";import{L as U,hr as D,q as w,w as n,v as T,r as $,A as p,a3 as V,x as u,y as L,z as c,B as O,D as z,Q as v,J as b,F as m,ak as F,X as H,_ as I,hx as K,K as q}from"./index-a2fbd71b.js";import{d as f}from"./dropdown-7f4180b8.js";const J=U({trigger:d.trigger,placement:f.placement,disabled:d.disabled,visible:a.visible,transition:a.transition,popperOptions:f.popperOptions,tabindex:f.tabindex,content:a.content,popperStyle:a.popperStyle,popperClass:a.popperClass,enterable:{...a.enterable,default:!0},effect:{...a.effect,default:"light"},teleported:a.teleported,title:String,width:{type:[String,Number],default:150},offset:{type:Number,default:void 0},showAfter:{type:Number,default:0},hideAfter:{type:Number,default:200},autoClose:{type:Number,default:0},showArrow:{type:Boolean,default:!0},persistent:{type:Boolean,default:!0},"onUpdate:visible":{type:Function}}),Q={"update:visible":t=>D(t),"before-enter":()=>!0,"before-leave":()=>!0,"after-enter":()=>!0,"after-leave":()=>!0},X="onUpdate:visible",j=w({name:"ElPopover"}),G=w({...j,props:J,emits:Q,setup(t,{expose:r,emit:s}){const o=t,g=n(()=>o[X]),l=T("popover"),i=$(),y=n(()=>{var e;return(e=p(i))==null?void 0:e.popperRef}),E=n(()=>[{width:V(o.width)},o.popperStyle]),P=n(()=>[l.b(),o.popperClass,{[l.m("plain")]:!!o.content}]),C=n(()=>o.transition===`${l.namespace.value}-fade-in-linear`),k=()=>{var e;(e=i.value)==null||e.hide()},B=()=>{s("before-enter")},S=()=>{s("before-leave")},A=()=>{s("after-enter")},N=()=>{s("update:visible",!1),s("after-leave")};return r({popperRef:y,hide:k}),(e,_)=>(u(),L(p(R),H({ref_key:"tooltipRef",ref:i},e.$attrs,{trigger:e.trigger,placement:e.placement,disabled:e.disabled,visible:e.visible,transition:e.transition,"popper-options":e.popperOptions,tabindex:e.tabindex,content:e.content,offset:e.offset,"show-after":e.showAfter,"hide-after":e.hideAfter,"auto-close":e.autoClose,"show-arrow":e.showArrow,"aria-label":e.title,effect:e.effect,enterable:e.enterable,"popper-class":p(P),"popper-style":p(E),teleported:e.teleported,persistent:e.persistent,"gpu-acceleration":p(C),"onUpdate:visible":p(g),onBeforeShow:B,onBeforeHide:S,onShow:A,onHide:N}),{content:c(()=>[e.title?(u(),O("div",{key:0,class:z(p(l).e("title")),role:"title"},v(e.title),3)):b("v-if",!0),m(e.$slots,"default",{},()=>[F(v(e.content),1)])]),default:c(()=>[e.$slots.reference?m(e.$slots,"reference",{key:0}):b("v-if",!0)]),_:3},16,["trigger","placement","disabled","visible","transition","popper-options","tabindex","content","offset","show-after","hide-after","auto-close","show-arrow","aria-label","effect","enterable","popper-class","popper-style","teleported","persistent","gpu-acceleration","onUpdate:visible"]))}});var M=I(G,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/popover/src/popover.vue"]]);const h=(t,r)=>{const s=r.arg||r.value,o=s==null?void 0:s.popperRef;o&&(o.triggerRef=t)};var W={mounted(t,r){h(t,r)},updated(t,r){h(t,r)}};const Y="popover",Z=K(W,Y),oe=q(M,{directive:Z});export{oe as E};
